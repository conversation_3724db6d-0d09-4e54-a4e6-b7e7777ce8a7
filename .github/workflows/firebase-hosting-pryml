# # This file was auto-generated by the Firebase CLI
# # https://github.com/firebase/firebase-tools

# name: Deploy to Firebase Hosting on Pull Request
# 'on':
#   pull_request:
#     branches:
#       - development

# jobs:
#   build:
#     runs-on: ubuntu-latest
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v2

#       - name: Setup Node.js
#         uses: actions/setup-node@v2
#         with:
#           node-version: 16.15.1

#       #      - name: Create NPMRC
#       #        run: |
#       #           echo '${{ secrets.NPM_PKG_TOKEN }}'
#       #            echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_PKG_TOKEN }}" >> ~/.npmrc
#       #            echo "@shoutout-labs:registry=https://npm.pkg.github.com" >> ~/.npmrc
#       #            echo 'registry "https://registry.yarnpkg.com"' >> ~/.yarnrc

#       - run: 'cp env.production.cicd .env.production.local'
#       - run: 'rm .npmrc'
#         shell: bash

#       - run: 'echo "//npm.pkg.github.com/:_authToken=$NPM_TOKEN" >> .npmrc'
#         shell: bash
#         env:
#           NPM_TOKEN: ${{ secrets.NPM_PKG_TOKEN }}
#       - run: 'echo "@shoutout-labs:registry=https://npm.pkg.github.com/shoutout-labs" >> .npmrc'
#         shell: bash
#       - run: 'npm install -g npm-install-peers'
#       - run: 'npm install --legacy-peer-deps'
#       - run: 'npm test'
#       - run: 'npm run build:staging:github'
#         env:
#           REACT_APP_BUILD_VARIANT: dev
#       #         shell: bash
#       #       - run: 'npm test'
#       #         shell: bash
#       #       - uses: borales/actions-yarn@v2.3.0
#       #         with:
#       #           auth-token: ${{ secrets.NPM_PKG_TOKEN }}
#       #           registry-url: https://npm.pkg.github.com/shoutout-labs
#       #           cmd: install # will run `yarn install` command
#       #       - uses: borales/actions-yarn@v2.3.0
#       #         with:
#       #           cmd: build # will run `yarn build` command
#       #       - uses: borales/actions-yarn@v2.3.0
#       #         with:
#       #           cmd: test # will run `yarn test` command
#       - name: Archive Production Artifact
#         uses: actions/upload-artifact@master
#         with:
#           name: build
#           path: build

#   deploy:
#     name: Deploy
#     needs: build
#     runs-on: ubuntu-latest
#     steps:
#       - name: Checkout Repo
#         uses: actions/checkout@master
#       - name: Download Artifact
#         uses: actions/download-artifact@master
#         with:
#           name: build
#           path: build
#       - uses: FirebaseExtended/action-hosting-deploy@v0
#         with:
#           repoToken: '${{ secrets.GITHUB_TOKEN }}'
#           firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_SHOUTOUT_39625 }}'
#           channelId: live
#           projectId: shoutout-39625
#           target: staging
#         env:
#           FIREBASE_CLI_PREVIEWS: hostingchannels

#       - name: Slack Notification
#         uses: rtCamp/action-slack-notify@v2
#         env:
#           SLACK_USERNAME: ShoutOUT Enterprise Loyalty Staging
#           SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
#           SLACK_TITLE: New staging version deployed to Firebase
#           SLACK_MESSAGE: New staging version of Enterprise Loyalty Dashboard is available on https://stagingadmin.topnotch.club
#           SLACK_ICON_EMOJI: hammer_and_wrench
#           SLACK_COLOR: ${{ job.status }}
