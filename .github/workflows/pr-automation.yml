name: Create Pull Requests
run-name: ${{ github.actor }} automatic pr creation

on:
  push:
    branches:
      - development

jobs:
  create_pull_requests:
    runs-on: ubuntu-latest
    steps:
      - name: 'Generate token'
        id: generate_token
        uses: tibdex/github-app-token@v1
        with:
          app_id: ${{ secrets.RELEASE_BOT_APP_ID }}
          private_key: ${{ secrets.RELEASE_BOT_APP_SECRET }}
          
      - name: 'Checking out code'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ steps.generate_token.outputs.token }}

      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Create Pull Requests
        env:
          GH_TOKEN: ${{ steps.generate_token.outputs.token }}
        run: |
          git fetch --all

          # List all branches starting with 'development-sprint-' except 'development'
          branches=$(git branch -r | grep 'origin/development-sprint-' | grep -v 'origin/development$' | sed 's|origin/||')
          echo "Branches found: $branches"

          # Define reviewers
          # reviewers=("maduraPradeep" "mevansupe" "Sh-Labs-IsuruG")
          reviewers=("maduraPradeep")

          # Iterate through each branch and create a PR
          for branch in $branches; do
            echo "Creating Pull Request from development to $branch"
            title="Merge changes from development to $branch"
            body="This pull request was automatically generated by a GitHub Action.\n\nSource Branch: development\nTarget Branch: $branch"
            response=$(curl -s -X POST -H "Authorization: token $GH_TOKEN" -d "{\"title\":\"$title\",\"body\":\"$body\",\"head\":\"development\",\"base\":\"$branch\"}" https://api.github.com/repos/${{ github.repository }}/pulls)
            
            pr_number=$(echo $response | jq -r '.number')
            if [ "$pr_number" == "null" ]; then
              echo "Failed to create PR for $branch: $(echo $response | jq -r '.message')"
              continue
            fi
            echo "Pull Request created for $branch: $(echo $response | jq -r '.html_url')"

            # Add reviewers to the pull request
            reviewers_json=$(printf '%s\n' "${reviewers[@]}" | jq -R . | jq -s .)
            reviewers_response=$(curl -s -X POST -H "Authorization: token $GH_TOKEN" -d "{\"reviewers\": $reviewers_json}" https://api.github.com/repos/${{ github.repository }}/pulls/$pr_number/requested_reviewers)
            echo "Reviewers added to the pull request for $branch: $(echo $reviewers_response | jq -r '.html_url')"
          done
