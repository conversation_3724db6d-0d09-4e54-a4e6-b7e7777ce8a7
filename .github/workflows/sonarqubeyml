name: Build

on:
  push:
    branches:
      - audit*
      - development
      - development-sprint-*
  schedule:
    - cron: '0 1 * * *' # every day at 1
env:
  PROJECT_KEY: shoutout_loyalty_service

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0 
      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      # - name: Sleep for 10 seconds
      #   run: sleep 10
      - name: Check SonarQube Status
        run: |
          curl -u ${{ secrets.SONAR_USERNAME }}:${{ secrets.SONAR_PASSWORD }} "${{ secrets.SONAR_HOST_URL }}/api/qualitygates/project_status?projectKey=${{ env.PROJECT_KEY }}" > result.json
          cat result.json
          status=$(cat result.json | jq -r '.projectStatus.status')
          echo "SonarQube Status: $status"
          if [[ "$status" == "ERROR" ]]; then
            echo "SonarQube Quality Gate is red. Code quality issues detected."
            exit 1
          elif [[ "$status" == "OK" ]]; then
            echo "SonarQube Quality Gate is green. Code quality is good."
          else
            echo "SonarQube Quality Gate is not OK or ERROR. Skipping further actions."
            exit 0
          fi

  delete-artifacts:
    runs-on: ubuntu-latest
    steps:
      - uses: kolpav/purge-artifacts-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          expire-in: 3days
