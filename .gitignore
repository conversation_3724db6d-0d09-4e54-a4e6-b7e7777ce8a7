# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Next.js build output (legacy)
.next
out

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

.firebase
.env.local
firebase-*.log
resources/keycloak/celeste-daily/node_modules
resources/keycloak/celeste-daily/build
resources/keycloak/celeste-daily/build_keycloak
resources/keycloak/spar-supermarket-demo/node_modules
resources/keycloak/spar-supermarket-demo/build
resources/keycloak/spar-supermarket-demo/build_keycloak
resources/cx-forge-customer-portal-keycloakify/node_modules
resources/cx-forge-customer-portal-keycloakify/build
resources/cx-forge-customer-portal-keycloakify/build_keycloak
resources/keycloak/cx_forge_customer_portal_keycloak_theme/node_modules
resources/keycloak/cx_forge_customer_portal_keycloak_theme/build
resources/keycloak/cx_forge_customer_portal_keycloak_theme/build_keycloak
.DS_Store

/migration/src/markdown/customer_templates/Celeste/RAW_*.md
/migration/src/markdown/customer_templates/Celeste/*.docx