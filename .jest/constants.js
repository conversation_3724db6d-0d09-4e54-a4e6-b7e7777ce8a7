const mongoose = require('mongoose');
const Card = require('./../lib/db/models/card.model');

const cardNumberEmbossed = '123456789012';
const cardIdEmbossed = '6555be201f866c97680f7cdc';
const cardPrintJobIdEmbossed = '6555bf1b2935b8b1db5d1db9';
const cardNumberEmbossedWithoutMemberPostalAddress = '123456789013';
const cardIdEmbossedWithoutMemberPostalAddress = '65646eba3417c5cacc1c7d62';
const cardPrintJobIdEmbossedWithoutMemberPostalAddress = '65646d31400522f2dcb53b1a';
const member2Id = '624fe7906a799cdc58714c05';
const merchant2Id = '625460dcb81a7d7af77b06dd';
const affinityGroup1Id = '6253e9d264467af5f2869c43';
const affinityGroup2Id = '6253e9d87089fa6ab294d506';
const secondaryMemberId = '626aeda5fae2f3903c6bbc0b';
const redeemPointsSubTransactionId = '624fe1e3db9efd82dcaf7ef9';
const adjustmentAddSubTransactionId = '624fe3ad520f9ed417e67a04';
const adjustmentSubtractSubTransactionId = '624fe3b16727a837810786ea';
const enrollBonusCollectionSubTransactionId = '66ab0e7dd8ca06e379deb9f8';
const redemptionLogItemId2 = '625db79ebeb31b5b69bf0790';
const tierId = '61954673423d24e396dc3ce0';
const expiryPoints = '66d97b5fe4e301d4f354f91c';
const billReversalAdjustmentPlus = '6752ac9f8e76ddef69481971';
const billReversalAdjustmentMinus = '6752aca4895460bab40cdf9c';

const getCard = async (id) => {
    return await Card.find({ _id: new mongoose.Types.ObjectId(id) });
};
const getEmbossedCard = async () => {
    return await getCard(cardIdEmbossed);
};

const getcardEmbossedWithoutMemberPostalAddress = async () => {
    return await getCard(cardPrintJobIdEmbossed);
};
module.exports = {
    cardNumberEmbossed,
    cardIdEmbossed,
    cardPrintJobIdEmbossedWithoutMemberPostalAddress,
    cardPrintJobIdEmbossed,
    cardNumberEmbossedWithoutMemberPostalAddress,
    cardIdEmbossedWithoutMemberPostalAddress,
    organizationId: process.env.ORGANIZATION_ID,
    regionId: process.env.REGION_ID,
    userId: process.env.USER_ID,
    apiUsername: process.env.API_USER_EMAIL,
    apiPassword: process.env.API_USER_PASSWORD,
    apiMerchantUsername: process.env.API_MERCHANT_USER_EMAIL,
    apiMerchantPassword: process.env.API_MERCHANT_USER_PASSWORD,
    keycloakAuthUrl: process.env.KEYCLOAK_AUTH_URL,
    merchantAccessToken: process.env.MERCHANT_ACCESS_TOKEN,
    merchantId: process.env.MERCHANT_ID,
    merchantLocationId: process.env.MERCHANT_LOCATION_ID,
    redemptionLogItemId: process.env.REDEMPTION_LOG_ITEM_ID,
    redemptionLogItemId2,
    userAccessToken: process.env.USER_ACCESS_TOKEN,
    memberId: process.env.MEMBER_ID,
    member2Id: member2Id,
    secondaryMemberId,
    merchant2Id,
    affinityGroup1Id,
    affinityGroup2Id,
    redeemPointsSubTransactionId: redeemPointsSubTransactionId,
    adjustmentAddSubTransactionId: adjustmentAddSubTransactionId,
    adjustmentSubtractSubTransactionId,
    enrollBonusCollectionSubTransactionId,
    tierId,
    expiryPoints,
    getEmbossedCard,
    getCard,
    getcardEmbossedWithoutMemberPostalAddress,
    billReversalAdjustmentPlus,
    billReversalAdjustmentMinus
};
