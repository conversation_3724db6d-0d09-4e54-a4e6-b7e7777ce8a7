const fs = require('fs');
const dotenv = require('dotenv');

console.log('Setting up env...');
// Function to load environment variables from a file
const loadEnvFromFile = (filePath) => {
    const envContent = fs.readFileSync(filePath, 'utf8');
    const parsedEnv = dotenv.parse(envContent);
    for (const key in parsedEnv) {
        process.env[key] = parsedEnv[key];
    }
};

// Load environment variables from both files
loadEnvFromFile('.env');
loadEnvFromFile('.env.test');

process.env.API_USER_EMAIL = '<EMAIL>';
process.env.API_USER_PASSWORD = 'massy@1234';
process.env.API_MERCHANT_USER_EMAIL = '<EMAIL>';
process.env.API_MERCHANT_USER_PASSWORD = 'mevan12345';

process.env.ORGANIZATION_ID = '6128e3537ed841e246e2e394';
process.env.REGION_ID = '6137a9fff48b8eb1845c78cf';
process.env.USER_ID = '613dfc56eac5369848619fe0';
process.env.MERCHANT_ID = '61681a20aebfdb7bcee4fe10'; //todo: change dummy data
process.env.MERCHANT_LOCATION_ID = '616957aff2ac7710eb25e094'; //todo: change dummy data
process.env.MEMBER_ID = '619c732af1142b6ffc9c32e0';
process.env.PARTNER_REWARD_ID = '619d26ce8a1132c0dd9a8a26';
process.env.REWARD_ID = '619d280027d3080141d13631';
process.env.PARTNER_REWARD_BUNDLE_ID = '619d26d36bfec48ae8c0e3ec';
process.env.REDEMPTION_LOG_ITEM_ID = '619d2a9c86d527255a649864';
process.env.TRANSACTION_ID = '619db72a3f6ccd502feea35f';
