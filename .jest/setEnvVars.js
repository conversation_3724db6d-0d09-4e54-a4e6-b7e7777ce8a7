const fs = require('fs');
const dotenv = require('dotenv');

console.log('Setting up env...');
// Function to load environment variables from a file
const loadEnvFromFile = (filePath) => {
    const envContent = fs.readFileSync(filePath, 'utf8');
    const parsedEnv = dotenv.parse(envContent);
    for (const key in parsedEnv) {
        process.env[key] = parsedEnv[key];
    }
};

// Load environment variables from both files
loadEnvFromFile('.env');
loadEnvFromFile('.env.test');

process.env.API_USER_EMAIL = '<EMAIL>';
process.env.API_USER_PASSWORD = 'massy@1234';

process.env.ORGANIZATION_ID = '6128e3537ed841e246e2e394';
process.env.REGION_ID = '6137a9fff48b8eb1845c78cf';
process.env.USER_ID = '613dfc56eac5369848619fe0';
