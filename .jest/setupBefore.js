const MongooseConnector = require('./../lib/db/connectors/MongooseConnector');
const axios = require('axios');
const qs = require('qs');

const { apiUsername, apiPassword } = require('./constants');

const getToken = (username, password) => {
    const data = qs.stringify({
        client_id: 'loyalty-admin-portal',
        grant_type: 'password',
        scope: 'openid',
        username: username,
        password: password
    });
    const config = {
        method: 'post',
        url: 'https://idp.loyaltybeta.cxforge.com/auth/realms/shoutout-loyalty-system/protocol/openid-connect/token',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: data
    };

    return axios(config);
};

beforeAll(async () => {
    try {
        console.log('Running setup before...');

        console.log('init connection...');

        const [userResponse] = await Promise.all([getToken(apiUsername, apiPassword), MongooseConnector.initialize()]);

        global.accessToken = userResponse.data.access_token;
    } catch (error) {
        console.error('Error fetching access token:', error.message);
    }
});

afterAll(async () => {
    console.log('close connection...');
    await MongooseConnector.close();
});
