const Member = require('./../lib/db/models/member.model');
const AffinityGroup = require('./../lib/db/models/affinity.group.model');
const Merchant = require('./../lib/db/models/merchant.model');
const MerchantLocation = require('./../lib/db/models/merchant.location.model');
const { Type, Status } = require('./../lib/db/models/enums/member.enums');
const { Type: MerchantType, Status: MerchantStatus } = require('./../lib/db/models/enums/merchant.enums');
const {
    TYPE: RewardType,
    STATUS: RewardStatus,
    SUB_TYPE,
    DAILY_REDEMPTION_LIMIT,
    POINTS_VALUE_TYPE,
    VALIDITY_PERIOD,
    PORTAL_VISIBILITY
} = require('./../lib/db/models/enums/reward.enums');
const { STATUS: MerchantLocationStatus } = require('./../lib/db/models/enums/merchant.location.enums');
const { STATUS: transactionStatus, TYPE: transactionType } = require('./../lib/db/models/enums/transaction.enums');
const {
    STATUS: RedemptionLogStatus,
    REFUND_STATUS,
    PROCESSING_STATUS: RewardProcessingStatus
} = require('./../lib/db/models/enums/reward.redemption.log.enums');
const SubTransactionType = require('./../lib/db/models/sub.transaction.type.model');
const Reward = require('./../lib/db/models/reward.model');
const RewardRedemptionLog = require('./../lib/db/models/reward.redemption.log.model');
const Transaction = require('./../lib/db/models/transaction.model');
const Region = require('./../lib/db/models/region.model');
const RewardVoucher = require('./../lib/db/models/reward.voucher.model');
const PointRule = require('./../lib/db/models/point.rules.model');
const Organization = require('./../lib/db/models/organization.model');
const {
    TYPE: SubTransType,
    OPERATION_TYPE,
    STATUS: SubTransStatus
} = require('./../lib/db/models/enums/sub.transaction.type.enums');
const { POINT_EXPIRY_METHOD } = require('./../lib/db/models/enums/regional.point.configuration.enums');
const { STATUS: RewardVoucherStatus } = require('./../lib/db/models/enums/reward.voucher.enums');
const Card = require('./../lib/db/models/card.model');
const Template = require('./../lib/db/models/template.model');

const MongooseConnector = require('./../lib/db/connectors/MongooseConnector');
const { CARD_STATUS, CARD_PROCESSING_STATUS, CARD_TYPES } = require('../lib/db/models/enums/card.enums');
const {
    cardNumberEmbossed,
    cardPrintJobIdEmbossed,
    cardIdEmbossed,
    cardPrintJobIdEmbossedWithoutMemberPostalAddress,
    cardNumberEmbossedWithoutMemberPostalAddress,
    member2Id,
    merchant2Id,
    affinityGroup1Id,
    affinityGroup2Id,
    secondaryMemberId,
    redeemPointsSubTransactionId,
    adjustmentAddSubTransactionId,
    adjustmentSubtractSubTransactionId,
    redemptionLogItemId2,
    tierId,
    enrollBonusCollectionSubTransactionId,
    expiryPoints,
    billReversalAdjustmentPlus,
    billReversalAdjustmentMinus
} = require('./constants');
const { default: mongoose, mongo } = require('mongoose');
const { enrollBonusCollection } = require('../lib/constants/SystemSubTransactionTypes');

const organizationId = process.env.ORGANIZATION_ID;
const regionId = process.env.REGION_ID;
const merchantId = process.env.MERCHANT_ID;
const merchantLocationId = process.env.MERCHANT_LOCATION_ID;
const userId = process.env.USER_ID;
const memberId = process.env.MEMBER_ID;
const rewardId = process.env.REWARD_ID;
const partnerRewardId = process.env.PARTNER_REWARD_ID;
const partnerRewardBundleId = process.env.PARTNER_REWARD_BUNDLE_ID;
const redemptionLogItemId = process.env.REDEMPTION_LOG_ITEM_ID;
const transactionId = process.env.TRANSACTION_ID;

const collectPointsBill = '619cb7f0eb33f2c02fb207ff';
const collectPointsManual = '619cb80083ee2ea7859548ff';
const refundPoints = '619dc28584f5d30cdfec8759';
const rewardVoucherId = '619dfb6c8a6ad8b6f9013aab';
const partnerRewardVoucherId = '619dfb7a0f8237c370abfefc';

const defaultRewardVoucherId = '625e4746bdb2357dea6ff67c';

const pointRule1Id = '653649712ac24f3f157fb4c6';
const pointRule2Id = '6667efe9fe42abdf870d462d';

const currentDate = new Date();

class TestDatabase {
    static async initialize() {
        try {
            await MongooseConnector.initialize();
            await this.cleanUpDatabase();
            const organizationModel = new Organization({
                _id: organizationId,
                organizationName: 'Loyalty Testing Organization',
                address: {
                    street: 'Baker',
                    city: 'galle',
                    zip: 'Street',
                    line1: '221B ',
                    line2: ' ',
                    line3: 'Street',
                    stateOrProvince: 'Southern Province',
                    zipOrPostcode: '80000'
                },
                organizationAppTitle: 'Loyalty Service Testing',
                rootUserId: mongoose.Types.ObjectId(userId),
                configuration: {
                    baseCountryISO2Code: 'BB',
                    baseCountryCurrencyCode: 'LKR',
                    cardConfiguration: {
                        loyaltyCardNumberLength: 16,
                        allowManualCardGeneration: true
                    },
                    portalConfiguration: {
                        allowSelfSignup: true,
                        allowedOrigins: ['http://localhost:3000'],
                        idpMetadata: {
                            realm: 'shoutout-loyalty-system',
                            clientId: 'loyalty-service',
                            clientSecret: 'xxxxx',
                            certUrl:
                                'https://idp.loyaltybeta.cxforge.com/auth/realms/shoutout-loyalty-system/protocol/openid-connect/certs',
                            audience: 'string123',
                            issuer: 'string123'
                        }
                    },
                    providerConfiguration: {
                        emailProvidersList: [],
                        smsProvidersList: []
                    },
                    notificationConfiguration: {
                        emailConfiguration: {
                            fromAddress: '<EMAIL>',
                            fromName: 'Dev'
                        },
                        smsConfiguration: {
                            alphanumericSenderId: 'ShoutTEST',
                            phoneNumber: '***********'
                        }
                    },
                    reportConfiguration: {
                        financialYearStartMonth: 1
                    },
                    tierConfiguration: {
                        tierCalculationInterval: 6,
                        tierCalculationWindow: 10
                    },
                    rewardConfiguration: {},
                    baseRegionId: regionId,
                    lastIssuedLoyaltyId: 100,
                    memberPrimaryAttribute: 'EMAIL'
                },
                subtransactionTypeIdMap: {
                    redeemPointsReward: redeemPointsSubTransactionId,
                    redeemPointsDonation: redeemPointsSubTransactionId,
                    collectPointsDonation: collectPointsBill,
                    adjustPointsTransferAdd: adjustmentAddSubTransactionId,
                    adjustPointsTransferSubstract: adjustmentSubtractSubTransactionId,
                    collectPointsBill: collectPointsBill,
                    collectPointsManual: collectPointsManual,
                    expirePoints: expiryPoints,
                    primaryAccountAdd: adjustmentAddSubTransactionId,
                    secondaryAccountSubtract: adjustmentSubtractSubTransactionId,
                    enrollBonusCollection: enrollBonusCollectionSubTransactionId,
                    signupBonusCollection: adjustmentAddSubTransactionId,
                    profileCompletionBonusCollection: adjustmentAddSubTransactionId,
                    transactionImportMinusAdjustment: adjustmentSubtractSubTransactionId,
                    cardReplacement: adjustmentSubtractSubTransactionId,
                    refundPoints: adjustmentAddSubTransactionId,
                    adjustPointsRegionalTransferAdd: adjustmentAddSubTransactionId,
                    adjustPointsRegionalTransferSubtract: adjustmentSubtractSubTransactionId,
                    billReversalAdjustmentPlus,
                    billReversalAdjustmentMinus
                }
            });

            const memberModel = new Member({
                _id: memberId,
                organizationId,
                email: '<EMAIL>',
                mobileNumber: '0777772071',
                loyaltyId: 'TEST00001',
                regionId,
                merchantLocationId,
                type: Type.PRIMARY,
                status: Status.ACTIVE,
                createdBy: userId,
                affinityGroup: {
                    affinityGroupId: affinityGroup1Id
                },
                points: 100,
                postalAddress: {
                    line2: 'no2, 6th lane',
                    line3: 'colombo 3',
                    city: 'colombo',
                    stateOrProvince: 'western',
                    zipOrPostcode: '000300'
                },
                tier: {
                    tierId: new mongoose.Types.ObjectId(tierId)
                }
            });
            // const member = (await memberModel.save()).toObject({ getters: true });

            const member2Model = new Member({
                _id: member2Id,
                organizationId,
                loyaltyId: 'TEST00002',
                regionId,
                email: '<EMAIL>',
                mobileNumber: '0777782071',
                merchantLocationId,
                type: Type.PRIMARY,
                status: Status.ACTIVE,
                createdBy: userId,
                points: 100
            });
            // const member2 = (await member2Model.save()).toObject({ getters: true });

            const secondaryMemberModel = new Member({
                _id: secondaryMemberId,
                parentMemberId: memberId,
                organizationId,
                loyaltyId: 'TEST00003',
                regionId,
                merchantLocationId,
                type: Type.SECONDARY,
                status: Status.ACTIVE,
                createdBy: userId,
                points: 0
            });
            // const secondaryMember = (await secondaryMemberModel.save()).toObject({
            //     getters: true
            // });

            const affinityGroupModel = new AffinityGroup({
                _id: affinityGroup1Id,
                organizationId,
                regionId,
                name: 'Test affinity group 1',
                description: 'Test affinity group 1',
                imageUrl: 'Test affinity group 1',
                benefits: ['Test affinity group 1'],
                status: 'ENABLED',
                createdBy: userId
            });
            // const affinityGroup = (await affinityGroupModel.save()).toObject({
            //     getters: true
            // });

            const affinityGroupModel2 = new AffinityGroup({
                _id: affinityGroup2Id,
                organizationId,
                regionId,
                name: 'Test affinity group 2',
                description: 'Test affinity group 2',
                imageUrl: 'Test affinity group 2',
                benefits: ['Test affinity group 2'],
                status: 'ENABLED',
                createdBy: userId
            });
            // const affinityGroup2 = (await affinityGroupModel2.save()).toObject({
            //     getters: true
            // });

            //region
            const regionModel = new Region({
                _id: regionId,
                organizationId,
                regionName: 'Barbados',
                defaultCountryISO2Code: 'BB',
                defaultCurrencyCode: 'BBD',
                regionIconUrl: 'https://gallery.getshoutout.com/images/58748/bbd_flag.jpeg',
                pointConfiguration: {
                    minPointRedemptionAmount: 10,
                    maxPointRedemptionAmount: 10000,
                    minPointsBalanceForRedemption: 100,
                    pointExpiryMethod: POINT_EXPIRY_METHOD.ROLLING,
                    pointExpiryGracePeriod: 90,
                    currencyAmountPerPoint: 0.1,
                    regionalPointConversionRates: [],
                    pointExpiryPeriod: 6,
                    pointExpiryCalculationSubTransactionTypeIds: [collectPointsBill, collectPointsManual]
                },
                memberConfiguration: {
                    maxSecondaryAccounts: 5,
                    defaultAffinityGroupId: affinityGroup1Id
                },
                notificationConfiguration: {
                    emailConfiguration: {
                        fromAddress: '<EMAIL>'
                    },
                    smsConfiguration: {
                        phoneNumber: '***********'
                    }
                },
                createdBy: userId,
                rewardConfiguration: { lastBatchId: 0 },
                defaultMerchantId: merchantId,
                defaultMerchantLocationId: merchantLocationId,
                supportInfo: {
                    phoneNumbers: ['**********'],
                    email: '<EMAIL>',
                    whatsappNumber: '***********'
                },
                jobStartTime: '0 5 * * *',
                providerConfiguration: {},
                timeZone: 'America/Port_of_Spain'
            });
            // const region = (await regionModel.save()).toObject({ getters: true });

            //merchant
            const merchantData = {
                _id: merchantId,
                organizationId,
                regionId,
                merchantName: 'Test Merchant',
                merchantLogoImageUrl:
                    'https://blog.getshoutout.com/wp-content/uploads/2021/04/ShoutOUT_Logo-e1619042507876.png',
                contact: {
                    mobileNumber: '***********',
                    email: '<EMAIL>',
                    address: {
                        line1: 'No 1',
                        line2: '',
                        line3: '',
                        city: 'Colombo',
                        stateOrProvince: '',
                        zipOrPostcode: '00700'
                    }
                },
                status: MerchantStatus.DRAFT,
                type: MerchantType.INTERNAL,
                countryISO2Code: 'LK',
                countryName: 'Sri Lanka',
                billingContact: {},
                technicalContact: {},
                businessRegistrationNumber: 'PV11111',
                options: {
                    adjustPoints: true,
                    enroll: true,
                    earn: true,
                    redeemPoints: true,
                    redeemRewards: true,
                    refund: true,
                    void: true,
                    claimReward: true
                },
                createdBy: userId
            };
            const merchantModel = new Merchant(merchantData);
            // const merchant = (await merchantModel.save()).toObject({ getters: true });

            const merchant2Model = new Merchant({
                ...merchantData,
                _id: merchant2Id
            });
            // const merchant2 = (await merchant2Model.save()).toObject({
            //     getters: true
            // });

            //location
            const merchantLocationModel = new MerchantLocation({
                _id: merchantLocationId,
                organizationId,
                regionId,
                status: MerchantLocationStatus.ACTIVE,
                merchantId: merchantId,
                locationName: 'string',
                code: 'LOC1',
                isPickupLocation: true,
                contact: {
                    mobileNumber: 'string',
                    email: 'string',
                    address: {
                        line1: 'No 1',
                        line2: '',
                        line3: '',
                        city: 'Colombo',
                        stateOrProvince: '',
                        zipOrPostcode: '00700'
                    }
                },
                options: {
                    adjustPoints: true,
                    enroll: true,
                    earn: true,
                    redeemPoints: true,
                    redeemRewards: true,
                    refund: true,
                    void: true,
                    claimReward: true
                },
                createdBy: userId
            });
            // const merchantLocation = (await merchantLocationModel.save()).toObject({
            //     getters: true
            // });

            //sub trans
            const subTransTypeBillModel = new SubTransactionType({
                _id: collectPointsBill,
                organizationId,
                transactionType: SubTransType.COLLECTION,
                operationType: OPERATION_TYPE.ADD,
                name: 'Points for Bill',
                referenceId: 'COL001',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });

            //sub trans
            const subTransTypeExpiryPoints = new SubTransactionType({
                _id: expiryPoints,
                organizationId,
                transactionType: SubTransType.ADJUSTMENT,
                operationType: OPERATION_TYPE.SUBTRACT,
                name: 'Expiry Points',
                referenceId: 'EXP001',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });
            // const subTransTypeBill = (await subTransTypeBillModel.save()).toObject({
            //     getters: true
            // });

            const subTransRedeemPointsModel = new SubTransactionType({
                _id: redeemPointsSubTransactionId,
                organizationId,
                transactionType: SubTransType.REDEMPTION,
                operationType: OPERATION_TYPE.SUBTRACT,
                name: 'Redemption',
                referenceId: 'RED001',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });
            // const subTransRedeemPoints = (await subTransRedeemPointsModel.save()).toObject({ getters: true });

            const subTransAdjustmentAddModel = new SubTransactionType({
                _id: adjustmentAddSubTransactionId,
                organizationId,
                transactionType: SubTransType.COLLECTION,
                operationType: OPERATION_TYPE.ADD,
                name: 'Adjustment ADD',
                referenceId: 'ADJ001',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });
            // const subTransAdjustmentAdd = (await subTransAdjustmentAddModel.save()).toObject({ getters: true });

            const subTransAdjustmentSubtractModel = new SubTransactionType({
                _id: adjustmentSubtractSubTransactionId,
                organizationId,
                transactionType: SubTransType.ADJUSTMENT,
                operationType: OPERATION_TYPE.SUBTRACT,
                name: 'Adjustment SUBTRACT',
                referenceId: 'ADJ002',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });
            // const subTransAdjustmentSubtract = (await subTransAdjustmentSubtractModel.save()).toObject({
            //     getters: true
            // });

            const subTransTypeManualModel = new SubTransactionType({
                _id: collectPointsManual,
                organizationId,
                transactionType: SubTransType.COLLECTION,
                operationType: OPERATION_TYPE.ADD,
                name: 'Points for Manual',
                referenceId: 'COL002',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });
            // const subTransTypeManual = (await subTransTypeManualModel.save()).toObject({ getters: true });

            const subTransTypeEnrollBonusModel = new SubTransactionType({
                _id: enrollBonusCollectionSubTransactionId,
                organizationId,
                transactionType: SubTransType.COLLECTION,
                operationType: OPERATION_TYPE.ADD,
                name: 'Enroll Bonus Collection',
                referenceId: 'ENRL001',
                createdBy: userId,
                status: SubTransStatus.ENABLED
            });

            //Rewards
            const rewardModel = new Reward({
                _id: rewardId,
                organizationId,
                regionId,
                name: 'CAL Miles',
                description: 'CAL Miles barbados',
                type: RewardType.TANGIBLE,
                subType: SUB_TYPE.VOUCHER,
                imageUrls: [''],
                pointValueType: POINTS_VALUE_TYPE.STATIC,
                pointsStatic: 10,
                validityPeriod: VALIDITY_PERIOD.OPEN,
                dailyRedemptionLimit: DAILY_REDEMPTION_LIMIT.UNLIMITED,
                createdBy: userId,
                status: RewardStatus.ENABLED,
                rewardMetadata: {
                    allowAllClaimLocations: true
                },
                portalVisibility: PORTAL_VISIBILITY.NONE,
                totalCount: 0,
                usedCount: 0,
                claimedCount: 0
            });
            // const reward = (await rewardModel.save()).toObject({ getters: true });

            const partnerRewardModel = new Reward({
                _id: partnerRewardId,
                organizationId,
                regionId,
                name: 'CAL Miles',
                description: 'CAL Miles barbados',
                type: RewardType.TANGIBLE,
                subType: SUB_TYPE.PARTNER,
                imageUrls: [''],
                pointValueType: POINTS_VALUE_TYPE.BUNDLE,
                pointsBundles: [
                    {
                        points: 10,
                        bundleName: '20 CAL Miles',
                        bundleValue: 20,
                        _id: partnerRewardBundleId
                    }
                ],
                partnerRewardMetadata: {
                    partnerRewardConfig: 'string',
                    customFileName: 'string',
                    partnerContactNumber: ['string'],
                    partnerBundleUnitOfMeasure: 'CAL Miles'
                },
                validityPeriod: VALIDITY_PERIOD.OPEN,
                dailyRedemptionLimit: DAILY_REDEMPTION_LIMIT.UNLIMITED,
                createdBy: userId,
                status: RewardStatus.ENABLED,
                rewardMetadata: {
                    allowAllClaimLocations: true
                },
                portalVisibility: PORTAL_VISIBILITY.NONE,
                totalCount: 0,
                usedCount: 0,
                claimedCount: 0
            });
            // const partnerReward = (await partnerRewardModel.save()).toObject({
            //     getters: true
            // });

            //Redemption Log item
            const redemptionLogModel = new RewardRedemptionLog({
                _id: redemptionLogItemId,
                organizationId,
                regionId,
                memberId,
                rewardId,
                transactionId,
                pointsRedeemed: 10,
                status: RedemptionLogStatus.REQUESTED,
                processingStatus: RewardProcessingStatus.PENDING,
                rewardType: RewardType.TANGIBLE,
                rewardSubType: SUB_TYPE.VOUCHER,
                refundStatus: REFUND_STATUS.NONE,
                createdBy: userId,
                metadata: {
                    claimLocationId: merchantLocationId
                }
            });
            // const redemptionLogItem = (await redemptionLogModel.save()).toObject({
            //     getters: true
            // });

            const redemptionLogModel2 = new RewardRedemptionLog({
                _id: redemptionLogItemId2,
                organizationId,
                regionId,
                memberId,
                rewardId,
                transactionId,
                pointsRedeemed: 10,
                status: RedemptionLogStatus.REQUESTED,
                processingStatus: RewardProcessingStatus.PENDING,
                rewardType: RewardType.TANGIBLE,
                rewardSubType: SUB_TYPE.VOUCHER,
                refundStatus: REFUND_STATUS.NONE,
                createdBy: userId,
                metadata: {
                    claimLocationId: merchantLocationId
                }
            });
            // const redemptionLogItem2 = (await redemptionLogModel2.save()).toObject({
            //     getters: true
            // });

            //Transaction
            const transactionModel = new Transaction({
                _id: transactionId,
                organizationId,
                regionId,
                merchantId,
                merchantLocationId,
                memberId,
                transactionOn: new Date(),
                redeemablePoints: 10,
                transactionAmount: 10,
                type: transactionType.REDEMPTION,
                subType: refundPoints,
                createdBy: userId,
                status: transactionStatus.VALID
            });
            // const transaction = (await transactionModel.save()).toObject({
            //     getters: true
            // });

            //Reward Voucher
            const defaultRewardVoucherModel = new RewardVoucher({
                _id: defaultRewardVoucherId,
                organizationId,
                regionId,
                rewardId,
                rewardRedemptionLogId: redemptionLogItemId,
                rewardTopUpId: '619b8171ee85754517419517',
                voucherCode: '14d80a72c867191e37e2eaf9cd394f6f',
                voucherCodeHash: '1fe5a910421ee11c387c2f553688f260b4b78842',
                status: RewardVoucherStatus.ISSUED,
                createdBy: userId
            });
            // const defaultRewardVoucher = (await defaultRewardVoucherModel.save()).toObject({ getters: true });

            const rewardVoucherModel = new RewardVoucher({
                _id: rewardVoucherId,
                organizationId,
                regionId,
                rewardId,
                rewardTopUpId: '619b8171ee85754517419517',
                voucherCode: '14c80a72c867191e37e2eaf9cd394f6f',
                voucherCodeHash: '1ee5a910421ee11c387c2f553687f260b4b78842',
                status: RewardVoucherStatus.OPEN,
                createdBy: userId
            });
            // const rewardVoucher = (await rewardVoucherModel.save()).toObject({
            //     getters: true
            // });

            const partnerRewardVoucherModel = new RewardVoucher({
                _id: partnerRewardVoucherId,
                organizationId,
                regionId,
                rewardId: partnerRewardId,
                rewardTopUpId: '619b8171ee85754517419517',
                voucherCode: '14c80a72c867191e37e2eaf9cd394f6f',
                voucherCodeHash: '1ee5a910421ee11c387c2f553687f360b4b78843',
                status: RewardVoucherStatus.OPEN,
                createdBy: userId
            });
            // const partnerRewardVoucher = (await partnerRewardVoucherModel.save()).toObject({ getters: true });

            const pointRule1Model = new PointRule({
                _id: pointRule1Id,
                organizationId,
                regionId,
                merchantId: merchantId,
                name: 'General Spending',
                description: 'General Spending',
                type: 'TRANSACTIONAL',
                subType: 'GENERAL',
                ruleData: {
                    amountPerPoint: 100
                },
                createdBy: userId,
                status: 'ENABLED'
            });

            const pointRule2Model = new PointRule({
                _id: pointRule2Id,
                organizationId,
                regionId,
                merchantId: merchantId,
                name: 'Profile Completion Bonus',
                description: 'Profile Completion Bonus',
                type: 'NON_TRANSACTIONAL',
                subType: 'PROFILE_COMPLETION',
                ruleData: {
                    completionBonusConfigurations: {
                        considerAllFields: true,
                        fieldConfigurations: [
                            { fieldName: 'birthDate', points: 5 },
                            { fieldName: 'email', points: 5 },
                            { fieldName: 'city', points: 5 },
                            { fieldName: 'stateOrProvince', points: 5 }
                        ],
                        totalBonusPoints: 20
                    }
                },
                createdBy: userId,
                status: 'ENABLED'
            });
            // const pointRule1 = (await pointRule1Model.save()).toObject({
            //     getters: true
            // });

            const cardModelEmbossed = new Card({
                _id: new mongoose.Types.ObjectId(cardIdEmbossed),
                organizationId: new mongoose.Types.ObjectId(organizationId),
                regionId: new mongoose.Types.ObjectId(regionId),
                memberId: new mongoose.Types.ObjectId(memberId),
                cardNo: Number(cardNumberEmbossed),
                cardNoStr: cardNumberEmbossed,
                printJobId: new mongoose.Types.ObjectId(cardPrintJobIdEmbossed),
                printJobNumber: '1',
                distributionJobId: new mongoose.Types.ObjectId(cardPrintJobIdEmbossed),
                distributionJobNumber: '1',
                status: CARD_STATUS.ASSIGNED,
                processingStatus: CARD_PROCESSING_STATUS.COMPLETED,
                type: CARD_TYPES.DIGITAL_CARD,
                assignedDate: currentDate,
                embossCard: {
                    embossRequestedOn: currentDate,
                    embossIssuedOn: currentDate,
                    merchantId: new mongoose.Types.ObjectId(merchantId),
                    merchantLocationId: new mongoose.Types.ObjectId(merchantLocationId),
                    printedName: 'ShoutOUT',
                    requestedBy: new mongoose.Types.ObjectId(userId),
                    previousCardType: CARD_TYPES.DIGITAL_CARD,
                    previousPrintJobId: new mongoose.Types.ObjectId(cardPrintJobIdEmbossed),
                    previousPrintJobNumber: '1'
                },
                createdBy: new mongoose.Types.ObjectId(userId),
                updatedBy: new mongoose.Types.ObjectId(userId)
            });
            // const cardEmbossed = (await cardModelEmbossed.save()).toObject({
            //     getters: true
            // });

            const cardModelEmbossedWithoutMemberPostalAddress = new Card({
                _id: cardPrintJobIdEmbossedWithoutMemberPostalAddress,
                organizationId,
                regionId,
                memberId: member2Id,
                cardNo: Number(cardNumberEmbossedWithoutMemberPostalAddress),
                cardNoStr: cardNumberEmbossedWithoutMemberPostalAddress,
                printJobId: cardPrintJobIdEmbossedWithoutMemberPostalAddress,
                printJobNumber: '1',
                distributionJobId: cardPrintJobIdEmbossedWithoutMemberPostalAddress,
                distributionJobNumber: '1',
                status: CARD_STATUS.ASSIGNED,
                processingStatus: CARD_PROCESSING_STATUS.COMPLETED,
                type: CARD_TYPES.DIGITAL_CARD,
                assignedDate: currentDate,
                embossCard: {
                    embossRequestedOn: currentDate,
                    embossIssuedOn: currentDate,
                    merchantId: merchantId,
                    merchantLocationId: merchantLocationId,
                    printedName: 'ShoutOUT',
                    requestedBy: userId,
                    previousCardType: CARD_TYPES.DIGITAL_CARD,
                    previousPrintJobId: cardPrintJobIdEmbossedWithoutMemberPostalAddress,
                    previousPrintJobNumber: '1'
                },
                createdBy: userId,
                updatedBy: userId
            });

            // const cardEmbossedWithoutMemberPostalAddress = (
            //     await cardModelEmbossedWithoutMemberPostalAddress.save()
            // ).toObject({ getters: true });

            const otpTemplateModel = new Template({
                _id: new mongoose.Types.ObjectId('669929731be8223ed75ebbe3'),
                organizationId,
                regionId: null,
                templateId: 'OTP',
                smsTemplate: {
                    body: '"Use the code <%= data.otpCode %> to authorize your request"'
                },
                emailTemplate: {
                    bodyHtml: '"Use the code <%= data.otpCode %> to authorize your request"',
                    bodyText: '"Use the code <%= data.otpCode %> to authorize your request"',
                    subject: '"OTP verification"'
                },
                status: 'ENABLED',
                createdBy: new mongoose.Types.ObjectId(userId),
                updatedBy: new mongoose.Types.ObjectId(userId)
            });

            await Promise.all([
                cardModelEmbossedWithoutMemberPostalAddress.save(),
                cardModelEmbossed.save(),
                pointRule1Model.save(),
                pointRule2Model.save(),
                partnerRewardVoucherModel.save(),
                rewardVoucherModel.save(),
                defaultRewardVoucherModel.save(),
                transactionModel.save(),
                redemptionLogModel2.save(),
                redemptionLogModel.save(),
                partnerRewardModel.save(),
                rewardModel.save(),
                subTransTypeManualModel.save(),
                subTransAdjustmentSubtractModel.save(),
                subTransAdjustmentAddModel.save(),
                subTransRedeemPointsModel.save(),
                subTransTypeBillModel.save(),
                merchantLocationModel.save(),
                merchant2Model.save(),
                merchantModel.save(),
                regionModel.save(),
                affinityGroupModel2.save(),
                affinityGroupModel.save(),
                secondaryMemberModel.save(),
                member2Model.save(),
                memberModel.save(),
                organizationModel.save(),
                subTransTypeEnrollBonusModel.save(),
                subTransTypeExpiryPoints.save(),
                otpTemplateModel.save()
            ]);

            return Promise.resolve();
        } catch (err) {
            console.error(err);
            return Promise.reject(err);
        }
    }

    static async cleanUpDatabase() {
        try {
            const promises = [
                Member.deleteMany({ _id: { $in: [memberId, member2Id, secondaryMemberId] } }),
                Merchant.deleteMany({ _id: { $in: [merchantId, merchant2Id] } }),
                MerchantLocation.deleteOne({ _id: merchantLocationId }),
                Transaction.deleteOne({ _id: transactionId }),
                Organization.deleteOne({ _id: organizationId }),
                SubTransactionType.deleteMany({
                    _id: {
                        $in: [
                            collectPointsBill,
                            collectPointsManual,
                            redeemPointsSubTransactionId,
                            adjustmentAddSubTransactionId,
                            adjustmentSubtractSubTransactionId,
                            enrollBonusCollectionSubTransactionId,
                            expiryPoints
                        ]
                    }
                }),
                RewardRedemptionLog.deleteMany({ _id: { $in: [redemptionLogItemId, redemptionLogItemId2] } }),
                Reward.deleteMany({ _id: { $in: [partnerRewardId, rewardId] } }),
                Region.deleteOne({ _id: regionId }),
                RewardVoucher.deleteMany({
                    _id: { $in: [rewardVoucherId, partnerRewardVoucherId, defaultRewardVoucherId] }
                }),
                AffinityGroup.deleteMany({ _id: { $in: [affinityGroup1Id, affinityGroup2Id] } }),
                PointRule.deleteMany({ _id: { $in: [pointRule1Id, pointRule2Id] } }),
                Card.deleteMany({ organizationId }),
                Template.deleteMany({ organizationId })
            ];

            await Promise.all(promises);
            return Promise.resolve();
        } catch (err) {
            console.error(err);
            return Promise.reject(err);
        }
    }

    static async teardown() {
        try {
            await this.cleanUpDatabase();
            await MongooseConnector.close();
            return Promise.resolve();
        } catch (err) {
            console.error(err);
            return Promise.reject(err);
        }
    }
}

TestDatabase.objIds = { collectPointsBill };
TestDatabase.testConstants = { pointRule2Id, userId };

module.exports = TestDatabase;
