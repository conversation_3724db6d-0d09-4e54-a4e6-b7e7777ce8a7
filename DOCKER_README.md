# LinkedIn Scraper - Docker Development Environment

This guide will help you set up and run the LinkedIn Profile Scraper in a Docker development environment.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose (included with Docker Desktop)
- Git (to clone the repository)

## Quick Start

### 1. Environment Setup

Make sure your `.env` file exists in the project root with the following variables:

```env
# LinkedIn Authentication
LINKEDIN_USERNAME=<EMAIL>
LINKEDIN_PASSWORD=your_password

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string
AZURE_STORAGE_CONTAINER_NAME=your_container_name

# Server Configuration
PORT=3000
```

### 2. Using the Helper Script (Recommended)

We've provided a helper script to make Docker operations easier:

```bash
# Make the script executable (first time only)
chmod +x docker-dev.sh

# Start the development environment
./docker-dev.sh start

# View logs
./docker-dev.sh logs

# Stop the environment
./docker-dev.sh stop
```

### 3. Manual Docker Commands

If you prefer using Docker commands directly:

```bash
# Build the image
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build

# Start the development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f

# Stop the environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
```

## Available Commands

### Helper Script Commands

```bash
./docker-dev.sh build     # Build the Docker image
./docker-dev.sh start     # Start development environment
./docker-dev.sh stop      # Stop development environment
./docker-dev.sh restart   # Restart development environment
./docker-dev.sh logs      # Show container logs
./docker-dev.sh status    # Show container status
./docker-dev.sh shell     # Open shell in container
./docker-dev.sh clean     # Clean up Docker resources
./docker-dev.sh exec [cmd] # Execute command in container
```

### Examples

```bash
# Check LinkedIn login status
./docker-dev.sh exec npm run check-login

# Run login process
./docker-dev.sh exec npm run login

# Take screenshots
./docker-dev.sh exec npm run screenshot

# Open interactive shell
./docker-dev.sh shell
```

## API Endpoints

Once the container is running, the API will be available at:

- **Base URL**: `http://localhost:3000`
- **Health Check**: `GET http://localhost:3000/`
- **Login Status**: `GET http://localhost:3000/api/linkedin/status`
- **Screenshot**: `POST http://localhost:3000/api/linkedin/screenshot`

### Example API Usage

```bash
# Check if LinkedIn is logged in
curl http://localhost:3000/api/linkedin/status

# Take a screenshot
curl -X POST http://localhost:3000/api/linkedin/screenshot \
  -H "Content-Type: application/json" \
  -d '{"profileUrl": "https://www.linkedin.com/in/example-profile"}'
```

## Development Features

### Hot Reload

The development environment supports hot reload:
- Changes to TypeScript files in `src/` will automatically restart the server
- No need to rebuild the Docker image for code changes

### Volume Mounts

The following directories are mounted for development:
- `./src` → `/app/src` (source code)
- `./temp` → `/app/temp` (screenshots)
- `./.env` → `/app/.env` (environment variables)
- `./linkedin-auth.json` → `/app/linkedin-auth.json` (LinkedIn session)

### Debugging

The development container exposes port 9229 for Node.js debugging:
- Debug port: `localhost:9229`
- You can attach your IDE's debugger to this port

## Troubleshooting

### Container Won't Start

1. Check if port 3000 is already in use:
   ```bash
   lsof -i :3000
   ```

2. Check Docker logs:
   ```bash
   ./docker-dev.sh logs
   ```

### LinkedIn Authentication Issues

1. Run the login process:
   ```bash
   ./docker-dev.sh exec npm run login
   ```

2. Check login status:
   ```bash
   ./docker-dev.sh exec npm run check-login
   ```

### Azure Upload Issues

1. Verify your Azure connection string in `.env`
2. Check container logs for Azure-related errors
3. Ensure the Azure container exists and has proper permissions

### Permission Issues

If you encounter permission issues:

```bash
# Fix ownership of files
sudo chown -R $USER:$USER .

# Rebuild the image
./docker-dev.sh clean
./docker-dev.sh build
./docker-dev.sh start
```

## File Structure

```
.
├── Dockerfile                 # Main Docker image definition
├── docker-compose.yml         # Base Docker Compose configuration
├── docker-compose.dev.yml     # Development overrides
├── docker-dev.sh             # Helper script for Docker operations
├── .dockerignore             # Files to exclude from Docker build
├── src/                      # Source code (mounted for hot reload)
├── temp/                     # Screenshots directory (mounted)
├── .env                      # Environment variables (mounted)
└── linkedin-auth.json        # LinkedIn session (mounted)
```

## Production Deployment

For production deployment, use only the base docker-compose.yml:

```bash
docker-compose up -d
```

This will run the application without development features like hot reload and debugging ports.
