# syntax=docker/dockerfile:experimental
FROM 458121188926.dkr.ecr.us-west-2.amazonaws.com/node:16.14.0-slim

COPY package.json package-lock.json .npmrc.docker ./
RUN mv .npmrc.docker .npmrc
RUN npm install

# Bundle app source
COPY bin ./bin/
COPY config ./config/
COPY lib ./lib/
COPY public ./public/
COPY routes ./routes/
COPY views ./views/
COPY workers ./workers/
COPY config.js app.js ./

EXPOSE  3000

CMD npm run start:prod
