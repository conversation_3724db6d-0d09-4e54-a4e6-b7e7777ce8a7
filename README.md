# ShoutOUT Loyalty Customer Portal

This is the frontend application for the ShoutOUT Loyalty Customer Portal, migrated from Next.js to Vite.

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
# or
yarn
```

### Development

To start the development server:

```bash
npm run dev
# or
yarn dev
```

The application will be available at http://localhost:3000.

### Building for Production

To build the application for production:

```bash
npm run build
# or
yarn build
```

### Preview Production Build

To preview the production build locally:

```bash
npm run preview
# or
yarn preview
```

## Project Structure

- `src/` - Source code
  - `components/` - Reusable components
    - `auth/` - Authentication-related components
    - `common/` - Common components
    - `ui/` - UI components (Shadcn UI)
  - `hooks/` - Custom React hooks
  - `lib/` - Utility functions and libraries
  - `pages/` - Page components
  - `stores/` - State management (Zustand)
  - `styles/` - Global styles
  - `App.tsx` - Main application component
  - `main.tsx` - Application entry point

## Technologies

- React 18
- TypeScript
- Vite
- React Router
- Tailwind CSS
- Shadcn UI
- Zustand for state management
- React Query for data fetching
- Keycloak for authentication
- i18next for internationalization 


## Change theme

- Add necessary images to assests folder
- Update `tailwind.config.ts` and `src/styles/globals.css` as required
- Update `src/config.ts` to match assets with the dashboard if required