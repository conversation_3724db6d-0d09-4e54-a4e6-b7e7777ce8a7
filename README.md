# shoutout_loyalty_service

## Please use the release-beta branch to do beta release

## Setup dev environment

Read the readme file inside dev folder.

- navigate to dev dir `cd dev`

- run `sh start.sh`

# Sonarqube Code Status Badges

[![Quality gate](https://audit.loyaltybeta.cxforge.com/api/project_badges/quality_gate?project=shoutout_loyalty_service&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)

[![Bugs](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=bugs&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Code Smells](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=code_smells&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Duplicated Lines (%)](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=duplicated_lines_density&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Vulnerabilities](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=vulnerabilities&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Lines of Code](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=ncloc&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)

[![Reliability Rating](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=reliability_rating&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Maintainability Rating](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=sqale_rating&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Security Rating](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=security_rating&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)  [![Coverage](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout_loyalty_service&metric=coverage&token=sqb_d775cd4fc735908cc9fb5d577f5a3f6c02f74b56)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout_loyalty_service)

# Custom Error Codes

Refer the linked document below.

[Custom Error Code References](https://shoutoutlabssl.atlassian.net/wiki/spaces/SEL/pages/1226244097/Backend+API+Custom+Error+Code+References)



## Run Postgresql db migration

### Apply migration

`npm run db:migrate`

### Rollback migration

`npm run db:migrate:undo`

