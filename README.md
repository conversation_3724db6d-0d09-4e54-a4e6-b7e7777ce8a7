# ShoutOUT Enterprise Loyalty Dashboard

[![Quality gate](https://audit.loyaltybeta.cxforge.com/api/project_badges/quality_gate?project=shoutout-enterprise-loyalty-dashboard&token=sqb_d939cb4e4bc0226a6996069473300e995d25c334)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-enterprise-loyalty-dashboard)

## Staging/Production Deployment Instructions
- https://shoutoutlabssl.atlassian.net/wiki/spaces/SI/pages/1174962177/DEPLOYMENT+Admin+Portal+Deployment+Instructions


## How to contribute

- Clone the repository
- Create a branch as development-yourusername
- Once the development is done, send a PR to the development branch


## Notes

- Always create scss files instead of pure css files
  


## How the staging deployment is handled?

github workflow file will use the env.production.cicd file for the build. 


## How the production build is handled?

- Find and run the neccessary deployment script inside scripts/deployments

NOTE: If you stop the build process in the middle of the build, .env.production.local file will be created in the project root, which needs to delete manually






This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `yarn start`

Runs the app in the development mode.<br />
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.<br />
You will also see any lint errors in the console.

### `yarn test`

Launches the test runner in the interactive watch mode.<br />
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `yarn build`

Builds the app for production to the `build` folder.<br />
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.<br />
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `yarn eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: https://facebook.github.io/create-react-app/docs/code-splitting

### Analyzing the Bundle Size

This section has moved here: https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size

### Making a Progressive Web App

This section has moved here: https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app

### Advanced Configuration

This section has moved here: https://facebook.github.io/create-react-app/docs/advanced-configuration

### Deployment

This section has moved here: https://facebook.github.io/create-react-app/docs/deployment

### `yarn build` fails to minify

This section has moved here: https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify


## How to do deployments
- **Note:-** Please note that before initiating any deployment, it is necessary to create a tag. This tag can then be used for deploying to **massy-beta**, **massy-production**, and **shoutout-production** environments.

### Create a new tag
- To create a new tag, please follow these steps:
    1. Navigate to the 'scripts/deployment' folder.
    2. Execute the 'new-tag.sh' file.
    3. When prompted, enter the desired tag value. (Example tag value format = v1.1.1)
        - *When running script you can see all list of tag & current tag in prompted massage.*

### For the massy-beta deployment
- Navigate to the 'scripts/deployment/massy-beta' folder.
    1. Open the 'massy-beta.sh' file.
    2. Locate the "TAG" variable.
    3. Replace the existing value with the **newly created tag value**.
    4. Execute the massy-beta.sh script file
    
## Steps for production release   
- Step1 - Create release
- Step2 - Run relevant script files(shoutout-prod or massy-prod) 

- **Note: Don't need to create tag again used early created tag.**
### How to create production release 
   1. Navigate to the 'scripts/deployment' folder.
   2. Open the 'new-release.sh' file.
   3. Replace the existing value with the new informations for PR.
        - TAG_NAME 
        - TASK_NUMBER
        - BASE_BRANCH
        - REVIEWERS
        - PR_MESSAGE
   4. Execute the new-release.sh script file
### For the massy-production deployment
- Navigate to the 'scripts/deployment/massy-beta' folder.
    1. Open the 'massy-prod.sh' file.
    2. Locate the "TAG" variable.
    3. Replace the existing value with the **newly created tag value**.
    4. Execute the massy-prod.sh script file

### For the shoutout-production deployment
- Navigate to the 'scripts/deployment/shoutout-production' folder.
    1. Open the 'shoutout-prod.sh' file.
    2. Locate the "TAG" variable.
    3. Replace the existing value with the **newly created tag value**.
    4. Execute the shoutout-prod.sh script file

## Important Notes
#### If requested username & password use following command with token.
```
export GITHUB_TOKEN=""
```

#### Check latest version
```
git describe --tags --abbrev=0
git describe --tags $(git rev-list --tags --max-count=1)
```

#### Print all version
```
git tag -l
```


