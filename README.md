# LinkedIn Profile Screenshot API

A TypeScript API for capturing LinkedIn profile page screenshots and uploading them to Azure Blob Storage.

## Features

- Capture screenshots of LinkedIn profile pages
- Upload images to Azure Blob Storage
- RESTful API endpoints for single and batch profile processing
- Automatic session management for LinkedIn authentication
- Temporary file cleanup

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- LinkedIn account
- Azure Blob Storage account (optional, but recommended)
- TypeScript (v4.5 or higher)

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd linkedin-profile-scraper-api
   ```

2. Install dependencies:
   ```
   npm install
   ```
   This will also automatically build the TypeScript code to JavaScript.

3. Create a `.env` file in the root directory with the following variables:
   ```
   # LinkedIn Authentication
   LINKEDIN_USERNAME=your_linkedin_username
   LINKEDIN_PASSWORD=your_linkedin_password

   # Azure Storage
   AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
   AZURE_STORAGE_CONTAINER_NAME=linkedin-profile-images

   # Server Configuration
   PORT=3000
   ```

4. Start the server:
   ```
   npm start
   ```

## Development

For development, you can use the following npm scripts:

- `npm run build` - Compile TypeScript to JavaScript
- `npm run dev` - Run the server using ts-node (no need to compile first)
- `npm run watch` - Watch for changes and compile TypeScript to JavaScript
- `npm run login` - Run the LinkedIn login script
- `npm run check-login` - Check if you're logged in to LinkedIn
- `npm run screenshot` - Take screenshots of LinkedIn profiles

### TypeScript Structure

The project is organized with the following structure:

- `src/` - TypeScript source files
  - `routes/` - API route handlers
  - `services/` - Business logic services
  - `utils/` - Utility functions
- `dist/` - Compiled JavaScript files (generated after build)
- `temp/` - Temporary files for storing images

## Authentication

The API uses two methods for LinkedIn authentication:

1. **Automatic login**: Using the credentials in the `.env` file
2. **Manual login**: If you prefer to log in manually, run:
   ```
   node login.js
   ```
   This will open a browser window where you can log in to LinkedIn. After logging in, press Enter in the terminal to save the session.

## API Endpoints

> **Note:** The API automatically normalizes URLs with multiple consecutive slashes. For example, both `/api/linkedin/profile-screenshot` and `//api/linkedin/profile-screenshot` will work.

### Check LinkedIn Login Status

```
GET /api/linkedin/status
```

**Response:**
```json
{
  "success": true,
  "isLoggedIn": true
}
```

### Capture Screenshot of a LinkedIn Profile Page

```
POST /api/linkedin/profile-screenshot
```

**Request Body:**
```json
{
  "profileUrl": "https://www.linkedin.com/in/username/"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "profileUrl": "https://www.linkedin.com/in/username/",
    "imageUrl": null,
    "azureUrl": "https://yourstorage.blob.core.windows.net/..."
  }
}
```

### Alternative Endpoint for Profile Screenshots

```
POST /api/linkedin/profile-image
```

**Request Body:**
```json
{
  "profileUrl": "https://www.linkedin.com/in/username/"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "profileUrl": "https://www.linkedin.com/in/username/",
    "imageUrl": null,
    "azureUrl": "https://yourstorage.blob.core.windows.net/..."
  }
}
```

> **Note:** This endpoint provides identical functionality to `/profile-screenshot` and can be used as an alternative if you're experiencing issues with the original endpoint.

### Capture Screenshots of Multiple LinkedIn Profiles

```
POST /api/linkedin/batch-profile-screenshots
```

**Request Body:**
```json
{
  "profileUrls": [
    "https://www.linkedin.com/in/username1/",
    "https://www.linkedin.com/in/username2/",
    "https://www.linkedin.com/in/username3/"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "profileUrl": "https://www.linkedin.com/in/username1/",
        "imageUrl": null,
        "azureUrl": "https://yourstorage.blob.core.windows.net/..."
      },
      {
        "profileUrl": "https://www.linkedin.com/in/username2/",
        "imageUrl": null,
        "azureUrl": "https://yourstorage.blob.core.windows.net/..."
      }
    ],
    "errors": [
      {
        "profileUrl": "https://www.linkedin.com/in/username3/",
        "error": "Failed to capture profile screenshot"
      }
    ],
    "totalProcessed": 3,
    "successCount": 2,
    "errorCount": 1
  }
}
```

## Example Usage

### Using cURL

```bash
# Check login status
curl -X GET http://localhost:3000/api/linkedin/status

# Capture profile screenshot
curl -X POST http://localhost:3000/api/linkedin/profile-screenshot \
  -H "Content-Type: application/json" \
  -d '{"profileUrl": "https://www.linkedin.com/in/username/"}'

# Alternative endpoint for profile screenshots
curl -X POST http://localhost:3000/api/linkedin/profile-image \
  -H "Content-Type: application/json" \
  -d '{"profileUrl": "https://www.linkedin.com/in/username/"}'

# Batch capture profile screenshots
curl -X POST http://localhost:3000/api/linkedin/batch-profile-screenshots \
  -H "Content-Type: application/json" \
  -d '{"profileUrls": ["https://www.linkedin.com/in/username1/", "https://www.linkedin.com/in/username2/"]}'
```

### Using JavaScript (Node.js)

```javascript
const axios = require('axios');

// Capture profile screenshot using the original endpoint
async function captureProfileScreenshot(profileUrl) {
  try {
    const response = await axios.post('http://localhost:3000/api/linkedin/profile-screenshot', {
      profileUrl
    });
    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Capture profile screenshot using the alternative endpoint
async function captureProfileImage(profileUrl) {
  try {
    const response = await axios.post('http://localhost:3000/api/linkedin/profile-image', {
      profileUrl
    });
    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Example usage
captureProfileScreenshot('https://www.linkedin.com/in/username/');
// Or use the alternative endpoint
// captureProfileImage('https://www.linkedin.com/in/username/');
```

## Notes

- LinkedIn may detect and block automated access. Use responsibly and respect LinkedIn's terms of service.
- The API includes rate limiting and error handling to minimize the risk of being blocked.
- If you don't provide an Azure Storage connection string, images will only be saved locally in the `temp` directory.

## License

ISC
