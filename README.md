# ShoutOUT Loyalty Campaign Service

## Introduction
The ShoutOUT Loyalty Campaign Service is a powerful microservice designed for creating, managing, and executing marketing campaigns within the ShoutOUT Loyalty platform. It enables businesses to target specific customer segments through multiple communication channels including SMS, Email, and Banner advertisements.

## Sonarqube Code Status Badges
[![Quality gate](https://audit.loyaltybeta.cxforge.com/api/project_badges/quality_gate?project=shoutout-loyalty-campaign-service&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)

[![Bugs](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=bugs&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Code Smells](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=code_smells&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Duplicated Lines (%)](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=duplicated_lines_density&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Vulnerabilities](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=vulnerabilities&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Lines of Code](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=ncloc&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)

[![Reliability Rating](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=reliability_rating&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Maintainability Rating](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=sqale_rating&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Security Rating](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=security_rating&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)  [![Coverage](https://audit.loyaltybeta.cxforge.com/api/project_badges/measure?project=shoutout-loyalty-campaign-service&metric=coverage&token=sqb_d7cbd90cc2558d246e990f2d94739c12e8abfdad)](https://audit.loyaltybeta.cxforge.com/dashboard?id=shoutout-loyalty-campaign-service)

## Table of Contents
- [Introduction](#introduction)
- [Features](#features)
- [Architecture](#architecture)
- [Campaign Flow](#campaign-flow)
- [Worker Responsibilities](#worker-responsibilities)
- [Base Class Pattern](#base-class-pattern)
- [API Reference](#api-reference)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)

## Features
- **Multi-channel Communication**: Send campaigns via SMS, Email, or display as Banners
- **Targeted Segmentation**: Filter customers based on detailed criteria
- **Campaign Scheduling**: Set future dates for campaign execution
- **Personalized Messaging**: Use Mustache templates to customize messages
- **Real-time Progress Tracking**: Monitor campaign execution status
- **Performance Analytics**: View detailed reports on campaign effectiveness
- **Public/Private Campaigns**: Control campaign visibility
- **Promotional/Transactional Types**: Support for different campaign purposes
- **Batch Processing**: Efficient handling of large member datasets
- **Resumable Processing**: Continue campaign execution from where it left off

## Architecture

### System Components
The service follows a modular architecture with clear separation of concerns:

1. **API Layer**
   - RESTful endpoints for campaign management
   - Request validation and authentication
   - Response formatting and error handling

2. **Business Logic Layer**
   - Campaign creation and management
   - Authorization and permission checks
   - Integration with other services

3. **Data Access Layer**
   - MongoDB database operations
   - Query optimization
   - Data validation

4. **Processing Layer**
   - Asynchronous job processing
   - Queue management
   - Message templating and delivery

5. **Monitoring Layer**
   - Progress tracking
   - Status updates
   - Performance metrics

### Technology Stack
- **Node.js**: Core runtime environment
- **Express**: Web framework
- **MongoDB**: Primary database
- **Redis**: Queue management and caching
- **BullMQ/Bull**: Job queue implementation
- **Mustache**: Template rendering
- **Passport**: Authentication
- **Joi**: Data validation
- **Swagger**: API documentation

## Campaign Flow

### SMS Campaign Creation Process
When an SMS campaign is created, the following steps occur:

1. **API Request**: Client submits campaign details to `/campaigns` endpoint
2. **Validation**: Request is validated using Joi schemas
3. **Authorization**: User permissions are checked
4. **Database Storage**: Campaign is saved to MongoDB
5. **Queue Addition**: Campaign is added to processing queue
6. **Status Update**: Campaign status is set to CREATING or SCHEDULED
7. **Processing**:
   - Campaign processor retrieves campaign details
   - Updates status to RUNNING
   - Processes segment filters to identify target members
   - Streams members from Loyalty Service
   - Builds personalized messages using templates
   - Queues messages for delivery in batches
8. **Message Forwarding**:
   - Messages are sent to appropriate channels
   - Delivery status is tracked
9. **Progress Tracking**:
   - Success/failure counts are updated
   - Campaign status is updated to FINISHED when complete

## API Reference

### Endpoints

#### Campaign Management
- `POST /campaigns`: Create a new campaign
- `GET /campaigns`: List campaigns with filtering and pagination
- `GET /campaigns/:id`: Get campaign details by ID
- `DELETE /campaigns/:id`: Archive a campaign

#### Campaign Reporting
- `GET /campaigns/report`: Get campaign performance report

#### Banner Campaigns
- `GET /campaigns/banners/public`: Get public banner campaigns
- `GET /campaigns/banners/private`: Get private banner campaigns

### Request/Response Examples

#### Create Campaign
```json
// POST /campaigns
// Request
{
  "regionId": "60a3e5e9b54c7e001c123456",
  "name": "Summer Promotion",
  "description": "Special offers for summer",
  "segmentFilters": [
    {
      "membersFilter": "{\"notificationPreference.allowPromotionalNotifications\":true}"
    }
  ],
  "channel": "SMS",
  "type": "PROMOTIONAL",
  "senderId": "ShoutOUT",
  "message": {
    "messageBody": "Hi {{firstName}}, enjoy 20% off your next purchase!"
  }
}

// Response (201 Created)
{
  "organizationId": "60a3e5e9b54c7e001c123123",
  "regionId": "60a3e5e9b54c7e001c123456",
  "name": "Summer Promotion",
  "description": "Special offers for summer",
  "segmentFilters": [...],
  "channel": "SMS",
  "type": "PROMOTIONAL",
  "senderId": "ShoutOUT",
  "message": {
    "messageBody": "Hi {{firstName}}, enjoy 20% off your next purchase!"
  },
  "status": "CREATING",
  "createdOn": "2023-06-15T10:30:00.000Z",
  "createdBy": "60a3e5e9b54c7e001c123789"
}
```

## Installation

### Prerequisites
- Node.js (v12 or higher)
- MongoDB
- Redis

### Setup
1. Clone the repository:
   ```bash
   git clone https://github.com/ShoutOUTLabs/shoutout-loyalty-campaign-service.git
   cd shoutout-loyalty-campaign-service
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables (see [Configuration](#configuration))

4. Start the service:
   ```bash
   npm start
   ```

## Configuration

Create a `.env` file in the root directory with the following variables:

```
# Server Configuration
PORT=3000
API_BASE_PATH=/api/v1
NODE_ENV=development

# MongoDB
MONGODB_URI=mongodb://localhost:27017/loyalty-campaigns

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Authentication
JWT_SECRET=your-jwt-secret
OPENID_ADMIN_JWKS_URI=https://your-auth-server/.well-known/jwks.json
OPENID_ADMIN_ISSUER=https://your-auth-server/

# Service Configuration
CAMPAIGN_PROGRESS_TRACKER_DELAY=5000
LOYALTY_SERVICE_MEMBER_REQUEST_CHUCK_SIZE=100
TEST_CAMPAIGN_BUILDING=false
```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Docker
```bash
docker build -t shoutout-loyalty-campaign-service .
docker run -p 3000:3000 shoutout-loyalty-campaign-service
```

## Development

### Project Structure
```
shoutout-loyalty-campaign-service/
├── app.js                 # Application entry point
├── config.js              # Configuration management
├── lib/                   # Core library code
│   ├── db/                # Database models and DAOs
│   ├── handlers/          # Business logic handlers
│   ├── middlewares/       # Express middlewares
│   ├── services/          # External service integrations
│   └── validators/        # Request validators
├── routes/                # API routes
├── workers/               # Background job processors
│   ├── base.classes/      # Abstract processor classes
│   └── processors/        # Specific job processors
└── __tests__/             # Test files
```

### Adding a New Campaign Channel
1. Create a new processor in `workers/processors/`
2. Extend the `CampaignProcessorBaseClass`
3. Implement the required methods
4. Add the channel enum in `lib/db/models/enums/campaign.enums.js`
5. Update validators to support the new channel

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific tests
npm test -- -t "campaign creation"

# Run with coverage
npm test -- --coverage
```

### Test Structure
- Unit tests for individual components
- Integration tests for API endpoints
- End-to-end tests for complete flows

## Deployment

### CI/CD Pipeline
The service uses AWS CodeBuild and CodePipeline for continuous integration and deployment.

### Infrastructure
- AWS ECS for container orchestration
- MongoDB Atlas for database
- ElastiCache for Redis
- API Gateway for API management

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

---

© 2023 ShoutOUT Labs. All rights reserved.
