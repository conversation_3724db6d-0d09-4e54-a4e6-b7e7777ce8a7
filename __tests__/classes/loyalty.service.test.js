const LoyaltyService = require('../../lib/services/LoyaltyService');

const organizationId = process.env.ORGANIZATION_ID;
const regionId = process.env.REGION_ID;

jest.setTimeout(20000);

describe('LoyaltyServiceTests', () => {
    test('should return members as an array', async () => {
        const members = await LoyaltyService.getMembersInSegment(
            organizationId,
            {
                memberFilter: '{"firstName": "Saul"}'
            },
            regionId,
            500,
            0
        );

        console.log(members);
        expect(members).toBeDefined();
    });
});
