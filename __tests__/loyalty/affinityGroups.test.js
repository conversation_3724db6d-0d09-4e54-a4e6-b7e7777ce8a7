'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/affinitygroups`);

const querystring = require('query-string');
const { regionId } = require('../../.jest/constants');
describe('Affinity Groups unit tests', () => {
    let createdAffinityGroupId, token;
    beforeAll(() => {
        token = global.accessToken;
    });
    it.skip('/GET get affinity group list should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.items).toBeDefined();
                expect(response.body.items).toBeInstanceOf(Array);
            });
    });

    it('/POST create affinity group should return 201', async () => {
        const affinityGroup = {
            regionId: regionId,
            name: 'Test affinity group',
            description: 'Test affinity group',
            imageUrl: 'Test affinity group',
            benefits: ['Test affinity group'],
            status: 'ENABLED'
        };
        return server
            .post('/')
            .send(affinityGroup)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(201);
                expect(response.body).toBeDefined();
                createdAffinityGroupId = response.body._id;
            });
    }, 10000);

    it.skip('/PUT update affinity group should return 200', async () => {
        const affinityGroup = {
            name: 'Test update',
            description: 'Test update',
            imageUrl: 'Test update',
            benefits: ['Test update'],
            status: 'DISABLED'
        };
        return server
            .put(`/${createdAffinityGroupId}`)
            .send(affinityGroup)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.status).toBe('DISABLED');
            });
    }, 10000);

    it.skip('/DELETE delete affinity group should return 200', async () => {
        return server
            .delete(`/${createdAffinityGroupId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.status).toBe('ARCHIVED');
            });
    }, 10000);
});
