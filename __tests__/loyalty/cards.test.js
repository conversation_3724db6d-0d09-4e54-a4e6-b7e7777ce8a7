'use strict';
const mongoose = require('mongoose');
const supertest = require('supertest');
const querystring = require('query-string');
const Card = require('./../../lib/db/models/card.model');
const OrganizationHandler = require('../../lib/handlers/OrganizationHandler');
const { regionId, organizationId, merchantLocationId, memberId } = require('../../.jest/constants');

const server = supertest.agent(`${process.env.API_HOST_URL}/cards`);
jest.setTimeout(50000);

describe('Cards unit tests', function () {
    let token, cardId;

    const cardNo = 9999999999;

    beforeAll(async () => {
        token = global.accessToken;

        const cardDetails = {
            memberId: new mongoose.Types.ObjectId(memberId),
            regionId: new mongoose.Types.ObjectId(regionId),
            cardNo: cardNo,
            cardNoStr: cardNo.toString(),
            organizationId: new mongoose.Types.ObjectId(organizationId),
            printJobId: mongoose.Types.ObjectId(),
            printJobNumber: 'INT-0000001',
            distributionJobId: mongoose.Types.ObjectId(),
            distributionJobNumber: 'INT-0000001',
            status: 'ACTIVE',
            processingStatus: 'COMPLETED',
            type: 'KEY_TAG',
            createdBy: mongoose.Types.ObjectId()
        };

        const CardModel = new Card(cardDetails);
        const card = (await CardModel.save()).toObject({ getters: true });

        cardId = card._id.toString();
        return Promise.resolve();
    });

    afterAll(async () => {
        await Card.deleteMany({ _id: cardId });
    });

    it('/GET get cards should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 20,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get cards should return 200', () => {
        return server
            .get(`/${cardId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get cards summary should return 200', () => {
        const query = querystring.stringify(
            {
                regionId,
                cardTypes: ['DIGITAL_CARD']
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/summary?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get cards stock should return 200', () => {
        const query = querystring.stringify(
            {
                regionId,
                cardTypes: ['DIGITAL_CARD']
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/stock?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/GET get a card should return 200', () => {
        return server
            .get(`/${cardId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/POST assign card to member', async () => {
        const payload = { memberId, cardNumber: cardNo };
        return server
            .post('/assign')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 10000);

    it('/POST assign card to member when sent the card number using the cardNoStr attribute', async () => {
        await Card.findOneAndUpdate({ _id: cardId }, { status: 'ACTIVE', memberId: null });
        const payload = { memberId, cardNumberStr: cardNo.toString() };
        return server
            .post('/assign')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 10000);

    it('/POST assign card should return 400 when both the cardNo and cardNoStr are empty', async () => {
        await Card.findOneAndUpdate({ _id: cardId }, { status: 'ACTIVE', memberId: null });
        const payload = { memberId };
        return server
            .post('/assign')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(400);
            });
    }, 10000);

    it.skip('/PUT Request emboss card should return 200', () => {
        return server
            .put(`/requestembossed/${cardId}`)
            .send({
                printedName: 'Test Name',
                merchantLocationId
            })
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/PUT update card details return 200', () => {
        return server
            .put(`/${cardId}`)
            .send({
                status: 'SUSPENDED',
                note: 'Test Content Updated'
            })
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/POST generate card manually', async () => {
        const payload = {
            regionId: regionId,
            cardNumbers: ['93472934832794']
        };

        // * Set manual generation to "true";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: true } }
        });

        return server
            .post('/generate')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 10000);

    it('/POST auto generate card', async () => {
        const payload = { regionId, autoGenerate: true };

        // * Set manual generation to "true";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: true } }
        });

        return server
            .post('/generate')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 10000);
});
