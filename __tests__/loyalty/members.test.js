'use strict';
const supertest = require('supertest');
const mongoose = require('mongoose');

const Utils = require('./../../lib/Utils');
const secretConfig = require('./../../config');
const server = supertest.agent(`${process.env.API_HOST_URL}/members`);
const Member = require('./../../lib/db/models/member.model');
const PointRule = require('./../../lib/db/models/point.rules.model');
const Card = require('./../../lib/db/models/card.model');
const Transaction = require('./../../lib/db/models/transaction.model');
const AffinityGroup = require('./../../lib/db/models/affinity.group.model');
const {
    PreferredCommunication,
    ChannelVerificationStatus,
    MemberVerificationStatus
} = require('../../lib/db/models/enums/member.enums');

const querystring = require('query-string');
const Organization = require('./../../lib/db/models/organization.model');
const MemberPrimaryAttributes = require('../../lib/constants/MemberPrimaryAttributes');
const {
    merchantLocationId,
    organizationId,
    regionId,
    affinityGroup1Id: affinityGroupId,
    affinityGroup2Id,
    memberId: dbTestMemberId
} = require('../../.jest/constants');
const OrganizationHandler = require('../../lib/handlers/OrganizationHandler');

const pointRuleId = '6549c4fff0b858e85e1a2009';
const cardNumber = '555666777888';

jest.setTimeout(50000);

describe('Members unit tests', () => {
    let token,
        secondaryMemberId,
        memberId,
        addSecondaryTestMemberId,
        primaryCardId,
        secondaryCardId,
        primaryMemberId,
        pointRuleTestMemberId;

    const createdMemberIds = [];

    beforeAll(async () => {
        token = global.accessToken;

        return Promise.resolve();
    });

    afterAll(async () => {
        try {
            await Promise.all([
                Member.deleteMany({
                    _id: {
                        $in: [
                            memberId,
                            secondaryMemberId,
                            addSecondaryTestMemberId,
                            pointRuleTestMemberId,
                            ...createdMemberIds
                        ]
                    }
                }),
                Card.deleteMany({
                    _id: { $in: [primaryCardId, secondaryCardId] }
                }),
                PointRule.deleteMany({
                    _id: { $in: [pointRuleId] }
                }),
                Organization.updateOne(
                    { _id: new mongoose.Types.ObjectId(organizationId) },
                    {
                        $set: {
                            'configuration.memberPrimaryAttribute': MemberPrimaryAttributes.EMAIL
                        }
                    }
                )
            ]);
        } catch (e) {}
    });

    it('Create primary loyalty profile', async () => {
        const user = {
            birthDate: '1988-01-21',
            firstName: 'Piyal',
            type: 'PRIMARY',
            companyName: 'Test Org',
            gender: 'MALE',
            regionId,
            merchantLocationId,
            mobileNumber: '94776361069',
            affinityGroup: {
                affinityGroupId,
                expiryDate: '2030-01-01'
            },
            email: '<EMAIL>'
        };
        const response = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(201);

        //if preferred name is not provided it should be generated
        expect(response.body.preferredName).toBe('Piyal');

        memberId = response.body._id;

        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(response.body._id)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const affinityGroup = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroupId)
        });
        expect(affinityGroup.membersCount).toBe(1);
    });

    it('Create primary loyalty profile with signup bonus', async () => {
        const pointRuleEnrollBonusModel = new PointRule({
            _id: pointRuleId,
            organizationId,
            regionId,
            name: 'Enroll Bonus',
            description: 'Enroll Bonus',
            type: 'NON_TRANSACTIONAL',
            subType: 'SIGNUP',
            ruleData: {
                points: 100
            },
            createdBy: '6549c4fff0b858e85e1a2009',
            status: 'ENABLED'
        });
        await pointRuleEnrollBonusModel.save();

        const user = {
            birthDate: '1988-01-21',
            firstName: 'Piyal',
            type: 'PRIMARY',
            companyName: 'Test Org',
            gender: 'MALE',
            regionId,
            merchantLocationId,
            mobileNumber: '94776361069',
            affinityGroup: {
                affinityGroupId,
                expiryDate: '2030-01-01'
            },
            email: '<EMAIL>'
        };
        const response = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        pointRuleTestMemberId = response.body._id;
        primaryMemberId = response.body._id;
        expect(response.statusCode).toBe(201);

        const member = await Member.findById(response.body._id).lean();
        expect(Number(member.points)).toBe(100);
        const pointRule = await PointRule.findById(pointRuleId).lean();
        expect(pointRule.matchedCount).toBe(1);

        const [transaction] = await Transaction.find().sort({ createdOn: 'desc' }).limit(1);
        const {
            subtransactionTypeIdMap: { signupBonusCollection }
        } = await Organization.findById(organizationId);
        expect(transaction.subType.toString()).toBe(signupBonusCollection.toString());
    });

    it.skip('Create primary loyalty profile with the same email should return 400', async () => {
        const user = {
            birthDate: '1988-01-21',
            firstName: 'Piyal',
            type: 'PRIMARY',
            companyName: 'Test Org',
            gender: 'MALE',
            regionId,
            merchantLocationId,
            mobileNumber: '94776361069',
            affinityGroup: {
                affinityGroupId
            },
            email: '<EMAIL>'
        };
        const response = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    }, 100000);

    it.skip('Create primary loyalty profile with the same mobile number should return 400', async () => {
        // * Change organization "memberPrimaryAttribute" to MOBILE_NUMBER.
        await Organization.updateOne(
            { _id: mongoose.Types.ObjectId(organizationId) },
            {
                $set: {
                    'configuration.memberPrimaryAttribute': MemberPrimaryAttributes.MOBILE_NUMBER
                }
            }
        );
        const user = {
            birthDate: '1988-01-21',
            firstName: 'Piyal',
            type: 'PRIMARY',
            companyName: 'Test Org',
            gender: 'MALE',
            regionId,
            merchantLocationId,
            mobileNumber: '94776361069',
            affinityGroup: {
                affinityGroupId
            },
            email: '<EMAIL>'
        };
        const response = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    }, 100000);

    it.skip('Create primary loyalty profile with a different mobile number should return 201', async () => {
        const user = {
            birthDate: '1988-01-21',
            firstName: 'Piyal',
            type: 'PRIMARY',
            companyName: 'Test Org',
            gender: 'MALE',
            regionId,
            merchantLocationId,
            mobileNumber: '94776361060',
            affinityGroup: {
                affinityGroupId
            },
            email: '<EMAIL>'
        };
        const response = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(201);
    }, 100000);

    it.skip('Create secondary loyalty profile', async () => {
        const user = {
            birthDate: '1988-01-21',
            firstName: 'Saman',
            lastName: 'Serasinghe',
            preferredName: 'Saman',
            type: 'SECONDARY',
            parentMemberId: memberId,
            merchantLocationId,
            companyName: 'Test Org',
            gender: 'MALE',
            regionId: regionId,
            mobileNumber: '94776361069',
            email: '<EMAIL>'
        };
        const response = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        expect(response.statusCode).toBe(201);
        secondaryMemberId = response.body._id;
        console.log(response.body);
        expect(response.body.affinityGroup.affinityGroupId).toBe(affinityGroupId);

        //when preferred name is provided create the user with given preferred name
        expect(response.body.preferredName).toBe('Saman');

        const affinityGroup = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroupId)
        });
        expect(affinityGroup.membersCount).toBe(2);
    }, 1000000);

    it.skip('update member affinity group', async () => {
        const user = {
            affinityGroupId: affinityGroup2Id,
            expiryDate: '2030-01-01'
        };
        const response = await server
            .put(`/affinityGroup/${memberId}`)
            .set('Authorization', `Bearer ${token}`)
            .send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(200);
        expect(response.body.affinityGroup.affinityGroupId).toBe(affinityGroup2Id);

        const affinityGroup = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroupId)
        });
        console.log(affinityGroup);
        expect(affinityGroup.membersCount).toBe(0);

        const affinityGroup2 = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroup2Id)
        });
        console.log(affinityGroup2);
        expect(affinityGroup2.membersCount).toBe(2);
    });

    it.skip('update secondary member affinity group', async () => {
        const user = {
            affinityGroupId: affinityGroup2Id,
            expiryDate: '2030-01-01'
        };
        const response = await server
            .put(`/affinityGroup/${secondaryMemberId}`)
            .set('Authorization', `Bearer ${token}`)
            .send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    });

    it.skip('update member affinity group to null', async () => {
        const user = {
            affinityGroupId: null
        };
        const response = await server
            .put(`/affinityGroup/${memberId}`)
            .set('Authorization', `Bearer ${token}`)
            .send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(200);
        expect(response.body.affinityGroup.affinityGroupId).toBe(affinityGroupId);

        //Members should be removed from their affinity group; And affinity group member count should be updated
        const affinityGroup2 = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroup2Id)
        });
        console.log(affinityGroup2);
        expect(affinityGroup2.membersCount).toBe(0);

        //Members should be in default affinity group; And affinity group member count should be updated
        const affinityGroup = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroupId)
        });
        console.log(affinityGroup);
        expect(affinityGroup.membersCount).toBe(2);
    });

    it.skip('Suspend loyalty profile should suspend all the associated cards and accounts', async () => {
        const card = {
            organizationId,
            regionId,
            cardNo: ***********,
            cardNoStr: '***********',
            status: 'ASSIGNED',
            processingStatus: 'COMPLETED',
            type: 'DIGITAL_CARD',
            memberId: mongoose.Types.ObjectId(memberId),
            createdBy: mongoose.Types.ObjectId()
        };
        const primaryCardModel = new Card(card);
        const secondaryCardModel = new Card({
            ...card,
            cardNo: ***********,
            cardNoStr: '***********',
            memberId: mongoose.Types.ObjectId(secondaryMemberId)
        });
        const primaryCard = (await primaryCardModel.save()).toObject({ getters: true });
        primaryCardId = primaryCard._id.toString();
        const secondaryCard = (await secondaryCardModel.save()).toObject({ getters: true });
        secondaryCardId = secondaryCard._id.toString();
        const user = {
            memberId: memberId,
            status: 'SUSPENDED',
            notes: 'Fraudulent transactions'
        };
        return server
            .post('/status')
            .set('Authorization', `Bearer ${token}`)
            .send(user)
            .then(async (response) => {
                expect(response.statusCode).toBe(200);

                const secondaryMember = await Member.findOne({
                    _id: mongoose.Types.ObjectId(secondaryMemberId)
                });
                expect(secondaryMember.status).toBe('SUSPENDED');

                const primaryCardResult = await Card.findOne({
                    _id: primaryCard._id
                });
                expect(primaryCardResult.status).toBe('SUSPENDED');

                const secondaryCardResult = await Card.findOne({
                    _id: secondaryCard._id
                });
                expect(secondaryCardResult.status).toBe('SUSPENDED');
            });
    });

    it.skip('Activate loyalty profile', async () => {
        const user = {
            memberId: memberId,
            status: 'ACTIVE',
            notes: 'Fraudulent transactions'
        };
        return server
            .post('/status')
            .set('Authorization', `Bearer ${token}`)
            .send(user)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get members should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get secondary members should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId,
                parentMemberId: '6192b7a2b5e1bfce4ff5a0a4',
                fields: ['preferredName', 'loyaltyId']
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get members with fields should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId,
                fields: ['preferredName', 'mobileNumber', 'email']
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get members with sort should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId,
                sortBy: 'preferredName'
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get members with search should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId,
                searchKey: 'Test'
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/POST get members with filter should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        const body = {
            $and: [
                {
                    $or: [
                        {
                            name: 'Test User'
                        },
                        {
                            email: '<EMAIL>'
                        }
                    ]
                },
                {
                    bio: 'Test Bio'
                }
            ]
        };
        return server
            .post(`/filter?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .send(body)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/POST get members with filter should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        const body = {
            gender: 'MALE'
        };
        return server
            .post(`/filter?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .send(body)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it('/GET get a member should return 200', () => {
        return server
            .get(`/${memberId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('Add a secondary member', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    affinityGroupId: affinityGroupId,
                    'tier.tierId': mongoose.Types.ObjectId()
                }
            }
        );

        await AffinityGroup.updateOne(
            {
                _id: mongoose.Types.ObjectId(affinityGroupId)
            },
            {
                $set: {
                    membersCount: 1
                }
            }
        );

        await AffinityGroup.updateOne(
            {
                _id: mongoose.Types.ObjectId(affinityGroup2Id)
            },
            {
                $set: {
                    membersCount: 0
                }
            }
        );

        const user = {
            birthDate: '1988-01-21',
            firstName: 'Dominic',
            type: 'PRIMARY',
            companyName: 'Test Org',
            gender: 'MALE',
            regionId,
            merchantLocationId,
            affinityGroup: {
                affinityGroupId: affinityGroup2Id,
                expiryDate: '2030-01-01'
            },
            mobileNumber: '94776361069',
            email: '<EMAIL>'
        };
        const userResponse = await server.post('/').set('Authorization', `Bearer ${token}`).send(user);

        console.log(userResponse.body);
        expect(userResponse.statusCode).toBe(201);
        expect(userResponse.body.affinityGroup.affinityGroupId).toBe(affinityGroup2Id);
        addSecondaryTestMemberId = userResponse.body._id;

        const affinityGroup2 = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroup2Id)
        });
        expect(affinityGroup2.membersCount).toBe(1);

        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(addSecondaryTestMemberId)
            },
            {
                $set: {
                    points: 500,
                    pointsToExpire: [
                        {
                            pointsToExpire: 250,
                            pointsExpireOn: '2024-01-01'
                        }
                    ],
                    'tier.tierId': mongoose.Types.ObjectId()
                }
            }
        );

        const dataObj = {
            primaryMemberId: memberId,
            secondaryMemberId: addSecondaryTestMemberId
        };
        const tokenRequestResponse = await server
            .post('/secondaryaddrequest')
            .set('Authorization', `Bearer ${token}`)
            .send(dataObj);
        console.log(tokenRequestResponse.body);
        expect(tokenRequestResponse.statusCode).toBe(200);
        expect(tokenRequestResponse.body.requestToken).toBeDefined();

        const { primaryOtpCode, secondaryOtpCode } = JSON.parse(
            Utils.decrypt(tokenRequestResponse.body.requestToken, secretConfig.DATA_ENCRYPTION_SECRET)
        );

        const addSecondaryRequest = {
            requestToken: tokenRequestResponse.body.requestToken,
            primaryOtpCode: primaryOtpCode,
            secondaryOtpCode: secondaryOtpCode
        };
        const addSecondaryRequestResponse = await server
            .post('/secondaryadd')
            .set('Authorization', `Bearer ${token}`)
            .send(addSecondaryRequest);
        console.log(addSecondaryRequestResponse.body);
        expect(addSecondaryRequestResponse.statusCode).toBe(200);
        expect(addSecondaryRequestResponse.body.type).toEqual('SECONDARY');
        expect(addSecondaryRequestResponse.body.affinityGroup.affinityGroupId).toEqual(affinityGroupId);

        const updatedSecondaryMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(addSecondaryTestMemberId)
        });
        expect(updatedSecondaryMember.points).toEqual(0);
        expect(updatedSecondaryMember.pointsToExpire).toEqual(0);

        const updatedPrimaryMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });
        expect(updatedPrimaryMember.points).toEqual(1000);

        const affinityGroup = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroupId)
        });
        expect(affinityGroup.membersCount).toBe(2);

        const affinityGroup2Updated = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroup2Id)
        });
        expect(affinityGroup2Updated.membersCount).toBe(0);
    }, 1000000);

    it.skip('Add a secondary member', async () => {
        const dataObj = {
            primaryMemberId: primaryMemberId,
            secondaryMemberId: memberId
        };
        const tokenRequestResponse = await server
            .post('/secondaryaddrequest')
            .set('Authorization', `Bearer ${token}`)
            .send(dataObj);
        console.log(tokenRequestResponse.body);
        expect(tokenRequestResponse.statusCode).toBe(400);
    }, 1000000);

    it('/POST member data export request should return 200', () => {
        const body = {
            memberId
        };
        return server
            .post(`/export`)
            .set('Authorization', `Bearer ${token}`)
            .send(body)
            .then((response) => {
                console.log(response.body);
                expect(response.statusCode).toBe(200);
            });
    });

    it('/POST member erase request should return 200', () => {
        const body = {
            memberId
        };
        return server
            .post(`/erase`)
            .set('Authorization', `Bearer ${token}`)
            .send(body)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/DELETE delete member should remove user from the affinity group and return 200', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    status: 'ACTIVE'
                }
            }
        );

        const response = await server.delete(`/${memberId}`).set('Authorization', `Bearer ${token}`);

        expect(response.statusCode).toBe(200);

        const affinityGroup = await AffinityGroup.findOne({
            _id: mongoose.Types.ObjectId(affinityGroupId)
        });
        console.log(response.body);
        expect(affinityGroup.membersCount).toBe(1);
    });

    it('/POST member verify channels request to verify email should return 200', async () => {
        const body = { memberId: dbTestMemberId, verifyEmail: ChannelVerificationStatus.VERIFY };

        const response = await server.post(`/verifychannels`).set('Authorization', `Bearer ${token}`).send(body);

        console.log(response.body);

        expect(response.statusCode).toBe(200);
    });

    it('/POST member verify channels request to verify both email and mobile number should return 200', async () => {
        const body = {
            memberId: dbTestMemberId,
            verifyEmail: ChannelVerificationStatus.VERIFY,
            verifyMobileNumber: ChannelVerificationStatus.VERIFY
        };

        const response = await server.post(`/verifychannels`).set('Authorization', `Bearer ${token}`).send(body);

        console.log(response.body);

        expect(response.statusCode).toBe(200);
    });

    it('/POST member verify channels request to unverify mobile number with an invalid payload should return 400 with the error code "160019"', async () => {
        // * Invalid payload. Missing both "verifyEmail" and "verifyMobileNumber" attributes.
        const body = { memberId };

        const response = await server.post(`/verifychannels`).set('Authorization', `Bearer ${token}`).send(body);

        console.log(response.body);

        expect(response.statusCode).toBe(400);
        expect(response.body?.errorCode).toBe('160019');
    });

    it('/POST member verification request should return 200', async () => {
        const body = { memberId: dbTestMemberId, verifyMember: MemberVerificationStatus.VERIFY };

        const response = await server.post(`/verificationstatus`).set('Authorization', `Bearer ${token}`).send(body);

        console.log(response.body);

        expect(response.statusCode).toBe(200);
    });

    it('/POST member verification request with an invalid payload should return 400 with the error code "160024"', async () => {
        // * Invalid payload. Missing the "verifyMember" attribute.
        const body = { memberId: dbTestMemberId };

        const response = await server.post(`/verificationstatus`).set('Authorization', `Bearer ${token}`).send(body);

        console.log(response.body);

        expect(response.statusCode).toBe(400);
        expect(response.body?.errorCode).toBe('160024');
    });

    // * Member Registration endpoints tests.
    const cardNumberMemReg = '554455667755441';
    it('MEMBER_REGISTER: Registering member (without otp) with valid data should create member, generate card and should assign that card to the created member', async () => {
        const user = {
            regionId,
            cardNumber: cardNumberMemReg,
            merchantLocationId,
            firstName: 'Register Member (without otp) 1',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '0889283748',
            email: '<EMAIL>',
            gender: 'MALE'
        };
        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        createdMemberIds.push(response.body._id);
        expect(response.statusCode).toBe(201);
        expect(response.body.cardNumber).toBe(cardNumberMemReg);

        const card = await Card.findOne({
            cardNoStr: cardNumberMemReg
        });
        expect(card).toBeDefined();
        expect(card.status).toBe('ASSIGNED');
        expect(card.memberId.toString()).toBe(response.body._id);
    });

    it('MEMBER_REGISTER: Registering member (without otp) with an invalid merchant location code should return an error with code 404 and "errorCode" "100002"', async () => {
        const user = {
            regionId,
            cardNumber,
            merchantLocationCode: 'TEST_INVALID_CODE',
            firstName: 'Register Member (without otp) invalid code',
            lastName: 'Test',
            preferredName: 'Test (invalid code)',
            mobileNumber: '0889281113748',
            email: '<EMAIL>',
            gender: 'MALE'
        };
        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(404);
        expect(response.body?.errorCode).toBe('100002');
    });

    it('MEMBER_REGISTER: Registering member (without otp) with existing card number should return an error with code 400', async () => {
        const user = {
            regionId,
            cardNumber: cardNumberMemReg,
            merchantLocationId,
            firstName: 'Register Member (without otp) 1 (card exisitng)',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '0889283748',
            email: '<EMAIL>',
            gender: 'MALE'
        };
        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    });

    it('MEMBER_REGISTER: Registering member (without otp) when organizations manual card generation is disabled should return an error with code 400', async () => {
        const user = {
            regionId,
            cardNumber: '76378485999',
            merchantLocationId,
            firstName: 'Register Member (without otp) 1 (manual generation not allowed)',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '0889283749',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        // * Set manual generation to "false";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: false } }
        });

        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        // * Set manual generation to "true";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: true } }
        });

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    });

    // * Register member with otp tests.
    const cardNumberForRegisterWithOtp = '1111222233334444';
    const emailForRegisterWithOtp = '<EMAIL>';
    it('MEMBER_REGISTER: REGISTER WITH OTP: Registering member (with otp) with valid data should create member, generate card and should assign that card to the created member', async () => {
        const payload = {
            regionId,
            cardNumber: cardNumberForRegisterWithOtp,
            merchantLocationCode: 'LOC1',
            firstName: 'Register Member (with otp) 1',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '999933443441',
            email: emailForRegisterWithOtp,
            gender: 'MALE'
        };

        const otpTokenResponse = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        console.log('Otp token response:', otpTokenResponse.body);

        expect(otpTokenResponse.statusCode).toBe(200);
        expect(otpTokenResponse.body.registerMemberToken).toBeDefined();

        const { otpCode } = JSON.parse(
            Utils.decrypt(otpTokenResponse.body.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET)
        ); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

        const response = await server.post('/registerwithotp').set('Authorization', `Bearer ${token}`).send({
            registerMemberToken: otpTokenResponse.body.registerMemberToken,
            otpCode
        });

        console.log(response.body);
        createdMemberIds.push(response.body._id);
        expect(response.statusCode).toBe(201);

        const card = await Card.findOne({
            cardNoStr: cardNumberForRegisterWithOtp
        });
        expect(card).toBeDefined();
        expect(card.status).toBe('ASSIGNED');
        expect(card.memberId.toString()).toBe(response.body._id);
    });

    it('MEMBER_REGISTER: REGISTER WITH OTP: Registering member (with otp) with the same email as before should return an error with code 400', async () => {
        const payload = {
            regionId,
            cardNumber: '1111222233335555',
            merchantLocationCode: 'LOC1',
            firstName: 'Register Member (with otp) 2',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '999933443442',
            email: emailForRegisterWithOtp,
            gender: 'MALE'
        };

        const otpTokenResponse = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        expect(otpTokenResponse.statusCode).toBe(400);
        expect(otpTokenResponse.body?.errorCode).toBe('150114');
    });

    it('MEMBER_REGISTER: REGISTER WITH OTP: Registering member (with otp) with the same card number as before should return an error with code 400', async () => {
        const payload = {
            regionId,
            cardNumber: cardNumberForRegisterWithOtp,
            merchantLocationCode: 'LOC1',
            firstName: 'Register Member (with otp) 2',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '999933443444',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        const otpTokenResponse = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        expect(otpTokenResponse.statusCode).toBe(400);
        expect(otpTokenResponse.body?.errorCode).toBe('180002');
    });

    it('MEMBER_REGISTER: REGISTER WITH OTP: Registering member (with otp) with 2 otp requests and using the otp from the first request should return an error with code 400 and "errorCode" "150110"', async () => {
        const payload = {
            regionId,
            cardNumber: '1111222233334999',
            merchantLocationCode: 'LOC1',
            firstName: 'Register Member (with otp) 4',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '999933443446',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        const otpTokenResponse1 = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        const otpTokenResponse2 = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        console.log('Otp token response 1:', otpTokenResponse1.body);
        console.log('Otp token response 2:', otpTokenResponse2.body);

        // * Both requests must succeed.
        // ? Testing request 1.
        expect(otpTokenResponse1.statusCode).toBe(200);
        expect(otpTokenResponse1.body.registerMemberToken).toBeDefined();

        // ? Testing request 2.
        expect(otpTokenResponse2.statusCode).toBe(200);
        expect(otpTokenResponse2.body.registerMemberToken).toBeDefined();

        const { otpCode: otpFromRes1 } = JSON.parse(
            Utils.decrypt(otpTokenResponse1.body.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET)
        ); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

        const { otpCode: _otpFromRes2 } = JSON.parse(
            Utils.decrypt(otpTokenResponse2.body.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET)
        ); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

        const response = await server.post('/registerwithotp').set('Authorization', `Bearer ${token}`).send({
            registerMemberToken: otpTokenResponse2.body.registerMemberToken,
            otpCode: otpFromRes1
        });

        console.log(response.body);
        expect(response.statusCode).toBe(400);
        expect(response.body?.errorCode).toBe('150110');
    });

    it('MEMBER_REGISTER: REGISTER WITH OTP: Registering member (with otp) with 2 otp requests and using the "registerMemberToken" from the first request should return an error with code 400 and "errorCode" "150109"', async () => {
        const payload = {
            regionId,
            cardNumber: '1111222233334888',
            merchantLocationCode: 'LOC1',
            firstName: 'Register Member (with otp) 5',
            lastName: 'Test',
            preferredName: 'Test',
            mobileNumber: '999933443449',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        const otpTokenResponse1 = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        const otpTokenResponse2 = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        console.log('Otp token response 1:', otpTokenResponse1.body);
        console.log('Otp token response 2:', otpTokenResponse2.body);

        // * Both requests must succeed.
        // ? Testing request 1.
        expect(otpTokenResponse1.statusCode).toBe(200);
        expect(otpTokenResponse1.body.registerMemberToken).toBeDefined();

        // ? Testing request 2.
        expect(otpTokenResponse2.statusCode).toBe(200);
        expect(otpTokenResponse2.body.registerMemberToken).toBeDefined();

        const { otpCode: _otpFromRes1 } = JSON.parse(
            Utils.decrypt(otpTokenResponse1.body.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET)
        ); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

        const { otpCode: otpFromRes2 } = JSON.parse(
            Utils.decrypt(otpTokenResponse2.body.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET)
        ); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

        const response = await server.post('/registerwithotp').set('Authorization', `Bearer ${token}`).send({
            registerMemberToken: otpTokenResponse1.body.registerMemberToken,
            otpCode: otpFromRes2
        });

        console.log(response.body);
        expect(response.statusCode).toBe(400);
        expect(response.body?.errorCode).toBe('150109');
    });

    // * Register endpoint tests with auto card generation.
    it('MEMBER_REGISTER: AUTO CARD GENERATION: Registering (without otp) member with valid data should create member, auto generate a card and should assign that card to the created member', async () => {
        const user = {
            regionId,
            autoGenerateCard: true,
            merchantLocationId,
            firstName: 'Register Member (without otp) Auto 1',
            lastName: 'Test Auto',
            preferredName: 'Test Auto 1',
            mobileNumber: '0889283741',
            email: '<EMAIL>',
            gender: 'MALE'
        };
        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        createdMemberIds.push(response.body._id);
        expect(response.statusCode).toBe(201);

        const card = await Card.findOne({
            cardNoStr: response.body.cardNumber
        });
        expect(card).toBeDefined();
        expect(card.status).toBe('ASSIGNED');
        expect(card.memberId.toString()).toBe(response.body._id);
    });

    it('MEMBER_REGISTER: AUTO CARD GENERATION: Registering (without otp) member when "autoGenerateCard=true" and a "cardNumber" is also provided, should return an error with code 400', async () => {
        const user = {
            regionId,
            cardNumber: '76378485999',
            autoGenerateCard: true,
            merchantLocationId,
            firstName: 'Register Member (without otp) Auto 2',
            lastName: 'Test Auto',
            preferredName: 'Test Auto 2',
            mobileNumber: '0889283742',
            email: '<EMAIL>',
            gender: 'MALE'
        };
        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    });

    it('MEMBER_REGISTER: AUTO CARD GENERATION: Registering (without otp) member when organizations manual card generation is disabled should return an error with code 400', async () => {
        const user = {
            regionId,
            autoGenerateCard: true,
            merchantLocationId,
            firstName: 'Register Member (without otp) Auto 3',
            lastName: 'Test Auto',
            preferredName: 'Test Auto 3',
            mobileNumber: '0889283743',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        // * Set manual generation to "false";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: false } }
        });

        const response = await server.post('/register').set('Authorization', `Bearer ${token}`).send(user);

        // * Set manual generation to "true";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: true } }
        });

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    });

    // * Register endpoint (with otp) tests with auto card generation.
    it('MEMBER_REGISTER: REGISTER WITH OTP: AUTO CARD GENERATION: Registering (with otp) member with valid data should create member, auto generate a card and should assign that card to the created member', async () => {
        const payload = {
            regionId,
            autoGenerateCard: true,
            merchantLocationId,
            firstName: 'Register Member (with otp) Auto 1',
            lastName: 'Test Auto',
            preferredName: 'Test Auto (with otp) 1',
            mobileNumber: '939444545442',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        const otpTokenResponse = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        expect(otpTokenResponse.statusCode).toBe(200);
        expect(otpTokenResponse.body.registerMemberToken).toBeDefined();

        const { otpCode } = JSON.parse(
            Utils.decrypt(otpTokenResponse.body.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET)
        ); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

        const response = await server.post('/registerwithotp').set('Authorization', `Bearer ${token}`).send({
            registerMemberToken: otpTokenResponse.body.registerMemberToken,
            otpCode
        });

        console.log(response.body);
        createdMemberIds.push(response.body._id);
        expect(response.statusCode).toBe(201);

        const card = await Card.findOne({
            cardNoStr: response.body.cardNumber
        });
        expect(card).toBeDefined();
        expect(card.status).toBe('ASSIGNED');
        expect(card.memberId.toString()).toBe(response.body._id);
    });

    it('MEMBER_REGISTER: REGISTER WITH OTP: AUTO CARD GENERATION: Registering (with otp) member when "autoGenerateCard=true" and a "cardNumber" is also provided, should return an error with code 400', async () => {
        const payload = {
            regionId,
            cardNumber: '76378485999',
            autoGenerateCard: true,
            merchantLocationId,
            firstName: 'Register Member (with otp) Auto 2',
            lastName: 'Test Auto',
            preferredName: 'Test Auto (with otp) 2',
            mobileNumber: '999821311',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        const otpTokenResponse = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        console.log(otpTokenResponse.body);
        expect(otpTokenResponse.statusCode).toBe(400);
        expect(otpTokenResponse.body?.errorCode).toBe('150101');
    });

    it('MEMBER_REGISTER: REGISTER WITH OTP: AUTO CARD GENERATION: Registering (with otp) member when organizations manual card generation is disabled should return an error with code 400', async () => {
        const payload = {
            regionId,
            autoGenerateCard: true,
            merchantLocationId,
            firstName: 'Register Member (with otp) Auto 3',
            lastName: 'Test Auto',
            preferredName: 'Test Auto (with otp) 3',
            mobileNumber: '993444321111',
            email: '<EMAIL>',
            gender: 'MALE'
        };

        // * Set manual generation to "false";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: false } }
        });

        const otpTokenResponse = await server
            .post('/registerwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        // * Set manual generation to "true";
        await OrganizationHandler.updateOrganization(organizationId, {
            configuration: { cardConfiguration: { allowManualCardGeneration: true } }
        });

        console.log(otpTokenResponse.body);
        expect(otpTokenResponse.statusCode).toBe(400);
        expect(otpTokenResponse.body?.errorCode).toBe('180001');
    });
});
