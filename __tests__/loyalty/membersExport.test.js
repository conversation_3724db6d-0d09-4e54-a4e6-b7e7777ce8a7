'use strict';
const supertest = require('supertest');

const server = supertest.agent(`${process.env.API_HOST_URL}/members`);
const querystring = require('query-string');
const JobType = require('./../../lib/db/models/job.type.model');
const mongoose = require('mongoose');
const { organizationId, userId, regionId } = require('../../.jest/constants');
jest.setTimeout(50000);

describe('Member export unit tests', () => {
    let token;
    const exportJobTypeId = '63cf8675fe73f08d3e0c80b2';
    beforeAll(async () => {
        token = global.accessToken;

        await new JobType({
            _id: new mongoose.Types.ObjectId(exportJobTypeId),
            organizationId: new mongoose.Types.ObjectId(organizationId),
            name: 'Members list export',
            description: 'Members list export',
            queueName: 'members_export_jobs_queue',
            status: 'ENABLED',
            metadata: {
                properties: {
                    aggregation: {
                        type: 'string',
                        title: 'Aggregation'
                    }
                },
                required: ['aggregation'],
                type: 'object'
            },
            updatedBy: new mongoose.Types.ObjectId(userId),
            updatedOn: new Date(),
            type: 'EXPORT_MEMBERS'
        });
        return Promise.resolve();
    });

    afterAll(async () => {
        await JobType.findOneAndDelete({
            _id: new mongoose.Types.ObjectId(exportJobTypeId),
            organizationId: new mongoose.Types.ObjectId(organizationId)
        });
    });

    it.skip('/POST export members with filter should return 200', () => {
        const query = querystring.stringify(
            {
                regionId,
                notificationEmails: ['<EMAIL>'],
                fields: ['firstName', 'lastName', 'points']
            },
            { arrayFormat: 'bracket' }
        );
        const body = {
            status: 'ACTIVE'
        };
        return server
            .post(`/filter/export?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .send(body)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });
});
