'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/redemptionlogs`);

const querystring = require('query-string');

const Member = require('./../../lib/db/models/member.model');
const RewardRedemptionLog = require('./../../lib/db/models/reward.redemption.log.model');
const { memberId } = require('../../.jest/constants');
describe('(MERCHANT) Redemption logs unit tests', () => {
    let token;
    beforeAll(async () => {
        token = global.accessToken;
        return Promise.resolve();
    });
    afterAll(() => {});

    const redemptionLogItemId = process.env.REDEMPTION_LOG_ITEM_ID;
    const regionId = process.env.REGION_ID;
    const merchantId = process.env.MERCHANT_ID;

    it.skip('/PUT update regional redemption log should return 403', async () => {
        const dataObj = {
            status: 'CANCELLED'
        };
        return server
            .put(`/${redemptionLogItemId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(403);
            });
    }, 10000);

    it.skip('/PUT refund regional redemption log should return 403', async () => {
        const dataObj = {
            notes: 'Test refund'
        };
        return server
            .put(`/refund/${redemptionLogItemId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(403);
            });
    }, 10000);

    it.skip('/GET get redemption logs should return 200', async () => {
        await RewardRedemptionLog.updateOne({ _id: redemptionLogItemId }, { $set: { merchantId } });
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.items).toBeDefined();
                expect(response.body.items).toBeInstanceOf(Array);
                for (const item of response.body.items) {
                    expect(item.merchantId).toBe(merchantId);
                }
            });
    }, 10000);

    it.skip('/PUT update redemption log should return 200', async () => {
        const dataObj = {
            status: 'CANCELLED'
        };
        return server
            .put(`/${redemptionLogItemId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.status).toBe('CANCELLED');
            });
    }, 10000);

    it.skip('/PUT refund redemption log should return 200', async () => {
        const dataObj = {
            notes: 'Test refund'
        };
        const member = await Member.findOne({ _id: memberId });
        return server
            .put(`/refund/${redemptionLogItemId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.totalPoints).toBe(member.points + 10);
                expect(response.body.points).toBe(10);
            });
    }, 10000);
});
