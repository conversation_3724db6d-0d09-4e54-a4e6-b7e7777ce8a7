'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/redemptionlogs`);

const querystring = require('query-string');
const Member = require('./../../lib/db/models/member.model');
const { memberId } = require('../../.jest/constants');
describe('Redemption logs unit tests', () => {
    let token;
    beforeAll(async () => {
        token = global.accessToken;

        return Promise.resolve();
    });

    const redemptionLogItemId = process.env.REDEMPTION_LOG_ITEM_ID;
    const regionId = process.env.REGION_ID;

    it('/GET get redemption logs should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.items).toBeDefined();
                expect(response.body.items).toBeInstanceOf(Array);
            });
    }, 10000);

    it('/PUT update redemption log should return 200', async () => {
        const dataObj = {
            status: 'CANCELLED'
        };
        return server
            .put(`/${redemptionLogItemId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.status).toBe('CANCELLED');
            });
    }, 10000);

    it.skip('/PUT refund redemption log should return 200', async () => {
        const dataObj = {
            notes: 'Test refund'
        };
        const member = await Member.findOne({ _id: memberId });
        return server
            .put(`/refund/${redemptionLogItemId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.totalPoints).toBe(member.points + 10);
                expect(response.body.points).toBe(10);
            });
    }, 10000);
});
