'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/rewarddistributionjobs`);

const querystring = require('query-string');

const RewardDistributionJob = require('./../../lib/db/models/reward.distribution.job.model');
const RewardRedemptionLog = require('./../../lib/db/models/reward.redemption.log.model');

describe('(MERCHANT) Reward distribution jobs unit tests', () => {
    const organizationId = process.env.ORGANIZATION_ID;
    const regionId = process.env.REGION_ID;
    const merchantId = process.env.MERCHANT_ID;
    const redemptionlogitemid = process.env.REDEMPTION_LOG_ITEM_ID;

    let token, rewardDistributionJobId, regionalRewardDistributionJobId;
    beforeAll(async () => {
        token = global.merchantAccessToken;

        const regionalRewardDistributionJobModel = new RewardDistributionJob({
            organizationId,
            regionId,
            batchId: 'BATCH-52',
            itemCount: 10,
            status: 'PENDING',
            createdBy: '613dfc56eac5369848619fe0',
            successCount: 0,
            failedCount: 0
        });
        const regionalRewardDistributionJob = await regionalRewardDistributionJobModel.save();
        regionalRewardDistributionJobId = regionalRewardDistributionJob._id.toString();
        return Promise.resolve();
    });

    it.skip('/GET get CSV for distribution job should return 200', () => {
        const query = querystring.stringify(
            {
                distributionJobId: rewardDistributionJobId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/getcsv/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body.url).toBeDefined();
                expect(response.body.url).toBe(typeof String);
            });
    }, 10000);

    it.skip('/POST create reward distribution job with unauthorized redemption log ids should return 400', async () => {
        const dataObj = {
            regionId,
            redemptionLogIds: [redemptionlogitemid]
        };
        return server
            .post(`/`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(400);
            });
    }, 10000);

    it.skip('/POST create reward distribution job should return 201', async () => {
        await RewardRedemptionLog.updateOne({ _id: redemptionlogitemid }, { $set: { merchantId } });
        const dataObj = {
            regionId,
            redemptionLogIds: [redemptionlogitemid]
        };
        return server
            .post(`/`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(201);
                expect(response.body).toBeDefined();
                expect(response.body.merchantId).toBe(merchantId);
                rewardDistributionJobId = response.body._id;
            });
    }, 10000);

    it.skip('/GET get reward distribution jobs should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.items).toBeDefined();
                expect(response.body.items).toBeInstanceOf(Array);
                for (const item of response.body.items) {
                    expect(item.merchantId).toBe(merchantId);
                }
            });
    }, 10000);

    it.skip('/PUT update reward distribution job should return 200', async () => {
        const dataObj = {
            status: 'DISPATCHED'
        };
        return server
            .put(`/${rewardDistributionJobId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.status).toBe('DISPATCHED');
            });
    }, 10000);

    it.skip('/PUT update regional reward distribution job should return 403', async () => {
        const dataObj = {
            status: 'DISPATCHED'
        };
        return server
            .put(`/${regionalRewardDistributionJobId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(403);
            });
    }, 10000);
});
