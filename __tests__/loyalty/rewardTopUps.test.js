'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/rewardtopups`);

const querystring = require('query-string');
const MongooseConnector = require('../../lib/db/connectors/MongooseConnector');
const Reward = require('./../../lib/db/models/reward.model');
const RewardTopUp = require('../../lib/db/models/reward.topup.model');
const VoucherCodeGenerator = require('voucher-code-generator');
const {
    TYPE,
    SUB_TYPE,
    POINTS_VALUE_TYPE,
    VALIDITY_PERIOD,
    DAILY_REDEMPTION_LIMIT,
    STATUS,
    PORTAL_VISIBILITY
} = require('../../lib/db/models/enums/reward.enums');

describe('Reward top up job unit tests', () => {
    const organizationId = process.env.ORGANIZATION_ID;
    const regionId = process.env.REGION_ID;
    const merchantId = process.env.MERCHANT_ID;
    const userId = process.env.USER_ID;

    let token, rewardId;
    beforeAll(async () => {
        token = global.accessToken;
        await MongooseConnector.initialize();
        const rewardModel = new Reward({
            organizationId,
            regionId,
            name: 'Test Reward',
            description: 'Test reward  description',
            type: TYPE.TANGIBLE,
            subType: SUB_TYPE.VOUCHER,
            imageUrls: 'https://i.picsum.photos/id/0/5616/3744.jpg',
            pointValueType: POINTS_VALUE_TYPE.STATIC,
            pointsStatic: 30,
            validityPeriod: VALIDITY_PERIOD.OPEN,
            dailyRedemptionLimit: DAILY_REDEMPTION_LIMIT.UNLIMITED,
            createdBy: userId,
            status: STATUS.ENABLED,
            portalVisibility: PORTAL_VISIBILITY.PARTNER_AND_CUSTOMER
        });
        const { _id } = await rewardModel.save();
        rewardId = _id;
        return Promise.resolve();
    });
    afterAll(async () => {
        await RewardTopUp.deleteMany({ rewardId });
        await Reward.deleteOne({ _id: rewardId });
        await MongooseConnector.close();
    });

    it.skip('/POST upload reward top up file should return 200', async () => {
        const query = querystring.stringify(
            {
                rewardId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .post(`/topupfile?${query}`)
            .attach('file', `${__dirname}/files/rewardtopup.csv`)
            .set('Authorization', `Bearer ${token}`)
            .set('Content-Type', 'multipart/form-data')
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 10000);

    it('/POST manual top up file should return 200', async () => {
        const payload = {
            regionId,
            rewardId,
            voucherCodes: VoucherCodeGenerator.generate({
                count: 10,
                charset: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
            }),
            method: 'MANUAL'
        };
        return server
            .post(`/manual`)
            .send(payload)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body.merchantId).toBeUndefined();
            });
    }, 10000);

    it('/POST system generated top up file should return 200', async () => {
        const payload = {
            regionId,
            rewardId,
            requestedVouchersCount: 10,
            method: 'GENERATED'
        };
        return server
            .post(`/manual`)
            .send(payload)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body.merchantId).toBeUndefined();
            });
    }, 10000);

    it('/POST merchant scoped system generated top up file should return 200', async () => {
        const payload = {
            regionId,
            rewardId,
            merchantId,
            requestedVouchersCount: 10,
            method: 'GENERATED'
        };
        return server
            .post(`/manual`)
            .send(payload)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body.merchantId).toBeDefined();
            });
    }, 10000);
});
