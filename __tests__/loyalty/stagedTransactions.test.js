'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/stagedtransactions`);

const querystring = require('query-string');
const mongoose = require('mongoose');

describe('Staged transactions unit tests', () => {
    let token;
    beforeAll(async () => {
        token = global.accessToken;
        return Promise.resolve();
    });
    afterAll(() => {});

    const regionId = process.env.REGION_ID;
    const merchantId = process.env.MERCHANT_ID;
    const merchantLocationId = process.env.MERCHANT_LOCATION_ID;

    const stagedTransactionId = '6184190516e46d9caee81f41'; //mongoose.Types.ObjectId().toString();

    it('/GET get staged transactions', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/PUT update a staged transaction should return 200', async () => {
        const update = {
            cardNo: '****************',
            transactionOn: new Date().toISOString()
        };
        return server
            .put(`/${stagedTransactionId}`)
            .set('Authorization', `Bearer ${token}`)
            .send(update)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/DELETE delete a staged transaction should return 200', async () => {
        return server
            .delete(`/${stagedTransactionId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });
});
