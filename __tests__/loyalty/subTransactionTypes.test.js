'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/subtransactiontypes`);

const querystring = require('query-string');

describe('Sub transaction types unit tests', () => {
    let token, subTransactionTypeId;
    beforeAll(async () => {
        token = global.accessToken;
        return Promise.resolve();
    });
    afterAll(() => {});

    it('/GET get sub transaction type list should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.items).toBeDefined();
                expect(response.body.items).toBeInstanceOf(Array);
            });
    });

    it('/POST create sub transaction type should return 201', async () => {
        const dataObj = {
            transactionType: 'COLLECTION',
            operationType: 'ADD',
            name: 'Test sub transaction type',
            description: 'Test sub transaction type',
            referenceId: '13215496788945'
        };
        return server
            .post('/')
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(201);
                expect(response.body).toBeDefined();
                subTransactionTypeId = response.body._id;
            });
    }, 10000);

    it('/PUT update sub transaction type should return 200', async () => {
        const dataObj = {
            name: 'Test update'
        };
        return server
            .put(`/${subTransactionTypeId}`)
            .send(dataObj)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.name).toBe('Test update');
            });
    }, 10000);

    it('/DELETE delete sub transaction type should return 200', async () => {
        return server
            .delete(`/${subTransactionTypeId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
                expect(response.body).toBeDefined();
                expect(response.body.status).toBe('ARCHIVED');
            });
    }, 10000);
});
