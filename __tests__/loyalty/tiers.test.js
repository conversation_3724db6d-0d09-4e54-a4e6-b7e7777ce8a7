'use strict';
const supertest = require('supertest');
const server = supertest.agent(`${process.env.API_HOST_URL}/tiers`);

const querystring = require('query-string');

describe('Tiers unit tests', () => {
    let token;
    beforeAll(async () => {
        token = global.accessToken;
        return Promise.resolve();
    });

    const regionId = '6137a9fff48b8eb1845c78cf'; //'613b3341c4a848acb1f934af'
    let tierId;

    it.skip('/POST create a tier 1 should return 201', async () => {
        let tier = {
            name: 'Garnet',
            regionId,
            status: 'ENABLED',
            benefits: ['Up to 80% discount on all products', 'Up to 80% discount on all products 222'],
            imageUrl:
                'https://s3.amazonaws.com/gallery.getshoutout.com/images/2757/22caf8ab-3031-43bf-8942-1c6849ea7a49.undefined',
            points: 0
        };
        return server
            .post('/')
            .set('Authorization', `Bearer ${token}`)
            .send(tier)
            .then((response) => {
                expect(response.statusCode).toBe(201);
            });
    }, 10000);

    it.skip('/POST create tier 2 should return 201', async () => {
        let tier = {
            name: 'Silver',
            regionId,
            status: 'ENABLED',
            benefits: ['Up to 80% discount on all products', 'Up to 80% discount on all products 222'],
            imageUrl:
                'https://s3.amazonaws.com/gallery.getshoutout.com/images/2757/22caf8ab-3031-43bf-8942-1c6849ea7a49.undefined',
            points: 100
        };
        return server
            .post('/')
            .set('Authorization', `Bearer ${token}`)
            .send(tier)
            .then((response) => {
                expect(response.statusCode).toBe(201);
                tierId = response.body._id;
            });
    }, 10000);

    it('/GET get tiers list should return 200', () => {
        const query = querystring.stringify(
            {
                skip: 0,
                limit: 100,
                regionId
            },
            { arrayFormat: 'bracket' }
        );
        return server
            .get(`/?${query}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/PUT update a tier should return 200', async () => {
        let tier = {
            name: 'Garnet',
            benefits: ['Up to 80% discount on all products updates'],
            imageUrl:
                'https://s3.amazonaws.com/gallery.getshoutout.com/images/2757/22caf8ab-3031-43bf-8942-1c6849ea7a49.undefined',
            points: 70
        };
        return server
            .put(`/${tierId}`)
            .set('Authorization', `Bearer ${token}`)
            .send(tier)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });

    it.skip('/DELETE delete a tier should return 200', async () => {
        return server
            .delete(`/${tierId}`)
            .set('Authorization', `Bearer ${token}`)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    });
});
