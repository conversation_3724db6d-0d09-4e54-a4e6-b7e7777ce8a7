'use strict';
const mongoose = require('mongoose');
const { regionId, organizationId, merchantLocationId, memberId, expiryPoints, adjustmentSubtractSubTransactionId } = require('../../.jest/constants');
const TransactionModelMongo = require('../../lib/db/models/transaction.model');
const { TYPE: transactionType } = require('../../lib/db/models/enums/transaction.enums');
const PostgreSQLConnector = require('../../lib/db/connectors/PostgreSQLConnector');
const TransactionStreamConsumer = require('../../workers/processors/transactions.stream.consumer');
const {models: { Transaction } } = PostgreSQLConnector;
const merchantId = process.env.MERCHANT_ID;

describe('Transaction Steam Consumer Tests', function() {
    const transactionIds = [];

    beforeAll(async () => {

    });

    afterAll(async () => {
        await TransactionModelMongo.deleteMany({
            _id: {
                $in: transactionIds,
            },
        });
    });

    it('on transaction received event should create a record in postgres successfully', async () => {
        const transactionModel = new TransactionModelMongo({
            organizationId,
            regionId,
            merchantId,
            merchantLocationId,
            memberId,
            transactionOn: new Date(),
            redeemablePoints: 10,
            transactionAmount: 10,
            type: transactionType.REDEMPTION,
            subType: adjustmentSubtractSubTransactionId,
            createdBy: mongoose.Types.ObjectId(),
            status: 'VALID',
        });
        const transaction = await transactionModel.save();
        const transactionId = transaction._id.toString();
        transactionIds.push(transaction._id);

        const stringifiedTransaction = JSON.stringify(transaction);

        await PostgreSQLConnector.initialize();
        TransactionStreamConsumer.TransactionModel = TransactionModelMongo;

        await TransactionStreamConsumer.processAndCreateTransaction({ partition: 1, message: { key: mongoose.Types.ObjectId(), value: stringifiedTransaction } });

        const retrievedTransaction = await Transaction.findOne({
            where: {
                transactionId
            }
        });
        console.log(retrievedTransaction);
        expect(retrievedTransaction).toBeInstanceOf(Object);
        expect(retrievedTransaction.isExpiryPoints).toBe(false);
    });

    it('when received transaction is a type of expiry points should set the isExpiryPoints attribute to true', async () => {
        const transactionModel = new TransactionModelMongo({
            organizationId,
            regionId,
            merchantId,
            merchantLocationId,
            memberId,
            transactionOn: new Date(),
            redeemablePoints: 10,
            transactionAmount: 10,
            type: transactionType.REDEMPTION,
            subType: expiryPoints,
            createdBy: mongoose.Types.ObjectId(),
            status: 'VALID',
        });
        const transaction = await transactionModel.save();
        const transactionId = transaction._id.toString();
        transactionIds.push(transaction._id);

        const stringifiedTransaction = JSON.stringify(transaction);

        await PostgreSQLConnector.initialize();
        TransactionStreamConsumer.TransactionModel = TransactionModelMongo;

        await TransactionStreamConsumer.processAndCreateTransaction({ partition: 1, message: { key: mongoose.Types.ObjectId(), value: stringifiedTransaction } });

        const retrievedTransaction = await Transaction.findOne({
            where: {
                transactionId
            }
        });
        console.log(retrievedTransaction);
        expect(retrievedTransaction).toBeInstanceOf(Object);
        expect(retrievedTransaction.isExpiryPoints).toBe(true);
    });

});
