'use strict';

const supertest = require('supertest');
const mongoose = require('mongoose');
const server = supertest.agent(`${process.env.API_HOST_URL}/points`);

const Utils = require('../../lib/Utils');
const secretConfig = require('../../config');

const Member = require('../../lib/db/models/member.model');
const Transaction = require('../../lib/db/models/transaction.model');
const { roundOffToTwoDecimals } = require('../../lib/utils/NumberUtils');
const {
    memberId,
    member2Id,
    secondaryMemberId,
    redeemPointsSubTransactionId: subTransRedeemPointsId,
    adjustmentAddSubTransactionId: subTransAdjustmentAddId,
    adjustmentSubtractSubTransactionId: subTransAdjustmentSubtractId,
    merchantId,
    merchantLocationId,
    organizationId
} = require('../../.jest/constants');

jest.setTimeout(50000);

describe('Points unit tests', () => {
    let token;

    beforeAll(async () => {
        token = global.accessToken;
        // await supertest.agent(`${process.env.API_HOST_URL}`)
        // .get(`/members/${memberId}`)
        // .set('Authorization', `Bearer ${token}`)
        // .then((response) => {
        //     member=response.body;
        // })
        // await Member.updateOne(
        //     {
        //         _id: new mongoose.Types.ObjectId(memberId)
        //     },
        //     {
        //         'affinityGroup.affinityGroupId': new mongoose.Types.ObjectId(),
        //         'tier.tierId': new mongoose.Types.ObjectId(tierId ),
        //         pointsToExpire: []
        //     }
        // );
        return Promise.resolve();
    });
    afterAll(async () => {
        await Transaction.deleteMany({ organizationId: new mongoose.Types.ObjectId(organizationId) });
        return true;
    });

    it('POINTS-TESTS: collect points bill', async () => {
        const payload = {
            memberId,
            merchantId,
            merchantLocationId,
            billAmount: 1250,
            transactionDate: new Date().toISOString(),
            invoiceData: {
                invoiceId: `INV-${new Date().getTime()}`
            }
        };

        return server
            .post('/collect/bill')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 100000);

    it('collect points manual without idempotent key should generate one', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId,
            merchantId,
            merchantLocationId,
            transactionDate: new Date().toISOString(),
            pointsAmount: 500
        };

        const response = await server.post('/collect/points').set('Authorization', `Bearer ${token}`).send(payload);

        /*
            Test Member point changes
        */
        expect(response.body.totalPoints).toBe(1000);

        const transaction = await Transaction.findOne({ _id: response.body.transactionId });

        expect(transaction.idempotentKey).toBeDefined();
        expect(transaction.idempotentKey.split('-')[0]).toBe('mls');

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it('collect points manual with idempotent key should use the provided idempotent key', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId,
            merchantId,
            merchantLocationId,
            transactionDate: new Date().toISOString(),
            pointsAmount: 500,
            idempotentKey: 'f90f4745c449e06a4a04eead694d56c4553ca0ca'
        };

        const response = await server.post('/collect/points').set('Authorization', `Bearer ${token}`).send(payload);

        console.log(response.body);

        expect(response.body.totalPoints).toBe(1000);

        const transaction = await Transaction.findOne({ _id: response.body.transactionId });

        expect(transaction.idempotentKey).toBe('f90f4745c449e06a4a04eead694d56c4553ca0ca');
    });

    it('collect points manual with duplicate idempotent key should return 400', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId,
            merchantId,
            merchantLocationId,
            transactionDate: new Date().toISOString(),
            pointsAmount: 500,
            idempotentKey: 'f90f4745c449e06a4a04eead694d56c4553ca0ca'
        };

        const response = await server.post('/collect/points').set('Authorization', `Bearer ${token}`).send(payload);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
    });

    it('collect points manual to a secondary member should add those points to the primary member and remove them from the secondary account', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 0
                }
            }
        );

        const payload = {
            memberId: secondaryMemberId,
            merchantId,
            merchantLocationId,
            transactionDate: new Date().toISOString(),
            pointsAmount: 500
        };

        const response = await server.post('/collect/points').set('Authorization', `Bearer ${token}`).send(payload);

        const primaryMember = await Member.findOne({ _id: memberId }).lean();
        const primaryMemberPoints = roundOffToTwoDecimals(primaryMember?.points ? Number(primaryMember?.points) : 0);
        expect(primaryMemberPoints).toBe(500);

        const secondaryMember = await Member.findOne({ _id: secondaryMemberId }).lean();
        const secondaryMemberPoints = roundOffToTwoDecimals(
            secondaryMember?.points ? Number(secondaryMember?.points) : 0
        );
        expect(secondaryMemberPoints).toBe(0);
    }, 10000);

    it('adjust points (ADD)', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 500,
            transactionDate: new Date().toISOString(),
            transactionSubTypeId: subTransAdjustmentAddId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/adjust').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.totalPoints).toBe(1000);

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it('adjust points (SUBTRACT)', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionDate: new Date().toISOString(),
            transactionSubTypeId: subTransAdjustmentSubtractId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/adjust').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.totalPoints).toBe(250);

        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });
    });

    it.skip('donate points', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        await Member.updateMany(
            {
                _id: mongoose.Types.ObjectId(member2Id)
            },
            {
                $set: {
                    type: 'CHARITY',
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            charityMemberId: member2Id,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/donate').set('Authorization', `Bearer ${token}`).send(payload);

        /*
            Test Member point changes
        */
        expect(response.statusCode).toBe(200);
        expect(response.body.totalPoints).toBe(250);
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transactions = await Transaction.find().sort({ createdOn: 'desc' }).limit(2).lean();
        expect(updatedMember.lastTransactionOn).toBeDefined();
        for (let transaction of transactions) {
            if (transaction.signedRedeemablePoints < 0) {
                expect(transaction.affinityGroupId?.toString()).toBe(
                    updatedMember.affinityGroup?.affinityGroupId?.toString()
                );
                expect(transaction.tierId?.toString()).toBe(updatedMember.tier?.tierId?.toString());
            }
        }
    });

    it('redeem points', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionSubTypeId: subTransRedeemPointsId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/redeem').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.balancePoints).toBe(250);

        /*
            Test Member point changes
        */
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it.skip('redeem points', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionSubTypeId: subTransRedeemPointsId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/redeem').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.balancePoints).toBe(250);

        /*
            Test Member point changes
        */
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it.skip('redeem with OTP', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionSubTypeId: subTransRedeemPointsId,
            notes: 'TEST NOTE'
        };

        const otpTokenResponse = await server
            .post('/redeemwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        expect(otpTokenResponse.statusCode).toBe(200);
        expect(otpTokenResponse.body.otpToken).toBeDefined();

        const { otpCode } = JSON.parse(Utils.decrypt(otpTokenResponse.body.otpToken, secretConfig.VERIFY_TOKEN_SECRET));

        const response = await server.post('/redeemwithotp').set('Authorization', `Bearer ${token}`).send({
            redemptionToken: otpTokenResponse.body.otpToken,
            otpCode
        });

        /*
            Test Member point changes
        */
        console.table(response.body);
        expect(response.statusCode).toBe(200);
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it('redeem with OTP and use the same OTP for second redemption should return an error', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionSubTypeId: subTransRedeemPointsId,
            notes: 'TEST NOTE'
        };

        const otpTokenResponse = await server
            .post('/redeemwithotprequest')
            .set('Authorization', `Bearer ${token}`)
            .send(payload);

        expect(otpTokenResponse.statusCode).toBe(200);
        expect(otpTokenResponse.body.otpToken).toBeDefined();

        const { otpCode } = JSON.parse(Utils.decrypt(otpTokenResponse.body.otpToken, secretConfig.VERIFY_TOKEN_SECRET));

        const response = await server.post('/redeemwithotp').set('Authorization', `Bearer ${token}`).send({
            redemptionToken: otpTokenResponse.body.otpToken,
            otpCode
        });

        const response2 = await server.post('/redeemwithotp').set('Authorization', `Bearer ${token}`).send({
            redemptionToken: otpTokenResponse.body.otpToken,
            otpCode
        });

        /*
            Test Member point changes
        */
        console.table(response2.body);
        expect(response2.statusCode).toBe(400);
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it('redeem points with negative endpoint', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionSubTypeId: subTransRedeemPointsId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/negative/redeem').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.balancePoints).toBe(250);

        /*
            Test Member point changes
        */
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it.skip('redeem points with negative endpoint while member has a negative points amount', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: -250
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionSubTypeId: subTransRedeemPointsId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/negative/redeem').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.balancePoints).toBe(-500);

        /*
            Test Member point changes
        */
        const updatedMember = await Member.findOne({
            _id: mongoose.Types.ObjectId(memberId)
        });

        /*
            Analytics requirements
            Test if the transaction includes the affinityGroupId, tierId, newMember attributes
        */
        const transaction = await Transaction.findOne({ _id: response.body.transactionId });
        expect(updatedMember.lastTransactionOn).toBeDefined();
        expect(transaction.affinityGroupId.toString()).toBe(updatedMember.affinityGroup?.affinityGroupId?.toString());
        expect(transaction.tierId.toString()).toBe(updatedMember.tier.tierId.toString());
    });

    it.skip('adjust points (SUBTRACT) using negative endpoint', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: 500
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionDate: new Date().toISOString(),
            transactionSubTypeId: subTransAdjustmentSubtractId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/negative/adjust').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.totalPoints).toBe(250);
    });

    it.skip('adjust points (SUBTRACT) using negative endpoint while member has a negative points amount', async () => {
        await Member.updateOne(
            {
                _id: mongoose.Types.ObjectId(memberId)
            },
            {
                $set: {
                    points: -250
                }
            }
        );

        const payload = {
            memberId: memberId,
            merchantId,
            merchantLocationId,
            pointsAmount: 250,
            transactionDate: new Date().toISOString(),
            transactionSubTypeId: subTransAdjustmentSubtractId,
            notes: 'TEST NOTE'
        };

        const response = await server.post('/negative/adjust').set('Authorization', `Bearer ${token}`).send(payload);

        expect(response.statusCode).toBe(200);
        expect(response.body.totalPoints).toBe(-500);
    });

    it('POINTS-TESTS: collect points bill with product items', async () => {
        const payload = {
            memberId,
            merchantId,
            merchantLocationId,
            billAmount: 3000,
            transactionDate: new Date().toISOString(),
            productItems: [
                {
                    productId: 'PD1234',
                    productName: 'Milk Powder',
                    quantity: 3,
                    amount: 1500
                },
                {
                    productId: 'PDTR3343',
                    productName: 'Dark Chocolate',
                    productCategory: ['Cooking', 'Sweets and Desserts'],
                    quantity: 1,
                    amount: 500
                },
                {
                    productId: 'PDRR4443',
                    productName: 'Dishwash Soap',
                    productCategory: ['Kitchen Products', 'Cleaning'],
                    quantity: 3,
                    amount: 1000
                }
            ],
            invoiceData: {
                invoiceId: `INV-${new Date().getTime()}`
            }
        };

        return server
            .post('/collect/bill')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(200);
            });
    }, 100000);

    it('POINTS-TESTS: collect points bill with product items should fail with productItems[1].productId is required', async () => {
        const payload = {
            memberId,
            merchantId,
            merchantLocationId,
            billAmount: 3000,
            transactionDate: new Date().toISOString(),
            productItems: [
                {
                    productId: 'PD1234',
                    productName: 'Milk Powder',
                    productCategory: ['Cooking', 'Essentials'],
                    quantity: 3,
                    amount: 1500
                },
                {
                    productName: 'Dark Chocolate',
                    productCategory: ['Cooking', 'Sweets and Desserts'],
                    quantity: 1,
                    amount: 500
                },
                {
                    productId: 'PDRR4443',
                    productName: 'Dishwash Soap',
                    productCategory: ['Kitchen Products', 'Cleaning'],
                    quantity: 3,
                    amount: 1000
                }
            ],
            invoiceData: {
                invoiceId: `INV-${new Date().getTime()}`
            }
        };

        return server
            .post('/collect/bill')
            .set('Authorization', `Bearer ${token}`)
            .send(payload)
            .then((response) => {
                expect(response.statusCode).toBe(400);
            });
    }, 100000);

    // *** Custom error codes testing ***

    it('collect points manual with a non-existing card number should return 400 with the error code "100105"', async () => {
        await Member.updateOne({ _id: mongoose.Types.ObjectId(memberId) }, { $set: { points: 500 } });

        const payload = {
            cardNo: '9999999999999999',
            merchantId,
            merchantLocationId,
            transactionDate: new Date().toISOString(),
            pointsAmount: 500
        };

        const response = await server.post('/collect/points').set('Authorization', `Bearer ${token}`).send(payload);

        console.log(response.body);
        expect(response.statusCode).toBe(400);
        expect(response.body?.errorCode).toBe('100105');
    });
});
