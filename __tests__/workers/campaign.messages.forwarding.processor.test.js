const CampaignMessagesForwardingProcessorMq = require('../../workers/processors/campaign.messages.forwarding.processor.mq');
const QueuedCampaign = require('../../lib/db/models/queued.campaign.job.model');
const Bull = require('bull');
const mongoose = require('mongoose');

jest.mock('bullmq');
jest.mock('bull');

const organizationId = process.env.ORGANIZATION_ID;
const regionId = process.env.REGION_ID;
const campaignId = '66b336c662e8530631b2195d';

describe('CampaignMessagesForwardingProcessor', () => {
    beforeAll(async () => {
        await QueuedCampaign({
            organizationId,
            regionId,
            memberId: '667d3cd9c00bf90329f4586d',
            campaignId,
            message: {
                messageBody: 'Hello {name}'
            },
            to: '<EMAIL>',
            senderId: 'sender1'
        }).save();

        await QueuedCampaign({
            organizationId,
            regionId,
            memberId: '667aa5f397c358d3479f15ae',
            campaignId,
            message: {
                messageBody: 'Hello {name}'
            },
            to: '<EMAIL>',
            senderId: 'sender1'
        }).save();
    });

    afterAll(async () => {
        await QueuedCampaign.deleteMany({
            campaignId
        });
    });

    test('should run job and add the messages to the campaigns queue', async () => {
        // Act
        await CampaignMessagesForwardingProcessorMq.runJob({
            data: {
                campaignId,
                transport: 'EMAIL'
            }
        });

        expect(Bull.prototype.add).toHaveBeenNthCalledWith(1, {
            campaignId: mongoose.Types.ObjectId(campaignId),
            memberId: mongoose.Types.ObjectId('667d3cd9c00bf90329f4586d'),
            message: 'Hello {name}',
            organizationId: mongoose.Types.ObjectId(organizationId),
            regionId: mongoose.Types.ObjectId(regionId),
            senderId: 'sender1',
            subject: undefined,
            to: '<EMAIL>'
        });

        expect(Bull.prototype.add).toHaveBeenNthCalledWith(2, {
            campaignId: mongoose.Types.ObjectId(campaignId),
            memberId: mongoose.Types.ObjectId('667aa5f397c358d3479f15ae'),
            message: 'Hello {name}',
            organizationId: mongoose.Types.ObjectId(organizationId),
            regionId: mongoose.Types.ObjectId(regionId),
            senderId: 'sender1',
            subject: undefined,
            to: '<EMAIL>'
        });
    });
});
