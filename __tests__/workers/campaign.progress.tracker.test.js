const CampaignProgressTrackerMq = require('../../workers/processors/campaign.progress.tracker.mq');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const CampaignsDAO = require('../../lib/db/dao/CampaignsDAO');
const Campaign = require('../../lib/db/models/campaign.model');
const moment = require('moment');
const campaignProgressTrackerQueue = require('../../workers/processors/campaign.progress.tracker.mq').getQueue();

const organizationId = process.env.ORGANIZATION_ID;
const regionId = process.env.REGION_ID;
const userId = process.env.USER_ID;

describe('Campaign Progress Tracker', () => {
    let campaignId, progressJobId;
    const campaignIds = [];

    beforeEach(async () => {
        const campaign = await CampaignsDAO.createCampaign(
            {
                regionId,
                name: 'Campaign Test 101',
                description: 'Campaign Test 101',
                segmentFilters: [],
                channel: 'EMAIL',
                type: 'PROMOTIONAL',
                senderId: 'senderId',
                message: {
                    messageBody: 'Welcome {name}',
                    messageSubject: 'Welcome {name}'
                }
            },
            organizationId,
            userId
        );
        campaignIds.push(campaign._id.toString());
        campaignId = campaign._id.toString();
        progressJobId = `${organizationId}:${regionId}:${campaignId}`;
    });

    afterEach(async () => {
        await RedisConnector.scanAndDelete('bull:campaign_progress_tracker_queue:*');
    });

    afterAll(async () => {
        await Campaign.deleteMany({
            _id: { $in: campaignIds }
        });
    });

    test(
        'should run the progress tracker job and,' +
            'add only the initial execution attribute when ran with no difference in the job data,' +
            'and job should be moved to delayed jobs',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 0,
                    successCount: 0,
                    failedCount: 0
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 0,
                successCount: 0,
                failedCount: 0,
                initialExecution: progressTrackingJob.data.initialExecution
            });
            expect(movedToDelayed).toEqual(true);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign as FINISHED' +
            ' 2. add the endOn attribute to the campaign' +
            ' 3. progress tracker job should be completed' +
            ' When no response was sent from the message in the last 24 hours',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 0,
                    successCount: 0,
                    failedCount: 0,
                    initialExecution: moment().subtract(25, 'hours')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 0,
                successCount: 0,
                failedCount: 0,
                initialExecution: progressTrackingJob.data.initialExecution
            });
            expect(movedToDelayed).toEqual(false);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('FINISHED');
            expect(campaign.endOn).toBeDefined();
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign success count' +
            ' 2. update the campaign status to RUNNING' +
            ' 3. progress tracker job should be moved to the delayed jobs' +
            ' When success count has been updated in the progress tracker',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 1,
                    failedCount: 0,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(15, 'minutes')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 1,
                failedCount: 0,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(true);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('RUNNING');
            expect(campaign.successCount).toEqual(1);
            expect(campaign.failedCount).toEqual(0);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign failed count' +
            ' 2. update the campaign status to RUNNING' +
            ' 3. progress tracker job should be moved to the delayed jobs' +
            ' When failed count has been updated in the progress tracker',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 0,
                    failedCount: 1,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(15, 'minutes')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 0,
                failedCount: 1,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(true);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('RUNNING');
            expect(campaign.failedCount).toEqual(1);
            expect(campaign.successCount).toEqual(0);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign success count' +
            ' 2. update the campaign failed count' +
            ' 3. update the campaign status to RUNNING' +
            ' 4. progress tracker job should be moved to the delayed jobs' +
            ' When success and failed counts has been updated in the progress tracker',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 1,
                    failedCount: 1,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(15, 'minutes')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 1,
                failedCount: 1,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(true);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('RUNNING');
            expect(campaign.failedCount).toEqual(1);
            expect(campaign.successCount).toEqual(1);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign status to FINISHED' +
            ' 2. progress tracker job should be completed' +
            ' 3. add the endOn attribute to the campaign' +
            ' 4. progress tracker job should be completed' +
            ' When no response was sent from the message service in the last 24 hours and success count and failed counts has been updated in the progress tracker',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 1,
                    failedCount: 1,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(25, 'hours')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 1,
                failedCount: 1,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(false);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('FINISHED');
            expect(campaign.endOn).toBeDefined();
            expect(campaign.failedCount).toEqual(1);
            expect(campaign.successCount).toEqual(1);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign success count' +
            ' 2. update the campaign status to FINISHED' +
            ' 3. progress tracker job should be completed' +
            ' When success count is equal to the totalMessagesCount',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 5,
                    failedCount: 0,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(15, 'minutes')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 5,
                failedCount: 0,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(false);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('FINISHED');
            expect(campaign.successCount).toEqual(5);
            expect(campaign.failedCount).toEqual(0);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign failed count' +
            ' 2. update the campaign status to FINISHED' +
            ' 3. progress tracker job should be completed' +
            ' When failed count is equal to the totalMessagesCount',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 0,
                    failedCount: 5,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(15, 'minutes')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 0,
                failedCount: 5,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(false);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('FINISHED');
            expect(campaign.successCount).toEqual(0);
            expect(campaign.failedCount).toEqual(5);
        }
    );

    test(
        'should run the job and,' +
            ' 1. update the campaign success count' +
            ' 2. update the campaign failed count' +
            ' 3. update the campaign status to FINISHED' +
            ' 4. progress tracker job should be completed' +
            ' When total of the success and failed counts are equal to the totalMessagesCount',
        async () => {
            let movedToDelayed = false;
            await campaignProgressTrackerQueue.add(
                'EMAIL',
                {
                    organizationId,
                    campaignId,
                    totalMessagesCount: 5,
                    successCount: 3,
                    failedCount: 2,
                    initialExecution: moment().subtract(15, 'minutes'),
                    lastResponseOn: moment().subtract(15, 'minutes')
                },
                {
                    jobId: progressJobId
                }
            );

            const progressTrackingJob = await campaignProgressTrackerQueue.getJob(progressJobId);
            progressTrackingJob.moveToDelayed = () => {
                movedToDelayed = true;
            };

            // Act
            await CampaignProgressTrackerMq.runJob(progressTrackingJob);

            expect(progressTrackingJob.data.initialExecution).toBeDefined();
            expect(progressTrackingJob.data).toEqual({
                organizationId,
                campaignId,
                totalMessagesCount: 5,
                successCount: 3,
                failedCount: 2,
                initialExecution: progressTrackingJob.data.initialExecution,
                lastResponseOn: progressTrackingJob.data.lastResponseOn
            });
            expect(movedToDelayed).toEqual(false);

            const campaign = await Campaign.findOne({ _id: campaignId });
            expect(campaign.status).toEqual('FINISHED');
            expect(campaign.successCount).toEqual(3);
            expect(campaign.failedCount).toEqual(2);
        }
    );
});
