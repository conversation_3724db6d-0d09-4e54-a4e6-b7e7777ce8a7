const CampaignEmailJobProcessor = require('../../workers/processors/campaign.email.job.processor.mq');
const CampaignsDAO = require('../../lib/db/dao/CampaignsDAO');
const LoyaltyService = require('../../lib/services/LoyaltyService');
const Campaign = require('../../lib/db/models/campaign.model');
const QueuedCampaign = require('../../lib/db/models/queued.campaign.job.model');
const bullMq = require('bullmq');

jest.mock('../../lib/services/LoyaltyService');
jest.mock('bullmq');
jest.mock('bull');

const organizationId = process.env.ORGANIZATION_ID;
const regionId = process.env.REGION_ID;
const userId = process.env.USER_ID;

describe('CampaignEmailJobProcessor', () => {
    let job,
        processor,
        currentCampaignId,
        campaignIds = [];

    afterAll(async () => {
        await Promise.all([
            Campaign.deleteMany({
                _id: { $in: campaignIds }
            }),
            QueuedCampaign.deleteMany({
                currentCampaignId: {
                    $in: campaignIds
                }
            })
        ]);
    });

    beforeEach(async () => {
        const campaign = await CampaignsDAO.createCampaign(
            {
                regionId,
                name: 'Campaign Test 101',
                description: 'Campaign Test 101',
                segmentFilters: [],
                channel: 'SMS',
                type: 'PROMOTIONAL',
                senderId: 'senderId',
                message: {
                    messageBody: 'Welcome {name}'
                }
            },
            organizationId,
            userId
        );
        currentCampaignId = campaign._id.toString();
        campaignIds.push(currentCampaignId);

        job = {
            id: 'job1',
            data: {
                organizationId,
                regionId,
                campaignId: currentCampaignId,
                segmentFilters: ['{"$or":[{"firstName":"Test"},{"firstName":"Test"}]}'],
                message: { messageBody: 'Hello {name}', messageSubject: 'Welcome {name}' },
                senderId: 'sender1'
            },
            log: jest.fn(),
            updateData: jest.fn()
        };

        processor = new CampaignEmailJobProcessor(job);
    });

    test('should run job and update the job status to RUNNING, campaign total message count to be 2, creat messages in queues messages collection and add the campaign id to the forwarding queue', async () => {
        // Mock other dependencies
        LoyaltyService.getMembersInSegment = jest.fn().mockResolvedValue({
            items: [
                {
                    _id: '667aa5f397c358d3479f15ae',
                    loyaltyId: '0005iu7373',
                    points: 1111,
                    lastSeenOn: '2024-08-01T17:15:43.217Z',
                    firstName: 'Staging',
                    lastName: 'TESTING MEMBER ANALYTICS',
                    mobileNumber: '94777777777',
                    email: '<EMAIL>',
                    cardNumber: '49100002154',
                    tierPoints: 0
                },
                {
                    _id: '667d3cd9c00bf90329f4586d',
                    loyaltyId: '00072mej2q',
                    points: 1150,
                    lastSeenOn: '2024-07-22T09:45:38.630Z',
                    firstName: 'Staging',
                    lastName: 'TESTING MEMBER ANALYTICS',
                    mobileNumber: '94777777777',
                    email: '<EMAIL>',
                    cardNumber: '49100002146',
                    tierPoints: 0
                }
            ],
            total: 0
        });

        // Act
        await processor.run('sms');

        // Assertions
        const campaign = await Campaign.findById(currentCampaignId);
        expect(campaign.status).toEqual('RUNNING');
        expect(campaign.totalMessages).toEqual(2);

        const queuedCampaigns = await QueuedCampaign.find({
            campaignId: currentCampaignId
        });
        expect(queuedCampaigns.length).toEqual(2);

        expect(bullMq.Queue.prototype.add).toHaveBeenCalledWith(
            '',
            { campaignId: currentCampaignId, transport: 'sms' },
            { jobId: currentCampaignId }
        );
    });

    test('should run job and run the campaign as a normal campaign and ignore the duplicate messages when members list contains duplicate members', async () => {
        // Mock other dependencies
        LoyaltyService.getMembersInSegment = jest.fn().mockResolvedValue({
            items: [
                {
                    _id: '667aa5f397c358d3479f15ae',
                    loyaltyId: '0005iu7373',
                    points: 1111,
                    lastSeenOn: '2024-08-01T17:15:43.217Z',
                    firstName: 'Staging',
                    lastName: 'TESTING MEMBER ANALYTICS',
                    mobileNumber: '94777777777',
                    email: '<EMAIL>',
                    cardNumber: '49100002154',
                    tierPoints: 0
                },
                {
                    _id: '667d3cd9c00bf90329f4586d',
                    loyaltyId: '00072mej2q',
                    points: 1150,
                    lastSeenOn: '2024-07-22T09:45:38.630Z',
                    firstName: 'Staging',
                    lastName: 'TESTING MEMBER ANALYTICS',
                    mobileNumber: '94777777777',
                    email: '<EMAIL>',
                    cardNumber: '49100002146',
                    tierPoints: 0
                },
                {
                    _id: '667d3cd9c00bf90329f4586d',
                    loyaltyId: '00072mej2q',
                    points: 1150,
                    lastSeenOn: '2024-07-22T09:45:38.630Z',
                    firstName: 'Staging',
                    lastName: 'TESTING MEMBER ANALYTICS',
                    mobileNumber: '94777777777',
                    email: '<EMAIL>',
                    cardNumber: '49100002146',
                    tierPoints: 0
                }
            ],
            total: 0
        });

        // Act
        await processor.run('sms');

        // Assertions
        const campaign = await Campaign.findById(currentCampaignId);
        expect(campaign.status).toEqual('RUNNING');
        expect(campaign.totalMessages).toEqual(2);

        const queuedCampaigns = await QueuedCampaign.find({
            campaignId: currentCampaignId
        });
        expect(queuedCampaigns.length).toEqual(2);

        expect(bullMq.Queue.prototype.add).toHaveBeenCalledWith(
            '',
            { campaignId: currentCampaignId, transport: 'sms' },
            { jobId: currentCampaignId }
        );
    });
});
