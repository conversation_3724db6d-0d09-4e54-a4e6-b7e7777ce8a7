'use strict';
const path = require('path');
const createError = require('http-errors');
const express = require('express');
const cookieParser = require('cookie-parser');
const cors = require('cors');
const authorizerPortal = require('./lib/middlewares/portal.authorizer.middleware');

const logger = require('./lib/logger');
const config = require('./lib/config');
const secretConfig = require('./config');
const indexRouter = require('./routes/index');
const passport = require('passport');
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const swaggerOptions = require('./lib/swagger').getOptions();
const swaggerPortalOptions = require('./lib/swagger').getPortalOptions();
const swaggerSystemOptions = require('./lib/swagger').getSystemOptions();
const analyticsOptions = require('./lib/swagger').getAnalyticsOptions();

const specs = swaggerJsdoc(swaggerOptions);
const portalSpecs = swaggerJsdoc(swaggerPortalOptions);
const systemSpecs = swaggerJsdoc(swaggerSystemOptions);
const analyticsSpecs = swaggerJsdoc(analyticsOptions);
const log = logger(config.logger);

const MongooseConnector = require('./lib/db/connectors/MongooseConnector');
const KafkaConnector = require('./lib/db/connectors/KafkaConnector');
const PostgreSQLConnector = require('./lib/db/connectors/PostgreSQLConnector');

/*require('@shoutout-labs/passport-jwt-authorizer')(passport, {
    secret: secretConfig.JWT_SECRET,
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
});*/
require('@shoutout-labs/passport-jwt-openid-authorizer')(passport, {
    certUrl: secretConfig.OPENID_ADMIN_JWKS_URI,
    secretKey: secretConfig.JWT_SECRET,
    issuer: secretConfig.OPENID_ADMIN_ISSUER,
    //audience: config.openid.audience,
    strategyId: 'jwt',
    authZRolePrefix: 'loyalty_'
});
require('@shoutout-labs/passport-jwt-oid-authz')(passport, {
    certUrl: secretConfig.OPENID_ADMIN_JWKS_URI,
    secretKey: secretConfig.JWT_SECRET,
    issuer: secretConfig.OPENID_ADMIN_ISSUER,
    //audience: config.openid.audience,
    strategyId: 'jwt-ability'
});
const ConfigsDAO = require('./lib/db/dao/ConfigsDAO');
const { default: helmet } = require('helmet');
const http = require('http');

const handlerFunc = async (originUrl) => {
    const config = await ConfigsDAO.getConfigByOriginURL(originUrl);
    if (!config) {
        throw new Error('config not found');
    }
    return config.portal.certUrl;
};

require('@shoutout-labs/passport-jwt-openid-authorizer')(passport, {
    //certUrl: config.openid.portal.jwks_uri,
    handlerFunc,
    secretKey: secretConfig.JWT_SECRET,
    //issuer: config.openid.issuer,
    //audience: config.openid.audience,
    strategyId: 'jwt-portal',
    authZRolePrefix: 'portal_',
    strategyFunc: (jwtPayload, done) => {
        done(null, {
            id: jwtPayload['sub'],
            contactId: jwtPayload['contact_id'],
            ownerId: jwtPayload['owner_id'],
            name: jwtPayload['name'],
            email: jwtPayload['email']
        });
    }
});

const authorizer = passport.authenticate('jwt', { session: false });
const authorizerAbility = passport.authenticate('jwt-ability', { session: false });
// const authorizerPortal = passport.authenticate('jwt-portal', { session: false });

const BASE_PATH = config.api.base_path;

const app = express();
const server = http.createServer(app);
const expressWs = require('express-ws')(app, server, { leaveRouterUntouched: true });

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'pug');

app.use(cors({ exposedHeaders: ['x-skip,x-limit,x-total'] }));
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: false, limit: '5mb' }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.disable('x-powered-by');
app.use(helmet());

app.use(`${BASE_PATH}/`, indexRouter);
app.use(`${BASE_PATH}/docs`, swaggerUi.serve, (...args) => swaggerUi.setup(specs)(...args));
app.use(`${BASE_PATH}/docs-portal`, swaggerUi.serve, (...args) => swaggerUi.setup(portalSpecs)(...args));

app.use(`${BASE_PATH}/docs-system`, swaggerUi.serve, (...args) => swaggerUi.setup(systemSpecs)(...args));

app.use(`${BASE_PATH}/docs-analytics`, swaggerUi.serve, (...args) => swaggerUi.setup(analyticsSpecs)(...args));

//FOR LOGGING ALL INCOMING REQUESTS
app.use(function (req, res, next) {
    log.info('REQUEST: ', {
        method: req.method,
        url: req.headers.host + req.originalUrl,
        origin: req.get('origin') || req.get('Origin'),
        body: JSON.stringify(req.body),
        queryParams: JSON.stringify(req.query)
    });
    next();
});

app.use(passport.initialize());

app.use(`${BASE_PATH}/announcements`, require('./routes/announcements')(authorizer));
app.use(`${BASE_PATH}/configs`, require('./routes/configs')(authorizer));
app.use(`${BASE_PATH}/pointrules`, require('./routes/pointRules')(authorizerAbility));
app.use(`${BASE_PATH}/redemptionlogs`, require('./routes/redemptionLogs')(authorizerAbility));
app.use(`${BASE_PATH}/rewardredeem`, require('./routes/rewardRedeem')(authorizerAbility));
app.use(`${BASE_PATH}/rewards`, require('./routes/rewards')(authorizerAbility));
app.use(`${BASE_PATH}/tiers`, require('./routes/tiers')(authorizerAbility));
app.use(`${BASE_PATH}/transactions`, require('./routes/transactions')(authorizerAbility));
app.use(`${BASE_PATH}/members`, require('./routes/members')(authorizerAbility));
app.use(`${BASE_PATH}/charities`, require('./routes/charities')(authorizerAbility));
app.use(`${BASE_PATH}/organizations`, require('./routes/organizations')(authorizerAbility));
app.use(`${BASE_PATH}/membernotes`, require('./routes/memberNotes')(authorizerAbility));
app.use(`${BASE_PATH}/cards`, require('./routes/cards')(authorizerAbility));
app.use(`${BASE_PATH}/cardprintjobs`, require('./routes/cardPrintJobs')(authorizerAbility));
app.use(`${BASE_PATH}/cardconfigurations`, require('./routes/cardConfigurations')(authorizerAbility));
app.use(`${BASE_PATH}/merchants`, require('./routes/merchants')(authorizerAbility));
app.use(`${BASE_PATH}/locations`, require('./routes/merchantLocations')(authorizerAbility));
app.use(`${BASE_PATH}/affinitygroups`, require('./routes/affinityGroupMemberImportJobs')(authorizerAbility));
app.use(`${BASE_PATH}/affinitygroups`, require('./routes/affinityGroups')(authorizerAbility));
app.use(`${BASE_PATH}/stagedtransactions`, require('./routes/stagedTransactions')(authorizerAbility));
app.use(`${BASE_PATH}/subtransactiontypes`, require('./routes/subTransactionTypes')(authorizerAbility));
app.use(`${BASE_PATH}/transactionimportjobs`, require('./routes/transactionImportJobs')(authorizerAbility));
app.use(`${BASE_PATH}/regions`, require('./routes/regions')(authorizerAbility));
app.use(`${BASE_PATH}/rewardtopups`, require('./routes/rewardTopUps')(authorizerAbility));
app.use(`${BASE_PATH}/rewardclaims`, require('./routes/rewardClaims')(authorizerAbility));
app.use(`${BASE_PATH}/rewarddistributionjobs`, require('./routes/rewardDistributionJobs')(authorizerAbility));
app.use(`${BASE_PATH}/imageuploads`, require('./routes/imageUploads')(authorizerAbility));
app.use(`${BASE_PATH}/carddistributionjobs`, require('./routes/cardDistributionJobs')(authorizerAbility));
app.use(`${BASE_PATH}/jobtypes`, require('./routes/jobTypes')(authorizerAbility));
app.use(`${BASE_PATH}/jobs`, require('./routes/jobs')(authorizerAbility));
app.use(`${BASE_PATH}/jobexecutions`, require('./routes/jobExecutions')(authorizerAbility));
app.use(`${BASE_PATH}/klip`, require('./routes/klip')(authorizerAbility));
app.use(`${BASE_PATH}/transactionimportretryjobs`, require('./routes/transactionImportRetryJobs')(authorizerAbility));
app.use(`${BASE_PATH}/segments`, require('./routes/segments')(authorizerAbility));
app.use(`${BASE_PATH}/segmentcategories`, require('./routes/segmentCategories')(authorizerAbility));
app.use(`${BASE_PATH}/attributes`, require('./routes/attributes')(authorizerAbility));
app.use(`${BASE_PATH}/productcatalogues`, require('./routes/productCatalogue')(authorizerAbility));

//portal service
app.use(`${BASE_PATH}/portal/users`, require('./routes/portal/users')(authorizerPortal));
app.use(`${BASE_PATH}/portal/configs`, require('./routes/portal/configs')(authorizerPortal));
app.use(`${BASE_PATH}/portal/announcements`, require('./routes/portal/announcements')(authorizerPortal));
app.use(`${BASE_PATH}/portal/rewards`, require('./routes/portal/rewards')(authorizerPortal));
app.use(`${BASE_PATH}/portal/rewardredeem`, require('./routes/portal/rewardRedeem')(authorizerPortal));
app.use(`${BASE_PATH}/portal/transactions`, require('./routes/portal/transactions')(authorizerPortal));
app.use(`${BASE_PATH}/portal/redemptionlogs`, require('./routes/portal/userRewards')(authorizerPortal));
app.use(`${BASE_PATH}/portal/tiers`, require('./routes/portal/tiers')(authorizerPortal));
app.use(`${BASE_PATH}/portal/pointrules`, require('./routes/portal/pointRules')(authorizerPortal));
app.use(`${BASE_PATH}/portal/accounts`, require('./routes/portal/accounts')(authorizerPortal));
app.use(`${BASE_PATH}/portal/merchants`, require('./routes/portal/merchants')(authorizerPortal));
app.use(`${BASE_PATH}/portal/regions`, require('./routes/portal/regions')(authorizerPortal));
app.use(`${BASE_PATH}/portal/locations`, require('./routes/portal/merchantLocations')(authorizerPortal));
app.use(`${BASE_PATH}/portal/organizations`, require('./routes/portal/organizations')(authorizerPortal));
app.use(`${BASE_PATH}/portal/charities`, require('./routes/portal/charities')(authorizerPortal));
app.use(`${BASE_PATH}/portal/points`, require('./routes/portal/points')(authorizerPortal));
app.use(`${BASE_PATH}/portal/members`, require('./routes/portal/members')(authorizerPortal));
app.use(`${BASE_PATH}/portal/imageuploads`, require('./routes/portal/imageUploads')(authorizerPortal));
app.use(`${BASE_PATH}/portal/publicdata`, require('./routes/portal/publicData')(authorizerPortal));

//system endpoints
app.use(`${BASE_PATH}/system/merchants`, require('./routes/system/merchants')(authorizer));
app.use(`${BASE_PATH}/system/locations`, require('./routes/system/merchantLocations')(authorizer));
app.use(`${BASE_PATH}/system/members`, (require('./routes/system/members')(authorizer, expressWs)));
app.use(`${BASE_PATH}/system/regions`, require('./routes/system/regions')(authorizer));
app.use(`${BASE_PATH}/system/organizations`, require('./routes/system/organizations')(authorizer));
app.use(`${BASE_PATH}/system/cards`, require('./routes/system/cards')(authorizer));
app.use(`${BASE_PATH}/system/event`, require('./routes/system/events')(authorizer));
app.use(`${BASE_PATH}/system/segments`, require('./routes/system/segments')(authorizer));

//analytics endpoints
app.use(`${BASE_PATH}/analytics/members`, require('./routes/analytics/members')(authorizerAbility));
app.use(`${BASE_PATH}/analytics/rewards`, require('./routes/analytics/rewards')(authorizerAbility));
app.use(`${BASE_PATH}/analytics/merchants`, require('./routes/analytics/merchants')(authorizerAbility));
app.use(`${BASE_PATH}/analytics/cards`, require('./routes/analytics/cards')(authorizerAbility));
app.use(`${BASE_PATH}/analytics/tiers`, require('./routes/analytics/tiers')(authorizerAbility));
app.use(`${BASE_PATH}/analytics/points`, require('./routes/analytics/points')(authorizerAbility));

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    next(createError(404));
});

//AUDIT LOGGER
app.use(require('@shoutout-labs/audit-logger')('LOYALTY'));

app.use((err, req, res, next) => {
    if (err.err) err = err.err;
    log.error(err);

    let status,
        message,
        errorCode = err?.errorCode || null;

    if (err?.name === 'ForbiddenError') {
        status = 403;
        message = err.message;
        errorCode = '000403';
    } else {
        status = err.status ? Number(err.status) : 500;
        message = err.status ? err.message : 'something went wrong';

        if (!errorCode && !err.status) errorCode = '000500';
    }
    res.status(status).json({ error: message, ...(errorCode ? { errorCode } : {}) });
});

module.exports = async () => {
    try {
        await Promise.all([
            MongooseConnector.initialize(),
            PostgreSQLConnector.initialize(),
            KafkaConnector.initializeProducer()
        ]);
        return { app, server };
    } catch (e) {
        log.error(e);
        return Promise.reject(e);
    }
};
