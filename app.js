'use strict';
const path = require('path');
const createError = require('http-errors');
const express = require('express');
const cookieParser = require('cookie-parser');
const cors = require('cors');
const authorizerPortal = require('./lib/middlewares/portal.authorizer.middleware');

const logger = require('./lib/logger');
const config = require('./lib/config');
const secretConfig = require('./config');
const indexRouter = require('./routes/index');
const passport = require('passport');
const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");
const swaggerOptions = require('./lib/swagger').getOptions();

const specs = swaggerJsdoc(swaggerOptions);
const log = logger(config.logger);

const MongooseConnector = require('./lib/db/connectors/MongooseConnector');

require('@shoutout-labs/passport-jwt-oid-authz')(passport, {
    certUrl: secretConfig.OPENID_ADMIN_JWKS_URI,
    secretKey: secretConfig.JWT_SECRET,
    issuer: secretConfig.OPENID_ADMIN_ISSUER,
    //audience: config.openid.audience,
    strategyId: 'jwt-ability'
});

const authorizerAbility = passport.authenticate('jwt-ability', { session: false });

const BASE_PATH = config.api.base_path;

const app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'pug');

app.use(cors({ exposedHeaders: ['x-skip,x-limit,x-total'] }));
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: false, limit: '5mb' }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.disable('x-powered-by');

app.use(`${BASE_PATH}/`, indexRouter);
app.use(
    `${BASE_PATH}/docs`,
    swaggerUi.serve,
    (...args) => swaggerUi.setup(specs)(...args)
)

//FOR LOGGING ALL INCOMING REQUESTS
app.use(function(req, res, next) {
    log.info('REQUEST: ', {
        method: req.method,
        url: req.headers.host + req.originalUrl,
        origin: req.get('origin') || req.get('Origin'),
        body: JSON.stringify(req.body),
        queryParams: JSON.stringify(req.query),
    });
    next();
});

app.use(passport.initialize());

app.use(`${BASE_PATH}/campaigns`, require('./routes/campaigns')(authorizerAbility, authorizerPortal));

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    next(createError(404));
});

//AUDIT LOGGER
app.use(require('@shoutout-labs/audit-logger')('CAMPAIGN'));

// error handler
app.use(function (err, req, res, next) {
    log.error(err);
    let status, message;
    if (err.name === 'ForbiddenError') {
        status = 403;
        message = err.message;
    } else {
        status = err.status ? Number(err.status) : 500;
        message = err.status ? err.message : 'something went wrong';
    }
    res.status(status).send({ error: message });
});

module.exports = new Promise(async (resolve, reject) => {
    try {
        await Promise.all([
            MongooseConnector.initialize()
        ]);

        return resolve(app);
    } catch (e) {
        log.error(e);
        return reject(e);
    }
});
