'use strict';
const path = require('path');
const createError = require('http-errors');
const express = require('express');
const cookieParser = require('cookie-parser');
const cors = require('cors');
require('./lib/db/models');

const logger = require('./lib/logger');
const config = require('./lib/config');
const secretConfig = require('./config');
const indexRouter = require('./routes/index');
const passport = require('passport');
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const swaggerOptions = require('./lib/swagger').getOptions();
const swaggerPortalOptions = require('./lib/swagger').getPortalOptions();
const swaggerSystemOptions = require('./lib/swagger').getSystemOptions();
const analyticsOptions = require('./lib/swagger').getAnalyticsOptions();

const specs = swaggerJsdoc(swaggerOptions);
const portalSpecs = swaggerJsdoc(swaggerPortalOptions);
const systemSpecs = swaggerJsdoc(swaggerSystemOptions);
const analyticsSpecs = swaggerJsdoc(analyticsOptions);
const log = logger(config.logger);

const MongooseConnector = require('./lib/db/connectors/MongooseConnector');
// const PostgreSQLConnector = require('./lib/db/connectors/PostgreSQLConnector');
const KafkaConnector = require('./lib/db/connectors/KafkaConnector');

require('@shoutout-labs/passport-jwt-openid-authorizer')(passport, {
    certUrl: secretConfig.OPENID_ADMIN_JWKS_URI,
    secretKey: secretConfig.JWT_SECRET,
    issuer: secretConfig.OPENID_ADMIN_ISSUER,
    //audience: config.openid.audience,
    strategyId: 'jwt',
    authZRolePrefix: 'loyalty_'
});
require('@shoutout-labs/passport-jwt-oid-authz')(passport, {
    certUrl: secretConfig.OPENID_ADMIN_JWKS_URI,
    secretKey: secretConfig.JWT_SECRET,
    issuer: secretConfig.OPENID_ADMIN_ISSUER,
    //audience: config.openid.audience,
    strategyId: 'jwt-ability'
});
const ConfigsDAO = require('./lib/db/dao/ConfigsDAO');
const { default: helmet } = require('helmet');
const handlerFunc = async (originUrl) => {
    const config = await ConfigsDAO.getConfigByOriginURL(originUrl);
    if (!config) {
        throw new Error('config not found');
    }
    return config.portal.certUrl;
};

require('@shoutout-labs/passport-jwt-openid-authorizer')(passport, {
    //certUrl: config.openid.portal.jwks_uri,
    handlerFunc,
    secretKey: secretConfig.JWT_SECRET,
    //issuer: config.openid.issuer,
    //audience: config.openid.audience,
    strategyId: 'jwt-portal',
    authZRolePrefix: 'portal_',
    strategyFunc: (jwtPayload, done) => {
        done(null, {
            id: jwtPayload['sub'],
            contactId: jwtPayload['contact_id'],
            ownerId: jwtPayload['owner_id'],
            name: jwtPayload['name'],
            email: jwtPayload['email']
        });
    }
});

const authorizerAbility = passport.authenticate('jwt-ability', { session: false });
// const authorizerPortal = passport.authenticate('jwt-portal', { session: false });

const BASE_PATH = config.api.base_path;

const app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'pug');

app.use(cors({ exposedHeaders: ['x-skip,x-limit,x-total'] }));
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ extended: false, limit: '5mb' }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.disable('x-powered-by');
app.use(helmet());
app.use(`${BASE_PATH}/`, indexRouter);
app.use(`${BASE_PATH}/docs`, swaggerUi.serve, (...args) => swaggerUi.setup(specs)(...args));
app.use(`${BASE_PATH}/docs-portal`, swaggerUi.serve, (...args) => swaggerUi.setup(portalSpecs)(...args));

app.use(`${BASE_PATH}/docs-system`, swaggerUi.serve, (...args) => swaggerUi.setup(systemSpecs)(...args));

app.use(`${BASE_PATH}/docs-analytics`, swaggerUi.serve, (...args) => swaggerUi.setup(analyticsSpecs)(...args));

//FOR LOGGING ALL INCOMING REQUESTS
app.use(function (req, res, next) {
    log.info('REQUEST: ', {
        method: req.method,
        url: req.headers.host + req.originalUrl,
        origin: req.get('origin') || req.get('Origin'),
        body: JSON.stringify(req.body),
        queryParams: JSON.stringify(req.query)
    });
    next();
});

app.use(passport.initialize());

app.use(`${BASE_PATH}/points`, require('./routes/points')(authorizerAbility));

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    next(createError(404));
});

//AUDIT LOGGER
app.use(require('@shoutout-labs/audit-logger')('LOYALTY'));

// * Error handler
// eslint-disable-next-line no-unused-vars
app.use((err, req, res, next) => {
    log.error(err);

    let status,
        message,
        errorCode = err?.errorCode || null;

    if (err?.name === 'ForbiddenError') {
        status = 403;
        message = err.message;
        errorCode = '000403';
    } else {
        status = err.status ? Number(err.status) : 500;
        message = err.status ? err.message : 'something went wrong';

        if (!errorCode && !err.status) errorCode = '000500';
    }
    res.status(status).json({ error: message, ...(errorCode ? { errorCode } : {}) });
});

module.exports = async () => {
    try {
        await Promise.all([
            MongooseConnector.initialize(),
            // PostgreSQLConnector.initialize(),
            KafkaConnector.initializeProducer()
        ]);
        return app;
    } catch (e) {
        log.error(e);
        return Promise.reject(e);
    }
};
