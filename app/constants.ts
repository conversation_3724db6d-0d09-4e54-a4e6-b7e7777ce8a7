const AWS_REGION = process.env.AWS_REGION;
const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY;
const SQS_QUEUE_URL = process.env.SQS_QUEUE_URL;
const SHOPIFY_ADMIN_API_TOKEN = process.env.SHOPIFY_ADMIN_API_TOKEN;
const SHOUTOUT_IDENTITY_SERVICE_API_URL = process.env.SHOUTOUT_IDENTITY_SERVICE_API_URL ?? 'https://api.loyaltybeta.cxforge.com/api/identityservice';
const SHOUTOUT_LOYALTY_SERVICE_API_URL = process.env.SHOUTOUT_LOYALTY_SERVICE_API_URL ?? 'https://api.loyaltybeta.cxforge.com/api/loyaltyservice';


const LOYALTY_POINT_COLLECTION_API_URL = `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/points/collect/bill`;
const LOYALTY_MEMBER_REGISTRATION_API_URL = `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/members/register`;
const SHOUTOUT_GET_MEMBER_API_URL = `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/klip/members`;
const SHOUTOUT_ACCESS_TOKEN_API_URL = process.env.SHOUTOUT_ACCESS_TOKEN_API_URL;
const SHOUTOUT_API_CLIENT_ID = process.env.SHOUTOUT_API_CLIENT_ID;
const SHOUTOUT_API_CLIENT_SECRET = process.env.SHOUTOUT_API_CLIENT_SECRET;
const SHOUTOUT_ADMIN_PORTAL_URL = process.env.SHOUTOUT_ADMIN_PORTAL_URL ?? 'https://admin.loyalty.cxforge.com';
const SHOPIFY_DISCOUNT_FUNCTION_ID =  process.env.SHOPIFY_DISCOUNT_FUNCTION_ID;
const PRIMARY_KEY = process.env.PRIMARY_KEY ?? 'MOBILE_NUMBER';


export {
  AWS_REGION,
  AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY,
  SQS_QUEUE_URL,
  SHOUTOUT_ACCESS_TOKEN_API_URL,
  LOYALTY_POINT_COLLECTION_API_URL,
  LOYALTY_MEMBER_REGISTRATION_API_URL,
  SHOUTOUT_API_CLIENT_ID,
  SHOUTOUT_API_CLIENT_SECRET,
  SHOUTOUT_IDENTITY_SERVICE_API_URL,
  SHOUTOUT_LOYALTY_SERVICE_API_URL,
  SHOUTOUT_GET_MEMBER_API_URL,
  SHOUTOUT_ADMIN_PORTAL_URL,
  SHOPIFY_DISCOUNT_FUNCTION_ID,
  SHOPIFY_ADMIN_API_TOKEN,
  PRIMARY_KEY
}
