import { OrganizationCreateStatus } from "@prisma/client";
import prisma from "../db.server";

class OrganizationCreateStatusDao {

    public static async createOrganizationCreateStatus(organizationCreateStatus: Omit<OrganizationCreateStatus, 'id' | 'createdAt' | 'updatedAt'>): Promise<OrganizationCreateStatus> {
        return prisma.organizationCreateStatus.create({
          data: organizationCreateStatus
        });
    }

    public static async getOrganizationCreateStatus(shop: string): Promise<OrganizationCreateStatus | null> {
        return prisma.organizationCreateStatus.findUnique({
          where: {
            shop: shop
          }
        });
    }

    public static async upsertOrganizationCreateStatus(organizationCreateStatus: Partial<Omit<OrganizationCreateStatus, 'id' | 'createdAt' | 'updatedAt'>> & { shop: string }): Promise<OrganizationCreateStatus> {
        const { id,updatedAt,createdAt, ...rest } = organizationCreateStatus as unknown as any;
        return prisma.organizationCreateStatus.upsert({
          where: {
            shop: organizationCreateStatus.shop
          },
          update: rest,
          create: {
            ...organizationCreateStatus,
            organizationCreated: organizationCreateStatus.organizationCreated ?? false
          }
        });
    }
}

export default OrganizationCreateStatusDao;
