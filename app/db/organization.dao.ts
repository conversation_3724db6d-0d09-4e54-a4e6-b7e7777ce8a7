import { Organization } from "@prisma/client";
import NodeCache from 'node-cache';
import prisma from "../db.server";

class OrganizationDao {
    private static cache = new NodeCache({ stdTTL: 3600 }); // 1 hour default TTL

    public static async createOrganization(organization: Omit<Organization, 'id' | 'createdAt' | 'updatedAt'>): Promise<Organization> {
        return prisma.organization.create({
          data: organization
        });
    }

    public static async updateOrganization(organization: Omit<Organization,  'createdAt' | 'updatedAt'>): Promise<Organization> {
        return prisma.organization.update({
          where: {
            id: organization.id
          },
          data: organization
        });
    }

    public static async getOrganization(shop: string): Promise<Organization | null> {
        // Try to get from cache first
        const cacheKey = `org:${shop}`;
        const cachedOrg = this.cache.get<Organization>(cacheKey);
        if (cachedOrg) {
            return cachedOrg;
        }

        // If not in cache, get from database
        const organization = await prisma.organization.findUnique({
            where: {
                shop: shop
            }
        });

        // Store in cache if found
        if (organization) {
            this.cache.set(cacheKey, organization);
        }

        return organization;
    }
}

export default OrganizationDao;
