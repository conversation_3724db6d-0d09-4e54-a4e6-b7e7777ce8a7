import ShopifyAdminApiService from "../services/shopify.adminapi.service";
import ShoutoutLoyaltyService from "../services/shoutout.loyalty.service.v2";
import {generateRandomCode} from "../utils/code.utils";
import type {
  HandleDiscountCodesParams, HandleGenerateFreeShippingCouponParams,
  HandleLoaderDefaultParams, HandleLoaderDefaultResponse, HandleRedeemPointsWithOtpParams,
  HandleRedeemPointsWithOtpRequestParams,
  HandleRegisterWithOtpParams, HandleRegisterWithOtpRequestParams
} from "../types/proxy.types";
import {json} from "@remix-run/node";
import OrganizationDao from "../db/organization.dao";
import {PrimaryKeys} from "../data";


const headers = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type",
  "Cache-Control": "no-store",
  "Content-Type": "application/json",
};

class AppLoyaltyMemberHandler {

  static async handleDiscountCodes({admin, customerId}: HandleDiscountCodesParams) {
    try {
      const response = await ShopifyAdminApiService.ListCustomerDiscounts(
        admin,
        customerId
      );
      const data = await response.json();
      const discountCodes = data.data.discountNodes.nodes
        .filter((node: {
          id: string,
          discount?: {
            codes?: {
              nodes?: Array<{ code: string }>
            },
            customerSelection?: {
              customers?: {
                id: string
              }[]
            }
          }
        }) => {
          // First check if the discount code exists
          const hasCode = node.discount?.codes?.nodes?.[0]?.code;

          // Then check if this discount is assigned to the current customer
          const customers = node.discount?.customerSelection?.customers;
          const isForCurrentCustomer = !customers ||
            customers.some(customer => customer.id.includes(`gid://shopify/Customer/${customerId}`));

          return hasCode && isForCurrentCustomer;
        })
        .map((node: {
          id: string,
          metafields: {
            nodes: Array<{
              key: string,
              value: string
            }>
          },
          discount: {
            title: string;
            codes: {
              nodes: Array<{ code: string }>
            };
            status: string;
            endsAt: string | null;
            usageLimit: number;
            asyncUsageCount: number;
            appliesOncePerCustomer: boolean;
            startsAt: string;
          }
        }) => {
          const discount = node.discount;
          const configMetafield = node.metafields?.nodes?.find(
            node => node.key === 'function-configuration'
          );

          // Parse the metafield value to get the discount amount
          const discountConfig = configMetafield
            ? JSON.parse(configMetafield.value)
            : null;

          const pointsRedeemed = discountConfig?.quantity || 0;

          const isUsed =
            discount.status === 'EXPIRED' ||
            (discount.usageLimit && discount.asyncUsageCount >= discount.usageLimit) ||
            (discount.endsAt && new Date(discount.endsAt) < new Date());

          return {
            id: node.id,
            code: discount.codes.nodes[0].code,
            status: isUsed ? 'USED' : discount.status,
            endsAt: discount.endsAt,
            startsAt: discount.startsAt,
            timesUsed: discount.asyncUsageCount || 0,
            usageLimit: discount.usageLimit || 1,
            title: discount?.title || "",
            pointsRedeemed
          };
        });

      return json(discountCodes, {status: 200, headers});
    } catch (error) {
      console.error("Error fetching discount codes:", error);
      let errorMessage = "Failed to fetch discount codes";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }

  static async handleLoaderDefault({customerId, shop, primaryKey, mobileNumber}: HandleLoaderDefaultParams) {
    try {
      let freeShippingEligible = false;
      const organization = await OrganizationDao.getOrganization(shop);
      if (!organization) {
        return json({error: 'Organization not found'}, {status: 404});
      }
      const organizationPublicData = await ShoutoutLoyaltyService.getPublicData(organization.regionId, shop);
      const {
        minPointRedemptionAmount = 1,
        maxPointRedemptionAmount = 1000000,
        minPointsBalanceForRedemption = 0,
        currencyAmountPerPoint = 1
      } = organizationPublicData?.region?.pointConfiguration || {};
      const generalSpendingPointRule = organizationPublicData?.pointRules?.find((rule: any) => rule?.subType === "GENERAL") || {};
      let handleLoaderDefaultResponse: HandleLoaderDefaultResponse = {
        tiers: organizationPublicData?.tiers ?? [],
        member: null,
        freeShippingEligible,
        pointConfiguration: {
          minPointRedemptionAmount,
          maxPointRedemptionAmount,
          minPointsBalanceForRedemption,
          currencyAmountPerPoint,
          pointsEarnedRatio: 100,
        },
        generalSpendingRule: {
          subType: generalSpendingPointRule?.subType || "GENERAL",
          ruleData: {
            pointAmountsForRangesEnabled: generalSpendingPointRule?.ruleData?.pointAmountsForRangesEnabled || false,
            ...(generalSpendingPointRule?.ruleData?.pointAmountsForRangesEnabled ? {
              pointAmountMappingsForRanges: generalSpendingPointRule?.ruleData?.pointAmountMappingsForRanges || [],
            } : {
              amountPerPoint: generalSpendingPointRule?.ruleData?.amountPerPoint || 0,
            }),
          },
        },
      };
      if (!customerId && !mobileNumber) {
        return json(handleLoaderDefaultResponse, {status: 200, headers});
      }
      const [members = null] = await Promise.all([
        ...(customerId && primaryKey === PrimaryKeys.CARD_NUMBER ? [
          await ShoutoutLoyaltyService.getMember(shop, organization.regionId, PrimaryKeys.CARD_NUMBER, customerId)
        ] : []),
        ...(mobileNumber && primaryKey === PrimaryKeys.MOBILE_NUMBER ? [
          await ShoutoutLoyaltyService.getMember(shop, organization.regionId, PrimaryKeys.MOBILE_NUMBER, mobileNumber)
        ] : []),
      ]);
      console.log('members', members);

      if (members?.items?.length > 0) {
        const {
          firstName,
          lastName,
          _id,
          points,
          tierPoints,
          cardNumber,
          allowedRedeemablePoints,
          tier
        } = members.items[0];
        handleLoaderDefaultResponse.member = {
          firstName,
          lastName,
          cardNumber,
          _id,
          points,
          tierPoints,
          allowedRedeemablePoints,
          tier
        }
      }

      if (organization?.configuration?.freeShippingEligibleTiers?.includes(members?.items[0]?.tier?.tierId)) {
        handleLoaderDefaultResponse.freeShippingEligible = true;
      }
      return json(handleLoaderDefaultResponse, {status: 200, headers});
    } catch (error) {
      console.error('Error fetching member:', error);
      let errorMessage = "Internal server error";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }

  static async handleRegisterWithOtpRequest({mobileNumber, customerId, shop, admin, session, primaryKey}: HandleRegisterWithOtpRequestParams) {
    try {
      const organization = await OrganizationDao.getOrganization(shop);
      if (!organization) {
        return json({error: 'Organization not found'}, {status: 404});
      }
      const customer = await admin!.rest.resources.Customer.find({
        session: session,
        id: customerId,
      });

      if(customer?.phone){
        const members =  await ShoutoutLoyaltyService.getMember(shop, organization.regionId, PrimaryKeys.MOBILE_NUMBER, customer?.phone);
        if(members?.items?.length > 0){
          return json({error: 'Member already exists'}, {status: 400, headers});
        }
      }

      if (!customer) {
        return json({error: 'Customer not found'}, {status: 404});
      }

      if (primaryKey === PrimaryKeys.MOBILE_NUMBER && Number(mobileNumber) !== Number(customer.phone)) {
        const response = await ShopifyAdminApiService.updateCustomerByGraphQLAdmin(admin, customerId, {
          phone: mobileNumber
        });
        const responseJson = await response.json();
        const errors = responseJson?.data?.customerUpdate?.userErrors;
        if (errors?.length !== 0) {
          return json({error: errors || 'Failed to update customer'}, {status: 400, headers: headers});
        }
      }
      const otpRequestResult = await ShoutoutLoyaltyService.registerMemberWithOtpRequest(
        shop,
        {
          regionId: organization.regionId,
          ...(primaryKey === PrimaryKeys.MOBILE_NUMBER ? {
            autoGenerateCard: true,
          } : {
            cardNumber: String(customerId),
          }),
          mobileNumber: mobileNumber,
          email: customer.email,
          firstName: customer.first_name ?? '-',
          lastName: customer.last_name ?? '-',
          preferredName: `${customer.first_name} ${customer.last_name}`,
          merchantLocationId: organization.merchantLocationId,
          customAttributes: {},
          notificationPreference: {
            preferredChannel: "MOBILE",
            allowPromotionalNotifications: true
          }
        }
      );
      return json(otpRequestResult, {status: 200, headers});
    } catch (error) {
      console.error("Error in registerWithOtpRequest:", error);
      let errorMessage = "Internal server error";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }

  static async handleRegisterWithOtp({registerMemberToken, otpCode, shop}: HandleRegisterWithOtpParams) {
    try {
      const result = await ShoutoutLoyaltyService.registerMemberWithOtp(shop, {registerMemberToken, otpCode});
      return json(result, {status: 200, headers});
    } catch (error) {
      console.error("Error in registerWithOtp:", error);
      let errorMessage = "Internal server error";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }

  static async handleRedeemPointsWithOtpRequest({pointsAmount, memberId, shop, action, admin, customerId}: HandleRedeemPointsWithOtpRequestParams) {
    try {
      const organization = await OrganizationDao.getOrganization(shop);
      if (!organization) {
        return json({error: 'Organization not found'}, {status: 404});
      }
      const requestData = {
        memberId,
        pointsAmount,
        merchantId: organization.merchantId,
        merchantLocationId: organization.merchantLocationId,
        transactionSubTypeId: organization.redeemPointsSubTransactionTypeId ?? '',
        transactionDate: new Date().toISOString(),
        // idempotentKey: "1234567890",//TODO: try to attach order id here
        notes: "Redeem Points from Shopify",
        // invoiceData: {
        //     invoiceId: "1234567890",//TODO: try to attach order id here
        //     billAmount: pointsAmount
        // }
      };
      switch (action) {
        case 'redeemPointsWithOtpRequest' : {
          const result = await ShoutoutLoyaltyService.redeemPointsWithOtpRequest(shop, requestData);
          return json({shoutoutResult: result}, {status: 200, headers});
        }
        case 'redeemPoints': {
          const result = await ShoutoutLoyaltyService.redeemPoints(shop, requestData);
          const regionData = await ShoutoutLoyaltyService.getRegionByIdWithShop(organization.regionId, shop);
          const currencyAmountPerPoint = regionData?.pointConfiguration?.currencyAmountPerPoint ?? 1;
          const discountAmount = pointsAmount * currencyAmountPerPoint;
          const code = generateRandomCode(8);
          const discountResponse = await ShopifyAdminApiService.createDiscountCode(
            discountAmount,
            String(customerId),
            admin,
            code,
            shop
          );
          const responseJson = await discountResponse.json();
          const errors = responseJson.data.discountCreate?.userErrors;
          return json({shoutoutResult: result, errors, discountCode: code}, {status: 200, headers});
        }
        default: {
          return json({error: "Internal server error"}, {status: 500, headers});
        }
      }

    } catch (error) {
      console.error("Error in redeemPointsWithOtpRequest:", error);
      let errorMessage = "Internal server error";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }

  static async handleRedeemPointsWithOtp({redemptionToken, otpCode, shop}: HandleRedeemPointsWithOtpParams) {
    try {
      const result = await ShoutoutLoyaltyService.redeemPointsWithOtp(shop, {redemptionToken, otpCode});
      return json(result, {status: 200, headers});
    } catch (error) {
      console.error("Error in redeemPointsWithOtp:", error);
      let errorMessage = "Internal server error";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }

  static async handleGenerateFreeShippingCoupon({shop, admin, customerId,memberId}:HandleGenerateFreeShippingCouponParams ) {
    try {
      const organization = await OrganizationDao.getOrganization(shop);
      if (!organization) {
        return json({error: 'Organization not found'}, {status: 404});
      }
      const members = await ShoutoutLoyaltyService.getMemberById(shop, memberId);
      if (!organization?.configuration?.freeShippingEligibleTiers?.includes(members?.tier?.tierId)) {
        return json({error: "Customer not eligible for free shipping"}, {status: 403, headers});
      }

      const code = generateRandomCode(8);
      const discountResponse = await ShopifyAdminApiService.createFreeShippingDiscountCode(admin, code, String(customerId));
      const responseJson = await discountResponse.json();
      const errors = responseJson.data?.discountCodeFreeShippingCreate?.userErrors;

      if (errors?.length > 0) {
        return json({error: errors}, {status: 400, headers});
      }
      console.log('members', members);
      return json({discountCode: code}, {status: 200, headers});
    } catch (error) {
      console.error("Error in generateFreeShippingCoupon:", error);
      let errorMessage = "Internal server error";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }
}

export default AppLoyaltyMemberHandler;
