import shopify, {authenticate} from "../shopify.server";
import {AuthPublicCheckoutSuccessResponse, AuthPublicProxySuccessResponse} from "../types/proxy.types";

class AppShopifyAuthHandler {
  public static async authenticatePublicProxyRequest(request: Request): Promise<AuthPublicProxySuccessResponse | Error> {
    const {admin, session} = await authenticate.public.appProxy(request);
    const shop = session?.shop;
    if (!shop) throw new Error('Shop not found');
    return {admin, session, shop} as AuthPublicProxySuccessResponse;
  }

  public static async authenticatePublicCheckoutRequest(request: Request): Promise<AuthPublicCheckoutSuccessResponse | Error> {
    const {sessionToken} = await shopify.authenticate.public.checkout(request);
    const shop = sessionToken.dest ?? '';
    let customerId: string = "";
    if (sessionToken?.sub) {
      customerId = sessionToken.sub.split('/')?.pop() || "";
    }
    if (!shop) {
      throw new Error('Shop domain not found');
    }
    return {shop, customerId} as AuthPublicCheckoutSuccessResponse;

  }
}

export default AppShopifyAuthHandler;
