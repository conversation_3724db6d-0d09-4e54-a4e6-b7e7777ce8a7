import type {LoaderFunctionArgs} from "@remix-run/node";
import {json} from "@remix-run/node";
import ProxyValidator from "../validators/ProxyValidator";
import AppLoyaltyMemberHandler from "../handlers/app.loyalty.member.handler";
import AppShopifyAuthHandler from "../handlers/app.shopify.auth.handler";

const headers = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Max-Age": "86400" // 24 hours
};

export async function loader({request}: LoaderFunctionArgs) {
  try {
    if (request.method === "OPTIONS") {
      return new Response(null, {headers});
    }
    console.log("Checkout request received:", request.url);
    console.log("Request headers:", Object.fromEntries(request.headers.entries()));
    const authResponse = await AppShopifyAuthHandler.authenticatePublicCheckoutRequest(request);
    if (authResponse instanceof Error) {
      return json({error: authResponse.message}, {status: 401, headers});
    }
    const {shop, customerId} = authResponse;
    const url = new URL(request.url);
    const mobileNumber = url.searchParams.get('mobileNumber') || "";
    const primaryKey = url.searchParams.get('primaryKey') || "";
    const validateLoaderDefault = await ProxyValidator.validateLoaderDefault({
      customerId,
      shop,
      primaryKey,
      mobileNumber
    });
    return await AppLoyaltyMemberHandler.handleLoaderDefault({...validateLoaderDefault});

  } catch (error: unknown) {
    console.error("Error in checkout API:", error instanceof Error ? error.message : error);
    if (error && typeof error === 'object' && 'body' in error) {
      console.error("Error body:", (error as { body: unknown }).body);
    }
    return json({error: "Internal server error"}, {status: 500, headers});
  }
}

export async function action({request}: LoaderFunctionArgs) {
  if (request.method === "OPTIONS") {
    return new Response(null, {headers});
  }
  const authResponse = await AppShopifyAuthHandler.authenticatePublicCheckoutRequest(request);
  if (authResponse instanceof Error) {
    return json({error: authResponse.message}, {status: 401, headers});
  }
  const {shop, customerId} = authResponse;
  const body = await request.json();
  const {action} = body;
  switch (action) {
    case 'redeemPoints': {
      const {memberId, pointsAmount} = body;
      const validateRedeemPointsWithOtpRequest = ProxyValidator.validateRedeemPointsWithOtpRequest({
        pointsAmount,
        memberId,
        customerId,
        shop,
        action,
        admin: null
      });
      return await AppLoyaltyMemberHandler.handleRedeemPointsWithOtpRequest({...validateRedeemPointsWithOtpRequest});
    }
  }
}
