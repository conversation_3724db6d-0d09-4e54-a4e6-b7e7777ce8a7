import {json, type ActionFunctionArgs, type LoaderFunctionArgs} from "@remix-run/node";
import {authenticate} from "../shopify.server";
import ShoutoutLoyaltyService from "../services/shoutout.loyalty.service.v2";
import {generateRandomCode} from "../utils/code.utils";
import ShopifyAdminApiService from "../services/shopify.adminapi.service";
import OrganizationDao from "../db/organization.dao";
import type {Organization} from "@prisma/client";

const headers = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Max-Age": "86400", // 24 hours
};

export const loader = async ({request}: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {headers});
  }
  return json({error: "Method Not Allowed"}, {status: 405});
};

const authRequest = async (request: Request): Promise<{
  shopDomain: string;
  admin: any;
  organization: Organization
}> => {
  const {admin, session} = await authenticate.admin(request);
  const shopDomain = session?.shop;

  if (!shopDomain) {
    throw new Error("Shop domain not found");
  }

  const organization = await OrganizationDao.getOrganization(shopDomain);
  if (!organization) {
    throw new Error("Organization not found");
  }

  return {shopDomain, admin, organization};
};


export const action = async ({request}: ActionFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {headers});
  }

  try {
    const {shopDomain, admin, organization} = await authRequest(request);
    const body = await request.json();

    const {action, customerId: cardNumber, mobileNumber, pointsAmount, redemptionToken, otpCode} = body;

    switch (action) {
      case "redeemPointsOtpRequest": {
        if (!pointsAmount || !mobileNumber || !cardNumber) {
          return json({error: "Invalid request parameters"}, {status: 400, headers: headers});
        }

        const requestData = {
          cardNo: String(cardNumber),
          pointsAmount,
          merchantId: organization.merchantId,
          merchantLocationId: organization.merchantLocationId,
          transactionSubTypeId: organization.redeemPointsSubTransactionTypeId ?? "",
          transactionDate: new Date().toISOString(),
          notes: "Redeem Points from Shopify",
        };

        const result = await ShoutoutLoyaltyService.redeemPointsWithOtpRequest(shopDomain, requestData);
        const regionData = await ShoutoutLoyaltyService.getRegionByIdWithShop(organization.regionId, shopDomain);

        const currencyAmountPerPoint = regionData?.pointConfiguration?.currencyAmountPerPoint ?? 1;
        const code = generateRandomCode(8);

        const discountResponse = await ShopifyAdminApiService.createDiscountCode(
          pointsAmount * currencyAmountPerPoint,
          cardNumber,
          admin,
          code
        );

        const responseJson = await discountResponse.json();
        const errors = responseJson.data?.discountCreate?.userErrors;
        if (errors?.length !== 0) {
          return json({error: errors}, {status: 400, headers: headers});
        }
        return json({
          shoutoutResult: result,
          discountCode: code,
        }, {status: 200, headers: headers});
      }

      case "redeemPointsWithOtp": {
        if (!redemptionToken || !otpCode) {
          return json({error: "Invalid request parameters"}, {status: 400, headers: headers});
        }
        const result = await ShoutoutLoyaltyService.redeemPointsWithOtp(shopDomain, {redemptionToken, otpCode});
        return json(result, {status: 200, headers: headers});
      }
      default:
        return json({error: "Invalid action"}, {status: 500, headers: headers});
    }
  } catch (error) {
    console.error("Error redeeming points:", error instanceof Error ? error.message : error);
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
    return json({error: "Internal server error"}, {status: 500, headers: headers});
  }
};
