import {json, LoaderFunctionArgs} from "@remix-run/node";
import AppShopifyAuthHandler from "../handlers/app.shopify.auth.handler";
import AppLoyaltyMemberHandler from "../handlers/app.loyalty.member.handler";
import ProxyValidator from "../validators/ProxyValidator";

export const headers = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type",
  "Cache-Control": "no-store",
  "Content-Type": "application/json",
};

export async function loader({request}: LoaderFunctionArgs) {
  console.log("Proxy request received:", request.url);
  const authResponse = await AppShopifyAuthHandler.authenticatePublicProxyRequest(request);
  if (authResponse instanceof Error) {
    return json({error: authResponse.message}, {status: 401, headers});
  }
  const {admin, shop} = authResponse;
  const url = new URL(request.url);
  const path = url.pathname;
  const customerId = url.searchParams.get('logged_in_customer_id');
  const mobileNumber = url.searchParams.get('mobileNumber');
  const primaryKey = url.searchParams.get('primaryKey');

  if (path.endsWith("/discount-codes")) {
    try {
      const validateDiscountCodes = await ProxyValidator.validateDiscountCodes({admin, customerId});
      return await AppLoyaltyMemberHandler.handleDiscountCodes({...validateDiscountCodes});
    } catch (error) {
      let errorMessage = "Failed to fetch discount codes";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  } else {
    try {
      const validateLoaderDefault  = await ProxyValidator.validateLoaderDefault({customerId,shop,primaryKey,mobileNumber});
      return await AppLoyaltyMemberHandler.handleLoaderDefault({...validateLoaderDefault});
    } catch (error) {
      let errorMessage = "Failed to fetch member";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return json({error: errorMessage}, {status: 500, headers});
    }
  }
}

export async function action({request}: LoaderFunctionArgs) {
  if (request.method === "OPTIONS") return new Response(null, {headers});
  const url = new URL(request.url);
  const body = await request.json();
  const authResponse = await AppShopifyAuthHandler.authenticatePublicProxyRequest(request);
  if (authResponse instanceof Error) {
    return json({error: authResponse.message}, {status: 401, headers});
  }
  const {admin, shop,session} = authResponse;
  const {action} = body;
  switch (action) {
    case 'registerWithOtpRequest': {
      const customerId = url.searchParams.get('logged_in_customer_id');
      const {mobileNumber,primaryKey} = body;
      const validateRegisterWithOtpRequest = await ProxyValidator.validateRegisterWithOtpRequest({primaryKey,admin, customerId, mobileNumber, session, shop});
      return await AppLoyaltyMemberHandler.handleRegisterWithOtpRequest({...validateRegisterWithOtpRequest});
    }
    case 'registerWithOtp': {
      const {registerMemberToken, otpCode} = body;
      const validateRegisterWithOtp = ProxyValidator.validateRegisterWithOtp({ registerMemberToken,otpCode,shop });
      return await AppLoyaltyMemberHandler.handleRegisterWithOtp({...validateRegisterWithOtp});
    }
    case 'redeemPoints':
    case 'redeemPointsWithOtpRequest': {
      const {pointsAmount, memberId,customerId} = body;
      const validateRedeemPointsWithOtpRequest= ProxyValidator.validateRedeemPointsWithOtpRequest({ pointsAmount,memberId,customerId,shop,action,admin});
      return await AppLoyaltyMemberHandler.handleRedeemPointsWithOtpRequest({...validateRedeemPointsWithOtpRequest});
    }
    case 'redeemPointsWithOtp': {
      const {redemptionToken, otpCode} = body;
      const validateRedeemPointsWithOtp = ProxyValidator.validateRedeemPointsWithOtp({ redemptionToken, otpCode,shop });
      return await AppLoyaltyMemberHandler.handleRedeemPointsWithOtp({...validateRedeemPointsWithOtp});
    }
    case 'generateFreeShippingCoupon': {
      const {customerId,memberId} = body;
      const validateGenerateFreeShippingCoupon= ProxyValidator.validateGenerateFreeShippingCoupon({ shop, admin, customerId,memberId});
      return await AppLoyaltyMemberHandler.handleGenerateFreeShippingCoupon({...validateGenerateFreeShippingCoupon});
    }
    default:
      return json({error: 'Invalid action'}, {status: 400,headers});
  }
}

