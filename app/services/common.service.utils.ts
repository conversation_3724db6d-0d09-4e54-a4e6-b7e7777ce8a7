import ServiceException from "app/utils/service.exception";
import ShoutoutIdentityService from "./shoutout.identity.service";
import { jwtDecode } from 'jwt-decode';
import NodeCache from 'node-cache';
import prisma from "../db.server";
class CommonServiceUtils {
    private static sessionCache = new NodeCache({ stdTTL: 300 }); // 5 minutes TTL

    private static async parseResponse(response: Response) {
        if (!response.ok) {
            let errorResponse;
            try {
                errorResponse = await response.json();
            } catch (e) {
                throw new ServiceException(response.statusText, response.status);
            }
            throw new ServiceException(
                errorResponse?.error,
                errorResponse?.errorCode

            );
        }
        console.log(response.status, response.statusText, response.body);
        return response.json();
    }


    private static async getToken(shop: string): Promise<string | null> {
        const organization = await prisma.organization.findUnique({
            where: {
                shop: shop
            }
        });
        if (organization && organization.clientId && organization.clientSecret) {
            const tokenResponse = await ShoutoutIdentityService.getClientToken(organization.clientId, organization.clientSecret);
            if (tokenResponse) {
                const token = tokenResponse?.access_token;
                const decodedToken = jwtDecode<{ exp: number }>(token);
                const expiresAt = new Date(decodedToken.exp * 1000);
                const currentTime = new Date();
                await prisma.loyaltySession.upsert({
                    where: { shop: shop },
                    update: {
                        token: token,
                        expiresAt: expiresAt
                    },
                    create: {
                        shop: shop,
                        token: token,
                        expiresAt: expiresAt
                    }
                });

                // Calculate TTL in seconds
                const ttlSeconds = Math.floor((expiresAt.getTime() - currentTime.getTime()) / 1000) - 60;

                // Store new token in cache with specific TTL
                this.sessionCache.set(shop, { token: token }, ttlSeconds);
                return token;
            }
        }
        throw new ServiceException("No token found", 401);
    }


    private static async _getAuth(shop: string, forceRefresh: boolean = false): Promise<string | null> {

        if (forceRefresh) {
            return await this.getToken(shop);
        }
        // Try to get from cache first
        const cachedSession = this.sessionCache.get<{ token: string }>(shop);
        if (cachedSession) {
            return cachedSession.token;
        }

        const session = await prisma.loyaltySession.findUnique({
            where: {
                shop: shop
            }
        });

        const currentTime = new Date();

        if (session && session.expiresAt > currentTime) {
            // Calculate TTL in seconds with 60 second buffer before expiration
            const ttlSeconds = Math.floor((session.expiresAt.getTime() - currentTime.getTime()) / 1000) - 60;

            // Store valid session in cache with specific TTL
            this.sessionCache.set(shop, { token: session.token }, ttlSeconds);
            return session.token;
        }
        return await this.getToken(shop);

    }
    public static async get(url: string, headers?: any) {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...(headers || {})
            }
        });
        return this.parseResponse(response);
    }

    public static async post(url: string, body: any, headers?: any) {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(headers || {})
            },
            body: JSON.stringify(body)
        });
        return this.parseResponse(response);
    }

    public static async postFormData(url: string, body: any, headers?: any) {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                ...(headers || {})
            },
            body: new URLSearchParams(body)
        });
        return this.parseResponse(response);
    }


    private static async makeAuthRequest(
        shop: string,
        url: string,
        method: 'GET' | 'POST',
        options: {
            queryParams?: any,
            body?: any,
            headers?: any,
            maxRetries?: number
        }
    ) {
        const maxRetries = options.maxRetries || 3;
        let retryCount = 0;

        const makeRequest = async (forceRefresh = false) => {
            const finalUrl = options.queryParams
                ? `${url}?${new URLSearchParams(options.queryParams)}`
                : url;

            return fetch(finalUrl, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${await this._getAuth(shop, forceRefresh)}`,
                    ...(options.headers || {})
                },
                ...(options.body && { body: JSON.stringify(options.body) })
            });
        };

        let response = await makeRequest();
        console.debug("status:", response.status)
        while (response.status === 401 && retryCount < maxRetries) {
            retryCount++;
            response = await makeRequest(true);
        }

        return this.parseResponse(response);
    }

    public static async postAuth(shop: string, url: string, body: any, headers?: any) {
        return this.makeAuthRequest(shop, url, 'POST', { body, headers });
    }

    public static async getAuth(shop: string, url: string, queryParams?: any, headers?: any) {
        return this.makeAuthRequest(shop, url, 'GET', { queryParams, headers });
    }
}

export default CommonServiceUtils;

