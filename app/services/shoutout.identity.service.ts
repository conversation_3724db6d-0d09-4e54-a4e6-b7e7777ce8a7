import { SHOUTOUT_ACCESS_TOKEN_API_URL, SHOUTOUT_API_CLIENT_ID, SHOUTOUT_API_CLIENT_SECRET, SHOUTOUT_IDENTITY_SERVICE_API_URL } from "app/constants";
import CommonServiceUtils from "./common.service.utils";
import { PermissionGroup } from "app/models/auth/permission.group";


type SignupRequest = {
    userData: {
        username: string,
        firstName: string,
        lastName: string,
        gender?: "MALE" | "FEMALE";
        contact: {
            mobileNumber?: string,
            email: string,
            address?: {
                street?: string,
                city?: string,
                zip?: string
            },

        },
        password: string,

    },
    emailVerified: boolean
}
class ShoutoutIdentityService {

    public static async signup(signupRequest: SignupRequest) {
        const url = `${SHOUTOUT_IDENTITY_SERVICE_API_URL}/system/accounts/signup`;
        return CommonServiceUtils.post(url, signupRequest);
    }

    public static async getUserToken(username: string, password: string) {
        const url = `${SHOUTOUT_ACCESS_TOKEN_API_URL}`;
        return CommonServiceUtils.postFormData(url, {
            username,
            password,
            client_id: SHOUTOUT_API_CLIENT_ID,
            client_secret: SHOUTOUT_API_CLIENT_SECRET,
            scope: "openid",
            grant_type: "password"
        });
    }

    public static async getClientToken(clientId: string, clientSecret: string):Promise<{access_token:string} | null> {
        const url = `${SHOUTOUT_ACCESS_TOKEN_API_URL}`;
        return CommonServiceUtils.postFormData(url, {
            client_id: clientId,
            client_secret: clientSecret,
            grant_type: "client_credentials"
        });
    }

    public static async getModules(userToken: string) {
        const url = `${SHOUTOUT_IDENTITY_SERVICE_API_URL}/modules`;
        return CommonServiceUtils.get(url, {
            "Authorization": `Bearer ${userToken}`
        });
    }

    public static async createPermissionGroup(data: PermissionGroup, userToken: string) {
        const url = `${SHOUTOUT_IDENTITY_SERVICE_API_URL}/groups`;
        return CommonServiceUtils.post(url, data, {
            "Authorization": `Bearer ${userToken}`
        });
    }

    public static async createIntegrationClient(userToken: string, organizationId: string): Promise<{
        clientData: {
            clientId: string,
            clientSecret: string
        },
        id: string
    }> {

        return CommonServiceUtils.post(`${SHOUTOUT_IDENTITY_SERVICE_API_URL}/users`, {
            boundary: "GLOBAL",
            type: "CLIENT",
            status: "ACTIVE",
            clientData: {
                clientId: `shopify-client-${organizationId}`
            }
        },
            {
                "Authorization": `Bearer ${userToken}`
            });


    }

    public static async addIntegrationClientPermissionGroup(userId: string, permissionGroupId: string, regionId: string, userToken: string): Promise<string> {
        return CommonServiceUtils.post(`${SHOUTOUT_IDENTITY_SERVICE_API_URL}/userpermissions`, {
            userId: userId,
            regionId,
            permissions: [{
                groupId: permissionGroupId,
                allowOnAllMerchants: true,
                allowOnAllMerchantLocations: true
            }]
        }, {
            "Authorization": `Bearer ${userToken}`
        });
    }
}

export default ShoutoutIdentityService;
