import type {ICreatePointRule} from 'app/types/shoutout.pointrules';
import {SHOUTOUT_LOYALTY_SERVICE_API_URL} from '../constants';
import CommonServiceUtils from './common.service.utils';
import type {OrganizationCreateData} from 'app/models/organization/organization.data';
import type {IMember} from 'app/types/shoutout.member.type';
import type {ITransaction} from 'app/types/shoutout.transaction.type';

interface IRegisterWithOtp {
  registerMemberToken: string;
  otpCode: string;
}

interface IRedeemPointsWithOtpRequest {
  memberId?: string;
  cardNo?: string;
  merchantId: string;
  merchantLocationId: string;
  pointsAmount: number;
  transactionSubTypeId: string;
  transactionDate: string;
  notes?: string;
  invoiceData?: {
    invoiceId?: string;
    billAmount?: number;
  };
  idempotentKey?: string;
}

interface IRedeemPointsWithOtp {
  redemptionToken: string;
  otpCode: string;
}

interface ICreateSubTransactionType {
  transactionType: "COLLECTION" | "REDEMPTION" | "ADJUSTMENT";
  operationType: "ADD" | "SUBTRACT";
  name: string;
  description: string;
  referenceId: string;
  isVisibleToUser: boolean;
}

class ShoutoutLoyaltyService {
  private static tiersCache: { [key: string]: { data: any, timestamp: number } } = {};
  private static CACHE_DURATION = 60 * 60 * 1000; // 1hr in milliseconds
  private static regionsCache: { [key: string]: { data: any, timestamp: number } } = {};

  public static async updateLoyaltyPointsCollection(shop: string, data: ITransaction) {
    const response = await CommonServiceUtils.postAuth(shop, `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/points/collect/bill`, data);
    return response;
  }

  public static async registerLoyaltyMember(shop: string, data: IMember) {
    const response = await CommonServiceUtils.postAuth(shop, `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/members/register`, { ...data, autoGenerateCard: false });
    return response;
  }

  public static async createOrganization(organization: OrganizationCreateData, userToken: string) {
    return await CommonServiceUtils.post(`${SHOUTOUT_LOYALTY_SERVICE_API_URL}/organizations`, organization, {
      "Authorization": `Bearer ${userToken}`
    });
  }

  public static async createSubTransactionType(data: ICreateSubTransactionType, userToken: string) {
    const response = await CommonServiceUtils.post(
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/subtransactiontypes`,
      data,
      {
        "Authorization": `Bearer ${userToken}`
      }
    );
    return response;
  }

  public static async getRegionById(regionId: string, userToken: string): Promise<{
    _id: string;
    regionName: string;
    defaultMerchantId: string;
    defaultMerchantLocationId: string;
    pointConfiguration:{
      minPointRedemptionAmount:number;
      maxPointRedemptionAmount:number;
      minPointsBalanceForRedemption:number;
      currencyAmountPerPoint:number;
    }
  }> {
    const regions = await CommonServiceUtils.get(`${SHOUTOUT_LOYALTY_SERVICE_API_URL}/regions`, {
      "Authorization": `Bearer ${userToken}`
    });
    return (regions?.items ?? []).find((region: any) => region._id === regionId) ?? null;
  }

  public static async getMember(shop: string, regionId: string, searchField: string, searchKey: string) {
    const member = await CommonServiceUtils.getAuth(shop, `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/klip/members`, {
      skip: 0, limit: 1,
      regionId,
      searchField,
      searchKey
    });
    return member;
  }

  public static async getMemberById(shop: string,memberId: string) {
    return await CommonServiceUtils.getAuth(shop, `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/klip/members/${memberId}`);
  }

  public static async getPublicData(regionId: string,shop:string) {
    const params = new URLSearchParams({
      regionId,
    });
    return await CommonServiceUtils.get(`${SHOUTOUT_LOYALTY_SERVICE_API_URL}/portal/publicdata?${params.toString()}`,{Origin:`https://${shop}`});
  }

  public static async registerMemberWithOtpRequest(shop: string, data: IMember) {
    const response = await CommonServiceUtils.postAuth(
      shop,
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/members/registerwithotprequest`,
      data
    );
    return response;
  }

  public static async registerMemberWithOtp(shop: string, data: IRegisterWithOtp) {
    const response = await CommonServiceUtils.postAuth(
      shop,
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/members/registerwithotp`,
      data
    );
    return response;
  }

  public static async redeemPointsWithOtpRequest(shop: string, data: IRedeemPointsWithOtpRequest) {
    const response = await CommonServiceUtils.postAuth(
      shop,
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/points/redeemwithotprequest`,
      data
    );
    return response;
  }

  public static async redeemPointsWithOtp(shop: string, data: IRedeemPointsWithOtp) {
    const response = await CommonServiceUtils.postAuth(
      shop,
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/points/redeemwithotp`,
      data
    );
    return response;
  }
  public static async redeemPoints(shop: string, data: IRedeemPointsWithOtpRequest) {
    const response = await CommonServiceUtils.postAuth(
      shop,
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/points/redeem`,
      data
    );
    return response;
  }

  public static async createPointRule(data: ICreatePointRule, userToken: string) {
    const response = await CommonServiceUtils.post(
      `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/pointrules`,
      data,
      {
        "Authorization": `Bearer ${userToken}`
      }
    );
    return response;
  }

  public static async getTiers(shop: string, regionId: string, forceRefresh: boolean = false) {
    const cacheKey = `${shop}-${regionId}`;
    const now = Date.now();

    if (!forceRefresh &&
        this.tiersCache[cacheKey] &&
        now - this.tiersCache[cacheKey].timestamp < this.CACHE_DURATION) {
      return this.tiersCache[cacheKey].data;
    }

    const response = await CommonServiceUtils.getAuth(shop, `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/tiers`, {
      regionId,
      limit: 100,
      skip: 0
    });

    this.tiersCache[cacheKey] = {
      data: response,
      timestamp: now
    };

    return response;
  }

  public static async getRegionByIdWithShop(regionId: string, shop: string, forceRefresh: boolean = false): Promise<{
    _id: string;
    regionName: string;
    defaultMerchantId: string;
    defaultMerchantLocationId: string;
    pointConfiguration:{
      minPointRedemptionAmount:number;
      maxPointRedemptionAmount:number;
      minPointsBalanceForRedemption:number;
      currencyAmountPerPoint:number;
    }
  }> {
    const cacheKey = `${shop}-${regionId}`;
    const now = Date.now();

    if (!forceRefresh &&
        this.regionsCache[cacheKey] &&
        now - this.regionsCache[cacheKey].timestamp < this.CACHE_DURATION) {
      return this.regionsCache[cacheKey].data;
    }

    const regions = await CommonServiceUtils.getAuth(shop, `${SHOUTOUT_LOYALTY_SERVICE_API_URL}/regions`);
    const region = (regions?.items ?? []).find((region: any) => region._id === regionId) ?? null;

    this.regionsCache[cacheKey] = {
      data: region,
      timestamp: now
    };

    return region;
  }
}

export default ShoutoutLoyaltyService;



