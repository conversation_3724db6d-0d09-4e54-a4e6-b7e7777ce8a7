
export interface INotificationPreference {
    preferredChannel: "EMAIL" | "MOBILE",
    allowPromotionalNotifications: boolean
}

export interface IAddress {
    address1: string;
    address2: string;
    city: string;
    province: string;
    zip: string;
    country: string;
}

export interface IMember {
    regionId: string;
    cardNumber?: string;
    merchantLocationId: string;
    autoGenerateCard?:boolean;
    firstName: string;
    lastName: string;
    preferredName: string;
    mobileNumber: string;
    email: string;
    residentialAddress?: IAddress;
    customAttributes: Record<string, any>;
    notificationPreference: INotificationPreference;
}
