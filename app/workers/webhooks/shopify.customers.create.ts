import { Organization } from "@prisma/client";
import AbstractShopifyWebhookHandler from "../abstract.shopify.webhook.handler";
import ShoutoutLoyaltyService from "app/services/shoutout.loyalty.service.v2";
import { ShopifyWebhookRequest } from "app/types/shopify.workers";
import logger from "app/logger";
import { IMember } from "app/types/shoutout.member.type";

class ShopifyCustomersCreateHandler extends AbstractShopifyWebhookHandler<ShopifyWebhookRequest> {

    private _transformCustomerData(customer: any, organization: Organization): IMember {
        // Mandatory fields
        const regionId = organization.regionId;
        const cardNumber = customer.id.toString();
        const merchantLocationId = organization.merchantLocationId;
        const firstName = customer.first_name;
        const lastName = customer.last_name;
        const preferredName = firstName;
        const mobileNumber = customer.phone;
        const email = customer.email;

        // Validation
        if (!regionId) {
            throw new Error("Region ID is required.");
        }

        if (!cardNumber || cardNumber.trim() === "") {
            throw new Error("Card number is required.");
        }

        if (!merchantLocationId) {
            throw new Error("Merchant Location Id is required.");
        }

        if (!mobileNumber) {
            throw new Error("Mobile Number is required.");
        }

        if (!firstName || !lastName || !email) {
            throw new Error("First name, last name, and email are required.");
        }

        // Optional fields with default or fallback values
        // const autoGenerateCard = false; // TODo : Need figure out how to get this value

        // Address mapping
        // let residentialAddress: IAddress | null = null;

        // // Check if all address fields are present
        // if (
        //     customer.default_address?.address1 &&
        //     customer.default_address?.address2 &&
        //     customer.default_address?.city &&
        //     customer.default_address?.province &&
        //     customer.default_address?.zip &&
        //     customer.default_address?.country
        // ) {
        //     // Only create the Address object if all fields are present
        //     residentialAddress = new Address(
        //         customer.default_address.address1,
        //         customer.default_address.address2,
        //         customer.default_address.city,
        //         customer.default_address.province,
        //         customer.default_address.zip,
        //         customer.default_address.country
        //     );
        // }

        // Notification preference
        // let notificationPreference: INotificationPreference | null = null;

        // Custom attributes
        const customAttributes = customer.customAttributes ?? {};

        const memberData: IMember = {
            regionId,
            cardNumber,
            merchantLocationId,
            firstName,
            lastName,
            preferredName,
            mobileNumber,
            email,
            residentialAddress: {
                ...(customer.default_address.address1 && {address1: customer.default_address.address1}),
                ...(customer.default_address.address2 && {address2: customer.default_address.address2}),
                ...(customer.default_address.city && {city: customer.default_address.city}),
                ...(customer.default_address.province && {province: customer.default_address.province}),
                ...(customer.default_address.zip && {zip: customer.default_address.zip}),
                // ...(customer.default_address.country && {country: customer.default_address.country})
            },
            customAttributes,
            notificationPreference: {//TODO: get this from the shopify details
                preferredChannel: "EMAIL",
                allowPromotionalNotifications: true
            }
        }
        if(Object.keys(memberData?.residentialAddress ?? {}).length===0){
          delete memberData.residentialAddress;
        }
        return memberData;
        // // Construct and return the Member object
        // return new Member(
        //     regionId,
        //     cardNumber,
        //     autoGenerateCard,
        //     merchantLocationCode,
        //     firstName,
        //     lastName,
        //     preferredName,
        //     mobileNumber,
        //     email,
        //     residentialAddress,
        //     residentialAddress,
        //     customAttributes,
        //     notificationPreference
        // );
    }

    public async process(job: ShopifyWebhookRequest, organization: Organization): Promise<void> {
        logger.info("Processing customer create webhook for organization:", organization.id);

        const memberData = this._transformCustomerData(job.body, organization);
        return await ShoutoutLoyaltyService.registerLoyaltyMember(organization.shop, memberData);
    }
}

export default ShopifyCustomersCreateHandler;
