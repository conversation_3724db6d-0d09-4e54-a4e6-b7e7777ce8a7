database:
  dialect: sqlite3
  host:  book.db
  port:
  dbname: 
  username: 
  password: 
  migration: true

extension:
  master_generator: true
  cors_enabled: true
  security_enabled: true

log:
  request_log_format: ${remote_ip} ${account_name} ${uri} ${method} ${status}

staticcontents:
  path: ./public/

security:
  auth_path:
    - /api/.*
  exclude_path:
    - /swagger/.*
    - /api/auth/login$
    - /api/auth/logout$
    - /api/health$
  user_path:
    - /api/.*
  admin_path:
    - /api/.*