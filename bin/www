#!/usr/bin/env node

/**
 * Module dependencies.
 */
const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../.env')});
const appPromise = require('../app');
const debug = require('debug')('loyaltycampaignservice:server');
const http = require('http');
const config = require('../lib/config');
const logger = require('../lib/logger');
const {createTerminus} = require('@godaddy/terminus');
require('../workers/main');

const log = logger(config.logger);

appPromise.then((app) => {
    /**
     * Get port from environment and store in Express.
     */

    const port = normalizePort(process.env.PORT || '3003');

    app.set('port', port);

    /**
     * Create HTTP server.
     */

    const server = http.createServer(app);

    const healthCheckPath = `${config.api.base_path}/healthcheck`;
    const healthChecks = {};
    healthChecks[healthCheckPath] = onHealthCheck;
    createTerminus(server, {
        signal: 'SIGINT',
        healthChecks,
        onSignal
    });


    /**
     * Listen on provided port, on all network interfaces.
     */

    server.listen(port);

    server.on('error', onError);
    server.on('listening', onListening);

    function onSignal() {
        log.info('server is starting cleanup');
    }

    function onHealthCheck() {
        log.debug(`health check on ${new Date().toISOString()}`)
        // checks if the system is healthy, like the db connection is live
        // resolves, if health, rejects if not
    }


    /**
     * Normalize a port into a number, string, or false.
     */

    function normalizePort(val) {
        const port = parseInt(val, 10);

        if (isNaN(port)) {
            // named pipe
            return val;
        }

        if (port >= 0) {
            // port number
            return port;
        }

        return false;
    }

    /**
     * Event listener for HTTP server "error" event.
     */

    function onError(error) {
        if (error.syscall !== 'listen') {
            throw error;
        }

        const bind = typeof port === 'string'
            ? 'Pipe ' + port
            : 'Port ' + port;

        // handle specific listen errors with friendly messages
        switch (error.code) {
            case 'EACCES':
                console.error(bind + ' requires elevated privileges');
                process.exit(1);
                break;
            case 'EADDRINUSE':
                console.error(bind + ' is already in use');
                process.exit(1);
                break;
            default:
                throw error;
        }
    }

    /**
     * Event listener for HTTP server "listening" event.
     */

    function onListening() {
        const addr = server.address();
        const bind = typeof addr === 'string'
            ? 'pipe ' + addr
            : 'port ' + addr.port;
        debug('Listening on ' + bind);
        log.info(`server listening on ${addr.port}`);
    }
});
