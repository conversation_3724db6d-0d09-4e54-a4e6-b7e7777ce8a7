#!/usr/bin/env node

/**
 * Module dependencies.
 */
const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../.env')});
const appPromise = require('../app.points');
const debug = require('debug')('loyaltyservice:server');
const http = require('http');
const config = require('../lib/config');
const logger = require('../lib/logger');
const {createTerminus} = require('@godaddy/terminus');
const RedisConnector = require('../lib/db/connectors/RedisConnector');
const MongooseConnector = require('../lib/db/connectors/MongooseConnector');
const PostgreSQLConnector = require('../lib/db/connectors/PostgreSQLConnector');

const log = logger({...config.logger,name:"Loyalty Points Service"});

// appPromise.then((app) => {
(async()=>{
    const app=await appPromise();
    /**
     * Get port from environment and store in Express.
     */

    const port = normalizePort(process.env.PORT || '3000');

    app.set('port', port);

    /**
     * Create HTTP server.
     */

    const server = http.createServer(app);

    const healthCheckPath = `${config.api.base_path}/points/healthcheck`;
    const healthChecks = {};
    healthChecks[healthCheckPath] = onHealthCheck;
    createTerminus(server, {
        signal: 'SIGINT',
        healthChecks,
        onSignal
    });


    /**
     * Listen on provided port, on all network interfaces.
     */

    server.listen(port);

    server.on('error', onError);
    server.on('listening', onListening);

    async function onSignal() {
        log.info('server is starting cleanup');

        log.info('cleaning redis cache...');
        log.info('cleaning user cache...');
        await RedisConnector.scanAndDelete("users:*");
        log.info('cleaning modules cache...');
        await RedisConnector.scanAndDelete("groups:*");
        log.info('cleaning groups cache...');
        await RedisConnector.scanAndDelete("modules:*");

        log.info('closing redis connection...');
        await RedisConnector.closeConnection();
        log.info('closing mongo connection...')
        await MongooseConnector.close();
        log.info('closing postgres connection...')
        await PostgreSQLConnector.close();
    }

    function onHealthCheck() {
        log.debug(`health check on ${new Date().toISOString()}`)
        // checks if the system is healthy, like the db connection is live
        // resolves, if health, rejects if not
    }


    /**
     * Normalize a port into a number, string, or false.
     */

    function normalizePort(val) {
        const port = parseInt(val, 10);

        if (isNaN(port)) {
            // named pipe
            return val;
        }

        if (port >= 0) {
            // port number
            return port;
        }

        return false;
    }

    /**
     * Event listener for HTTP server "error" event.
     */

    function onError(error) {
        if (error.syscall !== 'listen') {
            throw error;
        }

        const bind = typeof port === 'string'
            ? 'Pipe ' + port
            : 'Port ' + port;

        // handle specific listen errors with friendly messages
        switch (error.code) {
            case 'EACCES':
                console.error(bind + ' requires elevated privileges');
                process.exit(1);
                break;
            case 'EADDRINUSE':
                console.error(bind + ' is already in use');
                process.exit(1);
                break;
            default:
                throw error;
        }
    }

    /**
     * Event listener for HTTP server "listening" event.
     */

    function onListening() {
        const addr = server.address();
        const bind = typeof addr === 'string'
            ? 'pipe ' + addr
            : 'port ' + addr.port;
        debug('Listening on ' + bind);
        log.info(`server listening on ${addr.port}`);
    }
})()
