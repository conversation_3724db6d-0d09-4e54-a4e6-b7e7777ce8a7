version: 0.2
env:
  parameter-store:
    GIT_USER_NAME: /shared/codeBuild/shoutout/github/user_name
    GIT_USER_TOKEN: /shared/codeBuild/shoutout/github/user_token
phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 458121188926.dkr.ecr.us-west-2.amazonaws.com
      - REPOSITORY_URI=${REPO_ECR}
      - echo "Repository url- $REPOSITORY_URI"
      - LATEST_TAG_ID=$(git describe --tags --abbrev=0)
      - echo "Latest Git tag ID:" $LATEST_TAG_ID
      - IMAGE_TAG=${LATEST_TAG_ID}
      - echo "Image tag- $IMAGE_TAG"
      - git checkout $IMAGE_TAG
      - echo "//npm.pkg.github.com/:_authToken=$NPM_PKG_TOKEN" >> .npmrc.docker
      - echo "@shoutout-labs:registry=https://npm.pkg.github.com/shoutout-labs" >> .npmrc.docker
      # Installing aws session manager plugin
      - curl "https://s3.amazonaws.com/session-manager-downloads/plugin/latest/ubuntu_64bit/session-manager-plugin.deb" -o "session-manager-plugin.deb"
      - sudo dpkg -i session-manager-plugin.deb
  build:
    commands:
      # Application
      - echo "Starting application test phase"
      - aws ec2 start-instances --instance-ids i-04ee5fb77456fe2f0 && sleep 60
      - aws s3 rm s3://so-loyalty-service-gtkgejdfde/test-results/test-results.txt # remove old test file
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline && git clone --branch $IMAGE_TAG --single-branch https://${GIT_USER_NAME}:${GIT_USER_TOKEN}@github.com/shoutout-labs/shoutout_loyalty_service.git"] && sleep 10
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && aws secretsmanager get-secret-value --secret-id shoutout-loyalty-service/env --region us-west-2 --query SecretString --output text > .env"]
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && aws secretsmanager get-secret-value --secret-id npmrc --region us-west-2 --query SecretString --output text > .npmrc"] && sleep 10
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && npm install"] && sleep 30
      #- aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && PID=$(lsof -t -i:3000) && kill -9 $PID && npm run start:prod"] && sleep 10
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && sh npm-start.sh"] && sleep 10
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && sh npm-test.sh"] && sleep 150
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && npm run start:prodpoints"] && sleep 10
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline/shoutout_loyalty_service && sh npm-test-point.sh"] && sleep 150
      - aws ssm send-command --document-name "AWS-RunShellScript" --targets "Key=instanceids,Values=i-04ee5fb77456fe2f0" --region us-west-2 --parameters commands=["cd /home/<USER>/pipeline-tests && sudo rm -rf shoutout_loyalty_service"] && sleep 10
      - aws ec2 stop-instances --instance-ids i-04ee5fb77456fe2f0 && sleep 10

      # Docker Image Build based on test result
      - aws s3 cp s3://so-loyalty-service-gtkgejdfde/test-results/test-results.txt results.txt && tail -n 10 results.txt > test-results.txt
      - aws s3 cp s3://so-loyalty-service-gtkgejdfde/test-results/test-results-point.txt results-point.txt && tail -n 10 results-point.txt > test-results-point.txt
      
      - echo Building the Docker images...
      - docker build -f Dockerfile -t loyalty-service-base:latest .
      - docker build -f DockerfileLoyalty -t $REPOSITORY_URI:latest .
      - docker build -f DockerfilePoints -t $REPOSITORY_URI:points-latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
      - docker tag $REPOSITORY_URI:points-latest $REPOSITORY_URI:points-$IMAGE_TAG
      - echo Pushing the Loyalty Docker image...
      - docker push $REPOSITORY_URI:$IMAGE_TAG    
      - echo Pushing the Loyalty Points Docker image...
      - docker push $REPOSITORY_URI:points-$IMAGE_TAG 
      - echo Build completed on `date`

      # - |
      #   FAILED_TEST_SUITES=$(tail -n 10 test-results.txt | grep -oP '(?<=Test Suites: )\d+(?= failed)')
      #   FAILED_TEST_SUITES_POINTS=$(tail -n 10 test-results-point.txt | grep -oP '(?<=Test Suites: )\d+(?= failed)')
      #   echo "Failed test suites (Loyalty): $FAILED_TEST_SUITES"
      #   echo "Failed test suites (Points): $FAILED_TEST_SUITES_POINTS"
      #   if [ "$FAILED_TEST_SUITES" -lt 5 ] && [ "$FAILED_TEST_SUITES_POINTS" -lt 5 ]; then
      #     echo "Less than 10 test suites failed in both files. Proceeding with Docker build..."
      #     echo Build started on `date`
      #     echo Building the Docker images...
      #     docker build -f Dockerfile -t loyalty-service-base:latest .
      #     docker build -f DockerfileLoyalty -t $REPOSITORY_URI:latest .
      #     docker build -f DockerfilePoints -t $REPOSITORY_URI:points-latest .
      #     docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
      #     docker tag $REPOSITORY_URI:points-latest $REPOSITORY_URI:points-$IMAGE_TAG
      #     echo Pushing the Loyalty Docker image...
      #     docker push $REPOSITORY_URI:$IMAGE_TAG    
      #     echo Pushing the Loyalty Points Docker image...
      #     docker push $REPOSITORY_URI:points-$IMAGE_TAG 
      #     echo Build completed on `date`
      #   else
      #     echo "10 or more test suites failed in either file. Skipping Docker build."
      #   fi


  # post_build:
  #   commands:
  #     - |
  #       FAILED_TEST_SUITES=$(tail -n 10 test-results.txt | grep -oP '(?<=Test Suites: )\d+(?= failed)')
  #       FAILED_TEST_SUITES_POINTS=$(tail -n 10 test-results-point.txt | grep -oP '(?<=Test Suites: )\d+(?= failed)')
  #       echo "Failed test suites (Loyalty): $FAILED_TEST_SUITES"
  #       echo "Failed test suites (Points): $FAILED_TEST_SUITES_POINTS"
  #       if [ "$FAILED_TEST_SUITES" -lt 5 ] && [ "$FAILED_TEST_SUITES_POINTS" -lt 5 ]; then
  #         echo "Less than 5 test suites failed. Proceeding with Trivy can..."
  #         wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add -
  #         apt-get install wget apt-transport-https gnupg
  #         echo deb https://aquasecurity.github.io/trivy-repo/deb bionic main | tee -a /etc/apt/sources.list.d/trivy.list
  #         apt-get update
  #         apt-get install -y trivy
  #         echo Start Trivy scan....
  #         touch shoutout_message_service_$IMAGE_TAG.txt
  #         trivy image $REPOSITORY_URI:$IMAGE_TAG >> shoutout_loyalty_service_$IMAGE_TAG.txt
  #         trivy image $REPOSITORY_URI:points-$IMAGE_TAG >> shoutout_loyalty_points_service_$IMAGE_TAG.txt
  #         aws s3 cp shoutout_loyalty_service_$IMAGE_TAG.txt s3://so-loyalty-service-gtkgejdfde/trivy/ 
  #         aws s3 cp shoutout_loyalty_points_service_$IMAGE_TAG.txt s3://so-loyalty-service-gtkgejdfde/trivy/  

  #         sed -i "s|__REPLACE_WITH_YOUR_PAT__|$GIT_TOKEN|" pr-create.sh
  #         sed -i "s|__REPLACE_WITH_TAG_ID__|$IMAGE_TAG|" pr-create.sh
  #         git clone https://suranga-jayalath:${GIT_TOKEN}@github.com/shoutout-labs/shoutout_loyalty_ecs_services_deployments.git
  #         sh ./pr-create.sh
  #         rm -rf pr-create.sh
  #       else
  #         echo "10 or more test suites failed. Skipping Trivy Scan."
  #       fi
