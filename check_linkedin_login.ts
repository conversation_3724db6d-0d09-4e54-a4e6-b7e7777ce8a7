import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

(async () => {
  const browser: Browser = await chromium.launch({ headless: false }); // So you can see what happens
  const context: BrowserContext = await browser.newContext({ storageState: 'linkedin-auth.json' });
  const page: Page = await context.newPage();
  await page.goto('https://www.linkedin.com/feed/', { waitUntil: 'domcontentloaded' });

  // The "Me" menu is only visible if you are logged in
  const isLoggedIn: boolean = await page.locator('img.global-nav__me-photo').count() > 0;

  if (isLoggedIn) {
    console.log('You are logged in to LinkedIn!');
  } else {
    console.log('You are NOT logged in to LinkedIn. Please run login.js again.');
  }

  await browser.close();
})();