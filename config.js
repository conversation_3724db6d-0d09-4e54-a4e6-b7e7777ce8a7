module.exports = {
    OTP_TOKEN_KEY: process.env.OTP_TOKEN_KEY,
    AWS_REGION_DEFAULT: process.env.AWS_REGION_DEFAULT,
    ANALYTIC_DATABASE: process.env.ANALYTIC_DATABASE,
    JWT_SECRET: process.env.JWT_SECRET,
    MONGO_URL: process.env.MONGO_URL,
    M<PERSON><PERSON>O_DB_CORE_SERVICE: process.env.MONGO_DB_CORE_SERVICE,
    MONGO_DB_LOYALTY_SERVICE: process.env.MONGO_DB_LOYALTY_SERVICE,
    SMTP_USERNAME: process.env.SMTP_USERNAME,
    SMTP_PASSWORD: process.env.SMTP_PASSWORD,
    VERIFY_TOKEN_SECRET: process.env.VERIFY_TOKEN_SECRET,
    DATA_ENCRYPTION_SECRET: process.env.DATA_ENCRYPTION_SECRET,
    OPENID_ADMIN_JWKS_URI: process.env.OPENID_ADMIN_JWKS_URI,
    OPENID_ADMIN_ISSUER: process.env.OPENID_ADMIN_ISSUER,
    OPENID_ADMIN_ACCOUNT: process.env.OPENID_ADMIN_ACCOUNT,
    KEYCLOAK_BASE_URL: process.env.KEYCLOAK_BASE_URL,
    USER_ASSETS_BUCKET: process.env.USER_ASSETS_BUCKET,
    STORAGE_SERVICE: process.env.STORAGE_SERVICE || 'S3',
    MAX_UPLOAD_FILE_SIZE: process.env.MAX_UPLOAD_FILE_SIZE || '*********',
    TEMP_FILE_UPLOAD_PATH: process.env.TEMP_FILE_UPLOAD_PATH || '/temp',
    //postgres
    SEQUELIZE_DIALECT: process.env.SEQUELIZE_DIALECT || 'postgres',
    SEQUELIZE_PORT: process.env.SEQUELIZE_PORT,
    SEQUELIZE_USER: process.env.SEQUELIZE_USER,
    SEQUELIZE_PASSWORD: process.env.SEQUELIZE_PASSWORD,
    SEQUELIZE_HOST: process.env.SEQUELIZE_HOST,
    SEQUELIZE_DB_SYNC_MODE: process.env.SEQUELIZE_DB_SYNC_MODE || 'none',
    SWAGGER_SERVER_URL: process.env.SWAGGER_SERVER_URL,
    KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
    KEYCLOAK_CLIENT_ID: process.env.KEYCLOAK_CLIENT_ID,
    KEYCLOAK_CLIENT_SECRET: process.env.KEYCLOAK_CLIENT_SECRET,
    IMAGE_DOWNLOAD_URL_EXPIRE_TIME: process.env.IMAGE_DOWNLOAD_URL_EXPIRE_TIME
        ? Number(process.env.IMAGE_DOWNLOAD_URL_EXPIRE_TIME)
        : 3600,
    LOYALTY_SERVICE_BASE_URL: process.env.LOYALTY_SERVICE_BASE_URL,
    LOYALTY_SERVICE_PUBLIC_BASE_URL: process.env.LOYALTY_SERVICE_PUBLIC_BASE_URL,
    PROVISION_ORGANIZATION: process.env.PROVISION_ORGANIZATION
        ? process.env.PROVISION_ORGANIZATION.toLowerCase().trim() === 'true'
        : false,
    SUPPORT_EMAIL: process.env.SUPPORT_EMAIL,
    WORKER_DB_REQUEST_CHUNK_SIZE: process.env.WORKER_DB_REQUEST_CHUNK_SIZE || 500,
    MAX_JOBS_IN_QUEUE_AFTER_COMPLETION: process.env.MAX_JOBS_IN_QUEUE_AFTER_COMPLETION || 1000,
    MAX_JOBS_IN_QUEUE_AFTER_FAILURE: process.env.MAX_JOBS_IN_QUEUE_AFTER_FAILURE || 1000,
    USER_PUBLIC_ASSETS_BUCKET: process.env.USER_PUBLIC_ASSETS_BUCKET,
    FILE_DOWNLOAD_BASE_URL: process.env.FILE_DOWNLOAD_BASE_URL,
    //KAFKA
    KAFKA_BROKERS: process.env.KAFKA_BROKERS ? process.env.KAFKA_BROKERS.split(',') : ['localhost:9092'],
    KAFKA_CLIENT_ID: process.env.KAFKA_CLIENT_ID || 'loyalty-service',
    KAFKA_SSL: process.env.KAFKA_SSL ? process.env.KAFKA_SSL.toLowerCase().trim() === 'true' : false,
    KAFKA_SASL_MECHANISM: process.env.KAFKA_SASL_MECHANISM || 'PLAIN',
    KAFKA_SASL_USERNAME: process.env.KAFKA_SASL_USERNAME,
    KAFKA_SASL_PASSWORD: process.env.KAFKA_SASL_PASSWORD,
    KAFKA_CONNECTION_TIMEOUT: process.env.KAFKA_CONNECTION_TIMEOUT || 10000,
    KAFKA_LOG_LEVEL: process.env.KAFKA_LOG_LEVEL || 'DEBUG',
    TRANSACTIONS_TOPIC: process.env.TRANSACTIONS_TOPIC || 'transactions-stream',
    ACTIVITIES_TOPIC: process.env.ACTIVITIES_TOPIC || 'activities-stream',
    AUTH_ACTIVITY_TOPIC: process.env.AUTH_ACTIVITY_TOPIC || 'member-log-in-topic',
    TRANSACTION_SYNC_SQS_URL: process.env.TRANSACTION_SYNC_SQS_URL,
    TRANSACTION_SYNC_SQS_WAIT_TIME: process.env.TRANSACTION_SYNC_SQS_WAIT_TIME || 20
};
