defaults: &defaults
  web:
    base_url: http://localhost:8080
  api:
    port: 3000
    base_url: http://localhost:3000
    base_path: /api/loyaltyservice
  stream:
    names:
      activities: activities_stream
  queue:
    names:
      contact_metadata: contact_metadata_queue
  openid:
    portal:
      jwks_uri: https://stagingauth.topnotch.club/auth/realms/pns-loyalty/protocol/openid-connect/certs
      issuer: https://stagingauth.topnotch.club/auth/realms/pns-loyalty
      audience: account
  images:
    category:
      branch: https://gallery.getshoutout.com/assets/icons/categories/branch.svg
      gender: https://gallery.getshoutout.com/assets/icons/categories/gender.svg
      tier: https://gallery.getshoutout.com/assets/icons/categories/other.svg
      country: https://gallery.getshoutout.com/assets/icons/categories/country.svg
      birth_day_of_month: https://gallery.getshoutout.com/assets/icons/categories/birthday.svg
      birth_month: https://gallery.getshoutout.com/assets/icons/categories/birthday.svg
      date: https://gallery.getshoutout.com/assets/icons/categories/date.svg
      address: https://gallery.getshoutout.com/assets/icons/categories/address.svg
      other: https://gallery.getshoutout.com/assets/icons/categories/other.svg
      tags: https://gallery.getshoutout.com/assets/icons/categories/tags.svg
      source: https://gallery.getshoutout.com/assets/icons/categories/source.svg
      location: https://gallery.getshoutout.com/assets/icons/categories/location.svg
  jwt:
    issuer: getshoutout.com
    audience: getshoutout.com
  email:
    host: smtp.sendgrid.net
    port: 587
    secure: false
  aws:
    document_download_url_expire_time: 3600
  swagger:
    server: http://localhost:3001/api/loyaltyservice
  logger:
    name: Loyalty Service
    level: debug
    levels:
      #trace:
      debug: STDOUT
      #info: STDOUT
      #warn: STDOUT
      #error: STDERR
      #fatal: STDERR

development:
  <<: *defaults

beta:
  <<: *defaults
  swagger:
    server: https://betaenterprise.api.topnotch.club/api/loyaltyservice
  jwt:
    issuer:
    audience:
production:
  <<: *defaults
  db:
    mysql:
      host: shoutout-do-sgp-mysql-do-user-7324450-0.b.db.ondigitalocean.com
      port: 25060
      dialect: mysql
      analytic_database: analytic_service_production
      loyalty_database: loyalty_service_production
    mongo:
      loyalty_service: loyalty_service_enterprise
      core_service: core_service_enterprise
  openid:
    admin:
      jwks_uri: https://api2.getshoutout.com/auth/realms/shoutout-enterprise-loyalty/protocol/openid-connect/certs
      issuer: https://api2.getshoutout.com/auth/realms/shoutout-enterprise-loyalty
      audience: account
    portal:
      jwks_uri: https://api2.getshoutout.com/auth/realms/pns-loyalty-portal/protocol/openid-connect/certs
      issuer: https://api2.getshoutout.com/auth/realms/pns-loyalty-portal
      audience: account
  swagger:
    server: https://enterprise.api.topnotch.club/api/loyaltyservice
  keycloak:
    base_url: https://api2.getshoutout.com/auth
  jwt:
    issuer:
    audience:
  #logger:
    #level: info
uat:
  <<: *defaults
  db:
    mysql:
      host: t-lounge-mobile-uat.cdfrizdmqonf.us-east-1.rds.amazonaws.com
      port: 3306
      dialect: mysql
      analytic_database: so_analytic_service
      loyalty_database: so_loyalty_service
    mongo:
      loyalty_service: so_loyalty_service
      core_service: so_core_service
  openid:
    admin:
      jwks_uri: https://api-uat.dilmaht-lounge.com/auth/realms/Dilmah/protocol/openid-connect/certs
      issuer: https://api-uat.dilmaht-lounge.com/auth/realms/Dilmah
      audience: account
    portal:
      jwks_uri:
      issuer:
      audience:
  swagger:
    server: https://loyaltyapi-uat.dilmaht-lounge.com/api/loyaltyservice
  keycloak:
    base_url: https://api-uat.dilmaht-lounge.com/auth
  jwt:
    issuer:
    audience:
