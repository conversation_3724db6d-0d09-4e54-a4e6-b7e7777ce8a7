# Loyalty platform development environment

## How to build


The build file is designed to create essential environment variables from the .env and .env.test files, respectively.

NOTE:

Ensure there is extra space at the bottom of each env file. Failure to do so will result in the script disregarding the last line.


- Run `./build.sh`


## Start environment

- Run `./start.sh`

This script will execute the build script prior to initiating the environment.