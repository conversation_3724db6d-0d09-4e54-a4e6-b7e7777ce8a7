#!/bin/bash
set -e

# Start MongoDB service in the background
mongod --fork --logpath /var/log/mongodb.log
# Access environment variables
database_name=$MONGO_LOYALTY_DATABASE
database_user=$MONGO_USERNAME
database_password=$MONGO_PASSWORD

# Wait for MongoDB to start
sleep 5

# Run mongo shell commands using the environment variables
mongosh admin <<EOF
use $database_name
db.createUser({
  user: "$database_user",
  pwd: "$database_password",
  roles: [
    { role: "readWrite", db: "$database_name" }
  ]
})

EOF

# Stop MongoDB service
mongod --shutdown

# Start MongoDB service without forking
mongod --bind_ip_all --replSet dbrs

# # Wait for MongoDB to start
# sleep 5

# sh /scripts/rs-init.sh