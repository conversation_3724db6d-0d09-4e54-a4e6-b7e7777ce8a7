[{"_id": {"$oid": "61ab62660f954b849de9254b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "150", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ACTIVE", "points": {"$numberDecimal": "1917915.36000000"}, "tierPoints": 20409.269999999997, "purchasesCount": 51, "purchasesValue": 958838, "tags": ["Loyalty User", "Black JS", "Black JS 2", "Black JS 3", "test 01", "test 02", "test 03", "test 04", "test 05", "cfggfgf", "<EMAIL>"], "lastSeenOn": {"$date": "2023-05-24T06:07:49.008Z"}, "registeredOn": {"$date": "2021-12-04T12:42:58.814Z"}, "firstName": "Sansa", "lastName": "<PERSON>", "preferredName": "Sans", "additionalPhoneNumbers": ["12462501234"], "countryCode": "LK", "country": "Sri Lanka", "createdBy": null, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "700895", "refName": "<PERSON><PERSON>", "notes": ""}, {"rewardId": {"$oid": "619b708839769b13f31d6dda"}, "refNumber": "12333", "refName": "ARE"}, {"rewardId": {"$oid": "619b713139769b13f31d6ddb"}, "refNumber": "Qww122332", "refName": "Sansa S"}, {"rewardId": {"$oid": "619b713f39769b13f31d6ddd"}, "refNumber": "fdsf", "refName": "f"}], "historyEvents": [{"eventDate": {"$date": "2023-04-20T11:52:44.681Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "6441278c513791d3080956d6"}}, {"eventDate": {"$date": "2023-04-20T11:31:37.997Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "64412299513791d308095634"}}, {"eventDate": {"$date": "2023-04-20T11:30:36.557Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "6441225c513791d3080955f8"}}, {"eventDate": {"$date": "2023-04-20T11:29:38.393Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "64412222513791d3080955bd"}}, {"eventDate": {"$date": "2023-04-20T11:28:57.069Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "644121f9513791d308095583"}}, {"eventDate": {"$date": "2023-04-20T11:26:38.831Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "6441216e513791d30809554a"}}, {"eventDate": {"$date": "2023-04-20T11:25:30.937Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "6441212a513791d308095512"}}, {"eventDate": {"$date": "2023-04-20T11:24:39.903Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "644120f7513791d3080954db"}}, {"eventDate": {"$date": "2023-04-20T10:12:46.993Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6441101e513791d30809548d"}}, {"eventDate": {"$date": "2023-04-17T05:12:50.979Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "643cd5528c89b4d4927930c4"}}, {"eventDate": {"$date": "2023-03-29T10:40:57.076Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "642415b98c89b4d49278d10e"}}, {"eventDate": {"$date": "2023-03-29T10:26:25.657Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "642412518c89b4d49278d032"}}, {"eventDate": {"$date": "2023-03-29T10:23:05.117Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "642411898c89b4d49278cfba"}}, {"eventDate": {"$date": "2023-03-28T10:05:31.682Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6422bbeb8c89b4d49278cb32"}}, {"eventDate": {"$date": "2023-03-28T10:03:46.862Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6422bb828c89b4d49278ca86"}}, {"eventDate": {"$date": "2023-03-28T04:36:07.555Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64226eb7d3aa515df562f435"}}, {"eventDate": {"$date": "2023-03-10T07:56:14.422Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640ae29ebf27578e25bc1c45"}}, {"eventDate": {"$date": "2023-03-08T12:30:54.497Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64087ffed93716201008d214"}}, {"eventDate": {"$date": "2023-03-08T12:28:02.016Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64087f52d93716201008d1e8"}}, {"eventDate": {"$date": "2023-03-08T12:27:38.102Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64087f3abf27578e25bc106b"}}, {"eventDate": {"$date": "2023-03-08T12:09:55.922Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64087b1329c40fcf3231c841"}}, {"eventDate": {"$date": "2023-03-08T11:52:04.593Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640876e429c40fcf3231c818"}}, {"eventDate": {"$date": "2023-03-08T11:51:10.674Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640876aebf27578e25bc0f07"}}, {"eventDate": {"$date": "2023-03-08T11:45:03.770Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6408753fbf27578e25bc0e50"}}, {"eventDate": {"$date": "2023-03-08T11:44:49.907Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64087531bf27578e25bc0e1e"}}, {"eventDate": {"$date": "2023-03-08T11:33:52.985Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640872a0bf27578e25bc0dd9"}}, {"eventDate": {"$date": "2023-03-07T11:45:20.809Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640723d040350d6e731fd611"}}, {"eventDate": {"$date": "2023-03-07T11:45:01.961Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640723bd40350d6e731fd5ef"}}, {"eventDate": {"$date": "2023-03-07T04:18:16.481Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6406bb0840350d6e731fcb0d"}}, {"eventDate": {"$date": "2023-01-31T18:47:04.815Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d96228053691806f7d6b8d"}}, {"eventDate": {"$date": "2023-01-31T18:46:25.649Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d96201053691806f7d6b52"}}, {"eventDate": {"$date": "2023-01-31T18:46:04.674Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d961ec053691806f7d6b19"}}, {"eventDate": {"$date": "2023-01-31T18:45:57.305Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d961e5053691806f7d6ae2"}}, {"eventDate": {"$date": "2023-01-31T18:45:45.070Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d961d9053691806f7d6aad"}}, {"eventDate": {"$date": "2023-01-31T18:45:19.517Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d961bf053691806f7d6a7a"}}, {"eventDate": {"$date": "2023-01-31T18:43:13.786Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d96141053691806f7d6a49"}}, {"eventDate": {"$date": "2023-01-31T13:28:51.806Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91793053691806f7d66d2"}}, {"eventDate": {"$date": "2023-01-31T12:31:03.919Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90a07053691806f7d5bf0"}}, {"eventDate": {"$date": "2023-01-31T12:30:52.508Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d909fc053691806f7d5bc5"}}, {"eventDate": {"$date": "2023-01-31T10:34:09.906Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d8eea19839dd01247d3a14"}}, {"eventDate": {"$date": "2023-01-31T09:59:33.254Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d8e685b8d1ee1fbf0ebced"}}, {"eventDate": {"$date": "2023-01-25T09:11:10.669Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0f22ebd1734863b4a605f"}}, {"eventDate": {"$date": "2023-01-25T08:56:17.737Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0eeb1bd1734863b4a5f99"}}, {"eventDate": {"$date": "2023-01-25T04:42:12.510Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0b324bd1734863b4a5de0"}}, {"eventDate": {"$date": "2023-01-25T04:42:02.550Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0b31abd1734863b4a5dba"}}, {"eventDate": {"$date": "2023-01-25T04:41:54.941Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0b312bd1734863b4a5d96"}}, {"eventDate": {"$date": "2023-01-25T04:26:39.779Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0af7fbd1734863b4a5d26"}}, {"eventDate": {"$date": "2023-01-25T03:55:59.296Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0a84fbd1734863b4a5c02"}}, {"eventDate": {"$date": "2023-01-24T12:03:55.174Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfc92bbd1734863b4a58d4"}}, {"eventDate": {"$date": "2023-01-24T11:31:17.323Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfc185bd1734863b4a5850"}}, {"eventDate": {"$date": "2023-01-24T11:22:17.612Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfbf69bd1734863b4a57ff"}}, {"eventDate": {"$date": "2023-01-24T11:21:43.478Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfbf47bd1734863b4a57ee"}}, {"eventDate": {"$date": "2023-01-24T11:16:13.547Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfbdfdbd1734863b4a57df"}}, {"eventDate": {"$date": "2023-01-24T11:16:01.701Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfbdf1bd1734863b4a57d2"}}, {"eventDate": {"$date": "2023-01-24T10:39:21.739Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfb559bd1734863b4a57c0"}}, {"eventDate": {"$date": "2023-01-24T10:38:59.702Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfb543bd1734863b4a57b7"}}, {"eventDate": {"$date": "2023-01-24T10:38:47.939Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cfb537bd1734863b4a5781"}}, {"eventDate": {"$date": "2022-11-23T17:32:18.956Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e592210fe45559197f92b"}}], "createdOn": {"$date": "2021-12-04T12:43:18.354Z"}, "updatedOn": {"$date": "2023-06-16T09:19:33.525Z"}, "__v": 0, "portalMetadata": {"username": "<EMAIL>", "userId": "e22fb458-85ae-4e6f-ae08-437e42f8c0cf", "platforms": [], "lastAccessedOn": {"$date": "2021-12-04T12:43:21.334Z"}}, "updatedBy": null, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-06-21T11:20:43.127Z"}, "tierJobId": {"$oid": "62b1a98adc1e4feb69f2269b"}}, "affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d87089fa6ab294d506"}, "joinedDate": {"$date": "2023-03-07T04:18:14.909Z"}, "expiryDate": null}, "notificationPreference": {"preferredChannel": "EMAIL_AND_MOBILE", "allowPromotionalNotifications": true}, "profilePicture": "https://d1umi9h6hi9bqe.cloudfront.net/images/9b9b5f00-9e3c-11ec-8fc4-5942336960b6", "pointsExpireOn": {"$date": "2025-10-24T00:00:00.000Z"}, "pointsToExpire": [{"pointsToExpire": 100, "pointsExpireOn": {"$date": "2025-10-24T00:00:00.000Z"}}, {"pointsToExpire": 50, "pointsExpireOn": {"$date": "2025-10-24T00:00:00.000Z"}}], "companyName": "Technical ", "occupation": "-", "lastTransactionOn": {"$date": "2023-06-16T09:19:33.246Z"}, "cardNumber": "42100000962", "lastTransactionLocation": {"$oid": "632b5af4673fc768fb3b97df"}, "cardReplacementCount": 19, "email": "<EMAIL>", "mobileNumber": "12462501234", "gender": "FEMALE", "birthDate": {"$date": "2023-03-01T00:00:00.000Z"}, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "11111111111111111"}], "memberInsight": {"customerLifeTimeValue": 515340.945}, "pointsDecimal": {"$numberDecimal": "253.52"}, "pointsLegacy": 1917327.3599999999}, {"_id": {"$oid": "6340062d7a1ed3162d274146"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "308", "profilePicture": "https://d1umi9h6hi9bqe.cloudfront.net/images/cb6cd2a0-462e-11ed-ad70-f7757d581154", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "CHARITY", "status": "ACTIVE", "description": "Charity", "points": 3752, "tierPoints": 130, "purchasesCount": 1, "purchasesValue": 1000, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-07T10:57:49.658Z"}, "preferredName": "Charity", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "residentialAddress": {"line1": "123", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-10-07T10:57:49.658Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-07T10:57:49.658Z"}}, "cardReplacementCount": 1, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "2009001", "refName": "Charity Redemption", "notes": "Test"}], "historyEvents": [{"eventDate": {"$date": "2022-11-23T18:29:51.227Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e669f4c7f3d6191858e37"}}], "createdOn": {"$date": "2022-10-07T10:57:49.880Z"}, "updatedOn": {"$date": "2023-04-28T02:31:02.644Z"}, "__v": 0, "cardNumber": "42100000802", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-28T02:31:02.519Z"}, "pointsToExpire": 0, "pointsLegacy": 3752}, {"_id": {"$oid": "6345bdd87a1ed3162d278542"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "309", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "type": "PRIMARY", "status": "ACTIVE", "points": 13, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "*********"}], "tierPoints": 15, "purchasesCount": 5, "purchasesValue": 4600, "tags": ["Loyalty User", "Gongalegoda Banda"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-11T19:02:47.845Z"}, "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON>", "mobileNumber": "94778845633", "additionalPhoneNumbers": [], "companyName": "ShoutOUT Labs", "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "1985-10-02T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "410/33,<PERSON><PERSON><PERSON><PERSON>,", "line2": "Colombo 07", "city": "Colombo", "stateOrProvince": "Ampara District", "zipOrPostcode": "00700"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d87089fa6ab294d506"}, "joinedDate": {"$date": "2023-03-07T04:18:14.909Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-11T19:02:47.845Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "619b713839769b13f31d6ddc"}, "refNumber": "100022", "refName": "<PERSON><PERSON>", "notes": "test"}], "historyEvents": [{"eventDate": {"$date": "2023-04-28T02:33:21.294Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": null, "_id": {"$oid": "644b307151337a3cd2a54e50"}}, {"eventDate": {"$date": "2023-03-09T12:36:20.739Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6409d2c4bf27578e25bc1786"}}, {"eventDate": {"$date": "2023-02-08T06:23:16.695Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63e33fd4053691806f7d93a8"}}, {"eventDate": {"$date": "2023-02-08T06:21:56.112Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63e33f84053691806f7d92fd"}}, {"eventDate": {"$date": "2023-02-06T11:58:13.703Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": null, "_id": {"$oid": "63e0eb55053691806f7d8b21"}}, {"eventDate": {"$date": "2023-01-30T09:38:38.153Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": null, "_id": {"$oid": "63d79020524e6f6c9eb8f796"}}, {"eventDate": {"$date": "2023-01-30T09:31:44.193Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": null, "_id": {"$oid": "63d78e80bb452dc5c5a94092"}}, {"eventDate": {"$date": "2023-01-30T09:29:24.922Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": null, "_id": {"$oid": "63d78df57968bac5585f2c57"}}, {"eventDate": {"$date": "2023-01-27T09:25:22.880Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": null, "_id": {"$oid": "63d39882bd1734863b4a7c62"}}, {"eventDate": {"$date": "2023-01-27T09:22:49.405Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": null, "_id": {"$oid": "63d397e9bd1734863b4a7c20"}}, {"eventDate": {"$date": "2023-01-25T04:21:30.124Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d0ae4abd1734863b4a5cbe"}}, {"eventDate": {"$date": "2022-12-12T11:08:41.067Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63970bb9cbe16cb2a9fafa99"}}], "createdOn": {"$date": "2022-10-11T19:02:48.067Z"}, "updatedOn": {"$date": "2023-06-13T19:23:45.448Z"}, "__v": 0, "cardNumber": "55555555412223", "updatedBy": null, "lastTransactionOn": {"$date": "2023-06-13T19:23:45.448Z"}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "pointsToExpire": [0], "pointsLegacy": 0}, {"_id": {"$oid": "634d80b97a1ed3162d27b904"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "310", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "********"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-17T16:20:09.748Z"}, "firstName": "<PERSON>han", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON>", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-10-03T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "1233", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-10-17T16:20:09.748Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-17T16:20:09.748Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "historyEvents": [{"eventDate": {"$date": "2022-10-17T17:53:36.352Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "634d96a07a1ed3162d27ba15"}}], "createdOn": {"$date": "2022-10-17T16:20:10.059Z"}, "updatedOn": {"$date": "2022-10-17T17:53:36.352Z"}, "__v": 0, "cardNumber": "42100000584", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "634d94367a1ed3162d27b9ba"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "311", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "1234567"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-17T17:43:18.466Z"}, "firstName": "<PERSON>han", "lastName": "sksk", "preferredName": "<PERSON>han sksk", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-10-10T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "q2345", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-10-17T17:43:18.466Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-17T17:43:18.466Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "historyEvents": [{"eventDate": {"$date": "2022-10-17T17:53:49.549Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "634d96ad7a1ed3162d27ba2d"}}], "createdOn": {"$date": "2022-10-17T17:43:18.685Z"}, "updatedOn": {"$date": "2022-10-17T17:53:49.549Z"}, "__v": 0, "cardNumber": "42100000585", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "634d95167a1ed3162d27b9e2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "312", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "123456"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-17T17:47:02.530Z"}, "firstName": "<PERSON>han", "lastName": "omom", "preferredName": "<PERSON>han omom", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-10-02T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "2345", "city": "Asadabad"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-10-17T17:47:02.530Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-17T17:47:02.530Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "historyEvents": [{"eventDate": {"$date": "2022-10-17T17:53:58.126Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "634d96b67a1ed3162d27ba45"}}], "createdOn": {"$date": "2022-10-17T17:47:02.752Z"}, "updatedOn": {"$date": "2022-10-17T17:53:58.127Z"}, "__v": 0, "cardNumber": "42100000583", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "634d97347a1ed3162d27ba71"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "313", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": {"$oid": "61ab62660f954b849de9254b"}, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "type": "SECONDARY", "status": "ACTIVE", "points": {"$numberDecimal": "235"}, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "123543"}], "tierPoints": 2399, "purchasesCount": 3, "purchasesValue": 690, "tags": ["Loyalty User", "Black JS", "Black JS 2", "Black JS 3", "test 01", "test 02", "test 03", "test 04", "test 05", "cfggfgf"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-17T17:56:04.054Z"}, "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON><PERSON>", "mobileNumber": "15623225489", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-10-11T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "1234", "city": "Greenland", "stateOrProvince": "<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d87089fa6ab294d506"}, "joinedDate": {"$date": "2023-03-09T10:29:01.302Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-17T17:56:04.054Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "historyEvents": [{"eventDate": {"$date": "2023-04-28T17:18:23.795Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "644bffdf51337a3cd2a55983"}}, {"eventDate": {"$date": "2023-04-28T17:17:02.291Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "644bff8e51337a3cd2a5594f"}}, {"eventDate": {"$date": "2023-04-25T12:44:08.739Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6447cb18513791d30809638e"}}, {"eventDate": {"$date": "2023-03-09T10:29:02.004Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6409b4eebf27578e25bc1577"}}, {"eventDate": {"$date": "2023-03-08T11:59:27.408Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6408789fbf27578e25bc0fb6"}}, {"eventDate": {"$date": "2023-02-28T05:32:35.322Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fd91f31389164d2807a5e9"}}, {"eventDate": {"$date": "2023-02-28T04:48:10.407Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fd878a113b13145517da8e"}}, {"eventDate": {"$date": "2023-02-28T04:45:22.789Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fd86e21389164d2807a3cc"}}, {"eventDate": {"$date": "2023-02-28T04:45:10.289Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fd86d61389164d2807a394"}}, {"eventDate": {"$date": "2023-02-27T13:23:18.682Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fcaec6dc3218e907b36fa6"}}, {"eventDate": {"$date": "2023-02-27T13:03:56.069Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fcaa3c268fe8948ee71c3a"}}, {"eventDate": {"$date": "2023-02-27T13:03:14.132Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fcaa12268fe8948ee71c10"}}, {"eventDate": {"$date": "2023-02-27T13:01:11.362Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca99779949c40d6eb606e"}}, {"eventDate": {"$date": "2023-02-27T12:53:51.398Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca7df79949c40d6eb6046"}}, {"eventDate": {"$date": "2023-02-27T12:50:17.385Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca70979949c40d6eb601f"}}, {"eventDate": {"$date": "2023-02-27T12:46:58.192Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca64279949c40d6eb5ff9"}}, {"eventDate": {"$date": "2023-02-27T12:45:01.801Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca5cde7cafdfff5f65d8d"}}, {"eventDate": {"$date": "2023-02-27T12:26:42.982Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca1827c5b3e53b4280c07"}}, {"eventDate": {"$date": "2023-02-27T12:24:41.544Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca1091389164d2807a0e5"}}, {"eventDate": {"$date": "2023-02-27T12:24:39.961Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fca1071389164d2807a0c4"}}, {"eventDate": {"$date": "2023-02-27T10:38:21.431Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc881dc4f572ccb06e82ec"}}, {"eventDate": {"$date": "2023-02-27T10:34:49.275Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc8749c4f572ccb06e82cc"}}, {"eventDate": {"$date": "2023-02-27T10:34:17.245Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc8729cbf7e49fc476d579"}}, {"eventDate": {"$date": "2023-02-27T10:34:16.027Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc8728cbf7e49fc476d55b"}}, {"eventDate": {"$date": "2023-02-27T10:03:27.044Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7fefc4f572ccb06e82a2"}}, {"eventDate": {"$date": "2023-02-27T10:01:04.343Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7f60787549284721c3da"}}, {"eventDate": {"$date": "2023-02-27T09:58:36.165Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7ecc78101e6da7ef75b5"}}, {"eventDate": {"$date": "2023-02-27T09:56:45.070Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7e5daf5abc5c6e2b40a8"}}, {"eventDate": {"$date": "2023-02-27T09:56:30.019Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7e4ecbf7e49fc476d4f1"}}, {"eventDate": {"$date": "2023-02-27T09:49:27.554Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7ca7cffd47c54659d0e6"}}, {"eventDate": {"$date": "2023-02-27T09:49:01.719Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7c8dcbf7e49fc476d491"}}, {"eventDate": {"$date": "2023-02-27T09:43:00.506Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7b24cffd47c54659d0d0"}}, {"eventDate": {"$date": "2023-02-27T09:42:46.799Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7b16cbf7e49fc476d3ed"}}, {"eventDate": {"$date": "2023-02-27T09:39:03.343Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7a37a9b8b9c83177cc9e"}}, {"eventDate": {"$date": "2023-02-27T09:38:18.808Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7a0acbf7e49fc476d391"}}, {"eventDate": {"$date": "2023-02-27T09:35:24.383Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc795ca9b8b9c83177cc8d"}}, {"eventDate": {"$date": "2023-02-27T09:34:58.944Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7943a9b8b9c83177cc7d"}}, {"eventDate": {"$date": "2023-02-27T09:34:35.188Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc792bcbf7e49fc476d2f0"}}, {"eventDate": {"$date": "2023-02-27T09:34:33.833Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7929cbf7e49fc476d2e1"}}, {"eventDate": {"$date": "2023-02-27T09:34:28.005Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7924cbf7e49fc476d2c8"}}, {"eventDate": {"$date": "2023-02-27T09:34:22.468Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc791ecbf7e49fc476d2b0"}}, {"eventDate": {"$date": "2023-02-27T09:31:12.095Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7860cbf7e49fc476d299"}}, {"eventDate": {"$date": "2023-02-27T09:26:45.133Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc7755cbf7e49fc476d245"}}, {"eventDate": {"$date": "2023-02-27T08:27:02.969Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc6956cbf7e49fc476d1de"}}, {"eventDate": {"$date": "2023-02-27T08:26:46.111Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc6946cbf7e49fc476d1ca"}}, {"eventDate": {"$date": "2023-02-27T04:43:41.119Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc34fdcbf7e49fc476d008"}}, {"eventDate": {"$date": "2023-02-27T04:43:39.902Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc34fbcbf7e49fc476d001"}}, {"eventDate": {"$date": "2023-01-30T09:30:38.712Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": null, "_id": {"$oid": "63d78e3ebb452dc5c5a94011"}}, {"eventDate": {"$date": "2023-01-30T09:20:42.188Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": null, "_id": {"$oid": "63d78beae4e783703034d35a"}}], "createdOn": {"$date": "2022-10-17T17:56:04.276Z"}, "updatedOn": {"$date": "2023-06-16T09:19:30.230Z"}, "__v": 0, "cardNumber": "42100000581", "updatedBy": null, "pointsToExpire": [0], "lastTransactionLocation": {"$oid": "632b5af4673fc768fb3b97df"}, "lastTransactionOn": {"$date": "2023-06-16T09:19:29.947Z"}, "portalMetadata": {"username": "<EMAIL>", "userId": "1ff45059-0f48-4fcc-9c58-082017999313", "platforms": [], "lastAccessedOn": {"$date": "2023-04-28T17:30:07.052Z"}}, "pointsLegacy": 235}, {"_id": {"$oid": "6357ddee7a1ed3162d2843a0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "314", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "635120b27a1ed3162d2806f5"}, "type": "PRIMARY", "status": "ACTIVE", "points": 1797, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "19460939342"}], "tierPoints": 3032, "purchasesCount": 5, "purchasesValue": 9000, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-25T13:00:30.336Z"}, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "preferredName": "<PERSON>", "mobileNumber": "12462455434", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": {"$numberLong": "-733104000000"}}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Greenland"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-10-25T13:00:30.336Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-25T13:00:30.336Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "1234432332", "refName": "<PERSON>", "notes": "Test"}, {"rewardId": {"$oid": "619b713f39769b13f31d6ddd"}, "refNumber": "sdf", "refName": "dfff", "notes": "Rest"}], "historyEvents": [{"eventDate": {"$date": "2023-01-31T13:32:07.101Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91857053691806f7d671e"}}], "createdOn": {"$date": "2022-10-25T13:00:30.567Z"}, "updatedOn": {"$date": "2023-04-19T15:42:14.364Z"}, "__v": 0, "cardNumber": null, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionOn": {"$date": "2023-01-31T13:29:31.314Z"}, "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "pointsToExpire": [], "pointsLegacy": 1797}, {"_id": {"$oid": "6358fe007a1ed3162d285f99"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "315", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "635120f07a1ed3162d2806ff"}, "type": "PRIMARY", "status": "ACTIVE", "points": 1500, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "test43434"}], "tierPoints": 169, "purchasesCount": 1, "purchasesValue": 200, "tags": ["Loyalty user"], "lastSeenOn": {"$date": "2022-09-30T08:07:50.764Z"}, "registeredOn": {"$date": "2022-10-26T09:29:36.020Z"}, "firstName": "Test ", "lastName": "Loca", "preferredName": "Test  Loca", "mobileNumber": "12462554323", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "1978-10-17T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "32", "city": "Bridgetown"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d87089fa6ab294d506"}, "joinedDate": {"$date": "2023-03-10T09:04:55.160Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-10-26T09:29:36.020Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "kl", "refName": "Test  Loca", "notes": ""}, {"rewardId": {"$oid": "619b713839769b13f31d6ddc"}, "refNumber": "P33333", "refName": "Test P 3"}], "historyEvents": [{"eventDate": {"$date": "2023-03-10T10:12:04.245Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640b0274bf27578e25bc1e2b"}}, {"eventDate": {"$date": "2023-03-10T09:04:57.914Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640af2ba833e3fa226b64450"}}, {"eventDate": {"$date": "2023-03-10T04:39:42.133Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640ab48ebf27578e25bc1ad9"}}, {"eventDate": {"$date": "2023-03-10T04:39:02.850Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640ab46793fc781484f716d0"}}, {"eventDate": {"$date": "2023-03-10T04:37:02.467Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640ab3eebf27578e25bc1a44"}}, {"eventDate": {"$date": "2023-03-10T04:36:09.009Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640ab3b993fc781484f716bb"}}, {"eventDate": {"$date": "2023-03-10T04:17:06.294Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640aaf42bf27578e25bc197b"}}, {"eventDate": {"$date": "2023-03-10T04:12:33.102Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "640aae31bf27578e25bc1917"}}, {"eventDate": {"$date": "2023-01-31T13:02:15.238Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91157053691806f7d5ff9"}}, {"eventDate": {"$date": "2023-01-26T12:06:51.398Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": null, "_id": {"$oid": "63d26cdbbd1734863b4a7488"}}, {"eventDate": {"$date": "2023-01-20T18:07:55.725Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cad87bbd1734863b4a4771"}}, {"eventDate": {"$date": "2023-01-20T18:07:41.535Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63cad86dbd1734863b4a4762"}}], "createdOn": {"$date": "2022-10-26T09:29:36.243Z"}, "updatedOn": {"$date": "2023-05-29T09:44:57.959Z"}, "__v": 0, "cardNumber": "42100000576", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-05-29T09:44:57.849Z"}, "pointsToExpire": [], "pointsLegacy": 1500}, {"_id": {"$oid": "6363958fd1f2bee4c5ec4685"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "316", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": {"$oid": "6372135b5777af82734b8e40"}, "merchantLocationId": {"$oid": "63511d337a1ed3162d280624"}, "type": "SECONDARY", "status": "ACTIVE", "points": -800, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "1452687996"}], "tierPoints": 500, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-01T06:46:52.075Z"}, "registeredOn": {"$date": "2022-11-03T10:18:55.411Z"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "preferredName": "<PERSON>", "mobileNumber": "12462565532", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": {"$numberLong": "-5011200000"}}, "gender": "MALE", "residentialAddress": {"line1": "45", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "joinedDate": {"$date": "2023-01-04T09:03:12.449Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-03T10:18:55.411Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "90021", "refName": "<PERSON>", "notes": "Test"}], "historyEvents": [{"eventDate": {"$date": "2023-01-04T09:03:12.952Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b540d06185525cf29e651f"}}, {"eventDate": {"$date": "2023-01-04T08:56:55.741Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b53f576185525cf29e6402"}}, {"eventDate": {"$date": "2023-01-04T08:55:06.409Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b53eea6185525cf29e6387"}}, {"eventDate": {"$date": "2023-01-04T08:54:17.703Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b53eb96185525cf29e62f8"}}, {"eventDate": {"$date": "2023-01-04T08:46:24.721Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b53ce06185525cf29e624a"}}, {"eventDate": {"$date": "2023-01-04T08:45:27.385Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b53ca76185525cf29e6220"}}, {"eventDate": {"$date": "2023-01-04T08:41:34.785Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b53bbe6185525cf29e6186"}}, {"eventDate": {"$date": "2023-01-04T05:29:12.049Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b50ea86185525cf29e6122"}}, {"eventDate": {"$date": "2022-11-23T17:55:43.530Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e5e9f6f6a6aa0f16224c3"}}], "createdOn": {"$date": "2022-11-03T10:18:55.671Z"}, "updatedOn": {"$date": "2023-01-09T17:40:18.686Z"}, "__v": 0, "cardNumber": "49100001203", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "632b5af4673fc768fb3b97df"}, "lastTransactionOn": {"$date": "2023-01-04T09:12:26.617Z"}, "pointsToExpire": [0], "pointsLegacy": -800}, {"_id": {"$oid": "6364a8a9604849e367695a03"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "317", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "635120f07a1ed3162d2806ff"}, "type": "PRIMARY", "status": "ACTIVE", "points": 77, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "125548763"}], "tierPoints": 227, "purchasesCount": 4, "purchasesValue": 8000, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-03T14:36:30.966Z"}, "registeredOn": {"$date": "2022-11-04T05:52:41.208Z"}, "firstName": "<PERSON>", "lastName": "Sansa Seco", "preferredName": "Stark Sansa Seco", "mobileNumber": "5923382286", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2003-11-19T00:00:00.000Z"}, "gender": "FEMALE", "residentialAddress": {"line1": "22", "city": "Crane"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-12-12T14:41:44.363Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-04T05:52:41.208Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "78988", "refName": "Secondary Stark"}, {"rewardId": {"$oid": "619b713139769b13f31d6ddb"}, "refNumber": "1234", "refName": "T2"}, {"rewardId": {"$oid": "619b713f39769b13f31d6ddd"}, "refNumber": "342", "refName": "fffds"}, {"rewardId": {"$oid": "619b708839769b13f31d6dda"}, "refNumber": "97883", "refName": "Stark Sansa Seco", "notes": "Test"}, {"rewardId": {"$oid": "619b713839769b13f31d6ddc"}, "refNumber": "23321212", "refName": "Sasr"}], "historyEvents": [{"eventDate": {"$date": "2023-01-31T13:01:46.926Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9113b053691806f7d5fb8"}}, {"eventDate": {"$date": "2023-01-31T12:55:34.473Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90fc6053691806f7d5f24"}}, {"eventDate": {"$date": "2023-01-31T12:55:05.203Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90fa9053691806f7d5ed9"}}, {"eventDate": {"$date": "2023-01-31T12:54:52.527Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90f9c053691806f7d5e90"}}, {"eventDate": {"$date": "2023-01-31T12:53:04.114Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90f30053691806f7d5e49"}}, {"eventDate": {"$date": "2023-01-31T12:51:19.817Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90ec7053691806f7d5e04"}}, {"eventDate": {"$date": "2023-01-31T12:51:02.902Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90eb6053691806f7d5dc1"}}, {"eventDate": {"$date": "2023-01-31T12:40:33.525Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90c41053691806f7d5cd7"}}, {"eventDate": {"$date": "2023-01-31T12:39:49.923Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d90c15053691806f7d5c83"}}, {"eventDate": {"$date": "2022-12-12T16:06:28.782Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "639751841e27b2070cc919d5"}}, {"eventDate": {"$date": "2022-12-12T14:41:44.875Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63973da812ff56a7d73adead"}}, {"eventDate": {"$date": "2022-12-12T14:32:06.139Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63973b66dc59d8c425f0ab19"}}, {"eventDate": {"$date": "2022-12-12T10:55:05.831Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63970889cbe16cb2a9fafa12"}}, {"eventDate": {"$date": "2022-12-10T08:52:46.342Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "639448df3d7faef85ecb88e1"}}, {"eventDate": {"$date": "2022-12-10T08:50:41.967Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63944861cbe16cb2a9faf1c1"}}, {"eventDate": {"$date": "2022-12-10T07:23:59.554Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "639434128416dad6e59e69ad"}}, {"eventDate": {"$date": "2022-12-10T06:42:08.371Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63942a40cbe16cb2a9faf0cb"}}, {"eventDate": {"$date": "2022-12-10T06:33:04.648Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63942820a50c2cd3c8c27930"}}, {"eventDate": {"$date": "2022-12-10T06:31:05.811Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "639427a9cbe16cb2a9faef39"}}, {"eventDate": {"$date": "2022-12-10T05:49:26.665Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63941deaa50c2cd3c8c278d8"}}, {"eventDate": {"$date": "2022-12-10T05:47:37.506Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63941d79cbe16cb2a9faeec3"}}, {"eventDate": {"$date": "2022-12-10T05:39:31.133Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63941b93a50c2cd3c8c2789a"}}, {"eventDate": {"$date": "2022-12-10T05:37:15.587Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63941b0bcbe16cb2a9faee14"}}, {"eventDate": {"$date": "2022-12-10T05:27:16.296Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "639418b4a50c2cd3c8c27852"}}, {"eventDate": {"$date": "2022-12-09T23:40:24.465Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6393c768cbe16cb2a9faecb3"}}, {"eventDate": {"$date": "2022-12-09T23:32:46.004Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6393c59ecbe16cb2a9faec0a"}}, {"eventDate": {"$date": "2022-12-09T23:12:17.423Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6393c0d1cbe16cb2a9faeb38"}}, {"eventDate": {"$date": "2022-12-09T11:15:09.913Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "639318bdcbe16cb2a9fae5d2"}}, {"eventDate": {"$date": "2022-12-09T11:08:29.413Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6393172dcbe16cb2a9fae54c"}}, {"eventDate": {"$date": "2022-12-08T14:34:15.895Z"}, "eventDetails": "Type changed from SECONDARY to PRIMARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6391f605c9b883b383c255b6"}}, {"eventDate": {"$date": "2022-11-23T17:54:02.545Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e5e3a10fe45559197fa77"}}, {"eventDate": {"$date": "2022-11-04T10:46:50.805Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364ed9a604849e367695e8c"}}, {"eventDate": {"$date": "2022-11-04T10:45:12.320Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364ed38604849e367695e7d"}}, {"eventDate": {"$date": "2022-11-04T10:02:22.678Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364e32e604849e367695dff"}}, {"eventDate": {"$date": "2022-11-04T10:02:15.548Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364e327604849e367695df4"}}, {"eventDate": {"$date": "2022-11-04T09:31:40.608Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364dbfc604849e367695d53"}}, {"eventDate": {"$date": "2022-11-04T09:31:30.970Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364dbf2604849e367695d4c"}}, {"eventDate": {"$date": "2022-11-04T09:22:52.813Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6364d9ec604849e367695c9c"}}], "createdOn": {"$date": "2022-11-04T05:52:41.459Z"}, "updatedOn": {"$date": "2023-05-15T10:56:24.347Z"}, "__v": 0, "cardNumber": "49100001199", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "632b5af4673fc768fb3b97df"}, "lastTransactionOn": {"$date": "2023-05-15T10:56:24.218Z"}, "pointsToExpire": [], "pointsLegacy": 77}, {"_id": {"$oid": "636505db604849e367696713"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "318", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ACTIVE", "points": 1732, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "1234"}], "tierPoints": 1720, "purchasesCount": 1, "purchasesValue": 1000, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-03T14:36:30.966Z"}, "registeredOn": {"$date": "2022-11-04T12:30:19.058Z"}, "firstName": "<PERSON>han", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON>", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "j<PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-11-17T04:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "123", "city": "Holetown", "stateOrProvince": "<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "membershipId": "ASDASD52465", "joinedDate": {"$date": "2022-11-09T08:55:52.566Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-04T12:30:19.058Z"}}, "cardReplacementCount": 2, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "rewardMetadata": [], "historyEvents": [{"eventDate": {"$date": "2022-11-14T10:03:06.587Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6372125a5777af82734b8df0"}}, {"eventDate": {"$date": "2022-11-14T10:02:39.512Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6372123f5777af82734b8de9"}}, {"eventDate": {"$date": "2022-11-09T08:55:53.900Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "636b6b19cc50658c82904342"}}], "createdOn": {"$date": "2022-11-04T12:30:19.283Z"}, "updatedOn": {"$date": "2023-01-20T10:01:25.262Z"}, "__v": 0, "cardNumber": null, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "lastTransactionOn": {"$date": "2023-01-20T10:01:25.261Z"}, "pointsToExpire": [], "portalMetadata": {"username": "j<PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "userId": "f01db781-de10-4d6e-9638-acf516833ed9", "platforms": [], "lastAccessedOn": {"$date": "2023-01-18T10:58:52.768Z"}}, "pointsLegacy": 1732}, {"_id": {"$oid": "636c60d4a7f36e688633f603"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "319", "regionId": {"$oid": "617ee8fcc6350cdeab2403fb"}, "parentMemberId": null, "merchantLocationId": {"$oid": "6364d5cfb0390000860056c3"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "864567890V"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2022-11-08T11:53:16.062Z"}, "registeredOn": {"$date": "2022-11-08T11:53:16.062Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "94776426789", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": true, "isValidMobileNumber": false, "birthDate": {"$date": "1995-11-10T00:00:00.000Z"}, "gender": "FEMALE", "residentialAddress": {"line1": "12..test road", "city": "Colombo", "stateOrProvince": "Western Province"}, "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": null, "joinedDate": {"$date": "2022-11-10T02:24:20.372Z"}, "expiryDate": null}, "tier": {"lastUpdatedOn": {"$date": "2022-11-10T02:24:20.372Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "rewardMetadata": [], "historyEvents": [], "createdOn": {"$date": "2022-11-10T02:24:20.377Z"}, "updatedOn": {"$date": "2022-11-10T02:24:25.538Z"}, "__v": 0, "portalMetadata": {"username": "<EMAIL>", "userId": "ba5994a5-c0d9-4a04-a833-f66cebef6b9b", "platforms": [], "lastAccessedOn": {"$date": "2022-11-10T02:24:24.175Z"}}, "updatedBy": null, "cardNumber": "49200000003", "pointsLegacy": 0}, {"_id": {"$oid": "6372135b5777af82734b8e40"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "320", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ACTIVE", "points": 230, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "123321"}], "tierPoints": 130, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-11T11:20:57.178Z"}, "registeredOn": {"$date": "2022-11-14T10:07:23.508Z"}, "firstName": "Humungosaur", "lastName": "lol", "preferredName": "Humungosaur lol", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "jaya<PERSON><PERSON>@gmail.com", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-11-19T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "k", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "joinedDate": {"$date": "2023-01-04T05:18:49.784Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-14T10:07:23.508Z"}}, "cardReplacementCount": 2, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "619b713139769b13f31d6ddb"}, "refNumber": "11221", "refName": "Partner 2 Test2221212"}, {"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "111113", "refName": "fsdf"}, {"rewardId": {"$oid": "619b713f39769b13f31d6ddd"}, "refNumber": "333333", "refName": "fffffff"}, {"rewardId": {"$oid": "619b708839769b13f31d6dda"}, "refNumber": "11221212", "refName": "dasd"}], "historyEvents": [{"eventDate": {"$date": "2023-01-31T13:26:38.770Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9170e053691806f7d6645"}}, {"eventDate": {"$date": "2023-01-31T13:26:33.495Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91709053691806f7d65f6"}}, {"eventDate": {"$date": "2023-01-31T13:26:23.012Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d916ff053691806f7d65a9"}}, {"eventDate": {"$date": "2023-01-31T13:26:12.603Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d916f4053691806f7d655e"}}, {"eventDate": {"$date": "2023-01-31T13:26:03.627Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d916eb053691806f7d6515"}}, {"eventDate": {"$date": "2023-01-31T13:24:56.527Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d916a8053691806f7d64ce"}}, {"eventDate": {"$date": "2023-01-31T13:24:42.664Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9169a053691806f7d6489"}}, {"eventDate": {"$date": "2023-01-31T13:24:11.761Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9167b053691806f7d6446"}}, {"eventDate": {"$date": "2023-01-31T13:24:03.703Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91673053691806f7d6405"}}, {"eventDate": {"$date": "2023-01-31T13:23:27.308Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9164f053691806f7d63c6"}}, {"eventDate": {"$date": "2023-01-31T13:22:39.927Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9161f053691806f7d6389"}}, {"eventDate": {"$date": "2023-01-31T13:22:24.741Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91610053691806f7d634e"}}, {"eventDate": {"$date": "2023-01-31T13:21:44.328Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d915e8053691806f7d6315"}}, {"eventDate": {"$date": "2023-01-31T13:21:31.675Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d915db053691806f7d62de"}}, {"eventDate": {"$date": "2023-01-31T13:20:30.428Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9159e053691806f7d62a9"}}, {"eventDate": {"$date": "2023-01-31T13:20:22.586Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91596053691806f7d6276"}}, {"eventDate": {"$date": "2023-01-31T13:19:52.362Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91578053691806f7d6245"}}, {"eventDate": {"$date": "2023-01-31T13:19:37.617Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91569053691806f7d6216"}}, {"eventDate": {"$date": "2023-01-31T13:18:47.401Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91537053691806f7d61e9"}}, {"eventDate": {"$date": "2023-01-31T13:18:35.082Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9152b053691806f7d61be"}}, {"eventDate": {"$date": "2023-01-31T13:07:34.798Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91296053691806f7d6159"}}, {"eventDate": {"$date": "2023-01-31T13:06:17.262Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91249053691806f7d6132"}}, {"eventDate": {"$date": "2023-01-31T13:06:11.500Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91243053691806f7d610d"}}, {"eventDate": {"$date": "2023-01-31T13:06:04.433Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9123c053691806f7d60ea"}}, {"eventDate": {"$date": "2023-01-31T13:05:51.411Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d9122f053691806f7d60c9"}}, {"eventDate": {"$date": "2023-01-31T13:05:25.095Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91215053691806f7d60aa"}}, {"eventDate": {"$date": "2023-01-31T13:04:03.435Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d911c3053691806f7d608d"}}, {"eventDate": {"$date": "2023-01-31T13:03:52.198Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d911b8053691806f7d6072"}}, {"eventDate": {"$date": "2023-01-31T13:03:40.774Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d911ac053691806f7d6059"}}, {"eventDate": {"$date": "2023-01-31T13:03:05.224Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d91189053691806f7d6042"}}, {"eventDate": {"$date": "2023-01-04T05:18:51.275Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63b50c3b6185525cf29e6034"}}, {"eventDate": {"$date": "2022-11-14T10:33:36.832Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637219805777af82734b8f7f"}}, {"eventDate": {"$date": "2022-11-14T10:33:29.387Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637219795777af82734b8f6e"}}, {"eventDate": {"$date": "2022-11-14T10:11:20.835Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637214485777af82734b8ecc"}}, {"eventDate": {"$date": "2022-11-14T10:11:14.287Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637214425777af82734b8ebf"}}, {"eventDate": {"$date": "2022-11-14T10:11:05.289Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637214395777af82734b8eb4"}}, {"eventDate": {"$date": "2022-11-14T10:10:55.352Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6372142f5777af82734b8eab"}}, {"eventDate": {"$date": "2022-11-14T10:08:13.646Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "6372138d5777af82734b8e6f"}}, {"eventDate": {"$date": "2022-11-14T10:08:05.816Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637213855777af82734b8e6a"}}], "createdOn": {"$date": "2022-11-14T10:07:23.733Z"}, "updatedOn": {"$date": "2023-05-02T06:34:37.873Z"}, "__v": 0, "cardNumber": "42100000992", "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-05-02T06:34:37.872Z"}, "pointsToExpire": [], "portalMetadata": {"username": "jaya<PERSON><PERSON>@gmail.com", "userId": "d65092bd-64cf-4f74-924a-d8eb756d7113", "platforms": [], "lastAccessedOn": {"$date": "2023-05-02T06:34:35.286Z"}}, "pointsLegacy": 230}, {"_id": {"$oid": "63721a5c5777af82734b8fa8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "321", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ACTIVE", "points": 170, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "123456"}], "tierPoints": 220, "purchasesCount": 4, "purchasesValue": 4000, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-11T11:20:57.178Z"}, "registeredOn": {"$date": "2022-11-14T10:37:16.664Z"}, "firstName": "<PERSON> ", "lastName": "Visits ", "preferredName": "<PERSON>", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-11-02T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "k", "city": "Greenland", "stateOrProvince": "<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-11-14T10:37:16.664Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-14T10:37:16.664Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "test", "refName": "tst", "notes": "test"}], "historyEvents": [], "createdOn": {"$date": "2022-11-14T10:37:16.891Z"}, "updatedOn": {"$date": "2023-01-23T05:01:28.325Z"}, "__v": 0, "cardNumber": "42100000800", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "619a60ce98ea23a5b884ed4e"}, "lastTransactionOn": {"$date": "2023-01-23T05:01:28.192Z"}, "pointsToExpire": [0], "pointsLegacy": 170}, {"_id": {"$oid": "637393dd5777af82734ba3db"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "322", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "63511d337a1ed3162d280624"}, "type": "PRIMARY", "status": "ACTIVE", "points": 983, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "5455"}], "tierPoints": 1000, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-11T11:20:57.178Z"}, "registeredOn": {"$date": "2022-11-15T13:27:57.460Z"}, "firstName": "dhkjsd", "lastName": "uhasakjs", "preferredName": "dhkjsd uhasakjs", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-11-04T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "\\75\\8", "city": "Greenland", "stateOrProvince": "<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2022-11-15T13:27:57.460Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-15T13:27:57.460Z"}}, "cardReplacementCount": 6, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "rewardMetadata": [], "historyEvents": [], "createdOn": {"$date": "2022-11-15T13:27:57.687Z"}, "updatedOn": {"$date": "2023-01-19T20:12:53.575Z"}, "__v": 0, "cardNumber": null, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "lastTransactionOn": {"$date": "2023-01-19T20:12:53.452Z"}, "pointsToExpire": [], "pointsLegacy": 983}, {"_id": {"$oid": "6375f75c69ca7e559af531a9"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "323", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": {"$oid": "634d97347a1ed3162d27ba71"}, "merchantLocationId": {"$oid": "624956623795bb2119dd989d"}, "type": "SECONDARY", "status": "ACTIVE", "points": 40, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "fsfsd"}], "tierPoints": 3152, "purchasesCount": 1, "purchasesValue": 100000, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-15T18:46:26.566Z"}, "registeredOn": {"$date": "2022-11-17T08:56:59.859Z"}, "firstName": "Test", "lastName": "test", "preferredName": "Test test", "mobileNumber": "12462543343", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-11-09T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "2"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d87089fa6ab294d506"}, "joinedDate": {"$date": "2023-02-28T05:34:59.990Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-17T08:56:59.859Z"}}, "cardReplacementCount": 3, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "300125", "refName": "Test test", "notes": "Test"}], "historyEvents": [{"eventDate": {"$date": "2023-02-28T05:35:00.889Z"}, "eventDetails": "Type changed from PRIMARY to SECONDARY", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fd92841389164d2807a6df"}}, {"eventDate": {"$date": "2023-02-28T05:34:18.052Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fd925a1389164d2807a6b0"}}, {"eventDate": {"$date": "2022-11-23T17:52:11.733Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e5dcb10fe45559197fa37"}}, {"eventDate": {"$date": "2022-11-23T17:51:34.045Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e5da66f6a6aa0f162243d"}}], "createdOn": {"$date": "2022-11-17T08:57:00.096Z"}, "updatedOn": {"$date": "2023-05-29T09:36:53.876Z"}, "__v": 0, "cardNumber": "42100000964", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-05-29T09:36:53.770Z"}, "pointsToExpire": [0], "pointsLegacy": 40}, {"_id": {"$oid": "6375fe8869ca7e559af53307"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "324", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "624956623795bb2119dd989d"}, "type": "PRIMARY", "status": "ACTIVE", "points": {"$numberLong": "999999989991"}, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "1232323"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-11-15T18:46:26.566Z"}, "registeredOn": {"$date": "2022-11-17T09:27:36.674Z"}, "firstName": "BLALALALA", "lastName": "Merch", "preferredName": "BLALALALA", "mobileNumber": "12462540034", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2022-11-03T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Speightstown"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61fa46465ab9ca395358fcd6"}, "membershipId": "7777777", "joinedDate": {"$date": "2023-01-11T07:15:06.297Z"}, "expiryDate": {"$date": "2022-08-01T00:00:00.000Z"}}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-11-17T09:27:36.674Z"}}, "cardReplacementCount": 1, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "rewardMetadata": [{"rewardId": {"$oid": "61958c9059938d8c20b956c3"}, "refNumber": "4002901", "refName": "Test Merch Merch", "notes": "Test"}, {"rewardId": {"$oid": "619b708839769b13f31d6dda"}, "refNumber": "10002121", "refName": "Test Merch Merch", "notes": "test"}, {"rewardId": {"$oid": "619b713839769b13f31d6ddc"}, "refNumber": "3000121", "refName": "Test Merch Merch", "notes": "test"}, {"rewardId": {"$oid": "619b713f39769b13f31d6ddd"}, "refNumber": "4000999", "refName": "Test Merch Merch", "notes": "Test"}, {"rewardId": {"$oid": "619b713139769b13f31d6ddb"}, "refNumber": "20001192", "refName": "Test Merch Merch", "notes": "test"}], "historyEvents": [{"eventDate": {"$date": "2023-01-11T07:19:41.306Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be630d238822532f9e34f6"}}, {"eventDate": {"$date": "2023-01-11T07:19:04.006Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be62e8238822532f9e34d5"}}, {"eventDate": {"$date": "2023-01-11T07:15:08.864Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be61fc238822532f9e34b6"}}, {"eventDate": {"$date": "2023-01-11T06:59:54.558Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be5e6a238822532f9e3493"}}, {"eventDate": {"$date": "2023-01-11T06:56:43.128Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be5dab7a9eb6e977781e50"}}, {"eventDate": {"$date": "2023-01-11T06:55:43.708Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be5d6fb8931be0859639aa"}}, {"eventDate": {"$date": "2023-01-11T06:55:24.237Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be5d5cb8931be08596398d"}}, {"eventDate": {"$date": "2023-01-11T06:54:19.569Z"}, "eventDetails": "Member affinity group updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be5d1bb8931be085963972"}}, {"eventDate": {"$date": "2023-01-09T11:05:05.171Z"}, "eventDetails": "This account will be activated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63bbf4e1a2297a126f41ce3a"}}, {"eventDate": {"$date": "2023-01-09T11:04:54.127Z"}, "eventDetails": "This account will be suspended", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63bbf4d6a2297a126f41ce1b"}}, {"eventDate": {"$date": "2022-11-28T05:02:25.289Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "638440e1d19dfa06846fe27a"}}, {"eventDate": {"$date": "2022-11-23T17:53:15.575Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e5e0b10fe45559197fa54"}}, {"eventDate": {"$date": "2022-11-23T17:52:50.496Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e5df210fe45559197fa47"}}, {"eventDate": {"$date": "2022-11-23T16:31:12.952Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637e4ad04c7f3d6191858b86"}}, {"eventDate": {"$date": "2022-11-18T05:28:06.559Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637717e64f706b63634e905a"}}, {"eventDate": {"$date": "2022-11-18T05:27:59.930Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "637717df4f706b63634e904f"}}], "createdOn": {"$date": "2022-11-17T09:27:36.958Z"}, "updatedOn": {"$date": "2023-04-06T06:11:09.217Z"}, "__v": 0, "cardNumber": "49100001206", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "632b5af4673fc768fb3b97df"}, "lastTransactionOn": {"$date": "2023-04-06T06:11:09.088Z"}, "pointsToExpire": [], "pointsLegacy": {"$numberLong": "999999989991"}}, {"_id": {"$oid": "639394fccbe16cb2a9fae9ab"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "325", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "type": "PRIMARY", "status": "ACTIVE", "points": 30538, "tierPoints": 30412, "purchasesCount": 2, "purchasesValue": 1000100, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2022-12-09T09:57:47.009Z"}, "registeredOn": {"$date": "2017-11-09T13:16:21.000Z"}, "firstName": "VANESSA", "lastName": "WILLIAMS", "preferredName": "VANESSA WILLIAMS", "mobileNumber": "1246868288-6321", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": {"$numberLong": "-1381017600000"}}, "residentialAddress": {"line1": "DIEGO MARTIN", "line2": "-", "city": "DIEGO MARTIN", "stateOrProvince": "-"}, "createdBy": {"$oid": "62459ca8ec6a833fc52c09a4"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "joinedDate": {"$date": "2022-12-09T20:05:16.004Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2022-12-09T20:05:16.004Z"}}, "cardReplacementCount": 17, "customAttributes": {"legacyId": "***********"}, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [{"rewardId": {"$oid": "619b713839769b13f31d6ddc"}, "refNumber": "660942", "refName": "Test Part 3"}], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-01-31T13:33:51.348Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63d918bf053691806f7d6752"}}, {"eventDate": {"$date": "2023-01-11T04:01:34.099Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be349ea2297a126f41e2bb"}}, {"eventDate": {"$date": "2023-01-11T04:01:24.644Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63be3494a2297a126f41e2aa"}}, {"eventDate": {"$date": "2023-01-09T10:46:37.890Z"}, "eventDetails": "This account will be activated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63bbf08da2297a126f41cd3c"}}, {"eventDate": {"$date": "2023-01-09T10:46:23.687Z"}, "eventDetails": "This account will be suspended", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63bbf07fa2297a126f41cd21"}}, {"eventDate": {"$date": "2022-12-27T05:28:57.787Z"}, "eventDetails": "This account will be activated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63aa82996878f4190977fa4d"}}, {"eventDate": {"$date": "2022-12-27T05:28:42.158Z"}, "eventDetails": "This account will be suspended", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63aa828a6878f4190977fa36"}}, {"eventDate": {"$date": "2022-12-20T05:28:50.114Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63a148126878f4190977dec9"}}, {"eventDate": {"$date": "2022-12-20T05:26:37.064Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63a1478d6878f4190977dec4"}}], "createdOn": {"$date": "2022-12-09T20:05:16.238Z"}, "updatedOn": {"$date": "2023-01-31T13:33:51.348Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "cardNumber": "***********", "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "lastTransactionOn": {"$date": "2023-01-20T10:29:31.136Z"}, "pointsLegacy": 30538}, {"_id": {"$oid": "63c1b5fa1c5798e8267b7cef"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "326", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-01-13T06:36:46.702Z"}, "registeredOn": {"$date": "2023-01-13T19:50:17.902Z"}, "preferredName": "", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": false, "createdBy": {"$oid": "620f472c11b2a1c14c2e5916"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2023-01-13T19:50:17.902Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-01-13T19:50:17.902Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-13T19:50:18.141Z"}, "updatedOn": {"$date": "2023-01-13T19:50:18.141Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "63c1b6861c5798e8267b7d00"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "327", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-01-13T06:36:46.702Z"}, "registeredOn": {"$date": "2023-01-13T19:52:37.603Z"}, "preferredName": "s", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": false, "createdBy": {"$oid": "620f472c11b2a1c14c2e5916"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2023-01-13T19:52:37.604Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-01-13T19:52:37.604Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-17T05:13:26.262Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "643cd5768c89b4d492793147"}}], "createdOn": {"$date": "2023-01-13T19:52:38.008Z"}, "updatedOn": {"$date": "2023-04-17T05:13:26.262Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "63c1b7ad1c5798e8267b7d11"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "328", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-01-13T06:36:46.702Z"}, "registeredOn": {"$date": "2023-01-13T19:57:32.967Z"}, "preferredName": "", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": false, "createdBy": {"$oid": "620f472c11b2a1c14c2e5916"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2023-01-13T19:57:32.967Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-01-13T19:57:32.967Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-13T19:57:33.204Z"}, "updatedOn": {"$date": "2023-01-13T19:57:33.204Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "63c1b7ff1c5798e8267b7d22"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "329", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-01-13T06:36:46.702Z"}, "registeredOn": {"$date": "2023-01-13T19:58:55.302Z"}, "preferredName": "", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": false, "createdBy": {"$oid": "620f472c11b2a1c14c2e5916"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2023-01-13T19:58:55.302Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-01-13T19:58:55.302Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-13T19:58:55.534Z"}, "updatedOn": {"$date": "2023-01-13T19:58:55.534Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "63c1b8ac1c5798e8267b7d38"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "330", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "type": "PRIMARY", "status": "ACTIVE", "points": 21, "tierPoints": 21, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-01-13T06:36:46.702Z"}, "registeredOn": {"$date": "2023-01-13T20:01:48.734Z"}, "preferredName": "", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": false, "createdBy": {"$oid": "620f472c11b2a1c14c2e5916"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "joinedDate": {"$date": "2023-01-13T20:01:48.734Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-01-13T20:01:48.734Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-13T20:01:48.987Z"}, "updatedOn": {"$date": "2023-01-13T20:10:04.140Z"}, "__v": 0, "cardNumber": "42100000973", "updatedBy": {"$oid": "620f472c11b2a1c14c2e5916"}, "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "lastTransactionOn": {"$date": "2023-01-13T20:10:04.139Z"}, "pointsLegacy": 21}, {"_id": {"$oid": "63e107f4053691806f7d8cd5"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "331", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 21, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12435675432"}], "tierPoints": 21, "purchasesCount": 1, "purchasesValue": 230, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-01-31T12:22:58.705Z"}, "registeredOn": {"$date": "2023-02-06T14:00:20.200Z"}, "firstName": "TRAIMAINE", "lastName": "TAYLOR", "preferredName": "TRAIMAINE TAYLOR", "mobileNumber": "12462501234", "additionalPhoneNumbers": [], "countryCode": "BB", "country": "Barbados", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "1993-10-10T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12343", "city": "Greenland", "stateOrProvince": "<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-06T14:00:20.200Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-06T14:00:20.200Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-02-06T14:00:20.474Z"}, "updatedOn": {"$date": "2023-03-22T10:53:45.821Z"}, "__v": 0, "cardNumber": "42100000969", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "lastTransactionOn": {"$date": "2023-03-22T10:53:45.820Z"}, "pointsLegacy": 21}, {"_id": {"$oid": "63fc8986cbf7e49fc476d600"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "332", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-02-21T16:57:54.594Z"}, "registeredOn": {"$date": "2023-02-27T10:44:21.736Z"}, "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-27T10:44:21.736Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-27T10:44:21.736Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-02-27T10:52:22.135Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc8b66cbf7e49fc476d675"}}], "createdOn": {"$date": "2023-02-27T10:44:22.296Z"}, "updatedOn": {"$date": "2023-02-27T10:52:22.136Z"}, "__v": 0, "cardNumber": "42100000967", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "63fc8a11c4f572ccb06e8313"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "333", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["test user", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-27T10:03:06.686Z"}, "registeredOn": {"$date": "2023-02-27T10:46:40.978Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-27T10:46:40.978Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-27T10:46:40.978Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-02-27T10:52:10.900Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc8b5acbf7e49fc476d65d"}}], "createdOn": {"$date": "2023-02-27T10:46:41.766Z"}, "updatedOn": {"$date": "2023-02-27T10:52:10.901Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "63fc8bc1cbf7e49fc476d69a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "334", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["test user shehan", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-21T16:57:54.594Z"}, "registeredOn": {"$date": "2023-02-27T10:53:52.913Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-27T10:53:52.913Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-27T10:53:52.913Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-02-27T10:59:43.509Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fc8d1fcbf7e49fc476d7af"}}], "createdOn": {"$date": "2023-02-27T10:53:53.211Z"}, "updatedOn": {"$date": "2023-02-27T10:59:43.509Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "63fc8dc4c4f572ccb06e8329"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "335", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 3, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 3, "purchasesCount": 0, "purchasesValue": 0, "tags": ["test user shehan", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-27T10:03:06.686Z"}, "registeredOn": {"$date": "2023-02-27T11:02:28.307Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-27T11:02:28.307Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-27T11:02:28.307Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-02-27T11:02:28.631Z"}, "updatedOn": {"$date": "2023-03-22T09:43:13.442Z"}, "__v": 0, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-03-22T09:43:13.439Z"}, "pointsLegacy": 3}, {"_id": {"$oid": "63fc8fe51683b72f4bbf543e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "336", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 3, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 3, "purchasesCount": 0, "purchasesValue": 0, "tags": ["test user shehan", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-27T11:07:07.319Z"}, "registeredOn": {"$date": "2023-02-27T11:11:33.253Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-27T11:11:33.253Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-27T11:11:33.253Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-02-27T11:11:33.513Z"}, "updatedOn": {"$date": "2023-03-22T06:26:50.830Z"}, "__v": 0, "lastTransactionLocation": {"$oid": "619785c1c85d41818d3fe0af"}, "lastTransactionOn": {"$date": "2023-03-22T06:26:50.829Z"}, "pointsLegacy": 3}, {"_id": {"$oid": "63fc90421683b72f4bbf544e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "337", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["BLACK-OPS", "SPEC-OPS", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-27T11:07:07.319Z"}, "registeredOn": {"$date": "2023-02-27T11:13:05.808Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-27T11:13:05.808Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-27T11:13:05.808Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-02-27T11:13:06.057Z"}, "updatedOn": {"$date": "2023-02-27T11:13:06.057Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "63fd8948113b13145517dac3"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "338", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["test user shehan 01", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-28T04:47:25.078Z"}, "registeredOn": {"$date": "2023-02-28T04:55:35.760Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-28T04:55:35.760Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-28T04:55:35.760Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-02-28T04:55:36.131Z"}, "updatedOn": {"$date": "2023-02-28T04:55:36.131Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "63fd8aa00f94fd47b6c3b125"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "339", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 100, "identifications": [{"identificationType": "DRIVER_LICENSE", "identificationNumber": "12345632"}], "tierPoints": 100, "purchasesCount": 2, "purchasesValue": 10000, "tags": [" <PERSON><PERSON> 02", "Loyalty User"], "lastSeenOn": {"$date": "2023-02-28T04:59:46.786Z"}, "registeredOn": {"$date": "2023-02-28T05:01:20.562Z"}, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "preferredName": "<PERSON><PERSON><PERSON>", "mobileNumber": "18682911234", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbado", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-02-15T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "12334", "city": "Ari<PERSON>", "stateOrProvince": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-02-28T05:01:20.562Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-02-28T05:01:20.562Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-02-28T10:57:22.329Z"}, "eventDetails": "This account will be activated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fdde121389164d2807a8ba"}}, {"eventDate": {"$date": "2023-02-28T07:32:42.898Z"}, "eventDetails": "This account will be suspended", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "63fdae1a1389164d2807a7e3"}}], "createdOn": {"$date": "2023-02-28T05:01:20.937Z"}, "updatedOn": {"$date": "2023-02-28T10:57:22.331Z"}, "__v": 0, "lastTransactionLocation": {"$oid": "632b5af4673fc768fb3b97df"}, "lastTransactionOn": {"$date": "2023-02-28T07:35:51.563Z"}, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 100}, {"_id": {"$oid": "64428707513791d308095955"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "340", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "********"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T12:52:23.119Z"}, "firstName": "TEst Pns", "lastName": "Data Sync Member", "preferredName": "TEst Pns Data Sync Member", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T12:52:23.119Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T12:52:23.119Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T12:52:23.375Z"}, "updatedOn": {"$date": "2023-04-21T12:52:28.545Z"}, "__v": 0, "cardNumber": "42100000963", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "64429373513791d308095991"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "341", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T13:45:22.804Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T13:45:22.804Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T13:45:22.804Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-21T13:52:07.477Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "64429507513791d3080959d7"}}], "createdOn": {"$date": "2023-04-21T13:45:23.051Z"}, "updatedOn": {"$date": "2023-04-21T13:52:07.477Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "64429528513791d3080959f6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "342", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T13:52:40.576Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T13:52:40.576Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T13:52:40.576Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T13:52:40.796Z"}, "updatedOn": {"$date": "2023-04-21T13:52:40.796Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "64429578513791d308095a0c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "343", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T13:53:59.951Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T13:53:59.951Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T13:53:59.951Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T13:54:00.168Z"}, "updatedOn": {"$date": "2023-04-21T13:54:00.168Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "6442979b513791d308095a8c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "344", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T14:03:07.197Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T14:03:07.197Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T14:03:07.197Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T14:03:07.419Z"}, "updatedOn": {"$date": "2023-04-21T14:03:07.419Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "64429859513791d308095aa2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "345", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T14:06:17.518Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T14:06:17.518Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T14:06:17.518Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T14:06:17.739Z"}, "updatedOn": {"$date": "2023-04-21T14:06:17.739Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "6442beb3513791d308095ab7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "346", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T16:49:54.989Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T16:49:54.989Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T16:49:54.989Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T16:49:55.211Z"}, "updatedOn": {"$date": "2023-04-21T16:49:57.607Z"}, "__v": 0, "cardNumber": "8883417818745816", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6442d451513791d308095b8c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "347", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T18:22:09.664Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T18:22:09.664Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T18:22:09.664Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T18:22:09.889Z"}, "updatedOn": {"$date": "2023-04-21T18:22:09.889Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "6442d476513791d308095b9c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "348", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T18:22:46.744Z"}, "firstName": "Test PnS Member", "lastName": "Data Sync API", "preferredName": "Test PnS Member Data Sync API", "mobileNumber": "94711234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T18:22:46.744Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T18:22:46.744Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T18:22:46.969Z"}, "updatedOn": {"$date": "2023-04-21T18:22:49.595Z"}, "__v": 0, "cardNumber": "6864573467406846", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6442d503513791d308095c3e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "349", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T18:25:07.166Z"}, "firstName": "Test PnS Member 2", "lastName": "Data Sync API 2", "preferredName": "Test PnS Member 2 Data Sync API 2", "mobileNumber": "94711234568", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T18:25:07.166Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T18:25:07.166Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T18:25:07.397Z"}, "updatedOn": {"$date": "2023-04-21T18:25:09.723Z"}, "__v": 0, "cardNumber": "1228984692535793", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6442df39513791d308095ca1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "350", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T19:08:41.291Z"}, "firstName": "Test PnS Member 22", "lastName": "Data Sync API 22", "preferredName": "Test PnS Member 22 Data Sync API 22", "mobileNumber": "94711234528", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T19:08:41.292Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T19:08:41.292Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T19:08:41.521Z"}, "updatedOn": {"$date": "2023-04-21T19:08:43.857Z"}, "__v": 0, "cardNumber": "8185796034415742", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6442e198513791d308095cc1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "351", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-21T19:18:48.739Z"}, "firstName": "Test PnS Member 1212", "lastName": "Data Sync 2121", "preferredName": "Test PnS Member 1212 Data Sync 2121", "mobileNumber": "94771234528", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-21T19:18:48.739Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-21T19:18:48.739Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-21T19:18:48.962Z"}, "updatedOn": {"$date": "2023-04-21T19:18:51.525Z"}, "__v": 0, "cardNumber": "5944946594932212", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6446c698513791d308096028"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "352", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-24T18:12:40.671Z"}, "firstName": "Member New", "lastName": "Data Sync PnS", "preferredName": "Member New Data Sync PnS", "mobileNumber": "94771234428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-24T18:12:40.671Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-24T18:12:40.671Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-24T18:12:40.897Z"}, "updatedOn": {"$date": "2023-04-24T18:12:43.461Z"}, "__v": 0, "cardNumber": "****************", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6446c73b513791d3080960e2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "353", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-24T18:15:23.210Z"}, "firstName": "Member New 1", "lastName": "Data Sync PnS", "preferredName": "Member New 1 Data Sync PnS", "mobileNumber": "94771234428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-24T18:15:23.210Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-24T18:15:23.210Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-24T18:15:23.432Z"}, "updatedOn": {"$date": "2023-04-24T18:15:23.432Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "6446c776513791d308096108"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "354", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-24T18:16:22.098Z"}, "firstName": "Member New 11", "lastName": "Data Sync PnS", "preferredName": "Member New 11 Data Sync PnS", "mobileNumber": "94771234428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "21", "city": "Colombo"}, "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-24T18:16:22.098Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-24T18:16:22.098Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-24T18:16:22.318Z"}, "updatedOn": {"$date": "2023-04-24T18:16:26.412Z"}, "__v": 0, "cardNumber": "6785754014188721", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6448b774513791d30809646d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "355", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-26T05:32:36.082Z"}, "firstName": "Member New 11", "lastName": "Data Sync PnS", "preferredName": "Member New 11 Data Sync PnS", "mobileNumber": "94771234428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-26T05:32:36.082Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-26T05:32:36.082Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-26T05:32:36.319Z"}, "updatedOn": {"$date": "2023-04-26T05:32:39.605Z"}, "__v": 0, "cardNumber": "3149815948267022", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6448b789513791d30809648d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "356", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-26T05:32:57.762Z"}, "firstName": "Member New 11", "lastName": "Data Sync PnS", "preferredName": "Member New 11 Data Sync PnS", "mobileNumber": "94771234428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-26T05:32:57.762Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-26T05:32:57.762Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-26T05:32:57.982Z"}, "updatedOn": {"$date": "2023-04-26T05:33:00.562Z"}, "__v": 0, "cardNumber": "7539527696408507", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "6448cb84513791d3080964b2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "357", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-20T09:13:37.649Z"}, "registeredOn": {"$date": "2023-04-26T06:58:12.660Z"}, "firstName": "Member New 888", "lastName": "Data Sync PnS", "preferredName": "Member New 888 Data Sync PnS", "mobileNumber": "94771734428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2023-04-12T00:00:00.000Z"}, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-26T06:58:12.660Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-26T06:58:12.660Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-26T06:58:12.886Z"}, "updatedOn": {"$date": "2023-04-26T06:58:15.278Z"}, "__v": 0, "cardNumber": "****************", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644965f551337a3cd2a541fb"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "358", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-26T17:57:08.857Z"}, "firstName": "Member New 888", "lastName": "Data Sync PnS", "preferredName": "Member New 888 Data Sync PnS", "mobileNumber": "94771734428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-26T17:57:08.857Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-26T17:57:08.857Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-26T17:57:09.448Z"}, "updatedOn": {"$date": "2023-04-26T17:57:09.448Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "6449660451337a3cd2a5420f"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "359", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-26T17:57:24.091Z"}, "firstName": "Member New 888", "lastName": "Data Sync PnS", "preferredName": "Member New 888 Data Sync PnS", "mobileNumber": "94771734428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-26T17:57:24.091Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-26T17:57:24.091Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-26T17:57:24.310Z"}, "updatedOn": {"$date": "2023-04-26T17:57:26.927Z"}, "__v": 0, "cardNumber": "3108921893913133", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b622151337a3cd2a551ba"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "360", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:05:21.102Z"}, "firstName": "TEst PnS", "lastName": "Member PnS", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:05:21.102Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:05:21.102Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:05:21.329Z"}, "updatedOn": {"$date": "2023-04-28T06:05:21.329Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "644b623051337a3cd2a551ce"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "361", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 30, "tierPoints": 30, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:05:36.589Z"}, "firstName": "PnS", "lastName": "Exist Test", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:05:36.589Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:05:36.589Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-29T13:40:43.245Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "644d1e5b51337a3cd2a55c12"}}, {"eventDate": {"$date": "2023-04-28T17:36:39.360Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "643e89d455aa2395b420a2bd"}, "_id": {"$oid": "644c042751337a3cd2a55aca"}}], "createdOn": {"$date": "2023-04-28T06:05:36.811Z"}, "updatedOn": {"$date": "2023-04-29T13:40:43.246Z"}, "__v": 0, "cardNumber": "2650297418861167", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "portalMetadata": {"userId": "a26b28ed-8c91-4889-a453-7eb941a65d76", "platforms": [], "lastAccessedOn": {"$date": "2023-04-28T17:37:12.776Z"}}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-28T17:37:15.904Z"}, "pointsLegacy": 30}, {"_id": {"$oid": "644b62d051337a3cd2a551ee"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "362", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:08:16.628Z"}, "firstName": "TEst PnS", "lastName": "Member PnS", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:08:16.628Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:08:16.628Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:08:16.848Z"}, "updatedOn": {"$date": "2023-04-28T06:08:19.552Z"}, "__v": 0, "cardNumber": "3682205370761005", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b635651337a3cd2a5520e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "363", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:10:30.064Z"}, "firstName": "TEst PnS", "lastName": "Member PnS", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:10:30.064Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:10:30.064Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:10:30.287Z"}, "updatedOn": {"$date": "2023-04-28T06:10:32.748Z"}, "__v": 0, "cardNumber": "7399521656487016", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b649051337a3cd2a5522e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "364", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:15:44.231Z"}, "firstName": "TEst PnS", "lastName": "Member PnS", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:15:44.231Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:15:44.231Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:15:44.452Z"}, "updatedOn": {"$date": "2023-04-28T06:15:47.020Z"}, "__v": 0, "cardNumber": "6360458336326545", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b658351337a3cd2a5524e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "365", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:19:46.952Z"}, "firstName": "TEst PnS", "lastName": "Member PnS", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:19:46.952Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:19:46.952Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:19:47.176Z"}, "updatedOn": {"$date": "2023-04-28T06:19:49.537Z"}, "__v": 0, "cardNumber": "2282795230609682", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b66da51337a3cd2a5526e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "366", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:25:30.619Z"}, "firstName": "TEst PnS", "lastName": "Member PnS", "preferredName": "TEst PnS Member PnS", "mobileNumber": "94771777428", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:25:30.619Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:25:30.619Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:25:30.858Z"}, "updatedOn": {"$date": "2023-04-28T06:25:33.326Z"}, "__v": 0, "cardNumber": "****************", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b6edc51337a3cd2a552c6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "367", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T06:59:40.700Z"}, "firstName": "Test PnS 2", "lastName": "Member PnS", "preferredName": "Test PnS 2 Member PnS", "mobileNumber": "94771777499", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T06:59:40.700Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T06:59:40.700Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T06:59:40.918Z"}, "updatedOn": {"$date": "2023-04-28T06:59:43.461Z"}, "__v": 0, "cardNumber": "6099902058158157", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b6f3151337a3cd2a552e6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "368", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T07:01:04.875Z"}, "firstName": "Test PnS 2", "lastName": "Member PnS", "preferredName": "Test PnS 2 Member PnS", "mobileNumber": "94771777499", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:01:04.875Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:01:04.875Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:01:05.095Z"}, "updatedOn": {"$date": "2023-04-28T07:01:07.629Z"}, "__v": 0, "cardNumber": "****************", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b700951337a3cd2a55306"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "369", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T07:04:41.329Z"}, "firstName": "Test PnS 2", "lastName": "Member PnS", "preferredName": "Test PnS 2 Member PnS", "mobileNumber": "94771777499", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:04:41.329Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:04:41.329Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:04:41.546Z"}, "updatedOn": {"$date": "2023-04-28T07:04:43.882Z"}, "__v": 0, "cardNumber": "2772446479017723", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b70ad51337a3cd2a5535f"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "370", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T07:07:25.201Z"}, "firstName": "Test PnS 2", "lastName": "Member PnS", "preferredName": "Test PnS 2 Member PnS", "mobileNumber": "94771777666", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:07:25.201Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:07:25.201Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:07:25.423Z"}, "updatedOn": {"$date": "2023-04-28T07:07:25.423Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "644b70b951337a3cd2a55373"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "371", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T07:07:37.552Z"}, "firstName": "Test PnS 2", "lastName": "Member PnS", "preferredName": "Test PnS 2 Member PnS", "mobileNumber": "94771777666", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:07:37.552Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:07:37.552Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:07:37.951Z"}, "updatedOn": {"$date": "2023-04-28T07:07:40.504Z"}, "__v": 0, "cardNumber": "7819014342377754", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b724f6b6a3c72afb3aff7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "372", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-28T07:05:12.898Z"}, "registeredOn": {"$date": "2023-04-28T07:05:12.898Z"}, "firstName": "Test PnS 2", "lastName": "Member PnS", "preferredName": "Test PnS 2 Member PnS", "mobileNumber": "94771777666", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:14:23.811Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:14:23.811Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:14:23.822Z"}, "updatedOn": {"$date": "2023-04-28T07:14:33.056Z"}, "__v": 0, "portalMetadata": {"userId": "a26b28ed-8c91-4889-a453-7eb941a65d76", "platforms": [], "lastAccessedOn": {"$date": "2023-04-28T07:14:27.864Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-28T07:14:30.964Z"}, "cardNumber": "49100000007", "pointsLegacy": 35}, {"_id": {"$oid": "644b72ff51337a3cd2a554d8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "373", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T07:17:19.520Z"}, "firstName": "Testing Create", "lastName": "Member PnS", "preferredName": "Testing Create Member PnS", "mobileNumber": "94771777123", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:17:19.520Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:17:19.520Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:17:19.741Z"}, "updatedOn": {"$date": "2023-04-28T07:17:19.741Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "644b731151337a3cd2a554ec"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "374", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T07:17:37.603Z"}, "firstName": "Testing Create 2", "lastName": "Member PnS", "preferredName": "Testing Create 2 Member PnS", "mobileNumber": "94771777123", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:17:37.603Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:17:37.603Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:17:37.822Z"}, "updatedOn": {"$date": "2023-04-28T07:17:42.505Z"}, "__v": 0, "cardNumber": "1658091137713640", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b73b86b6a3c72afb3b020"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "375", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-28T07:05:12.898Z"}, "registeredOn": {"$date": "2023-04-28T07:05:12.898Z"}, "firstName": "Testing Create 2", "lastName": "Member PnS", "preferredName": "Testing Create 2 Member PnS", "mobileNumber": "94771777123", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T07:20:24.819Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T07:20:24.819Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T07:20:24.826Z"}, "updatedOn": {"$date": "2023-04-28T07:20:33.422Z"}, "__v": 0, "portalMetadata": {"userId": "a26b28ed-8c91-4889-a453-7eb941a65d76", "platforms": [], "lastAccessedOn": {"$date": "2023-04-28T07:20:28.546Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-28T07:20:31.353Z"}, "cardNumber": "49100000008", "pointsLegacy": 35}, {"_id": {"$oid": "644b8da0bb1735ea98df91c3"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "376", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-28T09:10:45.571Z"}, "registeredOn": {"$date": "2023-04-28T09:10:45.571Z"}, "firstName": "Testing Create 2", "lastName": "Member PnS", "preferredName": "Testing Create 2 Member PnS", "mobileNumber": "94771777123", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T09:10:56.484Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T09:10:56.484Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T09:10:56.505Z"}, "updatedOn": {"$date": "2023-04-28T09:11:05.612Z"}, "__v": 0, "portalMetadata": {"userId": "a26b28ed-8c91-4889-a453-7eb941a65d76", "platforms": [], "lastAccessedOn": {"$date": "2023-04-28T09:11:00.644Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-28T09:11:03.538Z"}, "cardNumber": "49100000009", "pointsLegacy": 35}, {"_id": {"$oid": "644b922651337a3cd2a557bd"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "377", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-28T09:30:14.299Z"}, "firstName": "PnS Portal Member", "lastName": "Member 1", "preferredName": "PnS Portal Member Member 1", "mobileNumber": "94771721456", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T09:30:14.299Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T09:30:14.299Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-28T09:33:03.246Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644b92cf51337a3cd2a5581b"}}], "createdOn": {"$date": "2023-04-28T09:30:14.522Z"}, "updatedOn": {"$date": "2023-04-28T09:33:03.246Z"}, "__v": 0, "cardNumber": "5849499981922594", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "644b9267bb1735ea98df91eb"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "378", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 26, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-28T09:10:45.571Z"}, "registeredOn": {"$date": "2023-04-28T09:10:45.571Z"}, "firstName": "PnS Portal Member", "lastName": "Member 1", "preferredName": "PnS Portal Member Member 1", "mobileNumber": "94771721456", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-28T09:31:19.488Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-28T09:31:19.488Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-28T09:31:19.498Z"}, "updatedOn": {"$date": "2023-04-28T09:55:59.791Z"}, "__v": 0, "portalMetadata": {"username": "94771721456", "userId": "76e986a2-e95d-4147-925d-21afe2995011", "platforms": [], "lastAccessedOn": {"$date": "2023-04-28T09:31:23.230Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-28T09:55:59.683Z"}, "cardNumber": "49100000016", "pointsLegacy": 26}, {"_id": {"$oid": "644d1e9751337a3cd2a55c24"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "379", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T13:41:43.159Z"}, "firstName": "PnS Portal Member", "lastName": "Member 7", "preferredName": "PnS Portal Member Member 7", "mobileNumber": "94771721496", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T13:41:43.159Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T13:41:43.159Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-29T13:41:43.404Z"}, "updatedOn": {"$date": "2023-04-29T13:41:46.069Z"}, "__v": 0, "cardNumber": "****************", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644d1ec1b9f469e2858d8d72"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "380", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-29T07:07:37.560Z"}, "registeredOn": {"$date": "2023-04-29T07:07:37.560Z"}, "firstName": "PnS Portal Member", "lastName": "Member 7", "preferredName": "PnS Portal Member Member 7", "mobileNumber": "94771721496", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T13:42:25.495Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T13:42:25.495Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-29T13:42:25.509Z"}, "updatedOn": {"$date": "2023-04-29T13:42:34.793Z"}, "__v": 0, "portalMetadata": {"username": "94771721496", "userId": "cdd747a3-85df-4c09-beb9-1cf0c344fb3f", "platforms": [], "lastAccessedOn": {"$date": "2023-04-29T13:42:29.275Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-29T13:42:32.496Z"}, "cardNumber": "49100000026", "pointsLegacy": 35}, {"_id": {"$oid": "644d683e51337a3cd2a55c45"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "381", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T18:55:58.300Z"}, "firstName": "PnS Portal Member", "lastName": "Member 7", "preferredName": "PnS Portal Member Member 7", "mobileNumber": "94771721496", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T18:55:58.300Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T18:55:58.300Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-29T18:55:58.571Z"}, "updatedOn": {"$date": "2023-04-29T18:56:00.963Z"}, "__v": 0, "cardNumber": "2405172238834209", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644d6e9e51337a3cd2a55c65"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "382", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T19:23:10.159Z"}, "firstName": "PnS Portal Member", "lastName": "Test 3", "preferredName": "PnS Portal Member Test 3", "mobileNumber": "94771651496", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T19:23:10.159Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T19:23:10.159Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-29T19:23:10.382Z"}, "updatedOn": {"$date": "2023-04-29T19:23:12.934Z"}, "__v": 0, "cardNumber": "5984408959541653", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644d6f0451337a3cd2a55c85"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "383", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T19:24:52.516Z"}, "firstName": "PnS Portal Member", "lastName": "Test 4", "preferredName": "PnS Portal Member Test 4", "mobileNumber": "94771651477", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T19:24:52.516Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T19:24:52.516Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-29T19:24:52.755Z"}, "updatedOn": {"$date": "2023-04-29T19:24:55.276Z"}, "__v": 0, "cardNumber": "2677877783874921", "updatedBy": {"$oid": "643e89d455aa2395b420a2bd"}, "pointsLegacy": 0}, {"_id": {"$oid": "644d759851337a3cd2a55ca9"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "384", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T19:52:55.790Z"}, "firstName": "Testing Frontend", "lastName": "Pns New Member 1", "preferredName": "Testing Frontend Pns New Member 1", "mobileNumber": "94751234567", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T19:52:55.790Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T19:52:55.790Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-29T19:59:23.234Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644d771b51337a3cd2a55cff"}}], "createdOn": {"$date": "2023-04-29T19:52:56.012Z"}, "updatedOn": {"$date": "2023-04-29T19:59:23.235Z"}, "__v": 0, "cardNumber": "8222441913010715", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "644d78ce51337a3cd2a55d19"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "385", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T20:06:38.148Z"}, "firstName": "Testing Frontend", "lastName": "New PnS Member", "preferredName": "Testing Frontend New PnS Member", "mobileNumber": "94751236547", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T20:06:38.148Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T20:06:38.148Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-29T20:10:45.815Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644d79c551337a3cd2a55d3d"}}], "createdOn": {"$date": "2023-04-29T20:06:38.951Z"}, "updatedOn": {"$date": "2023-04-29T20:10:45.815Z"}, "__v": 0, "cardNumber": "****************", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "644d7bf651337a3cd2a55d56"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "386", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-29T20:20:06.348Z"}, "firstName": "Tested FrontEnd", "lastName": "PnS Existing Member 1", "preferredName": "Testing Frontend PnS New Member 1", "mobileNumber": "94751236548", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-29T20:20:06.348Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-29T20:20:06.348Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-29T20:41:03.898Z"}, "eventDetails": "Member details updated", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644d80df51337a3cd2a55d79"}}], "createdOn": {"$date": "2023-04-29T20:20:06.568Z"}, "updatedOn": {"$date": "2023-04-30T19:50:35.137Z"}, "__v": 0, "cardNumber": "3947632585571942", "updatedBy": null, "portalMetadata": {"username": "94751236548", "userId": "69e4f54b-51f7-4146-a272-8b25a5d57791", "platforms": [], "lastAccessedOn": {"$date": "2023-04-30T19:50:29.704Z"}}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T19:50:32.706Z"}, "pointsLegacy": 35}, {"_id": {"$oid": "644e3aa451337a3cd2a55dec"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "387", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 30, "tierPoints": 30, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-30T09:53:39.786Z"}, "firstName": "Testing FrontEnd", "lastName": "PnS New Member 1", "preferredName": "Testing FrontEnd PnS New Member 1", "mobileNumber": "94751236549", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T09:53:39.786Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T09:53:39.786Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-30T09:55:42.070Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644e3b1e51337a3cd2a55e26"}}], "createdOn": {"$date": "2023-04-30T09:53:40.008Z"}, "updatedOn": {"$date": "2023-04-30T09:55:42.070Z"}, "__v": 0, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T09:53:42.842Z"}, "cardNumber": "1051580431968543", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 30}, {"_id": {"$oid": "644e3b7e51337a3cd2a55e41"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "388", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 30, "tierPoints": 30, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-04-26T17:07:59.556Z"}, "registeredOn": {"$date": "2023-04-30T09:57:18.032Z"}, "firstName": "Testing Frontend", "lastName": "PnS New Member 2", "preferredName": "Testing Frontend PnS New Member 2", "mobileNumber": "94751236549", "additionalPhoneNumbers": [], "countryCode": "LK", "country": "Sri Lanka", "isValidEmail": false, "isValidMobileNumber": false, "gender": "MALE", "createdBy": {"$oid": "643e89d455aa2395b420a2bd"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T09:57:18.032Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T09:57:18.032Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-30T10:42:18.712Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644e460a51337a3cd2a55f2f"}}], "createdOn": {"$date": "2023-04-30T09:57:18.254Z"}, "updatedOn": {"$date": "2023-04-30T10:42:18.712Z"}, "__v": 0, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T09:57:20.905Z"}, "cardNumber": "3696754235575613", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 30}, {"_id": {"$oid": "644e3b99949e440fa895748b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "389", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T06:40:55.454Z"}, "registeredOn": {"$date": "2023-04-30T06:40:55.454Z"}, "firstName": "Testing Frontend", "lastName": "PnS New Member 2", "preferredName": "Testing Frontend PnS New Member 2", "mobileNumber": "94751236549", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T09:57:45.965Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T09:57:45.965Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-30T10:42:26.786Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644e461251337a3cd2a55f47"}}], "createdOn": {"$date": "2023-04-30T09:57:45.980Z"}, "updatedOn": {"$date": "2023-04-30T10:42:26.787Z"}, "__v": 0, "portalMetadata": {"username": "94751236549", "userId": "7b848965-b740-4e18-932c-a632e84dac41", "platforms": [], "lastAccessedOn": {"$date": "2023-04-30T09:57:49.180Z"}}, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T09:57:53.553Z"}, "cardNumber": "49100000028", "pointsLegacy": 35}, {"_id": {"$oid": "644e4366949e440fa89574b8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "390", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T06:40:55.454Z"}, "registeredOn": {"$date": "2023-04-30T06:40:55.454Z"}, "firstName": "New PnS Sync API", "lastName": "Test 1", "preferredName": "New PnS Sync API Test 1", "mobileNumber": "94771643277", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T10:31:02.525Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T10:31:02.525Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-30T10:31:02.537Z"}, "updatedOn": {"$date": "2023-04-30T10:31:12.311Z"}, "__v": 0, "portalMetadata": {"username": "94771643277", "userId": "364b7a6a-b272-429b-9d1a-79302527e055", "platforms": [], "lastAccessedOn": {"$date": "2023-04-30T10:31:06.208Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T10:31:10.303Z"}, "cardNumber": "5865930666125862", "pointsLegacy": 35}, {"_id": {"$oid": "644e4544949e440fa89574e1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "391", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T06:40:55.454Z"}, "registeredOn": {"$date": "2023-04-30T06:40:55.454Z"}, "firstName": "New PnS Sync API", "lastName": "Test 2", "preferredName": "New PnS Sync API Test 2", "mobileNumber": "94771643277", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T10:39:00.803Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T10:39:00.803Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-30T10:39:00.811Z"}, "updatedOn": {"$date": "2023-04-30T10:39:00.811Z"}, "__v": 0, "pointsLegacy": 0}, {"_id": {"$oid": "644e4595949e440fa89574ed"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "392", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T06:40:55.454Z"}, "registeredOn": {"$date": "2023-04-30T06:40:55.454Z"}, "firstName": "New PnS Sync API", "lastName": "Test 2", "preferredName": "New PnS Sync API Test 2", "mobileNumber": "94771643377", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T10:40:21.992Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T10:40:21.992Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": true}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-30T10:40:21.997Z"}, "updatedOn": {"$date": "2023-04-30T10:40:30.363Z"}, "__v": 0, "portalMetadata": {"username": "94771643377", "userId": "f329de18-e49b-4b2a-936c-619948bc66ad", "platforms": [], "lastAccessedOn": {"$date": "2023-04-30T10:40:25.606Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T10:40:28.365Z"}, "cardNumber": "8911257008598146", "pointsLegacy": 35}, {"_id": {"$oid": "644e469b949e440fa8957516"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "393", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T06:40:55.454Z"}, "registeredOn": {"$date": "2023-04-30T06:40:55.454Z"}, "firstName": "Frontend Testing", "lastName": "PnS New Member 3", "preferredName": "Frontend Testing PnS New Member 3", "mobileNumber": "94781236547", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T10:44:43.989Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T10:44:43.989Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-04-30T10:44:43.996Z"}, "updatedOn": {"$date": "2023-04-30T10:44:52.082Z"}, "__v": 0, "portalMetadata": {"username": "94781236547", "userId": "3580bc14-6e3f-4b6b-a5b5-150ecaba67b3", "platforms": [], "lastAccessedOn": {"$date": "2023-04-30T10:44:47.300Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-04-30T10:44:50.049Z"}, "cardNumber": "5915884497982915", "pointsLegacy": 35}, {"_id": {"$oid": "644ec946f85eb42e9ce4cbd6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "394", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T19:49:08.221Z"}, "registeredOn": {"$date": "2023-04-30T19:49:08.221Z"}, "firstName": "Tested FrontEnd", "preferredName": "Tested FrontEnd", "mobileNumber": "94751236548", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T20:02:14.877Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T20:02:14.877Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-30T20:05:27.829Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644eca0751337a3cd2a5617b"}}], "createdOn": {"$date": "2023-04-30T20:02:14.894Z"}, "updatedOn": {"$date": "2023-04-30T20:05:27.831Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "644ec9c1d07cb8bdf2dc4f0d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "395", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ARCHIVED", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-04-30T20:04:05.129Z"}, "registeredOn": {"$date": "2023-04-30T20:04:05.129Z"}, "firstName": "Tested FrontEnd", "preferredName": "Tested FrontEnd", "mobileNumber": "94751236548", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-04-30T20:04:17.275Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-04-30T20:04:17.275Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [{"eventDate": {"$date": "2023-04-30T20:05:20.226Z"}, "eventDetails": "status changed from ACTIVE to ARCHIVED", "eventBy": {"$oid": "616680f35958bc8372a1ee88"}, "_id": {"$oid": "644eca0051337a3cd2a56165"}}], "createdOn": {"$date": "2023-04-30T20:04:17.288Z"}, "updatedOn": {"$date": "2023-04-30T20:05:20.226Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "644f9a04784478245b6d44fa"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "396", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "type": "PRIMARY", "status": "ACTIVE", "points": 35, "tierPoints": 35, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-05-01T10:50:57.995Z"}, "registeredOn": {"$date": "2023-05-01T10:50:57.995Z"}, "firstName": "Frontend Testing", "lastName": "PnS New Member 10", "preferredName": "Frontend Testing PnS New Member 10", "mobileNumber": "94753214567", "additionalPhoneNumbers": [], "isValidEmail": false, "isValidMobileNumber": true, "birthDate": {"$date": "2004-05-04T00:00:00.000Z"}, "gender": "MALE", "createdBy": null, "registerMethod": "CUSTOMER_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-05-01T10:52:52.319Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-05-01T10:52:52.319Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "MOBILE", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-05-01T10:52:52.330Z"}, "updatedOn": {"$date": "2023-05-01T10:53:00.538Z"}, "__v": 0, "portalMetadata": {"username": "94753214567", "userId": "172aa118-0a29-4910-873d-2e19f1366311", "platforms": [], "lastAccessedOn": {"$date": "2023-05-01T10:52:55.295Z"}}, "updatedBy": null, "lastTransactionLocation": {"$oid": "61955446031f65c462d0c1ca"}, "lastTransactionOn": {"$date": "2023-05-01T10:52:58.420Z"}, "cardNumber": "49100000032", "pointsLegacy": 35}, {"_id": {"$oid": "6462054580c399698854503b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "397", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "type": "PRIMARY", "status": "ACTIVE", "points": 0, "identifications": [{"identificationType": "NATIONAL_ID", "identificationNumber": "fsfsdf"}], "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": ["Loyalty User"], "lastSeenOn": {"$date": "2023-05-15T10:03:40.194Z"}, "registeredOn": {"$date": "2023-05-15T10:11:17.004Z"}, "firstName": "Testing", "lastName": "121", "preferredName": "Testing 121", "mobileNumber": "18682915544", "additionalPhoneNumbers": [], "countryCode": "TT", "country": "Barbadom", "email": "<EMAIL>", "isValidEmail": false, "isValidMobileNumber": false, "birthDate": {"$date": "2007-05-11T00:00:00.000Z"}, "gender": "MALE", "residentialAddress": {"line1": "23", "city": "Ari<PERSON>"}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "registerMethod": "ADMIN_PORTAL", "affinityGroup": {"affinityGroupId": {"$oid": "6253e9d264467af5f2869c43"}, "joinedDate": {"$date": "2023-05-15T10:11:17.004Z"}, "expiryDate": null}, "tier": {"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "lastUpdatedOn": {"$date": "2023-05-15T10:11:17.004Z"}}, "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": true}, "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-05-15T10:11:17.390Z"}, "updatedOn": {"$date": "2023-05-15T10:11:20.631Z"}, "__v": 0, "cardNumber": "42100000961", "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "pointsLegacy": 0}, {"_id": {"$oid": "619c732af1142b6ffc9c32e0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "TEST00001", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "616957aff2ac7710eb25e094"}, "type": "PRIMARY", "status": "ACTIVE", "points": 100, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-01-20T06:07:28.986Z"}, "registeredOn": {"$date": "2023-01-20T06:07:28.986Z"}, "additionalPhoneNumbers": [], "createdBy": {"$oid": "613dfc56eac5369848619fe0"}, "registerMethod": "ADMIN_PORTAL", "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-20T06:07:38.812Z"}, "updatedOn": {"$date": "2023-01-20T06:07:38.812Z"}, "__v": 0, "pointsLegacy": 100}, {"_id": {"$oid": "624fe7906a799cdc58714c05"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "TEST00002", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": null, "merchantLocationId": {"$oid": "616957aff2ac7710eb25e094"}, "type": "PRIMARY", "status": "ACTIVE", "points": 100, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-01-20T06:07:28.986Z"}, "registeredOn": {"$date": "2023-01-20T06:07:28.986Z"}, "additionalPhoneNumbers": [], "createdBy": {"$oid": "613dfc56eac5369848619fe0"}, "registerMethod": "ADMIN_PORTAL", "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-20T06:07:45.090Z"}, "updatedOn": {"$date": "2023-01-20T06:07:45.090Z"}, "__v": 0, "pointsLegacy": 100}, {"_id": {"$oid": "626aeda5fae2f3903c6bbc0b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "loyaltyId": "TEST00003", "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "parentMemberId": {"$oid": "619c732af1142b6ffc9c32e0"}, "merchantLocationId": {"$oid": "616957aff2ac7710eb25e094"}, "type": "SECONDARY", "status": "ACTIVE", "points": 0, "tierPoints": 0, "purchasesCount": 0, "purchasesValue": 0, "tags": [], "lastSeenOn": {"$date": "2023-01-20T06:07:28.986Z"}, "registeredOn": {"$date": "2023-01-20T06:07:28.986Z"}, "additionalPhoneNumbers": [], "createdBy": {"$oid": "613dfc56eac5369848619fe0"}, "registerMethod": "ADMIN_PORTAL", "cardReplacementCount": 0, "notificationPreference": {"preferredChannel": "EMAIL", "allowPromotionalNotifications": false}, "identifications": [], "rewardMetadata": [], "pointsToExpire": [], "historyEvents": [], "createdOn": {"$date": "2023-01-20T06:07:47.948Z"}, "updatedOn": {"$date": "2023-01-20T06:07:47.948Z"}, "__v": 0, "pointsLegacy": 0}]