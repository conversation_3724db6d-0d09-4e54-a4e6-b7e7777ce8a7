[{"_id": {"$oid": "6195caa6b41a2d3f53dd3026"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Black JS", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/caa32850-331a-11ed-af54-d90358e9b15f", "contact": {"name": "Black JS", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "225", "city": "Batticaloa", "zipOrPostcode": "22111"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 1, "countryName": "Sri Lanka", "businessRegistrationNumber": "123456", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [{"name": "fasfd", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "sfa", "line2": "fdsa", "line3": "dsaf", "city": "fdsaf", "stateOrProvince": "fdsafs", "zipOrPostcode": "423rw"}}], "technicalContacts": [{"name": "fdsfs", "mobileNumber": "534535", "email": "<EMAIL>", "address": {"line1": "5435", "line2": "5435", "line3": "5435", "city": "fdsafs", "stateOrProvince": "sdafsd", "zipOrPostcode": "234"}}], "createdOn": {"$date": "2021-11-18T03:38:14.250Z"}, "updatedOn": {"$date": "2023-02-15T04:59:08.762Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "visibleForNearestLocations": true, "countryISO2Code": "LK"}, {"_id": {"$oid": "619a9cd598ea23a5b884f10c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "fdsds", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/961674a0-4b00-11ec-8538-c8850b444a43.jpg", "contact": {"name": "fdsds", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "fdsf", "line2": "fdsf", "line3": "fdsfsd", "city": "fdsf", "stateOrProvince": "fdsf", "zipOrPostcode": "fdsf", "_id": {"$oid": "619a9cd598ea23a5b884f10e"}}, "_id": {"$oid": "619a9cd598ea23a5b884f10d"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 1, "countryName": "Republic Of Congo", "businessRegistrationNumber": "fsdf", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-21T19:24:05.898Z"}, "updatedOn": {"$date": "2021-11-21T20:02:18.864Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619aa14e98ea23a5b884f1c4"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "555233", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/3ffea1c0-4b03-11ec-8258-e301c341cead.jpg", "contact": {"name": "555233", "mobileNumber": "432432", "email": "<EMAIL>", "address": {"line1": "434", "line2": "f", "line3": "sdfsdfsd", "city": "fsdfs", "stateOrProvince": "fdsf", "zipOrPostcode": "fdsf", "_id": {"$oid": "619aa14e98ea23a5b884f1c6"}}, "_id": {"$oid": "619aa14e98ea23a5b884f1c5"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 1, "countryName": "Afghanistan", "businessRegistrationNumber": "fds", "options": {"enroll": false, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-21T19:43:10.441Z"}, "updatedOn": {"$date": "2021-11-21T20:03:31.462Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61f0cb35c87eadf412ab6f5f"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Bottles. Corp", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/9698e1a0-8193-11ec-8151-9da3dcaafa98", "contact": {"name": "Bottles. Corp", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "23", "line2": "32", "line3": "test", "city": "Colombo", "stateOrProvince": "Colombo", "zipOrPostcode": "333223"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Sri Lanka", "businessRegistrationNumber": "33344", "options": {"enroll": false, "earn": true, "redeemPoints": true, "redeemRewards": false, "refund": true, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-26T04:16:53.047Z"}, "updatedOn": {"$date": "2022-02-02T04:22:20.460Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61fa61b95ab9ca395358ff76"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "<PERSON>han", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/c4cb4c00-8415-11ec-89cd-d4049bae59a8", "contact": {"name": "<PERSON>han", "mobileNumber": "25012", "email": "<EMAIL>", "address": {"line1": "1234", "line2": "1234", "line3": "123", "city": "aa", "stateOrProvince": "test", "zipOrPostcode": "test"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Aland Islands", "businessRegistrationNumber": "123344", "options": {"enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-02T10:49:29.341Z"}, "updatedOn": {"$date": "2022-02-02T10:49:42.780Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62024c44ff22bf1c4462447a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "<PERSON>han", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/aa91fc50-88cd-11ec-88c1-85fdb94d9868", "contact": {"name": "<PERSON>han", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "123", "line2": "1233", "line3": "123", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "<PERSON><PERSON>", "zipOrPostcode": "12344"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 1, "countryName": "Guyana", "businessRegistrationNumber": "12345", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": false, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-08T10:56:04.412Z"}, "updatedOn": {"$date": "2022-12-09T19:40:30.261Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "countryISO2Code": "GY"}, {"_id": {"$oid": "62051b2dcdb98c388d55ce88"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "<PERSON>han", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/18f49c90-8a7a-11ec-86b8-be38277b1575", "contact": {"name": "<PERSON>han", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "1233", "line2": "123", "city": "sjj", "stateOrProvince": "ss", "zipOrPostcode": "111"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Aland Islands", "businessRegistrationNumber": "kk", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-10T14:03:25.671Z"}, "updatedOn": {"$date": "2022-02-10T14:03:59.894Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6209ebadcdb98c388d55eac2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "black JS", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/d5ac7390-8d58-11ec-8871-cac936054b61", "contact": {"name": "black JS", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "123", "line2": "123", "city": "g", "stateOrProvince": "k", "zipOrPostcode": "1234"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Aland Islands", "businessRegistrationNumber": "1234", "options": {"enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": false, "refund": true, "void": true, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-14T05:42:05.578Z"}, "updatedOn": {"$date": "2022-02-14T05:42:14.640Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "622ce36f2e4f86567ede0bee"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/7b2cdb70-a230-11ec-8ff7-30bef0bb53d3", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "43", "line2": "Malwaththa 2 rd", "line3": "King's Lane", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Central Province", "zipOrPostcode": "025"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 1, "countryISO2Code": "LK", "countryName": "Sri Lanka", "businessRegistrationNumber": "re34343", "options": {"adjustPoints": false, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-12T18:16:15.258Z"}, "updatedOn": {"$date": "2022-04-13T08:54:39.520Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "622cfca22e4f86567ede0da8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "testtttt", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/7ceba4a0-a23f-11ec-882e-89bb2116470b", "contact": {"name": "testtttt", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "fsdf", "line2": "fsdf", "line3": "sdff", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church", "zipOrPostcode": "234234234"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "fsf32432", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-12T20:03:46.073Z"}, "updatedOn": {"$date": "2022-03-27T17:58:22.937Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62418d9cc02a56655658106e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "C#", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/c17d47b0-ae81-11ec-a720-d7c714f08773", "contact": {"name": "C#", "mobileNumber": "***********", "email": "C#@gmail.com", "address": {"line1": "1234", "city": "Greenland", "stateOrProvince": "<PERSON>"}}, "status": "DRAFT", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-28T10:27:40.234Z"}, "updatedOn": {"$date": "2022-03-28T10:28:07.505Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627dff5048e66d15807d4c1b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Point Rule", "contact": {"name": "Test Point Rule", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "23", "city": "Greenland"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "12", "options": {"adjustPoints": true, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T06:48:48.768Z"}, "updatedOn": {"$date": "2022-05-13T18:46:51.868Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627e1ff948e66d15807d4e54"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 22", "contact": {"name": "Test Points 2", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "34", "city": "Speightstown"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": true, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T09:08:09.113Z"}, "updatedOn": {"$date": "2022-05-13T11:36:07.625Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627e252148e66d15807d4f15"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 3", "contact": {"name": "Test Points 3", "mobileNumber": "12469994034", "email": "<EMAIL>", "address": {"line1": "4432", "city": "Greenland"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T09:30:09.256Z"}, "updatedOn": {"$date": "2022-05-13T09:33:27.546Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627e9e7948e66d15807d51cc"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 4", "contact": {"name": "Test Points 2", "mobileNumber": "12469995334", "email": "<EMAIL>", "address": {"line1": "5545", "city": "Bridgetown"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T18:07:53.306Z"}, "updatedOn": {"$date": "2022-05-13T18:46:24.505Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627ea0ae48e66d15807d52d7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 7", "contact": {"name": "Test Points 7", "mobileNumber": "12465995434", "email": "<EMAIL>", "address": {"line1": "4d", "city": "Speightstown"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T18:17:18.212Z"}, "updatedOn": {"$date": "2022-05-13T18:46:45.056Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619541b1423d24e396dbf07e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Price's Electronics", "merchantLogoImageUrl": "https://betaenterprise.api.topnotch.club/api/coreservice/files/52b15b00-47cf-11ec-8acd-ddef44280f8a.jpeg", "contact": {"name": "Price's Electronics", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "21", "line3": "12", "city": "Greenland", "stateOrProvince": "<PERSON>"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 4, "countryName": "Barbados", "businessRegistrationNumber": "102360023", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [{"name": "Gongalegoda Banda", "mobileNumber": "250234", "email": "<EMAIL>", "address": {"line1": "1234", "line2": "1234", "line3": "1234", "city": "black heart", "stateOrProvince": "black heart SP", "zipOrPostcode": "12345432"}}], "technicalContacts": [{"name": "Gongalegoda Banda", "mobileNumber": "250234", "email": "<EMAIL>", "address": {"line1": "1234", "line2": "1234", "line3": "1234", "city": "black heart", "stateOrProvince": "black heart SP", "zipOrPostcode": "12345432"}}], "createdOn": {"$date": "2021-11-17T17:53:53.763Z"}, "updatedOn": {"$date": "2023-02-10T09:43:12.051Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "visibleForNearestLocations": true, "countryISO2Code": "BB"}, {"_id": {"$oid": "619a9c7c98ea23a5b884f0eb"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "gdfg", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/5cb43580-4b00-11ec-8ce3-f0f05d205049.jpg", "contact": {"name": "gdfg", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "sfs", "line2": "fdsf", "line3": "fdsf", "city": "f", "stateOrProvince": "fdf", "zipOrPostcode": "ddfsf", "_id": {"$oid": "619a9c7c98ea23a5b884f0ed"}}, "_id": {"$oid": "619a9c7c98ea23a5b884f0ec"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 1, "countryName": "France", "businessRegistrationNumber": "fdsfdsf", "options": {"enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-21T19:22:36.963Z"}, "updatedOn": {"$date": "2021-11-21T20:03:38.071Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61cc24ff6f0e93ba03b2a7ad"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/8d1765f0-6886-11ec-8f4e-736d2ac536a0.png", "contact": {"name": "test", "mobileNumber": "***********", "email": "test@test.com2", "address": {"line1": "4343", "line2": "4334", "line3": "345", "city": "3435", "stateOrProvince": "ew<PERSON>r", "zipOrPostcode": "324234"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "businessRegistrationNumber": "231231", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-12-29T09:06:07.350Z"}, "updatedOn": {"$date": "2022-01-13T16:30:31.141Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61e8530bda41ab5803bb3b35"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "fadf", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/68c5dec0-7952-11ec-8838-de87cd9b93c2.jpg", "contact": {"name": "fadf", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "dsad", "line2": "dad", "line3": "dasd", "city": "dasd", "stateOrProvince": "dsad", "zipOrPostcode": "da22"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Afghanistan", "businessRegistrationNumber": "2w1w", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-19T18:06:03.115Z"}, "updatedOn": {"$date": "2022-01-19T18:08:23.184Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee784c5d0d7a5807220945"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/3155eb80-7cfc-11ec-8bc3-fde6966c46a5.png", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "dad", "line2": "das", "line3": "das", "city": "dad", "stateOrProvince": "dad", "zipOrPostcode": "313"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 3, "countryName": "Albania", "businessRegistrationNumber": "fsdf", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T09:58:36.845Z"}, "updatedOn": {"$date": "2022-01-26T04:11:28.266Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee7d045d0d7a5807220c5e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/f74f75c0-7cfe-11ec-8d9d-1ca577be5777.png", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "dasd", "line2": "dasd", "line3": "dasd", "city": "dasd", "stateOrProvince": "dasd", "zipOrPostcode": "dasd"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Antigua And Barbuda", "businessRegistrationNumber": "dasd", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T10:18:44.332Z"}, "updatedOn": {"$date": "2022-01-26T04:11:35.932Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61f001bac87eadf412ab66b6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Edit", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/93739b60-7de6-11ec-84ab-fd076d75491e", "contact": {"name": "Test Edit", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"city": "Colo"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "France", "businessRegistrationNumber": "ewe", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-25T13:57:14.364Z"}, "updatedOn": {"$date": "2022-01-30T06:39:25.600Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61f0026ec87eadf412ab66c1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "fdaf", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/15cebb30-7de7-11ec-86fd-97b425c99cc1", "contact": {"name": "fdaf", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "sdfdf", "line2": "fdsf", "line3": "fdsf", "city": "fdsfdsf", "stateOrProvince": "fdsf", "zipOrPostcode": "234324"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Antigua And Barbuda", "businessRegistrationNumber": "4342", "options": {"enroll": false, "earn": false, "redeemPoints": true, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-25T14:00:14.019Z"}, "updatedOn": {"$date": "2022-01-25T14:03:04.026Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61fa1f0e5ab9ca395358f950"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "gongalegoda banda", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/226e6cc0-83ee-11ec-8cd8-c131cb2324ec", "contact": {"name": "gongalegoda banda", "mobileNumber": "251234", "email": "<EMAIL>", "address": {"line1": "ABC", "line2": "1234", "line3": "1234", "city": "<PERSON><PERSON>", "stateOrProvince": "Berat County", "zipOrPostcode": "2022"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 1, "countryName": "Albania", "businessRegistrationNumber": "1233", "options": {"adjustPoints": true, "enroll": true, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-02T06:05:02.954Z"}, "updatedOn": {"$date": "2022-04-09T10:45:23.890Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "visibleForNearestLocations": false, "countryISO2Code": "AL"}, {"_id": {"$oid": "62185a6cb278d21bb7bd3b6e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "halfbloed prins", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/0c5c4d50-95f3-11ec-8bf7-0982c4cb537f", "contact": {"name": "halfbloed prins", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "12346", "line2": "12345", "city": "hogwarts", "stateOrProvince": "slytherin", "zipOrPostcode": "119"}}, "status": "DRAFT", "type": "INTERNAL", "locationsCount": 4, "countryName": "Aland Islands", "businessRegistrationNumber": "12345678", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": false, "refund": true, "void": false, "claimReward": true}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-25T04:26:20.173Z"}, "updatedOn": {"$date": "2022-04-05T06:04:52.408Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "622cf5a62e4f86567ede0ca2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "tertwr", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/57029540-a23b-11ec-82b9-b30935b27c5b", "contact": {"name": "tertwr", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "4324", "line2": "4324", "line3": "324", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church", "zipOrPostcode": "1212"}}, "status": "DRAFT", "type": "EXTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "432424", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": true, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-12T19:33:58.928Z"}, "updatedOn": {"$date": "2022-03-12T19:34:35.766Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "622cf9ae2e4f86567ede0cfd"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/a53a40b0-a514-11ec-8287-b246ca5ff97a", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "fsdfsdf", "line2": "fsfs", "line3": "fdsf", "city": "fsdfsd", "stateOrProvince": "<PERSON>", "zipOrPostcode": "5454235"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "fsdfw3rr", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": true, "refund": false, "void": false, "claimReward": true}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-12T19:51:10.672Z"}, "updatedOn": {"$date": "2022-03-29T18:10:54.064Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6231aa30d10d68b3da295480"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/4f14ba90-a509-11ec-846c-ce19492b5470", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "q3"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "fsdfdsf", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-16T09:13:20.954Z"}, "updatedOn": {"$date": "2022-03-16T17:39:46.385Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6231ac4dd10d68b3da2954c4"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "tret", "contact": {"name": "tret", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "434", "line2": "43", "zipOrPostcode": "e34"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "trtret", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-16T09:22:21.781Z"}, "updatedOn": {"$date": "2022-03-24T09:33:51.832Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "623b58e08d1b3e30481d7e87"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test", "contact": {"name": "Test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "34", "line2": "1122", "city": "Bathsheba", "stateOrProvince": "<PERSON>"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-23T17:29:04.242Z"}, "updatedOn": {"$date": "2022-03-24T09:32:43.187Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "623b79958d1b3e30481d8963"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "ertst", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/3685ec40-aae2-11ec-8de8-625fa17a5aab", "contact": {"name": "ertst", "mobileNumber": "24643432234", "email": "<EMAIL>", "address": {"line1": "5t4", "city": "Greenland"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-23T19:48:37.160Z"}, "updatedOn": {"$date": "2022-03-24T09:02:12.194Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6256940b70d1cd03569b77ca"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "testttestnamelongnam test test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/dd5b7120-bb09-11ec-a039-47641f3cedeb", "contact": {"name": "testttestnamelongnam long name merchant for badge intable test test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "23", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}}, "status": "DRAFT", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "223232", "options": {"adjustPoints": true, "enroll": false, "earn": false, "redeemPoints": true, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-04-13T09:12:43.635Z"}, "updatedOn": {"$date": "2022-04-13T18:54:00.348Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "626adf236c7308032754a801"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Merchant Colloect Points", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/5e78bff0-c722-11ec-bc1c-2b0b45db03d0", "contact": {"name": "Merchant Colloect Points", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "as<PERSON>s", "city": "Greenland", "stateOrProvince": "<PERSON>", "zipOrPostcode": "32323"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "232342342", "options": {"adjustPoints": true, "enroll": false, "earn": true, "redeemPoints": true, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-04-28T18:38:27.021Z"}, "updatedOn": {"$date": "2022-04-28T18:53:27.947Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627ea03d48e66d15807d52a0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 6", "contact": {"name": "Test Points 6", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "44", "city": "Crane"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T18:15:25.154Z"}, "updatedOn": {"$date": "2022-05-13T18:46:39.078Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619525ab423d24e396dbe8b3"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "osama bin laden", "merchantLogoImageUrl": "https://betaenterprise.api.topnotch.club/api/coreservice/files/9d752060-47be-11ec-8153-82fd80e0a80f.jpeg", "contact": {"name": "osama bin laden", "mobileNumber": "24623444444444444", "email": "<EMAIL>", "address": {"city": "galle", "_id": {"$oid": "61953a3c423d24e396dbec5f"}}, "_id": {"$oid": "619525ab423d24e396dbe8b4"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Sri Lanka", "businessRegistrationNumber": "12342343", "options": {"enroll": true, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": true, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-17T15:54:19.692Z"}, "updatedOn": {"$date": "2021-11-17T17:25:13.028Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61953b2a423d24e396dbeccc"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://betaenterprise.api.topnotch.club/api/coreservice/files/d167ab30-47ce-11ec-8dca-5352e846a2cc.jpeg", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"city": "Colombo", "_id": {"$oid": "61953b2a423d24e396dbecce"}}, "_id": {"$oid": "61953b2a423d24e396dbeccd"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Sri Lanka", "businessRegistrationNumber": "3444", "options": {"enroll": false, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-17T17:26:02.048Z"}, "updatedOn": {"$date": "2021-11-17T18:05:32.438Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619551e0423d24e396dc41c8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "AAA", "merchantLogoImageUrl": "https://betaenterprise.api.topnotch.club/api/coreservice/files/f8384cb0-47d8-11ec-8dda-adee4a82df95.png", "contact": {"name": "AAA", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"city": "Gampaha", "_id": {"$oid": "619551e0423d24e396dc41ca"}}, "_id": {"$oid": "619551e0423d24e396dc41c9"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Sri Lanka", "businessRegistrationNumber": "111111111", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-17T19:02:56.522Z"}, "updatedOn": {"$date": "2021-11-17T19:03:03.962Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619a608598ea23a5b884ed36"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Mugs. Cop s", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/c8a1a580-78ea-11ec-8e07-2080c52db31a", "contact": {"name": "Mugs. Cop", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "23", "line2": "<PERSON><PERSON>", "line3": "Kandy", "city": "Kandy District", "stateOrProvince": "Central Province", "zipOrPostcode": "22331"}}, "status": "ACTIVE", "type": "EXTERNAL", "locationsCount": 7, "countryName": "Sri Lanka", "businessRegistrationNumber": "QW22321", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [{"name": "<PERSON><PERSON><PERSON>", "mobileNumber": "246967856576576", "email": "<EMAIL>", "address": {"line1": "gjhgjh", "line2": "kljkhjkh", "line3": "kgjhjhfgj", "city": "jkhjgjhg", "stateOrProvince": "Sabaragamu Province", "zipOrPostcode": "898969", "_id": {"$oid": "619b49b198ea23a5b884f9e1"}}, "_id": {"$oid": "619b49b198ea23a5b884f9e0"}}], "technicalContacts": [{"name": "<PERSON><PERSON><PERSON>", "mobileNumber": "2468789676786", "email": "<EMAIL>", "address": {"line1": "hgjhjgj", "line2": "kghgjhgj", "line3": "hhgjhghg", "city": "gghgh", "stateOrProvince": "Sabaragamu Province", "zipOrPostcode": "70500", "_id": {"$oid": "619b499398ea23a5b884f9d8"}}, "_id": {"$oid": "619b499398ea23a5b884f9d7"}}], "createdOn": {"$date": "2021-11-21T15:06:45.610Z"}, "updatedOn": {"$date": "2022-10-20T10:20:32.988Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "visibleForNearestLocations": true, "countryISO2Code": "LK"}, {"_id": {"$oid": "619aa18a98ea23a5b884f1db"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "5555", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/646d1be0-4b03-11ec-8800-50bcea487f7f.jpg", "contact": {"name": "5555", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "sdfds", "line2": "fdsfs", "line3": "fdsfds", "city": "fdsf", "stateOrProvince": "fsdf", "zipOrPostcode": "fdsf3", "_id": {"$oid": "619aa18a98ea23a5b884f1dd"}}, "_id": {"$oid": "619aa18a98ea23a5b884f1dc"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 1, "countryName": "Antarctica", "businessRegistrationNumber": "fsdffd", "options": {"enroll": true, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": false, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-21T19:44:10.237Z"}, "updatedOn": {"$date": "2021-11-21T20:02:11.677Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b334e98ea23a5b884f6e7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Caps LTD", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/463b3d30-4b5a-11ec-8321-666db6eb2f42.png", "contact": {"name": "Caps LTD", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "24", "line2": "Muller st.", "line3": "10th Ave", "city": "Colombo", "stateOrProvince": "Western", "zipOrPostcode": "30302", "_id": {"$oid": "619b334e98ea23a5b884f6e9"}}, "_id": {"$oid": "619b334e98ea23a5b884f6e8"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 2, "countryName": "Sri Lanka", "businessRegistrationNumber": "qwe122", "options": {"enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-22T06:06:06.772Z"}, "updatedOn": {"$date": "2022-01-13T16:02:05.243Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee99565d0d7a58072215e6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "ttds", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/e09252b0-7d0f-11ec-8ec4-c6e62f3fdfdd.png", "contact": {"name": "ttds", "mobileNumber": "332323", "email": "fdsff@fsf", "address": {"line1": "323", "line2": "dsdsa", "line3": "fdsf", "city": "fdsf", "stateOrProvince": "fsdfs", "zipOrPostcode": "fdsfs32"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Aland Islands", "businessRegistrationNumber": "fdsfs", "options": {"enroll": false, "earn": false, "redeemPoints": true, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T12:19:34.161Z"}, "updatedOn": {"$date": "2022-01-25T14:03:24.104Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee9c2d5d0d7a5807221652"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "fdsf", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/92849040-7d11-11ec-8750-26bffb99e5f2.png", "contact": {"name": "fdsf", "mobileNumber": "***********", "email": "fdsf@fds", "address": {"line1": "fdsf", "line2": "fdsf", "line3": "fdsf", "city": "fdsff", "stateOrProvince": "fdsf", "zipOrPostcode": "fdf"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "<PERSON><PERSON><PERSON>", "businessRegistrationNumber": "fdsfs", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T12:31:41.190Z"}, "updatedOn": {"$date": "2022-01-25T20:09:32.213Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee9c675d0d7a580722165a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "qqqq", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/b206f840-7d11-11ec-8315-a9395cb627f2.png", "contact": {"name": "qqqq", "mobileNumber": "***********", "email": "fdf@we", "address": {"line1": "fdfsdf", "line2": "fsfsd", "line3": "fdsfs", "city": "fdsff", "stateOrProvince": "fdsfd", "zipOrPostcode": "324"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Finland", "businessRegistrationNumber": "fdsf", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T12:32:39.278Z"}, "updatedOn": {"$date": "2022-01-25T14:03:18.330Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee9d2b5d0d7a580722168d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test1222", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/2955b990-7d12-11ec-872d-6f82a1883df7.png", "contact": {"name": "test1222", "mobileNumber": "***********", "email": "te@re", "address": {"line1": "fdsfdsf", "line2": "fdsfsd", "line3": "fdsfsdf", "city": "fdsfs", "stateOrProvince": "fsdfds", "zipOrPostcode": "fdsfds"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Antigua And Barbuda", "businessRegistrationNumber": "fdsfs", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T12:35:55.135Z"}, "updatedOn": {"$date": "2022-01-25T14:03:11.907Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61f05895c87eadf412ab6c5b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Dancer", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/a56d3590-7e1a-11ec-8646-d186395776ad", "contact": {"name": "Dancer", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "22", "line2": "11", "line3": "test", "city": "dance", "stateOrProvince": "dance", "zipOrPostcode": "332332"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Cambodia", "businessRegistrationNumber": "12221", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": true, "refund": false, "void": true, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-25T20:07:49.107Z"}, "updatedOn": {"$date": "2022-01-25T20:09:24.339Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61f62adb5d70b9f8ac33bbf6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/b01865c0-8192-11ec-826b-763cee4fe9b0", "contact": {"name": "test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "ffd", "line2": "fdf", "line3": "fsdf", "city": "ffdf", "stateOrProvince": "fdsf", "zipOrPostcode": "ffds"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 1, "countryName": "Aland Islands", "businessRegistrationNumber": "2323", "options": {"enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-30T06:06:19.295Z"}, "updatedOn": {"$date": "2022-01-30T06:07:06.658Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62186109b278d21bb7bd3ca1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "kchabadam", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/ff03b810-95f6-11ec-896a-378dc87f0b22", "contact": {"name": "kchabadam", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "1234", "line2": "12345", "zipOrPostcode": "12345"}}, "status": "ACTIVE", "type": "INTERNAL", "locationsCount": 1, "countryName": "Albania", "businessRegistrationNumber": "12345", "options": {"enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-25T04:54:33.898Z"}, "updatedOn": {"$date": "2023-04-07T07:44:50.106Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "countryISO2Code": "AL"}, {"_id": {"$oid": "623b796d8d1b3e30481d895d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "rest", "contact": {"name": "rest", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "34", "city": "Crane"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": true, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-23T19:47:57.201Z"}, "updatedOn": {"$date": "2022-03-24T09:02:28.809Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627ea00348e66d15807d529a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 5", "contact": {"name": "Test Points 5", "mobileNumber": "12469994843", "email": "<EMAIL>", "address": {"line1": "33223", "city": "Bridgetown"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T18:14:27.387Z"}, "updatedOn": {"$date": "2022-05-13T18:46:33.336Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627ea7c748e66d15807d53a1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Point Rule BUG", "contact": {"name": "Test Point Rule BUG", "mobileNumber": "12464949332", "email": "<EMAIL>", "address": {"line1": "554", "city": "Bridgetown"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T18:47:35.035Z"}, "updatedOn": {"$date": "2022-05-13T18:49:08.889Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627ea9d848e66d15807d5409"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Point Rule", "contact": {"name": "Test Point Rule BUG", "mobileNumber": "12465566595", "email": "<EMAIL>", "address": {"line1": "434", "city": "Crane"}}, "status": "DRAFT", "type": "EXTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": true, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T18:56:24.880Z"}, "updatedOn": {"$date": "2022-09-14T04:47:50.321Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61955216423d24e396dc41d5"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "abcd", "merchantLogoImageUrl": "https://betaenterprise.api.topnotch.club/api/coreservice/files/186bb530-47d9-11ec-8215-098454726da8.png", "contact": {"name": "abcd", "mobileNumber": "***********", "email": "anjali456ap<PERSON><PERSON>@gmail.com", "address": {"city": "<PERSON><PERSON><PERSON><PERSON>", "_id": {"$oid": "61955216423d24e396dc41d7"}}, "_id": {"$oid": "61955216423d24e396dc41d6"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Sri Lanka", "businessRegistrationNumber": "211111", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-17T19:03:50.420Z"}, "updatedOn": {"$date": "2021-11-18T11:08:41.525Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6197f83d98ea23a5b884df38"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/38e3d0f0-4ad0-11ec-8fd2-2e3a83fbf352.jpg", "contact": {"name": "Test", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "32", "line2": "32", "line3": "22", "city": "22", "stateOrProvince": "22", "zipOrPostcode": "3333", "_id": {"$oid": "6197f83d98ea23a5b884df3a"}}, "_id": {"$oid": "6197f83d98ea23a5b884df39"}}, "status": "ARCHIVED", "type": "EXTERNAL", "locationsCount": 0, "countryName": "Sri Lanka", "businessRegistrationNumber": "wew12222", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-19T19:17:17.864Z"}, "updatedOn": {"$date": "2021-11-21T15:04:11.127Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619a9c0f98ea23a5b884f0d2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "tet", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/1fef9180-4b00-11ec-8b9f-5f460a1eaf84.jpg", "contact": {"name": "tet", "mobileNumber": "34243", "email": "<EMAIL>", "address": {"line1": "fdsfds", "line2": "fsdf", "line3": "fsdfsd", "city": "fdsfsf", "stateOrProvince": "fsdfs", "zipOrPostcode": "sdffds", "_id": {"$oid": "619a9c0f98ea23a5b884f0d4"}}, "_id": {"$oid": "619a9c0f98ea23a5b884f0d3"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 1, "countryName": "Faroe Islands", "businessRegistrationNumber": "fdfsd", "options": {"enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-21T19:20:47.225Z"}, "updatedOn": {"$date": "2021-11-21T20:03:19.310Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619a9fa798ea23a5b884f16a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "fdsf332222", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/43647e80-4b02-11ec-8bdc-4d49cc98aab4.jpg", "contact": {"name": "fdsf332222", "mobileNumber": "246432432423", "email": "<EMAIL>", "address": {"line1": "sdf", "line2": "fdsf", "line3": "fsdf", "city": "fdsfsd", "stateOrProvince": "fsds", "zipOrPostcode": "fdsf", "_id": {"$oid": "619a9fa798ea23a5b884f16c"}}, "_id": {"$oid": "619a9fa798ea23a5b884f16b"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 1, "countryName": "Haiti", "businessRegistrationNumber": "dfsdfa", "options": {"enroll": false, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-11-21T19:36:07.983Z"}, "updatedOn": {"$date": "2021-11-21T20:03:24.952Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61cc259d6f0e93ba03b2a7ff"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test b1", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/7b5afbc0-72da-11ec-886a-a4a804a81709.jpg", "contact": {"name": "Test b1", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "asas", "line2": "sdsad", "line3": "sa", "city": "kandy", "stateOrProvince": "central", "zipOrPostcode": "123123"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Aland Islands", "businessRegistrationNumber": "2234324", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2021-12-29T09:08:45.692Z"}, "updatedOn": {"$date": "2022-01-13T16:36:26.203Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61e854afda41ab5803bb3ba6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "fsdf", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/08749620-7bb8-11ec-8576-849522619516", "contact": {"name": "fsdf", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "fdsf", "line2": "fsf", "line3": "fsf", "city": "fdsf", "stateOrProvince": "fsf32", "zipOrPostcode": "fsf"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Afghanistan", "businessRegistrationNumber": "311", "options": {"enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-19T18:13:03.958Z"}, "updatedOn": {"$date": "2022-01-26T04:14:49.249Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61ee98d65d0d7a58072215c0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "dsad", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/93567120-7d0f-11ec-8a07-7af72655c9f8.png", "contact": {"name": "dsad", "mobileNumber": "***********", "email": "dasd@fdf", "address": {"line1": "dsfd", "line2": "czcz", "line3": "czxc", "city": "czxc", "stateOrProvince": "czc", "zipOrPostcode": "3243"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryName": "Czech Republic", "businessRegistrationNumber": "fdfdsf", "options": {"enroll": false, "earn": true, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-01-24T12:17:26.814Z"}, "updatedOn": {"$date": "2022-01-26T04:12:07.977Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61fa1cfb5ab9ca395358f903"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "<PERSON>", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/147bc920-83ec-11ec-8e5d-964ca9222a24", "contact": {"name": "<PERSON>", "mobileNumber": "246898767454", "email": "<EMAIL>", "address": {"line1": "12/A", "line2": "Flower road", "line3": "colombo", "city": "Colombo 05", "stateOrProvince": "Colombo District", "zipOrPostcode": "79998"}}, "status": "DRAFT", "type": "INTERNAL", "locationsCount": 1, "countryName": "Sri Lanka", "businessRegistrationNumber": "897678", "options": {"adjustPoints": true, "enroll": false, "earn": false, "redeemPoints": true, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-02T05:56:11.582Z"}, "updatedOn": {"$date": "2022-11-23T18:02:49.403Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "visibleForNearestLocations": false, "countryISO2Code": "LK"}, {"_id": {"$oid": "622c6b532e4f86567eddb5d1"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "A<PERSON><PERSON>", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/c8e1ab40-a1e8-11ec-828f-387a7059f369", "contact": {"name": "A<PERSON><PERSON>", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "21", "line2": "Colombo Street", "line3": "Kandy City Center", "city": "Kandy", "stateOrProvince": "Central Province", "zipOrPostcode": "1001"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "LK", "countryName": "Sri Lanka", "businessRegistrationNumber": "12332", "options": {"adjustPoints": false, "enroll": true, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": true}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-12T09:43:47.717Z"}, "updatedOn": {"$date": "2022-03-14T04:16:39.311Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "623aa28b95fd956f8e974736"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "<PERSON>", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/f0f795f0-aa61-11ec-8b4f-96426176c989", "contact": {"name": "<PERSON>", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "123", "city": "<PERSON><PERSON><PERSON>", "stateOrProvince": "Christ Church"}}, "status": "DRAFT", "type": "EXTERNAL", "locationsCount": 3, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": true, "earn": false, "redeemPoints": true, "redeemRewards": true, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-03-23T04:31:07.953Z"}, "updatedOn": {"$date": "2022-04-03T19:57:00.059Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627e208348e66d15807d4e6a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 3", "contact": {"name": "Test Points 3", "mobileNumber": "12464949932", "email": "<EMAIL>", "address": {"line1": "4443", "city": "Holetown"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T09:10:28.003Z"}, "updatedOn": {"$date": "2022-05-13T09:15:13.627Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "627e267248e66d15807d4f71"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "Test Points 3", "contact": {"name": "Test Points 3", "mobileNumber": "24644383943", "email": "<EMAIL>", "address": {"line1": "4223", "city": "Greenland"}}, "status": "ARCHIVED", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "BB", "countryName": "Barbados", "options": {"adjustPoints": false, "enroll": false, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-05-13T09:35:46.245Z"}, "updatedOn": {"$date": "2022-05-13T11:36:18.068Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63ec7e71fdbb49b2588b9f63"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantName": "<PERSON>ura", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/ad3a95e0-acfb-11ed-96da-37447c3cb8f9", "contact": {"name": "<PERSON>ura", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "1234", "city": "Bathsheba", "stateOrProvince": "<PERSON>"}}, "status": "ACTIVE", "type": "INTERNAL", "locationsCount": 1, "countryISO2Code": "BB", "countryName": "Barbados", "businessRegistrationNumber": "12345", "options": {"adjustPoints": true, "enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "visibleForNearestLocations": true, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2023-02-15T06:40:49.700Z"}, "updatedOn": {"$date": "2023-02-15T06:49:00.042Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "620a1287cdb98c388d55f255"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "617ee8fcc6350cdeab2403fb"}, "merchantName": "Black JS", "merchantLogoImageUrl": "https://d1umi9h6hi9bqe.cloudfront.net/images/fd0e6cb0-8d6f-11ec-8e6f-8dd7c7c8f2de", "contact": {"name": "Black JS", "mobileNumber": "***********", "email": "<EMAIL>", "address": {"line1": "1234", "line2": "1234", "zipOrPostcode": "2000"}}, "status": "ACTIVE", "type": "INTERNAL", "locationsCount": 1, "countryName": "Brazil", "businessRegistrationNumber": "12345", "options": {"enroll": true, "earn": true, "redeemPoints": true, "redeemRewards": true, "refund": true, "void": true, "claimReward": true}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2022-02-14T08:27:51.283Z"}, "updatedOn": {"$date": "2022-03-12T10:51:07.675Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "countryISO2Code": "BR"}, {"_id": {"$oid": "63ca2fa7f64e29b24c6bd036"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "63ca2fa7f64e29b24c6bd035"}, "merchantName": "Test Merchant", "merchantLogoImageUrl": "https://blog.getshoutout.com/wp-content/uploads/2021/04/ShoutOUT_Logo-e1619042507876.png", "contact": {"mobileNumber": "***********", "email": "<EMAIL>", "address": {"city": "Colombo"}}, "status": "DRAFT", "type": "INTERNAL", "locationsCount": 0, "countryISO2Code": "LK", "countryName": "Sri Lanka", "businessRegistrationNumber": "PV11111", "options": {"adjustPoints": false, "enroll": true, "earn": false, "redeemPoints": false, "redeemRewards": false, "refund": false, "void": false, "claimReward": false}, "visibleForNearestLocations": false, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "billingContacts": [], "technicalContacts": [], "createdOn": {"$date": "2023-01-20T06:07:35.281Z"}, "updatedOn": {"$date": "2023-01-20T06:07:35.281Z"}, "__v": 0}]