[{"_id": {"$oid": "61954f1a423d24e396dc4087"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "General Spending Rule 01", "description": "aaaaaaaaa", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 10, "daysOfWeek": [], "_id": {"$oid": "61954f1a423d24e396dc4088"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-17T18:51:06.742Z"}, "updatedOn": {"$date": "2021-11-19T17:41:52.664Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61978822c85d41818d3fe104"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "Test Location Rule", "description": "jkgjhgdtgh", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 20, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-19T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-11-29T18:30:00.000Z"}, "daysOfWeek": [], "_id": {"$oid": "61978822c85d41818d3fe105"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:18:58.842Z"}, "updatedOn": {"$date": "2021-11-30T03:28:22.257Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b662998ea23a5b88500ad"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Rule Name", "description": "no", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 100, "affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "isTierBased": false, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 0, "_id": {"$oid": "619b662998ea23a5b88500af"}}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 0, "_id": {"$oid": "619b662998ea23a5b88500b0"}}], "_id": {"$oid": "619b662998ea23a5b88500ae"}}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-22T09:43:05.299Z"}, "updatedOn": {"$date": "2021-11-30T03:29:24.046Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619df139ca76ef698b03149c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Location based rule test ", "description": "test", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 10, "merchantLocationId": {"$oid": "619a60ed98ea23a5b884ed5a"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-24T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-11-29T18:30:00.000Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-24T08:00:57.146Z"}, "updatedOn": {"$date": "2021-11-24T08:01:43.555Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61a59d38650a00b39a95dfd2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Location based rule 1", "description": "Test 3456", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 100, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "PERIOD", "fixedFromDate": {"$date": {"$numberLong": "-62031503964000"}}, "fixedToDate": {"$date": {"$numberLong": "-62010422364000"}}, "daysOfWeek": ["THURSDAY", "FRIDAY", "SUNDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-30T03:40:40.204Z"}, "updatedOn": {"$date": "2023-01-24T04:12:05.592Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "maxPoints": 100}, {"_id": {"$oid": "61fa43f65ab9ca395358fc62"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "General Spending Rule 1", "description": "des", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 2, "recurrence": "PERIOD", "fixedFromDate": {"$date": "2022-04-12T11:51:53.865Z"}, "fixedToDate": {"$date": "2022-04-25T18:30:00.000Z"}, "daysOfWeek": ["TUESDAY", "SUNDAY", "WEDNESDAY", "SATURDAY", "FRIDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-02-02T08:42:30.054Z"}, "updatedOn": {"$date": "2023-01-24T04:12:22.296Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62185c41b278d21bb7bd3bdd"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "62185a6cb278d21bb7bd3b6e"}, "name": "Expecto Patronum", "description": "I expect (or await) a guardian", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 100, "merchantLocationId": {"$oid": "62185b61b278d21bb7bd3b82"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-02-24T18:30:00.000Z"}, "fixedToDate": {"$date": "2022-02-28T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-02-25T04:34:09.534Z"}, "updatedOn": {"$date": "2023-01-24T04:12:27.548Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6242f469c02a5665565825bd"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test", "description": "test", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 3, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T11:58:33.611Z"}, "updatedOn": {"$date": "2022-03-29T12:23:43.417Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6243315bc02a566556582cf2"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Test", "description": "test", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 3, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "TUESDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T16:18:35.302Z"}, "updatedOn": {"$date": "2023-01-24T04:13:39.975Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62433a16c02a566556582ea5"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test23", "description": "13131", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 34, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T16:55:50.893Z"}, "updatedOn": {"$date": "2022-03-29T16:57:49.497Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62433a83c02a566556582ec0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test", "description": "testst33", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 3, "merchantLocationId": {"$oid": "6242f44bc02a5665565825ae"}, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T16:57:39.883Z"}, "updatedOn": {"$date": "2022-03-29T16:57:55.051Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624345e8c02a566556583232"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test", "description": "Test343", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 23, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T17:46:16.215Z"}, "updatedOn": {"$date": "2022-04-29T09:09:42.229Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624ad79185df8d81989d3dfb"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Test", "description": "teststst", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 23, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 51, "createdOn": {"$date": "2022-04-04T11:33:37.772Z"}, "updatedOn": {"$date": "2022-08-24T18:52:02.849Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624d431cd772aa04dbebc0db"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "name": "Rule Name", "description": "no name", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 10001, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-06T07:37:00.382Z"}, "updatedOn": {"$date": "2022-04-07T06:30:30.938Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62507e518a57bca455711286"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "Tetsts3434343", "description": "Test", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 23, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "TUESDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 9, "createdOn": {"$date": "2022-04-08T18:26:25.303Z"}, "updatedOn": {"$date": "2023-01-24T04:14:08.824Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "625080e38a57bca4557112eb"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "TestRTR##$#$", "description": "TEsts23423", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 43, "merchantLocationId": {"$oid": "6197a7aac85d41818d3fea76"}, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "SUNDAY", "THURSDAY", "FRIDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-08T18:37:23.074Z"}, "updatedOn": {"$date": "2023-01-24T04:14:13.843Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62512ab38a57bca455711745"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "61fa1cfb5ab9ca395358f903"}, "name": "Tesat", "description": "Tst", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 23, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-09T06:41:55.503Z"}, "updatedOn": {"$date": "2022-04-09T06:42:21.910Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "625163298a57bca455711ea7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "61fa1f0e5ab9ca395358f950"}, "name": "Test", "description": "TEssss", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 33, "merchantLocationId": {"$oid": "6249fdc73795bb2119dda1dc"}, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "WEDNESDAY", "FRIDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-09T10:42:49.507Z"}, "updatedOn": {"$date": "2023-01-24T04:14:27.547Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "625545598a57bca455713992"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Test Aff 2", "description": "Aff 2", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 0, "affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "isTierBased": true, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 80}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 90}, {"tierId": {"$oid": "61c01e4e10bfed0ab66a4b92"}, "amountPerPoint": 1000}]}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 5, "createdOn": {"$date": "2022-04-12T09:24:41.971Z"}, "updatedOn": {"$date": "2023-01-24T04:15:00.336Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6364f90d604849e367695f48"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "12", "description": "12", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 12, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-11-05T18:30:00.000Z"}, "fixedToDate": {"$date": "2022-11-10T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 1, "createdOn": {"$date": "2022-11-04T11:35:41.178Z"}, "updatedOn": {"$date": "2022-11-11T09:24:11.486Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619622cab41a2d3f53dd43d0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Massy Sunset Crest 123", "description": "Location based rule testing", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 30, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-18T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-12-15T18:30:00.000Z"}, "daysOfWeek": [], "_id": {"$oid": "619622cab41a2d3f53dd43d1"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-18T09:54:18.990Z"}, "updatedOn": {"$date": "2021-11-30T03:28:10.729Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61962457b41a2d3f53dd4680"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "61955216423d24e396dc41d5"}, "name": "Affinity group rule", "description": "Test with tiers", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 0, "affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "isTierBased": true, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 17, "_id": {"$oid": "61962457b41a2d3f53dd4682"}}, {"tierId": {"$oid": "619546b0423d24e396dc3d2d"}, "amountPerPoint": 20, "_id": {"$oid": "61962457b41a2d3f53dd4683"}}, {"tierId": {"$oid": "619546d2423d24e396dc3d37"}, "amountPerPoint": 22, "_id": {"$oid": "61962457b41a2d3f53dd4684"}}], "_id": {"$oid": "61962457b41a2d3f53dd4681"}}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-18T10:00:55.172Z"}, "updatedOn": {"$date": "2021-11-30T03:28:16.657Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61978957c85d41818d3fe14d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Test rule", "description": "jhfghfghdfgsd", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 22, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "FOREVER", "daysOfWeek": ["SUNDAY", "FRIDAY"], "_id": {"$oid": "61978957c85d41818d3fe14e"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:24:07.071Z"}, "updatedOn": {"$date": "2021-11-30T03:28:41.267Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b27b198ea23a5b884f5bc"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "no name", "description": "llkl", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 1000, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-22T05:08:22.198Z"}, "fixedToDate": {"$date": "2021-11-22T05:08:22.198Z"}, "daysOfWeek": [], "_id": {"$oid": "619b27b198ea23a5b884f5bd"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-22T05:16:33.264Z"}, "updatedOn": {"$date": "2021-11-30T03:29:16.621Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b35cc98ea23a5b884f78c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "no name", "description": "no", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 100, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-22T06:15:33.187Z"}, "fixedToDate": {"$date": "2021-11-22T06:15:33.187Z"}, "daysOfWeek": [], "_id": {"$oid": "619b35cc98ea23a5b884f78d"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-22T06:16:44.830Z"}, "updatedOn": {"$date": "2021-11-22T06:20:43.980Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b36aa98ea23a5b884f7f7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619b334e98ea23a5b884f6e7"}, "name": "Rule Name", "description": "jj", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 100, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-22T06:15:33.187Z"}, "fixedToDate": {"$date": "2021-11-22T06:15:33.187Z"}, "daysOfWeek": [], "_id": {"$oid": "619b36aa98ea23a5b884f7f8"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-22T06:20:26.515Z"}, "updatedOn": {"$date": "2021-11-22T06:20:36.278Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6197f20a9e3c056d7b855c65"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Enroll bonus", "description": "Enroll bonus", "type": "NON_TRANSACTIONAL", "subType": "ENROLL", "ruleData": {"daysOfWeek": [], "points": 5, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T18:50:50.793Z"}, "updatedOn": {"$date": "2023-03-13T16:48:43.585Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "maxPoints": 10}, {"_id": {"$oid": "6197f1a39e3c056d7b855c61"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Signup bonus", "description": "Signup bonus", "type": "NON_TRANSACTIONAL", "subType": "SIGNUP", "ruleData": {"daysOfWeek": [], "points": 30, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T18:49:07.084Z"}, "updatedOn": {"$date": "2023-01-24T04:46:25.234Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "maxPoints": 1}, {"_id": {"$oid": "6197f2259e3c056d7b855c69"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Birthday bonus", "description": "Birthday bonus", "type": "NON_TRANSACTIONAL", "subType": "BIRTHDAY", "ruleData": {"daysOfWeek": [], "points": 800, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "DISABLED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T18:51:17.967Z"}, "updatedOn": {"$date": "2022-04-12T13:03:07.168Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61cafc776f0e93ba03b2903b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "no name", "description": "kk", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 100, "recurrence": "FOREVER", "daysOfWeek": ["SATURDAY", "FRIDAY", "THURSDAY", "TUESDAY", "WEDNESDAY", "MONDAY", "SUNDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-12-28T12:00:55.100Z"}, "updatedOn": {"$date": "2023-01-24T04:12:16.971Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6218620eb278d21bb7bd3ce8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "62186109b278d21bb7bd3ca1"}, "name": "kchabadam", "description": "kchabadam", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 100, "merchantLocationId": {"$oid": "62186170b278d21bb7bd3cad"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-02-24T18:30:00.000Z"}, "fixedToDate": {"$date": "2022-02-28T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-02-25T04:58:54.808Z"}, "updatedOn": {"$date": "2023-01-24T04:12:32.707Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6240a59b8d1b3e30481dc4c3"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "622cfca22e4f86567ede0da8"}, "name": "terst", "description": "teststs", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 2, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-27T17:57:47.166Z"}, "updatedOn": {"$date": "2023-01-24T04:12:38.159Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62433f8bc02a566556583050"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test", "description": "Tests3434", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 3, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T17:19:07.293Z"}, "updatedOn": {"$date": "2022-03-29T17:19:16.946Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624c2a5785df8d81989d509f"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "test", "description": "test", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 12, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "recurrence": "FOREVER", "daysOfWeek": ["SUNDAY", "MONDAY", "TUESDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-05T11:39:03.146Z"}, "updatedOn": {"$date": "2023-01-24T04:13:45.713Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624ea473d772aa04dbebcd0a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Test", "description": "test", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 23, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "WEDNESDAY", "FRIDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-07T08:44:35.677Z"}, "updatedOn": {"$date": "2022-04-08T18:37:33.373Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6251497d8a57bca455711a9d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "61fa1cfb5ab9ca395358f903"}, "name": "Test", "description": "Test", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 23, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-09T08:53:17.123Z"}, "updatedOn": {"$date": "2022-04-09T09:11:08.729Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6251642e8a57bca455711f0e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "Test", "description": "test", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 34, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "FRIDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 14, "createdOn": {"$date": "2022-04-09T10:47:10.767Z"}, "updatedOn": {"$date": "2023-01-24T04:14:32.893Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}, "maxPoints": null}, {"_id": {"$oid": "625164568a57bca455711f1c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "`test", "description": "teetst", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 34, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 17, "createdOn": {"$date": "2022-04-09T10:47:50.752Z"}, "updatedOn": {"$date": "2023-01-24T04:14:38.733Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "625164bb8a57bca455711f35"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Testrer", "description": "12121", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 443, "recurrence": "PERIOD", "fixedFromDate": {"$date": "2022-04-08T18:30:00.000Z"}, "fixedToDate": {"$date": "2022-04-26T18:29:59.999Z"}, "daysOfWeek": ["MONDAY", "TUESDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-09T10:49:31.323Z"}, "updatedOn": {"$date": "2023-01-24T04:14:44.539Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6253be558a57bca4557123a6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "62418d9cc02a56655658106e"}, "name": "TEst", "description": "testst", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 0, "affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "isTierBased": true, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 10}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 110}, {"tierId": {"$oid": "61c01e4e10bfed0ab66a4b92"}, "amountPerPoint": 11110}]}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-11T05:36:21.273Z"}, "updatedOn": {"$date": "2022-04-11T05:37:39.060Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6255453a8a57bca455713963"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "61fa1cfb5ab9ca395358f903"}, "name": "Test Aff", "description": "Aff 1", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 100, "affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "isTierBased": false, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 0}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 0}, {"tierId": {"$oid": "61c01e4e10bfed0ab66a4b92"}, "amountPerPoint": 0}]}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-12T09:24:10.558Z"}, "updatedOn": {"$date": "2023-01-24T04:14:51.215Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62571b7d70d1cd03569b87d5"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6256940b70d1cd03569b77ca"}, "name": "Test Aff", "description": "Testing affinity", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 0, "affinityGroupId": {"$oid": "61963220b41a2d3f53dd4da3"}, "isTierBased": true, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 10}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 100}, {"tierId": {"$oid": "61c01e4e10bfed0ab66a4b92"}, "amountPerPoint": 1000}]}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-13T18:50:37.818Z"}, "updatedOn": {"$date": "2023-01-24T04:15:05.787Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "626ae2936c7308032754a85f"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "626adf236c7308032754a801"}, "name": "merchant collect point general spending rule", "description": "for test", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 100, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 39, "createdOn": {"$date": "2022-04-28T18:53:07.797Z"}, "updatedOn": {"$date": "2022-11-11T09:24:17.588Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "635f50e57a1ed3162d28a061"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "x", "description": "x", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 12, "merchantLocationId": {"$oid": "619785c1c85d41818d3fe0af"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-10-30T18:30:00.000Z"}, "fixedToDate": {"$date": "2022-11-04T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-10-31T04:36:53.826Z"}, "updatedOn": {"$date": "2022-10-31T04:37:18.548Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "637213c35777af82734b8e78"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "l", "description": "s", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 1, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-11-23T04:00:00.000Z"}, "fixedToDate": {"$date": "2022-11-25T03:59:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-11-14T10:09:07.075Z"}, "updatedOn": {"$date": "2023-01-24T04:15:12.274Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6372231c5777af82734b90fa"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "l", "description": "j", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 9, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-11-14T04:00:00.000Z"}, "fixedToDate": {"$date": "2022-11-15T03:59:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-11-14T11:14:36.205Z"}, "updatedOn": {"$date": "2023-01-24T04:15:43.511Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61978899c85d41818d3fe12d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Affinity rule", "description": "trtsxd", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 20, "affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "isTierBased": false, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 0, "_id": {"$oid": "61978899c85d41818d3fe12f"}}, {"tierId": {"$oid": "619546b0423d24e396dc3d2d"}, "amountPerPoint": 0, "_id": {"$oid": "61978899c85d41818d3fe130"}}, {"tierId": {"$oid": "619546d2423d24e396dc3d37"}, "amountPerPoint": 0, "_id": {"$oid": "61978899c85d41818d3fe131"}}], "_id": {"$oid": "61978899c85d41818d3fe12e"}}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:20:57.567Z"}, "updatedOn": {"$date": "2021-11-30T03:28:33.569Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619789a0c85d41818d3fe167"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Test seasonal", "description": "hgjhfgfhgfh", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 23, "recurrence": "PERIOD", "fixedFromDate": {"$date": "2022-11-16T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-12-22T18:30:00.000Z"}, "daysOfWeek": ["SUNDAY", "FRIDAY"], "_id": {"$oid": "619789a0c85d41818d3fe168"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:25:20.030Z"}, "updatedOn": {"$date": "2021-11-30T03:28:51.353Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b275398ea23a5b884f599"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "Rule Name", "description": " insures that rules later defined in the policy do not inadvertently permit access to the firewall", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 100, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-22T05:08:22.198Z"}, "fixedToDate": {"$date": "2021-11-22T05:08:22.198Z"}, "daysOfWeek": [], "_id": {"$oid": "619b275398ea23a5b884f59a"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-22T05:14:59.774Z"}, "updatedOn": {"$date": "2021-11-30T03:29:09.651Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619b36e598ea23a5b884f859"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "no name", "description": "kk", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 100, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "recurrence": "PERIOD", "fixedFromDate": {"$date": "2021-11-22T06:15:33.187Z"}, "fixedToDate": {"$date": "2021-11-22T06:15:33.187Z"}, "daysOfWeek": ["TUESDAY", "WEDNESDAY", "SATURDAY", "THURSDAY", "SUNDAY"], "_id": {"$oid": "619b36e598ea23a5b884f85a"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-22T06:21:25.412Z"}, "updatedOn": {"$date": "2021-11-22T06:21:38.541Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6242fcebc02a56655658283d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "<PERSON><PERSON>", "description": "teststst", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 34, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T12:34:51.304Z"}, "updatedOn": {"$date": "2022-03-29T15:18:39.621Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62433f01c02a566556583008"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test2323", "description": "rers", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 3, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T17:16:49.122Z"}, "updatedOn": {"$date": "2022-03-29T17:18:48.279Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62434a28c02a566556583350"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "62418d9cc02a56655658106e"}, "name": "Test", "description": "tets", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 2, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T18:04:24.383Z"}, "updatedOn": {"$date": "2022-04-04T17:29:29.727Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624e84fdd772aa04dbebca75"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "Test", "description": "Testtsts", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 0, "affinityGroupId": {"$oid": "61fa46465ab9ca395358fcd6"}, "isTierBased": false, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 0}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 0}, {"tierId": {"$oid": "61c01e4e10bfed0ab66a4b92"}, "amountPerPoint": 0}]}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 36, "createdOn": {"$date": "2022-04-07T06:30:21.102Z"}, "updatedOn": {"$date": "2023-01-24T04:14:03.430Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62a985b61b70a308d510fc8c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Uni students", "description": "test", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 10, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-06-15T07:09:42.106Z"}, "updatedOn": {"$date": "2022-10-31T04:37:51.633Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61954fa6423d24e396dc40a3"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "New Year Offers", "description": "aaaaaaaaaa", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 11, "affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "isTierBased": true, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 10, "_id": {"$oid": "6197ef9c98ea23a5b884ddbc"}}, {"tierId": {"$oid": "619546b0423d24e396dc3d2d"}, "amountPerPoint": 20, "_id": {"$oid": "6197ef9c98ea23a5b884ddbd"}}, {"tierId": {"$oid": "619546d2423d24e396dc3d37"}, "amountPerPoint": 30, "_id": {"$oid": "6197ef9c98ea23a5b884ddbe"}}], "_id": {"$oid": "6197ef9c98ea23a5b884ddbb"}}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-17T18:53:26.958Z"}, "updatedOn": {"$date": "2021-11-30T03:28:04.598Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61962423b41a2d3f53dd466c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Affinity Rule edit", "description": "test", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 20, "affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "isTierBased": false, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 0, "_id": {"$oid": "61962423b41a2d3f53dd466e"}}, {"tierId": {"$oid": "619546b0423d24e396dc3d2d"}, "amountPerPoint": 0, "_id": {"$oid": "61962423b41a2d3f53dd466f"}}, {"tierId": {"$oid": "619546d2423d24e396dc3d37"}, "amountPerPoint": 0, "_id": {"$oid": "61962423b41a2d3f53dd4670"}}], "_id": {"$oid": "61962423b41a2d3f53dd466d"}}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-18T10:00:03.442Z"}, "updatedOn": {"$date": "2021-11-18T10:01:30.201Z"}, "__v": 0, "updatedBy": {"$oid": "613d08491df0e38df616f411"}}, {"_id": {"$oid": "6197886fc85d41818d3fe11a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Seasonal point rule", "description": "967676hgghfghdfgs", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 10, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-19T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-11-22T18:30:00.000Z"}, "daysOfWeek": [], "_id": {"$oid": "6197886fc85d41818d3fe11b"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:20:15.146Z"}, "updatedOn": {"$date": "2021-11-30T03:28:28.308Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61978b9bc85d41818d3fe1c8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "no name", "description": "dfafjf", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 1234, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-18T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-11-19T11:33:16.329Z"}, "daysOfWeek": [], "_id": {"$oid": "61978b9bc85d41818d3fe1c9"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:33:47.119Z"}, "updatedOn": {"$date": "2021-11-30T03:28:56.835Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61979099c85d41818d3fe360"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Location Based Rule", "description": "test", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 10, "merchantLocationId": {"$oid": "6195d1dbb41a2d3f53dd3131"}, "recurrence": "FOREVER", "daysOfWeek": ["SUNDAY", "WEDNESDAY", "SATURDAY"], "_id": {"$oid": "61979099c85d41818d3fe361"}, "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "613d08491df0e38df616f411"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-19T11:55:05.749Z"}, "updatedOn": {"$date": "2021-11-30T03:29:02.045Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "619def51ca76ef698b03144a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "rtest", "description": "test", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 10, "recurrence": "NONE", "fixedFromDate": {"$date": "2021-11-29T18:30:00.000Z"}, "fixedToDate": {"$date": "2021-11-29T18:30:00.000Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-24T07:52:49.173Z"}, "updatedOn": {"$date": "2021-11-30T03:27:27.611Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61a59d0a650a00b39a95dfc6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "no name", "description": "kkkl", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 100, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-11-30T03:39:54.810Z"}, "updatedOn": {"$date": "2021-12-28T15:44:32.662Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61c0517ce7fdaec96456e09e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "Rule Name", "description": "Rule Name", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 100, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-12-20T09:48:44.337Z"}, "updatedOn": {"$date": "2021-12-28T12:01:06.951Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61c051eae7fdaec96456e0b4"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "Rule Name", "description": "no name", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 1000, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 24, "createdOn": {"$date": "2021-12-20T09:50:34.923Z"}, "updatedOn": {"$date": "2022-04-06T07:36:59.873Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "61cafb9d6f0e93ba03b2900a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "Rule Name", "description": "l", "type": "TRANSACTIONAL", "subType": "SEASONAL", "ruleData": {"amountPerPoint": 100, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-04-10T13:01:24.591Z"}, "fixedToDate": {"$date": "2022-04-10T13:01:24.591Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2021-12-28T11:57:17.764Z"}, "updatedOn": {"$date": "2023-01-24T04:12:11.707Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624337f9c02a566556582de7"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test", "description": "Test343", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 4, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T16:46:49.381Z"}, "updatedOn": {"$date": "2022-03-29T16:48:31.692Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62433ceac02a566556582f3a"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test3", "description": "trts", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 3, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T17:07:54.518Z"}, "updatedOn": {"$date": "2022-03-29T17:16:21.641Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624344d1c02a566556583159"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "623aa28b95fd956f8e974736"}, "name": "Test", "description": "test343", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 2, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-03-29T17:41:37.393Z"}, "updatedOn": {"$date": "2022-03-29T17:43:05.189Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624c388385df8d81989d511e"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "rerw23", "description": "32323", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 12, "merchantLocationId": {"$oid": "622c83802e4f86567eddd97e"}, "recurrence": "PERIOD", "fixedFromDate": {"$date": "2022-04-05T20:12:53.880Z"}, "fixedToDate": {"$date": "2022-04-05T20:12:53.880Z"}, "daysOfWeek": ["MONDAY", "TUESDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-05T12:39:31.317Z"}, "updatedOn": {"$date": "2022-04-08T16:31:10.011Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "624ca77e85df8d81989d642c"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "TesAFF", "description": "AFFF", "type": "TRANSACTIONAL", "subType": "AFFINITY", "ruleData": {"amountPerPoint": 10, "affinityGroupId": {"$oid": "61954ad1423d24e396dc3e67"}, "isTierBased": false, "daysOfWeek": [], "tierBasedAmountsPerPoint": [{"tierId": {"$oid": "61954673423d24e396dc3ce0"}, "amountPerPoint": 0}, {"tierId": {"$oid": "6197ea0998ea23a5b884d97b"}, "amountPerPoint": 0}, {"tierId": {"$oid": "61c01e4e10bfed0ab66a4b92"}, "amountPerPoint": 0}]}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-05T20:33:02.689Z"}, "updatedOn": {"$date": "2023-01-24T04:13:51.060Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62514dd48a57bca455711ce6"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "61fa1cfb5ab9ca395358f903"}, "name": "Test", "description": "tests", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 3, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-09T09:11:48.021Z"}, "updatedOn": {"$date": "2023-01-24T04:14:18.960Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "62571b2370d1cd03569b87b4"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6256940b70d1cd03569b77ca"}, "name": "Test Rule", "description": "Testing add rule", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 10, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-04-13T18:49:07.524Z"}, "updatedOn": {"$date": "2022-04-13T18:49:47.720Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "630673211e6be732ac95c307"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "dg", "description": "gdfg", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 33, "merchantLocationId": {"$oid": "6196004db41a2d3f53dd353c"}, "recurrence": "FOREVER", "daysOfWeek": ["MONDAY", "WEDNESDAY", "FRIDAY"], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-08-24T18:51:13.847Z"}, "updatedOn": {"$date": "2022-10-31T04:37:29.202Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "636e14aba7f36e68863410a0"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619a608598ea23a5b884ed36"}, "name": "7", "description": "7", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 8, "merchantLocationId": {"$oid": "619a60ce98ea23a5b884ed4e"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2022-11-30T04:00:00.000Z"}, "fixedToDate": {"$date": "2022-12-26T03:59:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2022-11-11T09:23:55.204Z"}, "updatedOn": {"$date": "2022-11-11T09:24:05.134Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63b6a31f6185525cf29e6fe8"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "GSRule", "description": "GSRule", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 1000, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 1, "createdOn": {"$date": "2023-01-05T10:14:55.954Z"}, "updatedOn": {"$date": "2023-01-24T04:52:15.752Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63ca58488e5085b1a704b20d"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "TetsRuleE", "description": "TetsRule", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 1000, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2023-01-19T18:30:00.000Z"}, "fixedToDate": {"$date": "2023-04-30T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 10, "createdOn": {"$date": "2023-01-20T09:00:56.336Z"}, "updatedOn": {"$date": "2023-03-09T11:14:45.082Z"}, "__v": 0, "maxPoints": 1000, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63ca597e82ed31633070b795"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "TetsRule", "description": "TetsRule", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 1000, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2023-01-19T18:30:00.000Z"}, "fixedToDate": {"$date": "2023-04-30T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": null, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2023-01-20T09:06:06.689Z"}, "updatedOn": {"$date": "2023-01-24T04:15:51.792Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63ce615ebd1734863b4a4c9b"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "sss", "description": "00", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 12, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2023-01-23T18:30:00.000Z"}, "fixedToDate": {"$date": "2023-01-24T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 100, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2023-01-23T10:28:46.378Z"}, "updatedOn": {"$date": "2023-01-23T10:31:37.085Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63ce61f9bd1734863b4a4cd3"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "kklklk", "description": "00", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 12, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2023-01-22T18:30:00.000Z"}, "fixedToDate": {"$date": "2023-01-23T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 100, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ARCHIVED", "matchedCount": 0, "createdOn": {"$date": "2023-01-23T10:31:21.314Z"}, "updatedOn": {"$date": "2023-01-24T04:13:58.172Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63cf5b9dbd1734863b4a4fad"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6195caa6b41a2d3f53dd3026"}, "name": "new test", "description": "10", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 10, "merchantLocationId": {"$oid": "632b5af4673fc768fb3b97df"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2023-01-23T18:30:00.000Z"}, "fixedToDate": {"$date": "2023-01-24T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 1000, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 0, "createdOn": {"$date": "2023-01-24T04:16:29.326Z"}, "updatedOn": {"$date": "2023-03-13T16:44:28.951Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63cf6400bd1734863b4a5081"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "name": "GSRule", "description": "GSRule", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 1000, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 100, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 0, "createdOn": {"$date": "2023-01-24T04:52:16.238Z"}, "updatedOn": {"$date": "2023-03-13T16:22:41.444Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "63ec7f29fdbb49b2588b9f81"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "63ec7e71fdbb49b2588b9f63"}, "name": "madura rulezzz 🤘", "description": "madura rulezzz 🤘", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 100, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 100, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 0, "createdOn": {"$date": "2023-02-15T06:43:53.911Z"}, "updatedOn": {"$date": "2023-03-20T15:46:11.937Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "64008d6445b9a13e558a10ef"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "619541b1423d24e396dbf07e"}, "name": "<PERSON>han", "description": "mm", "type": "TRANSACTIONAL", "subType": "GENERAL", "ruleData": {"amountPerPoint": 11, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 190005, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 7, "createdOn": {"$date": "2023-03-02T11:49:56.874Z"}, "updatedOn": {"$date": "2023-04-19T13:14:55.601Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}, {"_id": {"$oid": "6409bfa72eaab82a535bbd03"}, "organizationId": {"$oid": "6128e3537ed841e246e2e394"}, "regionId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "merchantId": {"$oid": "6137a9fff48b8eb1845c78cf"}, "name": "TetsRuleq", "description": "TetsRule", "type": "TRANSACTIONAL", "subType": "LOCATION", "ruleData": {"amountPerPoint": 1000, "merchantLocationId": {"$oid": "61955446031f65c462d0c1ca"}, "recurrence": "NONE", "fixedFromDate": {"$date": "2023-01-19T18:30:00.000Z"}, "fixedToDate": {"$date": "2023-04-30T18:29:59.999Z"}, "daysOfWeek": [], "tierBasedAmountsPerPoint": []}, "maxPoints": 1000, "createdBy": {"$oid": "616680f35958bc8372a1ee88"}, "status": "ENABLED", "matchedCount": 0, "createdOn": {"$date": "2023-03-09T11:14:47.400Z"}, "updatedOn": {"$date": "2023-03-13T16:44:19.516Z"}, "__v": 0, "updatedBy": {"$oid": "616680f35958bc8372a1ee88"}}]