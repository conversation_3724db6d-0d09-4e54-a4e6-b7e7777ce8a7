"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const playwright_1 = require("playwright");
(async () => {
    const browser = await playwright_1.chromium.launch({ headless: false }); // So you can see what happens
    const context = await browser.newContext({ storageState: 'linkedin-auth.json' });
    const page = await context.newPage();
    await page.goto('https://www.linkedin.com/feed/', { waitUntil: 'domcontentloaded' });
    // The "Me" menu is only visible if you are logged in
    const isLoggedIn = await page.locator('img.global-nav__me-photo').count() > 0;
    if (isLoggedIn) {
        console.log('You are logged in to LinkedIn!');
    }
    else {
        console.log('You are NOT logged in to LinkedIn. Please run login.js again.');
    }
    await browser.close();
})();
//# sourceMappingURL=check_linkedin_login.js.map