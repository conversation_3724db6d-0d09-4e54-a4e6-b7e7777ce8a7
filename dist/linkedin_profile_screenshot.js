"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const playwright_1 = require("playwright");
// Array of LinkedIn profile URLs
const PROFILE_URLS = [
    "https://www.linkedin.com/in/hoshiniperera/",
    "https://www.linkedin.com/in/ACwAAAidt6QB5avh5R7d_ZUGEhGRcLnYUMTtPaA",
    "https://www.linkedin.com/in/alazzazy",
    "https://www.linkedin.com/in/ACwAAAmVg4wBlZqTv_UA6v2qt8RlEB034RiTV0Y",
    "https://www.linkedin.com/in/ACwAAAvyWxQBjJC4LxyDTiFJ16AvESC3qQ2dE1A",
    // Add more URLs here
];
(async () => {
    const browser = await playwright_1.chromium.launch({ headless: false, slowMo: 50 });
    const context = await browser.newContext({ storageState: 'linkedin-auth.json' });
    const page = await context.newPage();
    for (let i = 0; i < PROFILE_URLS.length; i++) {
        const url = PROFILE_URLS[i];
        try {
            console.log(`Navigating to: ${url}`);
            await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
            await page.waitForTimeout(5000); // Wait for content to load
            // Create a unique filename for each screenshot
            const filename = `profile_${i + 1}.png`;
            await page.screenshot({ path: filename, fullPage: true });
            console.log(`Screenshot saved as ${filename}`);
        }
        catch (e) {
            console.error(`Error processing ${url}:`, e);
            await page.screenshot({ path: `profile_error_${i + 1}.png` });
            console.log(`Error screenshot saved as profile_error_${i + 1}.png`);
        }
    }
    await browser.close();
})();
//# sourceMappingURL=linkedin_profile_screenshot.js.map