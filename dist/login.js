"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const playwright_1 = require("playwright");
(async () => {
    const browser = await playwright_1.chromium.launch({ headless: false }); // So you can see the browser
    const context = await browser.newContext();
    const page = await context.newPage();
    await page.goto('https://www.linkedin.com/login');
    // Log in manually, then:
    console.log('After logging in, press Enter in your terminal...');
    process.stdin.once('data', async () => {
        await context.storageState({ path: 'linkedin-auth.json' });
        await browser.close();
        console.log('Saved session to linkedin-auth.json');
        process.exit(0);
    });
})();
//# sourceMappingURL=login.js.map