"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const linkedinService = __importStar(require("../services/linkedin"));
const router = express_1.default.Router();
/**
 * @route   GET /api/linkedin/status
 * @desc    Check LinkedIn login status
 * @access  Public
 */
router.get('/status', async (req, res) => {
    try {
        const isLoggedIn = await linkedinService.checkLoginStatus();
        res.json({
            success: true,
            isLoggedIn
        });
    }
    catch (error) {
        console.error('Error checking login status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to check LinkedIn login status'
        });
    }
});
/**
 * @route   POST /api/linkedin/profile-image
 * @desc    Extract profile image from LinkedIn profile
 * @access  Public
 * @body    { profileUrl: string }
 */
router.post('/profile-image', async (req, res) => {
    try {
        const { profileUrl } = req.body;
        // Validate input
        if (!profileUrl) {
            return res.status(400).json({
                success: false,
                error: 'Profile URL is required'
            });
        }
        // Check if URL is a valid LinkedIn profile URL
        if (!profileUrl.includes('linkedin.com/in/')) {
            return res.status(400).json({
                success: false,
                error: 'Invalid LinkedIn profile URL'
            });
        }
        // Extract profile image
        const result = await linkedinService.extractProfileImage(profileUrl);
        // Return result
        res.json({
            success: true,
            data: {
                profileUrl,
                imageUrl: result.imageUrl,
                azureUrl: result.azureUrl
            }
        });
    }
    catch (error) {
        console.error('Error extracting profile image:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to extract profile image'
        });
    }
});
/**
 * @route   POST /api/linkedin/batch-profile-images
 * @desc    Extract profile images from multiple LinkedIn profiles
 * @access  Public
 * @body    { profileUrls: string[] }
 */
router.post('/batch-profile-images', async (req, res) => {
    try {
        const { profileUrls } = req.body;
        // Validate input
        if (!profileUrls || !Array.isArray(profileUrls) || profileUrls.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Profile URLs array is required'
            });
        }
        // Process each profile URL
        const results = [];
        const errors = [];
        for (const profileUrl of profileUrls) {
            try {
                // Check if URL is a valid LinkedIn profile URL
                if (!profileUrl.includes('linkedin.com/in/')) {
                    errors.push({ profileUrl, error: 'Invalid LinkedIn profile URL' });
                    continue;
                }
                // Extract profile image
                const result = await linkedinService.extractProfileImage(profileUrl);
                results.push({
                    profileUrl,
                    imageUrl: result.imageUrl,
                    azureUrl: result.azureUrl
                });
            }
            catch (error) {
                errors.push({ profileUrl, error: error.message || 'Unknown error' });
            }
        }
        // Return results
        res.json({
            success: true,
            data: {
                results,
                errors,
                totalProcessed: profileUrls.length,
                successCount: results.length,
                errorCount: errors.length
            }
        });
    }
    catch (error) {
        console.error('Error processing batch profile images:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to process batch profile images'
        });
    }
});
exports.default = router;
//# sourceMappingURL=linkedin.js.map