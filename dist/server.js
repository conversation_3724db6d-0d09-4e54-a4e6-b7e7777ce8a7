"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const body_parser_1 = __importDefault(require("body-parser"));
const fileUtils = __importStar(require("./utils/fileUtils"));
const azureService = __importStar(require("./services/azure"));
const linkedin_1 = __importDefault(require("./routes/linkedin"));
// Initialize environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const port = process.env.PORT || 3000;
// Initialize temp directory and schedule cleanup
fileUtils.ensureTempDir();
fileUtils.scheduleCleanup();
// Initialize Azure Blob Storage container
(async () => {
    try {
        await azureService.initContainer();
    }
    catch (error) {
        console.error('Failed to initialize Azure Blob Storage container:', error);
        console.log('API will continue to function, but file uploads to Azure may fail');
    }
})();
// Middleware
app.use(body_parser_1.default.json());
app.use(body_parser_1.default.urlencoded({ extended: true }));
// Add request logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
    next();
});
// Use routes
app.use('/api/linkedin', linkedin_1.default);
// Basic route for testing
app.get('/', (req, res) => {
    res.json({
        message: 'LinkedIn Profile Scraper API',
        status: 'running',
        version: '1.0.0',
        endpoints: {
            status: 'GET /api/linkedin/status',
            profileImage: 'POST /api/linkedin/profile-image',
            batchProfileImages: 'POST /api/linkedin/batch-profile-images'
        }
    });
});
// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        error: 'Internal server error'
    });
});
// Handle 404 errors
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found'
    });
});
// Start server
app.listen(port, () => {
    console.log(`LinkedIn Profile Scraper API running on port ${port}`);
    console.log(`Server started at: ${new Date().toISOString()}`);
});
//# sourceMappingURL=server.js.map