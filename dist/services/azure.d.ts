/**
 * Initialize the Azure Blob Storage container
 * @returns {Promise<boolean>} - True if initialization was successful, false otherwise
 */
export declare function initContainer(): Promise<boolean>;
/**
 * Upload an image to Azure Blob Storage
 * @param {string} imagePath - Local path to the image file
 * @param {string} blobName - Name to use for the blob (filename)
 * @returns {Promise<string|null>} - URL of the uploaded blob or null if upload failed
 */
export declare function uploadImage(imagePath: string, blobName: string): Promise<string | null>;
/**
 * Delete a blob from Azure Blob Storage
 * @param {string} blobName - Name of the blob to delete
 * @returns {Promise<boolean>} - True if deletion was successful, false otherwise
 */
export declare function deleteBlob(blobName: string): Promise<boolean>;
