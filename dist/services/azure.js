"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initContainer = initContainer;
exports.uploadImage = uploadImage;
exports.deleteBlob = deleteBlob;
const storage_blob_1 = require("@azure/storage-blob");
// Get connection string and container name from environment variables
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || 'linkedin-profile-images';
// Create BlobServiceClient
const blobServiceClient = connectionString
    ? storage_blob_1.BlobServiceClient.fromConnectionString(connectionString)
    : null;
/**
 * Initialize the Azure Blob Storage container
 * @returns {Promise<boolean>} - True if initialization was successful, false otherwise
 */
async function initContainer() {
    if (!blobServiceClient) {
        console.warn('Azure Blob Storage connection string not provided. Storage functionality disabled.');
        return false;
    }
    try {
        // Get a reference to a container
        const containerClient = blobServiceClient.getContainerClient(containerName);
        // Create the container if it doesn't exist
        const containerExists = await containerClient.exists();
        if (!containerExists) {
            console.log(`Creating container: ${containerName}`);
            await containerClient.create({ access: 'blob' }); // 'blob' access means public read access for blobs
        }
        return true;
    }
    catch (error) {
        console.error('Error initializing Azure Blob Storage container:', error);
        return false;
    }
}
/**
 * Upload an image to Azure Blob Storage
 * @param {string} imagePath - Local path to the image file
 * @param {string} blobName - Name to use for the blob (filename)
 * @returns {Promise<string|null>} - URL of the uploaded blob or null if upload failed
 */
async function uploadImage(imagePath, blobName) {
    // If connection string is not provided, return null
    if (!blobServiceClient) {
        console.warn('Azure Blob Storage connection string not provided. Upload skipped.');
        return null;
    }
    try {
        // Initialize container
        const containerInitialized = await initContainer();
        if (!containerInitialized) {
            return null;
        }
        // Get a reference to a container
        const containerClient = blobServiceClient.getContainerClient(containerName);
        // Get a block blob client
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);
        // Upload file
        console.log(`Uploading to Azure Blob Storage as ${blobName}`);
        await blockBlobClient.uploadFile(imagePath);
        // Return the URL of the blob
        return blockBlobClient.url;
    }
    catch (error) {
        console.error('Error uploading to Azure Blob Storage:', error);
        return null;
    }
}
/**
 * Delete a blob from Azure Blob Storage
 * @param {string} blobName - Name of the blob to delete
 * @returns {Promise<boolean>} - True if deletion was successful, false otherwise
 */
async function deleteBlob(blobName) {
    // If connection string is not provided, return false
    if (!blobServiceClient) {
        console.warn('Azure Blob Storage connection string not provided. Delete skipped.');
        return false;
    }
    try {
        // Get a reference to a container
        const containerClient = blobServiceClient.getContainerClient(containerName);
        // Get a block blob client
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);
        // Delete the blob
        console.log(`Deleting blob: ${blobName}`);
        await blockBlobClient.delete();
        return true;
    }
    catch (error) {
        console.error('Error deleting blob from Azure Blob Storage:', error);
        return false;
    }
}
//# sourceMappingURL=azure.js.map