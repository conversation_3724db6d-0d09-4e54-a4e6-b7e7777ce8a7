import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontex<PERSON> } from 'playwright';
interface BrowserInit {
    browser: Browser;
    context: BrowserContext;
}
/**
 * Initialize browser and context with LinkedIn authentication
 */
export declare function initBrowser(): Promise<BrowserInit>;
export interface ProfileImageResult {
    imageUrl: string | null;
    localPath: string | null;
    azureUrl: string | null;
}
/**
 * Extract profile image from LinkedIn profile
 */
export declare function extractProfileImage(profileUrl: string): Promise<ProfileImageResult>;
/**
 * Check if user is logged in to LinkedIn
 */
export declare function checkLoginStatus(): Promise<boolean>;
export {};
