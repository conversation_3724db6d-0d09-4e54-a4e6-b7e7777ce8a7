"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initBrowser = initBrowser;
exports.extractProfileImage = extractProfileImage;
exports.checkLoginStatus = checkLoginStatus;
const playwright_1 = require("playwright");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const azureService = __importStar(require("./azure"));
// Directory to temporarily store profile images
const TEMP_DIR = path_1.default.join(__dirname, '../../temp');
// Ensure temp directory exists
if (!fs_1.default.existsSync(TEMP_DIR)) {
    fs_1.default.mkdirSync(TEMP_DIR, { recursive: true });
}
/**
 * Initialize browser and context with LinkedIn authentication
 */
async function initBrowser() {
    const browser = await playwright_1.chromium.launch({ headless: true });
    // Check if auth file exists
    const authPath = path_1.default.join(__dirname, '../../linkedin-auth.json');
    let context;
    if (fs_1.default.existsSync(authPath)) {
        // Use existing authentication
        context = await browser.newContext({ storageState: authPath });
    }
    else {
        // Create new authentication
        context = await browser.newContext();
        const page = await context.newPage();
        // Navigate to LinkedIn login
        await page.goto('https://www.linkedin.com/login');
        // Fill in login form
        await page.fill('#username', process.env.LINKEDIN_USERNAME || '');
        await page.fill('#password', process.env.LINKEDIN_PASSWORD || '');
        await page.click('button[type="submit"]');
        // Wait for navigation to complete
        await page.waitForNavigation();
        // Save authentication state
        await context.storageState({ path: authPath });
    }
    return { browser, context };
}
/**
 * Extract profile image from LinkedIn profile
 */
async function extractProfileImage(profileUrl) {
    const { browser, context } = await initBrowser();
    let imageUrl = null;
    let imagePath = null;
    let azureUrl = null;
    try {
        const page = await context.newPage();
        console.log(`Navigating to: ${profileUrl}`);
        // Navigate to profile
        await page.goto(profileUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
        await page.waitForTimeout(3000); // Wait for content to load
        // Extract profile image URL
        // LinkedIn profile images are in the img element with class pv-top-card-profile-picture__image
        const profileImageElement = await page.$('.pv-top-card-profile-picture__image, .profile-photo-edit__preview');
        if (profileImageElement) {
            imageUrl = await profileImageElement.getAttribute('src');
            if (imageUrl) {
                // Generate a unique filename
                const filename = `profile_${Date.now()}.jpg`;
                imagePath = path_1.default.join(TEMP_DIR, filename);
                // Download the image
                const response = await page.goto(imageUrl);
                if (response) {
                    const imageBuffer = await response.body();
                    fs_1.default.writeFileSync(imagePath, imageBuffer);
                    // Upload to Azure Blob Storage
                    azureUrl = await azureService.uploadImage(imagePath, filename);
                }
            }
        }
    }
    catch (error) {
        console.error('Error extracting profile image:', error);
        throw error;
    }
    finally {
        await browser.close();
    }
    return {
        imageUrl,
        localPath: imagePath,
        azureUrl
    };
}
/**
 * Check if user is logged in to LinkedIn
 */
async function checkLoginStatus() {
    const { browser, context } = await initBrowser();
    let isLoggedIn = false;
    try {
        const page = await context.newPage();
        await page.goto('https://www.linkedin.com/feed/', { waitUntil: 'domcontentloaded' });
        // The "Me" menu is only visible if you are logged in
        isLoggedIn = await page.locator('img.global-nav__me-photo').count() > 0;
    }
    catch (error) {
        console.error('Error checking login status:', error);
    }
    finally {
        await browser.close();
    }
    return isLoggedIn;
}
//# sourceMappingURL=linkedin.js.map