declare const TEMP_DIR: string;
/**
 * Ensure the temporary directory exists
 */
export declare function ensureTempDir(): void;
/**
 * Clean up temporary files older than the specified age
 * @param {number} maxAgeMs - Maximum age of files in milliseconds (default: 1 hour)
 */
export declare function cleanupTempFiles(maxAgeMs?: number): void;
/**
 * Schedule periodic cleanup of temporary files
 * @param {number} intervalMs - Interval in milliseconds (default: 1 hour)
 */
export declare function scheduleCleanup(intervalMs?: number): NodeJS.Timeout;
export { TEMP_DIR };
