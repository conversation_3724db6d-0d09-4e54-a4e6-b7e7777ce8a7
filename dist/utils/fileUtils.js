"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TEMP_DIR = void 0;
exports.ensureTempDir = ensureTempDir;
exports.cleanupTempFiles = cleanupTempFiles;
exports.scheduleCleanup = scheduleCleanup;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
// Directory to temporarily store profile images
const TEMP_DIR = path_1.default.join(__dirname, '../../temp');
exports.TEMP_DIR = TEMP_DIR;
/**
 * Ensure the temporary directory exists
 */
function ensureTempDir() {
    if (!fs_1.default.existsSync(TEMP_DIR)) {
        fs_1.default.mkdirSync(TEMP_DIR, { recursive: true });
        console.log(`Created temporary directory: ${TEMP_DIR}`);
    }
}
/**
 * Clean up temporary files older than the specified age
 * @param {number} maxAgeMs - Maximum age of files in milliseconds (default: 1 hour)
 */
function cleanupTempFiles(maxAgeMs = 60 * 60 * 1000) {
    try {
        ensureTempDir();
        const now = Date.now();
        const files = fs_1.default.readdirSync(TEMP_DIR);
        let deletedCount = 0;
        for (const file of files) {
            const filePath = path_1.default.join(TEMP_DIR, file);
            const stats = fs_1.default.statSync(filePath);
            // Check if the file is older than maxAgeMs
            if (now - stats.mtimeMs > maxAgeMs) {
                fs_1.default.unlinkSync(filePath);
                deletedCount++;
            }
        }
        if (deletedCount > 0) {
            console.log(`Cleaned up ${deletedCount} temporary files`);
        }
    }
    catch (error) {
        console.error('Error cleaning up temporary files:', error);
    }
}
/**
 * Schedule periodic cleanup of temporary files
 * @param {number} intervalMs - Interval in milliseconds (default: 1 hour)
 */
function scheduleCleanup(intervalMs = 60 * 60 * 1000) {
    // Clean up on startup
    cleanupTempFiles();
    // Schedule periodic cleanup
    const intervalId = setInterval(() => {
        cleanupTempFiles();
    }, intervalMs);
    console.log(`Scheduled temporary file cleanup every ${intervalMs / 1000 / 60} minutes`);
    return intervalId;
}
//# sourceMappingURL=fileUtils.js.map