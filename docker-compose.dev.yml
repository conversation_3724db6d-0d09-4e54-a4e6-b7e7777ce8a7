services:
  linkedin-scraper:
    # Override the command for development with hot reload
    command: npm run dev
    environment:
      - NODE_ENV=development
      - DEBUG=*
    volumes:
      # Additional development volumes
      - ./src:/app/src:cached
      - ./package.json:/app/package.json:cached
      - ./package-lock.json:/app/package-lock.json:cached
      - ./tsconfig.json:/app/tsconfig.json:cached
    # Enable development features
    stdin_open: true
    tty: true
    # Development port mapping
    ports:
      - "3000:3000"
      - "9229:9229"  # Node.js debugging port
