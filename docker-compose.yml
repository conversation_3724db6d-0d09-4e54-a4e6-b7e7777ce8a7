services:
  linkedin-scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: linkedin-scraper-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      # LinkedIn credentials (loaded from .env file)
      - LINKEDIN_USERNAME=${LINKEDIN_USERNAME}
      - LINKEDIN_PASSWORD=${LINKEDIN_PASSWORD}
      # Azure Storage configuration
      - AZURE_STORAGE_CONNECTION_STRING=${AZURE_STORAGE_CONNECTION_STRING}
      - AZURE_STORAGE_CONTAINER_NAME=${AZURE_STORAGE_CONTAINER_NAME}
    volumes:
      # Mount source code for development (hot reload)
      - ./src:/app/src
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./.env:/app/.env
      # Mount temp directory for screenshots
      - ./temp:/app/temp
      # Mount linkedin auth file
      - ./linkedin-auth.json:/app/linkedin-auth.json
      # Exclude node_modules to avoid conflicts
      - /app/node_modules
    networks:
      - linkedin-network
    restart: unless-stopped
    # Add security options for Playwright
    security_opt:
      - seccomp:unconfined
    # Add shared memory size for Chromium
    shm_size: 2gb
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  linkedin-network:
    driver: bridge

volumes:
  node_modules:
