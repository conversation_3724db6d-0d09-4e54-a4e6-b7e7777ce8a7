#!/bin/bash

# LinkedIn Scraper Docker Development Helper Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_error ".env file not found!"
        print_warning "Please create a .env file with the following variables:"
        echo "LINKEDIN_USERNAME=<EMAIL>"
        echo "LINKEDIN_PASSWORD=your_password"
        echo "AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string"
        echo "AZURE_STORAGE_CONTAINER_NAME=your_container_name"
        exit 1
    fi
    print_success ".env file found"
}

# Function to build the Docker image
build() {
    print_status "Building Docker image..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml build
    print_success "Docker image built successfully"
}

# Function to start the development environment
start() {
    print_status "Starting LinkedIn Scraper development environment..."
    check_env_file
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    print_success "Development environment started"
    print_status "API will be available at: http://localhost:3000"
    print_status "Use 'docker-dev.sh logs' to view logs"
}

# Function to stop the development environment
stop() {
    print_status "Stopping LinkedIn Scraper development environment..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
    print_success "Development environment stopped"
}

# Function to restart the development environment
restart() {
    print_status "Restarting LinkedIn Scraper development environment..."
    stop
    start
}

# Function to view logs
logs() {
    print_status "Showing logs (press Ctrl+C to exit)..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f
}

# Function to execute commands in the container
exec() {
    print_status "Executing command in container: $*"
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec linkedin-scraper "$@"
}

# Function to open a shell in the container
shell() {
    print_status "Opening shell in container..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec linkedin-scraper /bin/bash
}

# Function to clean up Docker resources
clean() {
    print_status "Cleaning up Docker resources..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v --remove-orphans
    docker system prune -f
    print_success "Docker resources cleaned up"
}

# Function to show status
status() {
    print_status "Docker containers status:"
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps
}

# Function to show help
help() {
    echo "LinkedIn Scraper Docker Development Helper"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build the Docker image"
    echo "  start     Start the development environment"
    echo "  stop      Stop the development environment"
    echo "  restart   Restart the development environment"
    echo "  logs      Show container logs"
    echo "  exec      Execute a command in the container"
    echo "  shell     Open a shell in the container"
    echo "  status    Show container status"
    echo "  clean     Clean up Docker resources"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start development environment"
    echo "  $0 logs                     # View logs"
    echo "  $0 exec npm run check-login # Check LinkedIn login status"
    echo "  $0 shell                    # Open shell in container"
}

# Main script logic
case "${1:-help}" in
    build)
        build
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs
        ;;
    exec)
        shift
        exec "$@"
        ;;
    shell)
        shell
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    help|*)
        help
        ;;
esac
