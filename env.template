JWT_SECRET=
MONGO_URL=
VERIFY_TOKEN_SECRET=
OPENID_ADMIN_JWKS_URI=
OPENID_ADMIN_ISSUER=
OPENID_ADMIN_ACCOUNT=
ANALYTIC_DATABASE=
MONGO_DB_CORE_SERVICE=
MONGO_DB_LOYALTY_SERVICE=
USER_ASSETS_BUCKET=
IMAGE_DOWNLOAD_URL_EXPIRE_TIME=
AWS_REGION_DEFAULT=

REDIS_HOST=
REDIS_PORT=
REDIS_DB=
REDIS_PASSWORD=

KEYCLOAK_REALM=
KEYCLOAK_CLIENT_ID=
KEYCLOAK_CLIENT_SECRET=
KEY<PERSON>OAK_BASE_URL=
IDENTITY_SERVICE_BASE_URL=
DATA_ENCRYPTION_SECRET=

SEQUELIZE_PORT=
SEQUELIZE_USER=
SEQUELIZE_PASSWORD=
SEQUELIZE_HOST=
SEQUELIZE_DB_SYNC_MODE= # alter, force, none. In production mode, it should be none.

SWAGGER_SERVER_URL=
FILE_DOWNLOAD_BASE_URL_ADMIN=
USER_PUBLIC_ASSETS_BUCKET_ADMIN=
LOYALTY_SERVICE_BASE_URL=
LOYALTY_SERVICE_PUBLIC_BASE_URL=
WORKER_DB_REQUEST_CHUNK_SIZE=
PROVISION_ORGANIZATION=
TEMP_FILE_UPLOAD_PATH=
SUPPORT_EMAIL=
MAX_JOBS_IN_QUEUE_AFTER_COMPLETION=
MAX_JOBS_IN_QUEUE_AFTER_FAILURE=
ALLOW_MANUAL_CARD_GENERATION=

KAFKA_CLIENT_ID=
TRANSACTIONS_TOPIC=
AUDIT_TOPIC=
KAFKA_SSL=
KAFKA_SASL_MECHANISM=
KAFKA_BROKERS=
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=
KAFKA_CONNECTION_TIMEOUT=
KAFKA_LOG_LEVEL=

API_HOST_URL=localhost:3000
PORT:3000
