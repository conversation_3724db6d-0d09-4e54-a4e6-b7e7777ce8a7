import React, {useEffect, useState} from 'react';
import {Tile, useApi, reactExtension} from '@shopify/ui-extensions-react/point-of-sale';
import type {LoyaltyMemberResponse} from "./Types";
import {useLoyaltyMember} from "./useLoyaltyMember";


export const serverUrl = "https://shopify.beta.loyalty.cxforge.com";//TODO: find way to pass as an environment variable

const SmartGridTile = () => {
  const api = useApi();
  const [enabledTile, setEnabledTile] = useState<boolean>(false);
  const [reloadMember, setReloadMember] = useState<boolean>(true);
  const [responseData, setResponseData] = useState<LoyaltyMemberResponse | null>({
    availableFreeShippingDiscountCode: false,
    discountCode: 0,
    member: {}
  });
  const [customerId, setCustomerId] = useState<number | undefined>(api?.cart?.subscribable?.initial?.customer?.id);

  api.cart.subscribable.subscribe((cart: { customer: { id: React.SetStateAction<number | undefined> } }) => {
    if (cart?.customer?.id) {
      setCustomerId(cart?.customer?.id);
    }
  });

  const {loading} = useLoyaltyMember(api, {
    customerId: customerId,
    generateFreeShippingDiscountCode: true,
  }, setResponseData, reloadMember, setReloadMember);

  useEffect(() => {
    async function setDiscountCode() {
      await api.cart.addCartCodeDiscount(responseData?.discountCode);
      api.toast.show('Discount code applied')
    }

    if (responseData?.member && Object.keys(responseData?.member).length !== 0) {
      setEnabledTile(true);
    }

    if (responseData?.availableFreeShippingDiscountCode) {
      setDiscountCode()
    }
  }, [api.cart, api.toast, responseData]);

  return (
    <Tile
      title='Loyalty User'
      subtitle={`Customer: ${responseData?.member.preferredName || responseData?.member.firstName || "customerId"} `}
      onPress={api.action.presentModal}
      enabled={loading || enabledTile}
    />
  );
};

export default reactExtension('pos.home.tile.render', () => {
  return <SmartGridTile/>
})
