type AffinityGroup = {
  affinityGroupId?: string;
  joinedDate?: string;
  expiryDate?: string | null;
};

type Tier = {
  tierId?: string | null;
  lastUpdatedOn?: string;
};

type NotificationPreference = {
  preferredChannel?: "MOBILE" | "EMAIL" | "SMS";
  allowPromotionalNotifications?: boolean;
};

type MemberInsight = {
  customerLifeTimeValue?: number;
};

type PointStats = {
  collectedPoints?: number;
  refundedPoints?: number;
  redeemedPoints?: number;
};

type LoyaltyUser = {
  _id?: string;
  organizationId?: string;
  loyaltyId?: string;
  regionId?: string;
  parentMemberId?: string | null;
  merchantLocationId?: string;
  type?: "PRIMARY" | "SECONDARY";
  status?: "ACTIVE" | "INACTIVE" | "SUSPENDED";
  points?: number;
  tierPoints?: number;
  purchasesCount?: number;
  purchasesValue?: number;
  tags?: string[];
  lastSeenOn?: string;
  registeredOn?: string;
  firstName?: string;
  lastName?: string;
  preferredName?: string;
  mobileNumber?: string;
  additionalPhoneNumbers?: string[];
  countryCode?: string;
  country?: string;
  email?: string;
  isValidEmail?: boolean;
  isValidMobileNumber?: boolean;
  createdBy?: string;
  registerMethod?: "ADMIN_PORTAL" | "SELF_REGISTERED";
  affinityGroup?: AffinityGroup;
  tier?: Tier;
  cardReplacementCount?: number;
  cardNumber?: string;
  customAttributes?: Record<string, unknown>;
  notificationPreference?: NotificationPreference;
  memberInsight?: MemberInsight;
  pointStats?: PointStats;
  identifications?: any[];
  rewardMetadata?: any[];
  pointsToExpire?: any[];
  historyEvents?: any[];
  createdOn?: string;
  updatedOn?: string;
  __v?: number;
  lastTransactionLocation?: string;
  lastTransactionOn?: string;
  updatedBy?: string | null;
  allowedRedeemablePoints?: number;
};

type OtpResponse = {
  discountCode: string,
  shoutoutResult: {
    otpToken: string
  }
}
type LoyaltyMemberResponse = {
  member: LoyaltyUser,
  discountCode: number,
  availableFreeShippingDiscountCode: boolean,
}

export type {
  LoyaltyUser,
  OtpResponse,
  LoyaltyMemberResponse
}
