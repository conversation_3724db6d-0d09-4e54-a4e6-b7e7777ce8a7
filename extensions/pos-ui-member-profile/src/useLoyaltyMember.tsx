import {useEffect, useState} from "react";
import {serverUrl} from "./Tile";
import type {LoyaltyMemberResponse} from "./Types";

export const useLoyaltyMember = (
  api: any,
  requestPayload: {
    customerId: number | undefined,
    generateFreeShippingDiscountCode: boolean,
  },
  setResponseData: React.Dispatch<React.SetStateAction<LoyaltyMemberResponse | null>>,
  reloadMember: boolean,
  setReloadMember: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getLoyaltyCustomerData = async () => {
      try {
        const sessionToken = await api.session.getSessionToken();
        const url = new URL(`${serverUrl}/loyalty/members/${requestPayload?.customerId}`);
        url.searchParams.set("generateFreeShippingDiscountCode", String(requestPayload.generateFreeShippingDiscountCode));
        const response = await fetch(url.toString(), {
          method: "GET",
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${sessionToken}`,
          },
          redirect: 'follow',
        });
        if (response.ok) {
          const data = await response.json();
          setResponseData(data);
        } else {
          throw new Error(`Failed to fetch loyalty data: ${response.status}`);
        }
      } catch (error) {
        api.toast.show(" Failed to load loyalty user");
        console.error("Error", error);
      } finally {
        setLoading(false);
        setReloadMember(false);
      }
    };

    if (requestPayload?.customerId && reloadMember) {
      getLoyaltyCustomerData();
    }
  }, [api, requestPayload?.customerId, setResponseData, reloadMember, setReloadMember]);

  return {loading};
};
