<style>
  /* Import Google Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&family=Open+Sans:wght@400;500;700&family=Lato:wght@400;700&family=Montserrat:wght@400;500;700&family=Cinzel:wght@400;500;700&family=EB+Garamond:wght@400;500;700&display=swap');

  :root {
    --rewards-primary-color: {{ block.settings.primary_color | default: '#C87F95' }};
    --rewards-text-color: {{ block.settings.text_color | default: '#333333' }};
    --rewards-font-family: {% if block.settings.font_family == 'shop' %}var(--font-body-family){% else %}{{ block.settings.font_family }}{% endif %};
    --rewards-text-secondary: #666666;
    --rewards-background: #FFFFFF;
    --rewards-border-color: #dddddd;
  }

  .rewards-container {
    font-family: var(--rewards-font-family);
  }

  .rewards-container button {
    font-family: var(--rewards-font-family);
  }

  .rewards-launcher {
    background: var(--rewards-primary-color) !important;
    box-shadow: 0 4px 12px rgba(var(--rewards-primary-color-rgb), 0.3) !important;
  }

  .rewards-panel .header-section {
    background: var(--rewards-primary-color);
  }

  .rewards-container .primary-button {
    background: var(--rewards-primary-color);
    color: white;
  }

  .rewards-container .primary-text {
    color: var(--rewards-primary-color);
  }

  .rewards-container .text-color {
    color: var(--rewards-text-color);
  }

  .rewards-container .secondary-text {
    color: var(--rewards-text-secondary);
  }

  .rewards-container .primary-border {
    border: 1px solid var(--rewards-primary-color);
  }

  .rewards-container .primary-background {
    background: var(--rewards-primary-color);
  }

  .rewards-container svg path[fill="#C87F95"] {
    fill: var(--rewards-primary-color);
  }
</style>

<div class="rewards-container">
  <button 
    class="rewards-launcher"
    id="rewards-button"
    style="
      position: fixed;
      bottom: 20px;
      right: 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: var(--rewards-primary-color);
      color: white;
      border: none;
      border-radius: 100px;
      cursor: pointer;
      z-index: 999997;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
      font-family: var(--rewards-font-family);
    "
    onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 16px rgba(200,127,149,0.4)'"
    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(200,127,149,0.3)'"
  >
    <span id="button-icon" style="
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    ">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.27 2 8.5C2 5.41 4.42 3 7.5 3C9.22 3 10.95 3.86 12 5.3C13.05 3.86 14.78 3 16.5 3C19.58 3 22 5.41 22 8.5C22 12.27 18.6 15.36 13.45 20.03L12 21.35Z" fill="#fff"/>
      </svg>
    </span>
    <span id="button-text" style="
      font-size: 14px;
      font-weight: 500;
    ">{{ block.settings.button_text }}</span>
  </button>

  <div id="rewards-panel" class="rewards-panel" style="
    position: fixed;
    bottom: 100px;
    right: 20px;
    width: 400px;
    height: 550px;
    background: #FFFFFF;
    border-radius: 20px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.15);
    z-index: 999998;
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: translateY(100%);
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
    font-family: var(--rewards-font-family);
  ">
    <!-- Header Section -->
    <div style="
      padding: 12px 30px;
      background: var(--rewards-primary-color);
      color: white;
      text-align: left;
      max-height: 100px;
    ">
      <h1 style="
        margin: 0;
        font-size: 12px;
        font-weight: 400;
        font-family: var(--rewards-font-family);
        color: white;
      ">Welcome to </h1>
      <h2 style="
        margin: 5px 0 0 0;
        font-size: 20px;
        font-weight: 700;
        line-height: 1.2;
        font-family: var(--rewards-font-family);
        color: white;
      ">{{ block.settings.program_name }}</h2>
    </div>

    <!-- Content Section -->
    <div id="home-section" style="
      padding: 30px;
      background: white;
      border-radius: 20px;
      height: calc(100% - 100px);
      overflow-y: auto;
      scroll-behavior: smooth;
    ">
      <!-- Content will be dynamically inserted here by JavaScript -->
    </div>
  </div>

  <!-- Add this right after the rewards-container div -->
  <div id="rewards-popup" style="
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 300px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.15);
    z-index: 999996;
    transform: translateY(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    pointer-events: none;
    padding: 20px;
    font-family: var(--rewards-font-family);
  ">
    <button 
      onclick="window.hideRewardsPopup()"
      style="
        position: absolute;
        top: 10px;
        right: 10px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
      "
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
        <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="#666"/>
      </svg>
    </button>

    <div style="
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 15px;
    ">
      <div style="
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--rewards-primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
      ">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#fff"/>
        </svg>
      </div>
      <h3 style="
        margin: 0;
        font-size: 18px;
        color: var(--rewards-text-color);
        font-weight: 500;
      ">Earn Rewards Points!</h3>
    </div>

    <p style="
      margin: 0 0 15px 0;
      font-size: 14px;
      line-height: 1.5;
      color: var(--rewards-text-color);
    ">Join our rewards program and earn <strong style="color: var(--rewards-primary-color)"><span id="potential-points">0</span> {{ block.settings.points_name }}</strong> on your current cart items!</p>

    <button 
      onclick="window.toggleRewardsPanel()"
      style="
        width: 100%;
        padding: 12px;
        background: var(--rewards-primary-color);
        color: white;
        border: none;
        border-radius: 100px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
      "
      onmouseover="this.style.opacity='0.9'"
      onmouseout="this.style.opacity='1'"
    >
      Join Now
    </button>
  </div>
</div>

<!-- Initialize data first -->
<script>
  window.customerData = {
    firstName: {{ customer.first_name | json }},
    lastName: {{ customer.last_name | json }},
    email: {{ customer.email | json }},
    phone: {{ customer.phone | json }},
    id: {{ customer.id | json }}
  };
  window.shopData = {
    name: {{ shop.name | json }},
    programName: {{ block.settings.program_name | json }},
    pointsName: {{ block.settings.points_name | json }},
    theme: {
      primaryColor: {{ block.settings.primary_color | json }},
      textColor: {{ block.settings.text_color | json }},
      fontFamily: {{ block.settings.font_family | json }}
    },
    currency: {{ shop.currency | json }}
  };

  // Add initial cart data
  window.Shopify = window.Shopify || {};
  window.Shopify.cart = {{ cart | json }};
</script>

<!-- Import lit-html from Skypack -->
<script type="module">
  import { html, render } from 'https://cdn.jsdelivr.net/gh/lit/dist@3/core/lit-core.min.js';
  window.html = html;
  window.render = render;
</script>

<!-- Then load and initialize the widget -->
<script type="module">
  import { 
    toggleRewardsPanel, 
    onlyNumbers, 
    formatPhoneNumber, 
    validateAndSubmit,
    verifyOTP,
    initializeWidget,
    showProfileView,
    showRedeemPoints,
    handleRedemption,
    applyDiscountCode,
    loadWidgetData,
    showEarnPoints,
    showAbout,
    resendOTP,
    showRewardsPopup,
    hideRewardsPopup,
    initializeInBackground,
    applyDiscountCodeSuccessCheck
  } from "{{ 'widget.js' | asset_url }}?v={{ "now" | date: "%Y%m%d%H%M%S" }}";
  
  // Make functions available globally
  window.onlyNumbers = onlyNumbers;
  window.formatPhoneNumber = formatPhoneNumber;
  window.validateAndSubmit = validateAndSubmit;
  window.verifyOTP = verifyOTP;
  window.showProfileView = showProfileView;
  window.showRedeemPoints = showRedeemPoints;
  window.handleRedemption = handleRedemption;
  window.applyDiscountCode = applyDiscountCode;
  window.showEarnPoints = showEarnPoints;
  window.showAbout = showAbout;
  window.resendOTP = resendOTP;
  window.toggleRewardsPanel = toggleRewardsPanel;
  window.showRewardsPopup = showRewardsPopup;
  window.hideRewardsPopup = hideRewardsPopup;
  
  // Function to fetch latest cart data and initialize widget
  async function initializeAndUpdateCart() {
    try {
      const response = await fetch('/cart.js');
      const cartData = await response.json();
      window.Shopify = window.Shopify || {};
      window.Shopify.cart = cartData;
      
      // Initialize widget data in background
      await initializeInBackground();
    } catch (error) {
      console.error('Error initializing widget:', error);
    }
  }

  // Initialize when page loads
  window.addEventListener('load', () => {
    // Small delay to ensure all data is loaded
    setTimeout(initializeAndUpdateCart, 1000);
    setTimeout(applyDiscountCodeSuccessCheck, 200);
  });

  // Update cart data and reinitialize when cart changes
  document.addEventListener('cart:updated', initializeAndUpdateCart);
  document.addEventListener('cart:refresh', initializeAndUpdateCart);

  // Add click handler to button
  document.getElementById('rewards-button').onclick = window.toggleRewardsPanel;
</script>

<!-- Add this after the initial customer data setup -->
<script>
  // Setup customer data observer
  const customerObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && !window.customerData?.id) {
        // Customer data was cleared - user logged out
        window.clearSessionData();
      }
    });
  });

  // Start observing customer data changes
  const customerDataContainer = document.querySelector('body');
  if (customerDataContainer) {
    customerObserver.observe(customerDataContainer, {
      childList: true,
      subtree: true
    });
  }
</script>

{% schema %}
{
  "name": "Rewards Widget",
  "target": "body",
  "settings": [
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "Rewards"
    },
    {
      "type": "text",
      "id": "program_name",
      "label": "Program Name",
      "default": "The Enchantress Club"
    },
    {
      "type": "text",
      "id": "points_name",
      "label": "Points Name",
      "default": "Loyalty Points"
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Primary Color",
      "default": "#C87F95"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#333333"
    },
    {
      "type": "select",
      "id": "font_family",
      "label": "Font Family",
      "options": [
        {
          "value": "shop",
          "label": "Use Shop Font"
        },
        {
          "value": "'Cinzel', serif",
          "label": "Cinzel"
        },
        {
          "value": "'EB Garamond', serif",
          "label": "EB Garamond"
        },
        {
          "value": "Arial",
          "label": "Arial"
        },
        {
          "value": "Helvetica",
          "label": "Helvetica"
        },
        {
          "value": "Roboto",
          "label": "Roboto"
        },
        {
          "value": "Open Sans",
          "label": "Open Sans"
        },
        {
          "value": "Lato",
          "label": "Lato"
        },
        {
          "value": "Montserrat",
          "label": "Montserrat"
        }
      ],
      "default": "shop"
    }
  ]
}
{% endschema %}
