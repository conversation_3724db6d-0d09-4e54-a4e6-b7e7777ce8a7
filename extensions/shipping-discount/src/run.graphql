query RunInput {
  discountNode {
    metafield(namespace: "$app:shipping-discount", key: "function-configuration") {
      value
    }
  }
  cart {
    deliveryGroups {
      deliveryOptions {
        title,
        handle
      }
    }
    buyerIdentity {
      customer{
        id,
        freeShippingEligible: metafield(namespace: "loyalty", key: "free_shipping_eligible") {
          value
          type
        }
        freeShippingExpiry: metafield(namespace: "loyalty", key: "free_shipping_expiry") {
          value
          type
        }
      }
    }
  }
}
