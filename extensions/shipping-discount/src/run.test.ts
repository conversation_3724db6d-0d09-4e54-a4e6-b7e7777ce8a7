import { describe, it, expect } from 'vitest';
import { run } from './run';
import {FunctionRunResult, RunInput} from '../generated/api';

const createMockInput = (overrides: Partial<RunInput> = {}): RunInput => ({
  __typename: "Input",
  discountNode: {
    metafield: null,
  },
  cart: {
    buyerIdentity: {
      customer: null,
    },
    deliveryGroups: [],
  },
  ...overrides,
});

describe('shipping discounts function', () => {
  it('returns no discounts when no customer is present', () => {
    const input = createMockInput();
    const result = run(input);
    const expected: FunctionRunResult = {
      discounts: [],
    };
    expect(result).toEqual(expected);
  });

  it('returns no discounts when customer has no metafields', () => {
    const input = createMockInput({
      cart: {
        buyerIdentity: {
          customer: {
            id: "gid://shopify/Customer/123",
            freeShippingEligible: null,
            freeShippingExpiry: null,
          },
        },
        deliveryGroups: [],
      },
    });
    const result = run(input);
    const expected: FunctionRunResult = {
      discounts: [],
    };
    expect(result).toEqual(expected);
  });

  it('returns no discounts when freeShippingEligible is false', () => {
    const input = createMockInput({
      cart: {
        buyerIdentity: {
          customer: {
            id: "gid://shopify/Customer/123",
            freeShippingEligible: { value: "false", type: "boolean" },
            freeShippingExpiry: {
              value: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              type: "date_time",
            },
          },
        },
        deliveryGroups: [],
      },
    });
    const result = run(input);
    const expected: FunctionRunResult = {
      discounts: [],
    };
    expect(result).toEqual(expected);
  });

  it('returns no discounts when freeShippingExpiry is in the past', () => {
    const input = createMockInput({
      cart: {
        buyerIdentity: {
          customer: {
            id: "gid://shopify/Customer/123",
            freeShippingEligible: { value: "true", type: "boolean" },
            freeShippingExpiry: {
              value: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
              type: "date_time",
            },
          },
        },
        deliveryGroups: [],
      },
    });
    const result = run(input);
    const expected: FunctionRunResult = {
      discounts: [],
    };
    expect(result).toEqual(expected);
  });

  it('applies free shipping discount when eligible and expiry is valid', () => {
    const input = createMockInput({
      cart: {
        buyerIdentity: {
          customer: {
            id: "gid://shopify/Customer/123",
            freeShippingEligible: { value: "true", type: "boolean" },
            freeShippingExpiry: {
              value: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              type: "date_time",
            },
          },
        },
        deliveryGroups: [
          {
            deliveryOptions: [
              { handle: "shipping-option-1", title: "Standard", cost: { amount: 10 } },
              { handle: "shipping-option-2", title: "Express", cost: { amount: 20 } },
            ],
          },
        ],
      },
    });
    const result = run(input);
    const expected: FunctionRunResult = {
      discounts: [
        {
          targets: [
            { deliveryOption: { handle: "shipping-option-1" } },
            { deliveryOption: { handle: "shipping-option-2" } },
          ],
          value: {
            percentage: { value: 100.0 },
          },
        },
      ],
    };
    expect(result).toEqual(expected);
  });
});
