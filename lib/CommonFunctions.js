const config = require('./config');
const logger = require('./logger');
const RedisConnector = require('./db/connectors/RedisConnector');
const { getAsync, setAsync } = RedisConnector.getCommands();
const OrganizationDAO = require('./db/dao/OrganizationDAO');

const log = logger(config.logger);

const setCacheRedis = async (cacheKey, data) => {
    try {
        await setAsync(cacheKey, JSON.stringify(data), 'EX', 60 * 60 * 3);
    } catch (e) {
        log.error('error\n', e);
    }
};

class CommonFunctions {
    static async promiseRaceAsync(promises, cacheKey) {
        try {
            const result = await Promise.any(promises);
            if (!result && !result.data) {
                return null;
            }

            if (result?.from !== 'cache') {
                setCacheRedis(cacheKey, result.data);
                return result.data;
            }
            return JSON.parse(result.data);
        } catch (error) {
            return Promise.reject(error);
        }
    }

    static async getWrapperForCache(func, params = [], dbMethod) {
        try {
            const result = await func(...params);
            if (result) {
                return {
                    data: result,
                    from: dbMethod ? 'db' : 'cache'
                };
            }
            return Promise.reject(`data not found in ${dbMethod ? 'DB' : 'CACHE'}`);
        } catch (e) {
            return Promise.reject(e);
        }
    }

    static async getOrganizationData(organizationId) {
        try {
            const cacheKey = `organizations:${organizationId}`;

            return await this.promiseRaceAsync(
                [
                    this.getWrapperForCache(getAsync, [cacheKey]),
                    this.getWrapperForCache(OrganizationDAO.getOrganization, [organizationId], true)
                ],
                cacheKey
            );
        } catch (e) {
            return Promise.reject(new Error('organization not found'));
        }
    }
}

module.exports = CommonFunctions;
