'use strict';
const crypto = require('crypto');
const { v1: uuidV1 } = require('uuid');
const { reduce } = require('lodash');
const moment = require('moment');
const algorithm = 'aes-256-cbc';

const CustomHttpError = require("./CustomHttpError");
const config = require('./config');
const logger = require('./logger');
const log = logger(config.logger);

const secretConfig = require('./../config');
const fs = require("fs");
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

class Utils {

    static buildProjection(attrArray) {
        return reduce(attrArray, (proj, attr) => {
            proj[attr] = 1;
            return proj;
        }, {});
    }

    static generateContactId(contact) {
        if (contact.user_id) {
            return this.generateId(contact.user_id, contact.owner_id);
        } else if (contact.mobile_number) {
            return this.generateId(contact.mobile_number, contact.owner_id);
        } else if (contact.email) {
            return this.generateId(contact.email, contact.owner_id);
        } else {
            return null;
        }
    }

    static encrypt(text, password) {
        let cipher = crypto.createCipher(algorithm, password);
        let crypted = cipher.update(text, 'utf8', 'hex');
        crypted += cipher.final('hex');
        return crypted;
    }

    static decrypt(text, password) {
        let decipher = crypto.createDecipher(algorithm, password);
        let dec = decipher.update(text, 'hex', 'utf8');
        dec += decipher.final('utf8');
        return dec;
    }

    static getHash(strToHash) {
        const shasum = crypto.createHash('sha1');
        shasum.update(strToHash);
        return shasum.digest('hex');
    }


    static generateId(secondaryId, ownerId) {
        let shasum = crypto.createHash('sha1');
        shasum.update(secondaryId + ':' + ownerId);
        return shasum.digest('hex');
    }

    static generateRegionalId(secondaryId, ownerId, regionId) {
        let shasum = crypto.createHash('sha1');
        shasum.update(`${secondaryId}:${ownerId}:${regionId}`);
        return shasum.digest('hex');
    }

    static getCurrentDateISO() {
        return new Date().toISOString();
    }

    static generateUUID() {
        return uuidV1();
    }

    static getEventUpdateObj (callerId, eventDetails, setObj, incObj=null) {
        return {
            $set: setObj,
            ...(incObj?{$inc: incObj}:{}),
            $push: {
                historyEvents: {
                    $each: [
                        {
                            eventDate: new Date(),
                            eventDetails,
                            eventBy: callerId
                        }
                    ],
                    $sort: { eventDate: -1 }
                }
            },
            $currentDate: { modifiedOn: true },
            updatedBy: callerId
        };
    }

}

module.exports = Utils;
