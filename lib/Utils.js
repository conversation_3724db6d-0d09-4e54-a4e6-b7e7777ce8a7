/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/28/16.
 */
'use strict';
const Queue = require('bull');
const mongoose = require('mongoose');
const crypto = require('crypto');
const uuidV1 = require('uuid/v1');
const lodash = require('lodash');
const { pullAt } = require('lodash');
const moment = require('moment');
const algorithm = 'aes-256-cbc';
const fs = require('fs');
const accepts = require('mongodb-language-model').accepts;
const IdentityService = require('./services/IdentityService');
const CustomHttpError = require('./CustomHttpError');
const config = require('./config');
const logger = require('./logger');
const secretConfig = require('./../config');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const StorageHandler = require('./handlers/StorageHandler');
const RedisConnector = require('./db/connectors/RedisConnector');
const { getAsync, setAsync } = RedisConnector.getCommands();
const RegionDAO = require('./db/dao/RegionDAO');
const TiersDAO = require('./db/dao/TiersDAO');
const MembersDAO = require('./db/dao/MembersDAO');
const SubTransactionTypesDAO = require('./db/dao/SubTransactionTypesDAO');
const CardDAO = require('./db/dao/CardDAO');
const OrganizationDAO = require('./db/dao/OrganizationDAO');
const { CATEGORY } = require('../workers/constants');
const { CARD_STATUS } = require('./db/models/enums/card.enums');
const { TYPE } = require('./db/models/enums/transaction.enums');
const {
    QUEUES: { EMAIL_MESSAGES_QUEUE }
} = require('./../workers/constants');

const log = logger(config.logger);

const emailMessagesQueue = new Queue(EMAIL_MESSAGES_QUEUE, {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000
    }
});

const setCacheRedis = async (cacheKey, data) => {
    try {
        await setAsync(cacheKey, JSON.stringify(data), 'EX', 60 * 60 * 3);
    } catch (e) {
        log.error('error\n', e);
    }
};

const promiseRaceAsync = async (promises, cacheKey) => {
    try {
        const result = await Promise.any(promises);
        if (!result && !result.data) {
            return null;
        }

        if (result?.from !== 'cache') {
            setCacheRedis(cacheKey, result.data);
            return result.data;
        }
        return JSON.parse(result.data);
    } catch (error) {
        return Promise.reject(error);
    }
};

const getWrapperForCache = async (func, params = [], dbMethod) => {
    try {
        const result = await func(...params);
        if (result) {
            return {
                data: result,
                from: dbMethod ? 'db' : 'cache'
            };
        }
        return Promise.reject(`data not found in ${dbMethod ? 'DB' : 'CACHE'}`);
    } catch (e) {
        return Promise.reject(e);
    }
};

class Utils {
    static async validateQueryFilter(queryFilter) {
        try {
            const queryString = JSON.stringify(queryFilter);
            log.debug('Filter string to validate', queryString);

            if (!accepts(queryString)) {
                throw new Error(`Not a valid mongo query!\n ${queryString}`);
            }

            log.debug('Validated mongo query', queryString);
            return Promise.resolve(JSON.parse(queryString));
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError(`Invalid query: ${err.message}`, '400'));
        }
    }

    static buildProjection(attrArray) {
        return lodash.reduce(
            attrArray,
            (proj, attr) => {
                proj[attr] = 1;
                return proj;
            },
            {}
        );
    }

    static generateContactId(contact) {
        if (contact.user_id) {
            return this.generateId(contact.user_id, contact.owner_id);
        } else if (contact.mobile_number) {
            return this.generateId(contact.mobile_number, contact.owner_id);
        } else if (contact.email) {
            return this.generateId(contact.email, contact.owner_id);
        } else {
            return null;
        }
    }

    // TODO: [SHTT-1529] - 'crypto.createCipher' and 'crypto.createDecipher' MUST be replaced with 'crypto.createCipheriv' and 'crypto.createDecipheriv' respectively due to poor security, will NOT work with Node 23+.
    static encrypt(text, password) {
        let cipher = crypto.createCipher(algorithm, password);
        let crypted = cipher.update(text, 'utf8', 'hex');
        crypted += cipher.final('hex');
        return crypted;
    }

    // TODO: [SHTT-1529] - 'crypto.createCipher' and 'crypto.createDecipher' MUST be replaced with 'crypto.createCipheriv' and 'crypto.createDecipheriv' respectively due to poor security, will NOT work with Node 23+.
    static decrypt(text, password) {
        let decipher = crypto.createDecipher(algorithm, password);
        let dec = decipher.update(text, 'hex', 'utf8');
        dec += decipher.final('utf8');
        return dec;
    }

    static getHash(strToHash) {
        const shasum = crypto.createHash('sha1');
        shasum.update(strToHash);
        return shasum.digest('hex');
    }

    static generateId(secondaryId, ownerId) {
        const shasum = crypto.createHash('sha1');
        shasum.update(secondaryId + ':' + ownerId);
        return shasum.digest('hex');
    }

    static generateRegionalId(secondaryId, ownerId, regionId) {
        const shasum = crypto.createHash('sha1');
        shasum.update(`${secondaryId}:${ownerId}:${regionId}`);
        return shasum.digest('hex');
    }

    static getCurrentDateISO() {
        return new Date().toISOString();
    }

    static generateUUID() {
        return uuidV1();
    }

    static getComparisonDates(fromDate, toDate) {
        const fromDateObj = fromDate ? moment(fromDate).hour(0).minute(0).second(0).millisecond(0) : null;
        const toDateObj = toDate
            ? moment(toDate).hour(23).minute(59).second(59).millisecond(999)
            : moment().hour(23).minute(59).second(59).millisecond(999);
        const durationDays = fromDateObj ? toDateObj.diff(fromDateObj, 'days') : 7;
        const fromDateObjMod = fromDateObj ? fromDateObj.clone() : null;
        const fromDateObjMod2 = fromDateObj ? fromDateObj.clone() : null;
        const toDateObjMod = toDateObj.clone();
        const backedToDateObj = fromDateObjMod
            ? fromDateObjMod.subtract(1, 'days').hour(23).minute(59).second(59).millisecond(999)
            : toDateObjMod.subtract(durationDays, 'days');
        return {
            currentFromDate: fromDateObj ? fromDateObj.toISOString() : null,
            backedFromDate: fromDateObj ? fromDateObjMod2.subtract(durationDays, 'days').toISOString() : null,
            currentToDate: toDateObj.toISOString(),
            backedToDate: backedToDateObj.toISOString()
        };
    }

    static groupBySequence(e, r, t = [], n = []) {
        for (r; r < e.length; ++r) {
            if (!r) {
                n.push(e[r]);
                continue;
            }
            if (e[r - 1] != e[r] - 1) {
                t.push(n);
                n = [];
            }
            n.push(e[r]);
        }
        t.push(n);
        return t;
    }

    static async getHistoryEventUserNames(historyEvents) {
        const newHistoryEvents = [];
        const userNames = await IdentityService.getUsersByIds(historyEvents.map((item) => item.eventBy));
        historyEvents.forEach((item) => {
            newHistoryEvents.push({
                ...item,
                eventByName: userNames[item.eventBy?.toString()] || null
            });
        });
        return Promise.resolve(newHistoryEvents);
    }

    static async getRegionData(organizationId, regionId) {
        try {
            const cacheKey = `regions:${organizationId}:${regionId}`;

            return await promiseRaceAsync(
                [getWrapperForCache(getAsync, [cacheKey]), getWrapperForCache(RegionDAO.getRegion, [regionId], true)],
                cacheKey
            );
        } catch (e) {
            return Promise.reject(new CustomHttpError('Region not found or is invalid', '404', '100000'));
        }
    }

    static async getZeroTier(organizationId, regionId) {
        try {
            const cacheKey = `zeroTier:${organizationId}:${regionId}`;

            return await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(TiersDAO.getZeroTier, [regionId, organizationId], true)
                ],
                cacheKey
            );
        } catch (e) {
            return null;
        }
    }

    static async getMemberFromCache(organizationId, memberId, ignoreCache = false) {
        try {
            const cacheKey = `members:${organizationId}:${memberId}`;
            if (!ignoreCache) {
                return await promiseRaceAsync(
                    [
                        getWrapperForCache(getAsync, [cacheKey]),
                        getWrapperForCache(MembersDAO.getMember, [organizationId, memberId], true)
                    ],
                    cacheKey
                );
            } else {
                const member = await MembersDAO.getMember(organizationId, memberId);
                if (member) {
                    setAsync(cacheKey, JSON.stringify(member), 'EX', 60 * 60 * 3);
                    return member;
                } else {
                    return Promise.reject(new Error('member not found'));
                }
            }
        } catch (e) {
            return Promise.reject(new Error('member not found'));
        }
    }

    static async getSubTypeFromCache(organizationId, subTypeId) {
        try {
            const cacheKey = `subTypes:${organizationId}:${subTypeId}`;
            return await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(
                        SubTransactionTypesDAO.getSubTransactionTypesById,
                        [organizationId, subTypeId],
                        true
                    )
                ],
                cacheKey
            );
        } catch (e) {
            return Promise.reject(new Error('sub type not found'));
        }
    }

    static async getSubTypesFromCache(organizationId) {
        try {
            const cacheKey = `subTypes:${organizationId}`;
            const result = await getAsync(cacheKey);
            if (result) {
                return JSON.parse(result);
            } else {
                const mappedDocs = {};
                const subTypes = await SubTransactionTypesDAO.getSubTransactionTypesByFilter(organizationId, {}, null, {
                    _id: 1,
                    name: 1
                });
                subTypes.forEach((item) => {
                    mappedDocs[item._id.toString()] = item.name;
                });
                await setAsync(cacheKey, JSON.stringify(mappedDocs), 'EX', 60 * 60 * 3);
                return mappedDocs;
            }
        } catch (e) {
            return Promise.reject(new Error('sub types not found'));
        }
    }

    static async setCache(organizationId, key, value, ttl) {
        try {
            const cacheKey = `${organizationId}:${key}`;
            setAsync(cacheKey, JSON.stringify(value), 'EX', ttl || 60 * 60 * 3);
        } catch (e) {
            return Promise.reject(new Error(`Error while setting cache ${e}`));
        }
    }

    static async getCache(organizationId, key) {
        try {
            const cacheKey = `${organizationId}:${key}`;
            const cacheValue = await getAsync(cacheKey);
            if (cacheValue) {
                return JSON.parse(cacheValue);
            } else {
                return null;
            }
        } catch (e) {
            return Promise.reject(new Error(`Error while reading cache ${e}`));
        }
    }

    static async getAssignedCardFromCacheByMemberId(organizationId, memberId) {
        try {
            const cacheKey = `members:cards:${organizationId}:${memberId}`;

            return await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(
                        CardDAO.getCard(organizationId, {
                            memberId: memberId,
                            status: CARD_STATUS.ASSIGNED
                        })
                    )
                ],
                cacheKey
            );
        } catch (e) {
            return null;
        }
    }

    static async getCardFromCacheByCardNumber(organizationId, cardNumber) {
        try {
            const cacheKey = `cards:${organizationId}:${cardNumber}`;

            return await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(
                        CardDAO.getCard,
                        [
                            organizationId,
                            {
                                cardNoStr: cardNumber,
                                status: CARD_STATUS.ASSIGNED
                            }
                        ],
                        true
                    )
                ],
                cacheKey
            );
        } catch (e) {
            return null;
        }
    }

    static async getOrganizationData(organizationId) {
        try {
            const cacheKey = `organizations:${organizationId}`;

            return await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(OrganizationDAO.getOrganization, [organizationId], true)
                ],
                cacheKey
            );
        } catch (e) {
            return Promise.reject(new Error('organization not found'));
        }
    }

    static async calculatePointsToExpire(
        pointsToExpire = [],
        pointsAmount,
        subTransactionTypeId,
        organizationId,
        regionId,
        type
    ) {
        if (type !== TYPE.REDEMPTION && subTransactionTypeId) {
            const { pointConfiguration } = await this.getRegionData(organizationId, regionId);
            if (pointConfiguration.pointExpiryCalculationSubTransactionTypeIds) {
                const subTransactionTypeIds = JSON.parse(JSON.stringify(pointConfiguration.pointExpiryCalculationSubTransactionTypeIds));
                if (Array.isArray(subTransactionTypeIds) && subTransactionTypeIds.length > 0) {
                    if (!subTransactionTypeIds.includes(subTransactionTypeId)) {
                        return pointsToExpire;
                    }
                }
            } else {
                return pointsToExpire;
            }
        }
        const indexesToPull = [];
        if (pointsToExpire && Array.isArray(pointsToExpire) && pointsToExpire.length > 0) {
            for (const expireObjIndex in pointsToExpire) {
                const expireObjPointsToExpire = pointsToExpire[expireObjIndex].pointsToExpire;
                if (expireObjPointsToExpire > pointsAmount) {
                    pointsToExpire[expireObjIndex].pointsToExpire = expireObjPointsToExpire - pointsAmount;
                    break;
                } else {
                    pointsAmount = pointsAmount - expireObjPointsToExpire;
                    indexesToPull.push(expireObjIndex);
                }
            }
        }
        if (indexesToPull.length > 0) {
            pullAt(pointsToExpire, indexesToPull);
        }
        return pointsToExpire;
    }

    static generateIdempotentKey(unique, payload) {
        return `mls-${this.getHash(`${unique}:${payload}`)}`;
    }

    static async exportToCSV(
        dataCursor,
        organizationId,
        reportName,
        headers,
        folderName,
        stream = true,
        streamFunction,
        urlExpire = 3600
    ) {
        const folderLocation = `${secretConfig.TEMP_FILE_UPLOAD_PATH}/${folderName}`;
        const localFilePath = `${folderLocation}/${reportName}-${Date.now()}.csv`;

        if (!fs.existsSync(folderLocation)) {
            fs.mkdirSync(folderLocation, { recursive: true });
        }

        const csvWriter = createCsvWriter({
            path: localFilePath,
            header: headers
        });

        if (!stream) {
            await csvWriter.writeRecords(dataCursor);
        } else {
            if (streamFunction) {
                await streamFunction(dataCursor, csvWriter);
            } else {
                for (let record = await dataCursor.next(); record != null; record = await dataCursor.next()) {
                    const csvData = [record];
                    await csvWriter.writeRecords(csvData);
                }
            }
        }
        const fileBuffer = fs.readFileSync(localFilePath);
        const fileUploadPath = organizationId + `/${folderName}/${reportName}-${Date.now()}.csv`;
        await StorageHandler.upload(fileUploadPath, fileBuffer);
        const url = await StorageHandler.downloadAsURL(fileUploadPath, urlExpire);
        fs.unlinkSync(localFilePath);
        return Promise.resolve({
            url,
            fileUploadPath
        });
    }

    static isNewMember(lastTransactionOn, registeredOn) {
        if (moment(registeredOn).isSame(moment(), 'day')) {
            return true;
        }

        return !lastTransactionOn;
    }

    static async sendEmail(organizationId, regionId, memberId, subject, body, bodyHtml, to, from) {
        emailMessagesQueue.add(
            { organizationId, regionId, memberId, subject, body, bodyHtml, to, from, category: CATEGORY.SYSTEM },
            {
                jobId: mongoose.Types.ObjectId().toString()
            }
        );
    }

    static async uploadReportAndGenerateLink(organizationId, folderName, reportName, folderLocation, localFilePath) {
        const fileBuffer = fs.readFileSync(localFilePath);
        const fileUploadPath = organizationId + `/${folderName}/${reportName}`;
        await StorageHandler.upload(fileUploadPath, fileBuffer);
        const url = await StorageHandler.downloadAsURL(fileUploadPath, 86400);
        fs.unlinkSync(localFilePath);
        return Promise.resolve({
            url,
            fileUploadPath
        });
    }

    static getContactAttributeDiff(newAttributes, oldAttributes) {
        return Object.keys(newAttributes).reduce((result, key) => {
            if (
                oldAttributes &&
                oldAttributes[key] &&
                newAttributes[key] &&
                newAttributes[key].values &&
                newAttributes[key].values.length > 0
            ) {
                result[key] = lodash.differenceWith(
                    newAttributes[key].values || [],
                    oldAttributes[key].values || [],
                    lodash.isEqual
                );
            } else {
                if (newAttributes[key] && newAttributes[key].values && newAttributes[key].values.length > 0) {
                    result[key] = newAttributes[key].values;
                }
            }
            return result;
        }, {});
    }
}

Utils.promiseRaceAsync = promiseRaceAsync;
Utils.getWrapperForCache = getWrapperForCache;
module.exports = Utils;
