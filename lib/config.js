'use strict';
const path = require('path');
const yaml_config = require('node-yaml-config');
const fileName = 'common.yml';
let cachedConfig = null;
function loadConfig() {
  if (!cachedConfig) {
    // Load the configuration only if it hasn't been cached yet
    cachedConfig = yaml_config.load(
      path.join(__dirname, `../config/${fileName}`),
    );
  }
  return cachedConfig;
}

module.exports = loadConfig();
