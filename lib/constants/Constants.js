'use strict';

module.exports = {
    EVENT_TEMPLATES: {
        DEFAULT: 0,
        LOYALTY_POINT_COLLECTED: 10,
        LOYALTY_POINT_REDEEMED: 11,
        LOYALTY_REGISTERED: 12,
        LOY<PERSON>TY_REWARD_REDEEMED: 13,
        ENGAGEMENT_CAMPAIGN_CREATED: 14,
        ENGAGEMENT_COUPON_ISSUED: 15,
        ENGAGEMENT_FORM_RESPONSE: 16,
        ENGAGEMENT_CUSTOM_EVENT: 17,
        MESSAGE_INCOMING_SMS: 18,
        MESSAGE_OUTGOING_SMS: 19,
        MESSAGE_OUTGOING_EMAIL: 20,
        MESSAGE_INCOMING_EMAIL: 21,
        ENGAGEMENT_COUPON_REDEEMED: 22
    },
    SYSTEM_EVENTS: {
        COLLECT_POINTS_BILL: 'Collect Points Bill',
        COLLECT_POINTS_AMOUNT: 'Collect Points Amount',
        REDEEM_POINTS: 'Redeem Points',
        TRANSFER_POINTS: 'Transfer Points',
        RECEIVED_POINTS: 'Received Points',
        ADJUST_POINTS: 'Adjust Points',
        PRE_BIRTHDAY_REMINDER: 'Pre Birthday Reminder',
        BIRTHDAY_NOTIFICATION: 'Birthday Notification',
    },
    MODULE_IDS: {
        LOYALTY_MODULE: 1,
        ENGAGEMENT_MODULE: 2
    },
    COLLECT_POINTS_CALLER_TYPES: {
        COLLECT_POINTS_AMOUNT: 'COLLECT_POINTS_AMOUNT',
        COLLECT_POINTS_BILL: 'COLLECT_POINTS_BILL'
    },
    REWARD_TYPES: {
        VOUCHER: 'VOUCHER',
        DIGITAL: 'DIGITAL'
    },
    DIGITAL_REWARD_TYPES: {
        RELOAD: 'RELOAD'
    },
    DIGITAL_REWARD_SERVICES: {
        EZCASH: 'EZCASH',
        MCASH: 'MCASH'
    },
    AUTH_STATUS: {
        PENDING: 'PENDING',
        APPROVED: 'APPROVED',
        REJECTED: 'REJECTED'
    },
    CARD_REQUEST_STATUS: {
        NEW_REQUEST: 'NEW_REQUEST',
        PRINTING: 'PRINTING',
        PRINTED: 'PRINTED',
        POSTED: 'POSTED',
        RETURNED: 'RETURNED'
    },
    CARD_PRINT_JOB_STATUS: {
        IN_PROGRESS: 'IN_PROGRESS',
        COMPLETED: 'COMPLETED',
        FAILED: 'FAILED',
        CANCELLED: 'CANCELLED'
    },
    CARD_PRINT_STATUS: {
        IN_PROGRESS: 'IN_PROGRESS',
        COMPLETED: 'COMPLETED',
        FAILED: 'FAILED'
    },
    LETTER_PRINT_STATUS: {
        IN_PROGRESS: 'IN_PROGRESS',
        COMPLETED: 'COMPLETED',
        FAILED: 'FAILED'
    },
    LOYALTY_USER_TYPE: {
        PRIMARY: 'PRIMARY',
        SECONDARY: 'SECONDARY'
    },
    LOYALTY_USER_STATUS: {
        ACTIVE: 'ACTIVE',
        DEACTIVE: 'DEACTIVE',
        SUSPENDED: 'SUSPENDED',
        ARCHIVED: 'ARCHIVED'
    },
    MEMBER_EXPORT_LOOKUP_KEYS_MAP: {
        affinityGroup: (dataObj) => dataObj.affinityGroup?.details?.name,
        tier: (dataObj) => dataObj.tierData?.name,
        loyaltyCard: (dataObj) => dataObj.loyaltyCard?.cardNoStr
    },
    JOB_TYPES_MAP: {
        expiryPointsMonthlyLive: '62de6e75a9650000cf0005b2',
        expiryPointsMonthlySimulation: '6303266be3320000b4007204',
        rollingPointBalanceSummary: '6305da5c8b0200007d002664',
        movementsSummary: '6308c41f0804000077003666',
        rollingPointBalanceDetailed: '630caee40a1000003f002097'
    },
    MEMBER_EVENTS: { UPDATE_MEMBER_STATUS: 'UPDATE_MEMBER_STATUS' }
};
