'use strict';
const { TEMPLATE_ID, STATUS } = require('./../db/models/enums/template.enums');
const fs = require('fs');

//OTP
const otpCodeVerifyMsg = JSON.stringify('Use the code <%= data.otpCode %> to authorize your request');
const otpCodeVerifySubject =  JSON.stringify('OTP verification');

//VERIFY_ACCOUNT_URL
const verifyAccountURLHtml = JSON.stringify(fs.readFileSync(`${process.cwd()}/lib/constants/MessageTemplateResources/email_verify/html.html`, 'utf8'));
const verifyAccountURLText = JSON.stringify(fs.readFileSync(`${process.cwd()}/lib/constants/MessageTemplateResources/email_verify/text.txt`, 'utf8'));
const verifyAccountURLSubject = JSON.stringify('Please verify your email address');

//VERIFY_ACCOUNT_OTP
//TODO: USE NEW TEMPLATE
// const verifyAccountOTPHtml = JSON.stringify(fs.readFileSync(`${process.cwd()}/lib/constants/MessageTemplateResources/email_verify/html.html`, 'utf8'));
const verifyAccountOTPText = JSON.stringify('Use the code <%= data.otpCode %> to verify');
const verifyAccountOTPSubject = JSON.stringify('Please verify your email address');

//SECONDARY_MEMBER_AUTHORIZATION
//TODO: USE NEW TEMPLATE
// const secondaryMemberAuthorizationHtml = JSON.stringify(fs.readFileSync(`${process.cwd()}/lib/constants/MessageTemplateResources/email_verify/html.html`, 'utf8'));
const secondaryMemberAuthorizationText = JSON.stringify('Use the code <%= data.otpCode %> to authorize joining the family');
const secondaryMemberAuthorizationSubject = JSON.stringify('Authorization Code');

//PRIMARY_MEMBER_AUTHORIZATION
//TODO: USE NEW TEMPLATE
// const primaryMemberAuthorizationHtml = JSON.stringify(fs.readFileSync(`${process.cwd()}/lib/constants/MessageTemplateResources/email_verify/html.html`, 'utf8'));
const primaryMemberAuthorizationText = JSON.stringify('Use the code <%= data.otpCode %> to authorize adding a family member');
const primaryMemberAuthorizationSubject = JSON.stringify('Authorization Code');

//CONVERT_TO_PRIMARY_AUTHORIZATION
//TODO: USE NEW TEMPLATE
// const convertToPrimaryAuthorizationHtml = JSON.stringify(fs.readFileSync(`${process.cwd()}/lib/constants/MessageTemplateResources/email_verify/html.html`, 'utf8'));
const convertToPrimaryAuthorizationText = JSON.stringify('Use the code <%= data.otpCode %> to authorize converting to a PRIMARY account');
const convertToPrimaryAuthorizationSubject = JSON.stringify('Authorization Code');


// LOGIN_PORTAL_ACCOUNT_OTP
const verifyLoginOTPText = JSON.stringify('Use the code <%= data.otpCode %> to login');
const verifyLoginOTPSubject = JSON.stringify('Authorization Code');

module.exports = [
  {
    regionId: null,
    templateId: TEMPLATE_ID.OTP,
    smsTemplate: {
      body: otpCodeVerifyMsg,
    },
    emailTemplate: {
      bodyHtml: otpCodeVerifyMsg,
      bodyText: otpCodeVerifyMsg,
      subject: otpCodeVerifySubject
    },
    status: STATUS.ENABLED,
  },
  {
    regionId: null,
    templateId: TEMPLATE_ID.VERIFY_ACCOUNT_URL,
    smsTemplate: {
      body: JSON.stringify(''),
    },
    emailTemplate: {
      bodyHtml: verifyAccountURLHtml,
      bodyText: verifyAccountURLText,
      subject: verifyAccountURLSubject,
    },
    status: STATUS.ENABLED,
  },
  {
    regionId: null,
    templateId: TEMPLATE_ID.VERIFY_ACCOUNT_OTP,
    smsTemplate: {
      body: verifyAccountOTPText,
    },
    emailTemplate: {
      bodyHtml: verifyAccountOTPText,
      bodyText: verifyAccountOTPText,
      subject: verifyAccountOTPSubject,
    },
    status: STATUS.ENABLED,
  },
  {
    regionId: null,
    templateId: TEMPLATE_ID.SECONDARY_MEMBER_AUTHORIZATION,
    smsTemplate: {
      body: secondaryMemberAuthorizationText,
    },
    emailTemplate: {
      bodyHtml: secondaryMemberAuthorizationText,
      bodyText: secondaryMemberAuthorizationText,
      subject: secondaryMemberAuthorizationSubject,
    },
    status: STATUS.ENABLED,
  },
  {
    regionId: null,
    templateId: TEMPLATE_ID.PRIMARY_MEMBER_AUTHORIZATION,
    smsTemplate: {
      body: primaryMemberAuthorizationText,
    },
    emailTemplate: {
      bodyHtml: primaryMemberAuthorizationText,
      bodyText: primaryMemberAuthorizationText,
      subject: primaryMemberAuthorizationSubject,
    },
    status: STATUS.ENABLED,
  },
  {
    regionId: null,
    templateId: TEMPLATE_ID.CONVERT_TO_PRIMARY_AUTHORIZATION,
    smsTemplate: {
      body: convertToPrimaryAuthorizationText,
    },
    emailTemplate: {
      bodyHtml: convertToPrimaryAuthorizationText,
      bodyText: convertToPrimaryAuthorizationText,
      subject: convertToPrimaryAuthorizationSubject,
    },
    status: STATUS.ENABLED,
  },
  {
    regionId: null,
    templateId: TEMPLATE_ID.LOGIN_ACCOUNT_OTP,
    smsTemplate: {
      body: verifyLoginOTPText,
    },
    emailTemplate: {
      bodyHtml: verifyLoginOTPText,
      bodyText: verifyLoginOTPText,
      subject: verifyLoginOTPSubject,
    },
    status: STATUS.ENABLED,
  },
];
