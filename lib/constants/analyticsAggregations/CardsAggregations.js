'use strict';
const { CARD_TYPES } = require('./../../db/models/enums/card.enums');
const { COMMON: { CARDS_DATE_BUCKET } } = require('./../AnalyticsEnums');

const CardsSummaryByMemberTypeAggregation = (organizationId, filters) => [
  {
    '$project': {
      'regionId': 1,
      'organizationId': 1,
      'memberId': 1,
      'status': 1,
      'type': 1
    }
  }, {
    '$match': {
      'organizationId': organizationId,
      'type': {
        $ne: CARD_TYPES.EMBOSSED_CARD
      },
      ...filters
    }
  }, {
    '$group': {
      '_id': '$status',
      'count': {
        '$sum': 1
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'status': '$_id',
        'count': '$count'
      }
    }
  }
];

const ProcessingStatusStatsAggregation = (organizationId, filters) => [
  {
    '$match': {
      'organizationId': organizationId,
      ...filters
    }
  },
  {
    '$project': {
      'regionId': 1,
      'organizationId': 1,
      'memberId': 1,
      'status': 1,
      'type': 1,
      'processingStatus': 1
    }
  }, {
    '$group': {
      '_id': '$processingStatus',
      'count': {
        '$sum': 1
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'processingStatus': '$_id',
        'count': '$count'
      }
    }
  }
];

const CardActivationReportAggregation = (organizationId, filters, series, dateBucket) => [
  {
    '$match': {
      'organizationId': organizationId,
      ...filters,
      'assignedDate': {
        '$ne': null
      }
    }
  }, {
    '$project': {
      'assignedOnDate': {
        '$dateToString': {
          'format': '%Y-%m-%d',
          'date': '$assignedDate'
        }
      },
      'assignedOnMonth': {
        '$dateToString': {
          'format': '%Y-%m',
          'date': '$assignedDate'
        }
      },
      'assignedOnYear': {
        '$year': '$assignedDate'
      },
      'weekOfTheYear': {
        '$concat': [
          {
            '$toString': {
              '$year': '$assignedDate'
            }
          }, '-', {
            '$toString': {
              '$week': '$assignedDate'
            }
          }
        ]
      },
      'quarter': {
        '$cond': [
          {
            '$lte': [
              {
                '$month': '$assignedDate'
              }, 3
            ]
          }, '1', {
            '$cond': [
              {
                '$lte': [
                  {
                    '$month': '$assignedDate'
                  }, 6
                ]
              }, '2', {
                '$cond': [
                  {
                    '$lte': [
                      {
                        '$month': '$assignedDate'
                      }, 9
                    ]
                  }, '3', '4'
                ]
              }
            ]
          }
        ]
      },
      'type': 1
    }
  }, {
    '$addFields': {
      'quarterOfTheYear': {
        '$concat': [
          {
            '$toString': '$assignedOnYear'
          }, '-', '$quarter'
        ]
      }
    }
  }, {
    '$group': {
      '_id': {
        'date': (series ? `$${CARDS_DATE_BUCKET[dateBucket]}`: null)
      },
      'totalCount': {
        '$sum': 1
      },
      'digitalCards': {
        '$sum': {
          '$cond': [
            {
              '$eq': [
                '$type', 'DIGITAL_CARD'
              ]
            }, 1, 0
          ]
        }
      },
      'keyTagCards': {
        '$sum': {
          '$cond': [
            {
              '$eq': [
                '$type', 'KEY_TAG'
              ]
            }, 1, 0
          ]
        }
      },
      'RegularCards': {
        '$sum': {
          '$cond': [
            {
              '$eq': [
                '$type', 'REGULAR_CARD'
              ]
            }, 1, 0
          ]
        }
      },
      'keyTagAndRegularCards': {
        '$sum': {
          '$cond': [
            {
              '$eq': [
                '$type', 'REGULAR_CARD_AND_KEY_TAG'
              ]
            }, 1, 0
          ]
        }
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'dateBucketKey': (series ? '$_id.date': '$none'),
        'totalCount': '$totalCount',
        'digitalCards': '$digitalCards',
        'keyTagCards': '$keyTagCards',
        'RegularCards': '$RegularCards',
        'keyTagAndRegularCards': '$keyTagAndRegularCards'
      }
    }
  }, {
    '$sort': {
      'dateBucketKey': -1
    }
  }
];

module.exports = {
  CardsSummaryByMemberTypeAggregation,
  ProcessingStatusStatsAggregation,
  CardActivationReportAggregation
};
