const ageRangeSwitch = {
    $switch: {
        branches: [
            {
                case: {
                    $lt: ['$age', 19]
                },
                then: '0-18'
            },
            {
                case: {
                    $and: [
                        {
                            $gte: ['$age', 19]
                        },
                        {
                            $lt: ['$age', 25]
                        }
                    ]
                },
                then: '18-24'
            },
            {
                case: {
                    $and: [
                        {
                            $gte: ['$age', 25]
                        },
                        {
                            $lt: ['$age', 35]
                        }
                    ]
                },
                then: '25-34'
            },
            {
                case: {
                    $and: [
                        {
                            $gte: ['$age', 35]
                        },
                        {
                            $lt: ['$age', 45]
                        }
                    ]
                },
                then: '35-44'
            },
            {
                case: {
                    $and: [
                        {
                            $gte: ['$age', 45]
                        },
                        {
                            $lt: ['$age', 55]
                        }
                    ]
                },
                then: '45-54'
            },
            {
                case: {
                    $and: [
                        {
                            $gte: ['$age', 55]
                        },
                        {
                            $lt: ['$age', 65]
                        }
                    ]
                },
                then: '55-64'
            },
            {
                case: {
                    $gte: ['$age', 65]
                },
                then: '65+'
            }
        ],
        default: 'Invalid age range.'
    }
};

const includeMember = [
    {
        $lookup: {
            from: 'members',
            localField: 'memberId',
            foreignField: '_id',
            as: 'member'
        }
    },
    {
        $unwind: {
            path: '$member',
            includeArrayIndex: '0',
            preserveNullAndEmptyArrays: true
        }
    }
];

const dateBucketCalculationsProjection = (dateAttrib) => {
    return {
        registeredOnDate: { $dateToString: { format: '%Y-%m-%d', date: dateAttrib } },
        registeredOnMonth: { $dateToString: { format: '%Y-%m', date: dateAttrib } },
        registeredOnYear: { $year: dateAttrib },
        weekOfTheYear: { $concat: [{ $toString: { $year: dateAttrib } }, '-', { $toString: { $week: dateAttrib } }] },
        quarter: {
            $cond: [
                { $lte: [{ $month: dateAttrib }, 3] },
                '1',
                {
                    $cond: [
                        { $lte: [{ $month: dateAttrib }, 6] },
                        '2',
                        {
                            $cond: [{ $lte: [{ $month: dateAttrib }, 9] }, '3', '4']
                        }
                    ]
                }
            ]
        }
    };
};

module.exports = {
    ageRangeSwitch,
    includeMember,
    dateBucketCalculationsProjection
};
