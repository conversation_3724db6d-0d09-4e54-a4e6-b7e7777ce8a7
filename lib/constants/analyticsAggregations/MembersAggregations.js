'use strict';
const mongoose = require('mongoose');
const { ageRangeSwitch, dateBucketCalculationsProjection } = require('./CommonAggregations');

const ageRangeCalculations = [{
    '$match': {
      'birthDate': { '$ne': null }
    }
  },{
    '$addFields': {
      'age': {
        '$let':{
          'vars':{
            'diff': {
              '$subtract': [ "$$NOW", "$birthDate" ]
            }
          },
          'in': {
            '$divide': ["$$diff", (365 * 24*60*60*1000)]
          }
        }
      }
    }
  },
  {
    '$project': {
      'merchantLocationId': 1,
      ...dateBucketCalculationsProjection('$registeredOn'),
      // 'date': {
      //   '$dateToString': {
      //     'format': '%Y-%m-%d',
      //     'date': '$registeredOn'
      //   }
      // },
      'registeredOn': 1,
      'registerMethod': 1,
      'gender': 1,
      'age': 1,
      'organizationId': 1,
      'regionId': 1,
      'ageRange': ageRangeSwitch
    }
  }
];

const includeBranchNames = [{
  '$lookup': {
    'from': 'merchant_locations',
    'localField': 'merchantLocationId',
    'foreignField': '_id',
    'as': 'branch'
  }},
  {
    '$unwind': {
      'path': '$branch',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }];

const MemberAffinityGroupAggregation = (organizationId, groupBy, filters, totalRecords) => [
  {
    '$project': {
      'affinityGroupId': 1,
      'organizationId': 1,
      'regionId': 1,
      'tier': 1,
      ...dateBucketCalculationsProjection('$registeredOn'),
      // 'date': {
      //   '$dateToString': {
      //     'format': '%Y-%m-%d',
      //     'date': '$registeredOn'
      //   }
      // }
    }
  }, {
    '$addFields': {
      'quarterOfTheYear': { "$concat": [ { "$toString": "$registeredOnYear" }, "-", "$quarter" ] }
    },
  }, {
    '$match': {
      'organizationId': organizationId,
      ...filters
    }
  }, {
    '$lookup': {
      'from': 'affinity_groups',
      'localField': 'affinityGroupId',
      'foreignField': '_id',
      'as': 'affinityGroup'
    }
  }, {
    '$unwind': {
      'path': '$affinityGroup',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$group': {
      '_id': groupBy,
      'count': {
        '$sum': 1
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'affinityGroup': '$_id.affinityGroup',
        'dateBucketKey': '$_id.dateBucketKey',
        'tier': '$_id.tier',
        'count': '$count',
        'percentage': { '$multiply': [{ '$divide': [100, totalRecords] }, '$count'] }
      }
    }
  }
];

const MemberAffinityGroupTierAggregation = (organizationId, groupBy, filters, totalRecords) => [
  {
    '$project': {
      'affinityGroupId': 1,
      'organizationId': 1,
      'regionId': 1,
      'tier': 1,
      ...dateBucketCalculationsProjection('$registeredOn'),
      // 'date': {
      //   '$dateToString': {
      //     'format': '%Y-%m-%d',
      //     'date': '$registeredOn'
      //   }
      // }
    }
  }, {
    '$addFields': {
      'quarterOfTheYear': { "$concat": [ { "$toString": "$registeredOnYear" }, "-", "$quarter" ] }
    },
  }, {
    '$match': {
      'organizationId': organizationId,
      ...filters
    }
  }, {
    '$lookup': {
      'from': 'affinity_groups',
      'localField': 'affinityGroupId',
      'foreignField': '_id',
      'as': 'affinityGroup'
    }
  }, {
    '$unwind': {
      'path': '$affinityGroup',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$lookup': {
      'from': 'tiers',
      'localField': 'tier.tierId',
      'foreignField': '_id',
      'as': 'tier'
    }
  }, {
    '$unwind': {
      'path': '$tier',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$group': {
      '_id': groupBy,
      'count': {
        '$sum': 1
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'affinityGroup': '$_id.affinityGroup',
        'dateBucketKey': '$_id.dateBucketKey',
        'tier': '$_id.tier',
        'count': '$count',
        'percentage': { '$multiply': [{ '$divide': [100, totalRecords] }, '$count'] }
      }
    }
  }
];

const MemberTiersAggregation = (organizationId, groupBy, filters) => [
  {
    '$project': {
      'organizationId': 1,
      'regionId': 1,
      'tier': 1,
      'registeredOn': 1,
      ...dateBucketCalculationsProjection('$registeredOn'),
      // 'date': {
      //   '$dateToString': {
      //     'format': '%Y-%m-%d',
      //     'date': '$registeredOn'
      //   }
      // }
    }
  }, {
    '$addFields': {
      'quarterOfTheYear': { "$concat": [ { "$toString": "$registeredOnYear" }, "-", "$quarter" ] }
    },
  }, {
    '$match': {
      'organizationId': organizationId,
      ...filters
    }
  }, {
    '$lookup': {
      'from': 'tiers',
      'localField': 'tier.tierId',
      'foreignField': '_id',
      'as': 'tier'
    }
  }, {
    '$unwind': {
      'path': '$tier',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$group': {
      '_id': groupBy,
      'count': {
        '$sum': 1
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'tier': '$_id.tier',
        'dateBucketKey': '$_id.dateBucketKey',
        'count': '$count'
      }
    }
  }
];

const MemberTopShoppersAggregation = (organizationId, filters) => [
  {
    '$match': {
      'organizationId': organizationId,
      ...filters
    }
  }, {
    '$group': {
      '_id': '$memberId',
      'count': {
        '$sum': 1
      }
    }
  }, {
    '$sort': {
      'count': -1
    }
  }, {
    '$lookup': {
      'from': 'members',
      'localField': '_id',
      'foreignField': '_id',
      'as': 'member'
    }
  }, {
    '$unwind': {
      'path': '$member',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        'transactions': '$count',
        'memberId': '$member._id',
        'first_name': '$member.firstName',
        'lastName': '$member.lastName'
      }
    }
  }
];


const MemberSubTransactionWisePointsAggregation = (organizationId, filters, transactionType, skip, limit) => {
  const pipeLine = [
    {
      '$match': {
        'organizationId': organizationId,
        ...filters
      }
    }, {
      '$group': {
        '_id': '$subType',
        'transactionCount': {
          '$sum': 1
        },
        'totalPoints': {
          '$sum': '$redeemablePoints'
        }
      }
    }, {
      '$lookup': {
        'from': 'sub_transaction_types',
        'localField': '_id',
        'foreignField': '_id',
        'as': 'subType'
      }
    }, {
      '$unwind': {
        'path': '$subType',
        'includeArrayIndex': '0',
        'preserveNullAndEmptyArrays': true
      }
    }, {
      '$replaceRoot': {
        'newRoot': {
          'subTypeId': '$_id',
          'name': '$subType.name',
          'transactionType': '$subType.transactionType',
          'isVisibleToUser': '$subType.isVisibleToUser',
          'totalTransactions': '$transactionCount',
          'totalPoints': '$totalPoints'
        }
      }
    }
  ];

  if (transactionType) {
    pipeLine.push({
      '$match': {
        'transactionType': transactionType
      }
    });
  }

  pipeLine.push({
    '$sort': {
      'totalPoints': -1
    }
  });

  pipeLine.push({
    '$facet': {
      items: [{ $skip: skip }, { $limit: limit }],
      total: [
        {
          $count: 'count'
        }
      ]
    }
  });

  return pipeLine;
}

const MemberInsightAggregation = (organizationId, regionId, memberId) => [
  {
    '$match': {
      'organizationId': mongoose.Types.ObjectId(organizationId),
      'regionId': mongoose.Types.ObjectId(regionId),
      'memberId': mongoose.Types.ObjectId(memberId),
      'type': 'COLLECTION'
    }
  }, {
    '$group': {
      '_id': null,
      'points': {
        '$sum': '$signedRedeemablePoints'
      },
      'dollarAmount': {
        '$sum': '$invoiceData.billAmount'
      },
      'transactionCount': {
        '$sum': 1
      }
    }
  }
];

const MemberYearsAggregation = (organizationId, regionId, memberId) => [
  {
    '$match': {
      'organizationId': mongoose.Types.ObjectId(organizationId),
      'regionId': mongoose.Types.ObjectId(regionId),
      '_id': mongoose.Types.ObjectId(memberId)
    }
  }, {
    '$project': {
      'lastTransactionOn': 1,
      'registeredOn': 1
    }
  }, {
    '$addFields': {
      'numberOfYears': {
        '$let': {
          'vars': {
            'diff': {
              '$subtract': [
                '$lastTransactionOn', '$registeredOn'
              ]
            }
          },
          'in': {
            '$divide': [
              '$$diff', 31536000000
            ]
          }
        }
      }
    }
  }
];

module.exports = {
  ageRangeCalculations,
  includeBranchNames,
  MemberAffinityGroupAggregation,
  MemberAffinityGroupTierAggregation,
  MemberTiersAggregation,
  MemberTopShoppersAggregation,
  MemberInsightAggregation,
  MemberYearsAggregation,
  MemberSubTransactionWisePointsAggregation
};
