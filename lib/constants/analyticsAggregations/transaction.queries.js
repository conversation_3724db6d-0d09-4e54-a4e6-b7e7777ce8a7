const { MERCHANTS } = require('../AnalyticsEnums');
const { TYPE } = require('../../db/models/enums/transaction.enums');

const ageRangeCase = `CASE
                                  WHEN ("memberAge" < 18) THEN '0-18'
                                WHEN ("memberAge" >= 18 AND "memberAge" <= 24) THEN '18-24'
                                WHEN ("memberAge" >= 25 AND "memberAge" <= 34) THEN '25-34'
                                WHEN ("memberAge" >= 35 AND "memberAge" <= 44) THEN '35-44'
                                WHEN ("memberAge" >= 45 AND "memberAge" <= 54) THEN '45-54'
                                WHEN ("memberAge" >= 55 AND "memberAge" <= 64) THEN '55-64'
                                WHEN ("memberAge" >= 65) THEN '65+'
                                  ELSE 'invalid'
                                 END AS age`;

const PointsTransferReportQuerySelection = (groupBy, dateBucket, series, type) => {
    if (type === TYPE.ADJUSTMENT) {
        let queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''} COUNT("id") as count, SUM("redeemablePoints") filter (where "subTypeOperation" = 'ADD') as "addedPoints", SUM("redeemablePoints") filter (where "subTypeOperation" = 'SUBTRACT') as "subtractedPoints" FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
        let attachDocs;
        if (groupBy) {
            if (groupBy === MERCHANTS.AGE) {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"age" as "ageGroup", COUNT("id") as count, SUM("redeemablePoints") filter (where "subTypeOperation" = 'ADD') as "addedPoints", SUM("redeemablePoints") filter (where "subTypeOperation" = 'SUBTRACT') as "subtractedPoints"
                               FROM (SELECT
                                 *,
                                 ${ageRangeCase}
                                FROM transactions) as tr
                               WHERE "organizationId" = ? AND "type" = ?`;
            } else if (groupBy === MERCHANTS.MEMBER_TYPE) {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"memberType", COUNT("id") as count, SUM("redeemablePoints") filter (where "subTypeOperation" = 'ADD') as "addedPoints", SUM("redeemablePoints") filter (where "subTypeOperation" = 'SUBTRACT') as "subtractedPoints" FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
            } else if (groupBy === MERCHANTS.GENDER) {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"gender", COUNT("id") as count, SUM("redeemablePoints") filter (where "subTypeOperation" = 'ADD') as "addedPoints", SUM("redeemablePoints") filter (where "subTypeOperation" = 'SUBTRACT') as "subtractedPoints" FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
            } else {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"${MERCHANTS[groupBy].groupBy}", COUNT("id") as count, SUM("redeemablePoints") filter (where "subTypeOperation" = 'ADD') as "addedPoints", SUM("redeemablePoints") filter (where "subTypeOperation" = 'SUBTRACT') as "subtractedPoints" FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
                attachDocs = groupBy;
            }

        }

        return {
            queryString, attachDocs
        }
    } else {
        let queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''} COUNT("id") as count, SUM("redeemablePoints") FROM transactions WHERE "organizationId" = ? AND "type" = ?`;
        let attachDocs;
        if (groupBy) {
            if (groupBy === MERCHANTS.AGE) {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"age" as "ageGroup", COUNT("id") as count, SUM("redeemablePoints") FROM (SELECT
                                 *,
                                 ${ageRangeCase}
                                FROM transactions) as tr
                               WHERE "organizationId" = ? AND "type" = ?`;
            } else if (groupBy === MERCHANTS.MEMBER_TYPE) {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"memberType", COUNT("id") as count, SUM("redeemablePoints") FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
            } else if (groupBy === MERCHANTS.GENDER) {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"gender", COUNT("id") as count, SUM("redeemablePoints") FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
            } else {
                queryString = `SELECT ${series ? 'date_trunc(\''+ dateBucket +'\', "transactionOn") as dateBucketKey, ': ''}"${MERCHANTS[groupBy].groupBy}", COUNT("id") as count, SUM("redeemablePoints") FROM transactions
                               WHERE "organizationId" = ? AND "type" = ?`;
                attachDocs = groupBy;
            }

        }

        return {
            queryString, attachDocs
        }
    }

}

module.exports = {
    PointsTransferReportQuerySelection
};