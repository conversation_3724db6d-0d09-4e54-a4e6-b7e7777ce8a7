[{"_id": "62de6e75a9650000cf0005b2", "description": "Expiry points monthly live", "metadata": "{ }", "name": "Expiry points monthly live", "queueName": "expiry_points_monthly_live_jobs_queue", "status": "ENABLED"}, {"_id": "6303266be3320000b4007204", "description": "Expiry points monthly simulation", "metadata": "{ \"properties\" : { \"asAtDate\" : { \"type\" : \"string\", \"format\" : \"date\", \"title\" : \"As At Date\" } }, \"required\" : [ \"asAtDate\" ], \"type\" : \"object\" }", "name": "Expiry points monthly simulation", "queueName": "expiry_points_monthly_simulation_jobs_queue", "status": "ENABLED"}, {"_id": "6305da5c8b0200007d002664", "description": "Rolling point balance summary", "metadata": "{ \"properties\" : { \"fromDate\" : { \"type\" : \"string\", \"title\" : \"Start Date\", \"format\" : \"date\" }, \"toDate\" : { \"type\" : \"string\", \"title\" : \"End Date\", \"format\" : \"date\" } }, \"required\" : [ \"fromDate\", \"toDate\" ], \"type\" : \"object\" }", "name": "Rolling point balance summary", "queueName": "rolling_points_balance_summary_jobs_queue", "status": "ENABLED"}, {"_id": "6308c41f0804000077003666", "description": "Movements summary", "metadata": "{ \"properties\" : { \"fromDate\" : { \"type\" : \"string\", \"title\" : \"Start Date\", \"format\" : \"date\" }, \"merchants\" : { \"type\" : [ \"string\", \"null\" ], \"title\" : \"Merchant\" }, \"subTransactionTypes\" : { \"type\" : [ \"string\", \"null\" ], \"title\" : \"Sub Transaction Type\" }, \"toDate\" : { \"type\" : \"string\", \"title\" : \"End Date\", \"format\" : \"date\" }, \"transactionTypes\" : { \"type\" : [ \"string\", \"null\" ], \"title\" : \"Transaction Type\" } }, \"required\" : [ \"merchants\", \"transactionTypes\", \"subTransactionTypes\", \"fromDate\", \"toDate\" ], \"type\" : \"object\" }", "name": "Movements summary", "queueName": "mls_points_movement_summary_jobs_queue", "status": "ENABLED"}, {"_id": "630caee40a1000003f002097", "description": "Rolling point balance detailed", "metadata": "{ \"properties\" : { \"fromDate\" : { \"type\" : \"string\", \"title\" : \"Start Date\", \"format\" : \"date\" }, \"toDate\" : { \"type\" : \"string\", \"title\" : \"End Date\", \"format\" : \"date\" } }, \"required\" : [ \"fromDate\", \"toDate\" ], \"type\" : \"object\" }", "name": "Rolling point balance detailed", "queueName": "rolling_points_balance_detailed_jobs_queue", "status": "ENABLED"}, {"_id": "63cf8675fe73f08d3e0c80b2", "description": "Members list export", "metadata": "{ \"properties\" : { \"aggregation\" : { \"type\" : \"string\", \"title\" : \"Aggregation\" } }, \"required\" : [ \"aggregation\" ], \"type\" : \"object\" }", "name": "Members list export", "queueName": "members_export_jobs_queue", "status": "ENABLED"}]