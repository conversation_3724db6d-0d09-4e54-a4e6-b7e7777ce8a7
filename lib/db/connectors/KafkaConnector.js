const { Kafka } = require('kafkajs');
const secretConfig = require('./../../../config');

const kafka = new Kafka({
    brokers: secretConfig.KAFKA_BROKERS,
    clientId: secretConfig.KAFKA_CLIENT_ID,
    ssl: secretConfig.KAFKA_SSL,
    ...(secretConfig.KAFKA_SASL_USERNAME
        ? {
              sasl: {
                  mechanism: secretConfig.KAFKA_SASL_MECHANISM,
                  username: secretConfig.KAFKA_SASL_USERNAME,
                  password: secretConfig.KAFKA_SASL_PASSWORD
              }
          }
        : {}),
    connectionTimeout: secretConfig.KAFKA_CONNECTION_TIMEOUT,
    logLevel: secretConfig.KAFKA_LOG_LEVEL
});

let producer;
class KafkaConnector {
    static getConsumer(consumerGroup) {
        return kafka.consumer({
            groupId: consumerGroup
        });
    }

    static async initializeProducer() {
        producer = await kafka.producer();
        await producer.connect();
    }

    static async getProducer() {
        if (!producer) {
            await this.initializeProducer();
        }
        return producer;
    }

}

module.exports = KafkaConnector;