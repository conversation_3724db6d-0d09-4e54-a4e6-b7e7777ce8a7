const redis = require('redis');
const { promisify } = require('util');
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const redisClientOptions = {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT
};
const client = redis.createClient(redisClientOptions);
if (process.env.REDIS_PASSWORD) {
    client.auth(process.env.REDIS_PASSWORD);
}

const multiTransactions = client.multi();

const getAsync = promisify(client.get).bind(client);
const setAsync = promisify(client.set).bind(client);
const delAsync = promisify(client.del).bind(client);
const mGetAsync = promisify(client.mget).bind(client);
const mSetAsync = promisify(client.mset).bind(client);
const expireAsync = promisify(client.expire).bind(client);
const scanAsync = promisify(client.scan).bind(client);
const multiExecAsync = promisify(multiTransactions.exec).bind(multiTransactions);

class RedisConnector {
    static getConfig() {
        return process.env.REDIS_PASSWORD
            ? Object.assign(redisClientOptions, { password: process.env.REDIS_PASSWORD })
            : redisClientOptions;
    }
    static getCommands() {
        return { getAsync, setAsync, mGetAsync, mSetAsync, expireAsync, multiTransactions, multiExecAsync };
    }

    static async closeConnection() {
        return new Promise((resolve, reject) => {
            try {
                client.quit(() => {
                    resolve();
                });
            } catch (err) {
                reject(err);
            }
        });
    }

    static async scanAndDelete(pattern) {
        try {
            let cursor = '0';
            do {
                const reply = await scanAsync(cursor, 'MATCH', pattern, 'COUNT', '1000');
                cursor = reply[0];

                if (reply[1]?.length > 0) await delAsync(reply[1]);
            } while (cursor !== '0');
        } catch (e) {
            log.error(e);
        }
    }
}

module.exports = RedisConnector;
