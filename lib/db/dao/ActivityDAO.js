/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/28/16.
 */
'use strict';
const lodash = require('lodash');
const MongoDBConnector = require('@shoutout-labs/mongodb-connector'); //require('./../connectors/mongodb_connector');
const Utils = require('./../../Utils');
const config = require('./../../config');
const secretConfig = require('./../../../config');
const CORE_SERVICE_DB = secretConfig.MONGO_DB_CORE_SERVICE;
const ACTIVITIES_TABLE = 'activities';
const logger = require('./../../logger');
const log = logger(config.logger);

class ActivityDAO {
    static async updateActivities(ownerId, contactId, update) {
        try {
            let db = await MongoDBConnector.getDbConnection(CORE_SERVICE_DB, ownerId);
            let queryFilter = {
                ownerId: ownerId,
                contactId: contactId
            };
            let collection = db.collection(ACTIVITIES_TABLE);
            return collection.updateMany(queryFilter, update);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getActivities(ownerId, fromDate, toDate, contactId, eventId, limit, skip, userId, moduleId, templateIds) {
        try {
            let queryFilter = {
                ownerId: ownerId
            };
            if (contactId) {
                queryFilter['contactId'] = { $eq: contactId };
            } else if (userId) {
                queryFilter['contactId'] = { $eq: Utils.generateId(userId, ownerId) };
            }
            if (eventId) {
                queryFilter['eventId'] = { $eq: eventId };
            }
            if (moduleId) {
                queryFilter['moduleId'] = { $eq: moduleId };
            }
            if (templateIds) {
                queryFilter['templateId'] = { $in: templateIds }
            }
            if (fromDate && toDate) {
                let tDx = new Date(toDate);
                tDx.setHours(23, 59);
                queryFilter['createdOn'] = {
                    $gte: new Date(fromDate),
                    $lte: tDx
                };
            } else if (fromDate) {
                queryFilter['createdOn'] = {
                    $gte: new Date(fromDate)
                };
            } else if (toDate) {
                let tDy = new Date(toDate);
                tDy.setHours(23, 59);
                queryFilter['createdOn'] = {
                    $lte: tDy
                };
            }

            let db = await MongoDBConnector.getDbConnection(CORE_SERVICE_DB, ownerId);

            let collection = db.collection('activities');
            let cursor = collection.find(queryFilter).project({ _processing: 0 });
            let total = await cursor.count();
            if (skip) {
                cursor.skip(skip);
            }
            if (limit) {
                cursor.limit(limit);
            }
            let items = await cursor.toArray();
            return Promise.resolve({
                total,
                items: items
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async insertActivitiesList(activitiesList) {
        try {
            let groupedActivities = lodash.groupBy(activitiesList, (activity) => {
                return activity.ownerId;
            });
            let promiseArray = [];
            lodash.forEach(groupedActivities, (activities, ownerId) => {
                promiseArray.push(insertActivitiesListByOwnerId(activities, ownerId));
            });
            return Promise.all(promiseArray);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

async function insertActivitiesListByOwnerId(activities, ownerId) {
    try {
        let db = await MongoDBConnector.getDbConnection(CORE_SERVICE_DB, ownerId);
        let collection = db.collection(ACTIVITIES_TABLE);
        return collection.insertMany(activities, { ordered: false });
    } catch (e) {
        log.error(e);
        return Promise.reject(e);
    }
}

module.exports = ActivityDAO;
