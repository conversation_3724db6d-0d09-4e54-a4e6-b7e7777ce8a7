'use strict';
const config = require('../../config');
const mongoose = require('mongoose');
const logger = require('../../logger');
const log = logger(config.logger);
const AffinityGroupMemberImportLog = require('../models/affinity.group.member.import.log.model');
const CustomHttpError = require('../../CustomHttpError');

class AffinityGroupMemberImportLogsDAO {

    static async createImportLog(validatedObj, organizationId) {
        try {
            const AffinityGroupMemberImportLogModel = new AffinityGroupMemberImportLog({
                ...validatedObj,
                organizationId
            });
            const createdObj = await AffinityGroupMemberImportLogModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getImportLogs(organizationId, validatedObj, stream = false) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(validatedObj.regionId)
            };

            if (validatedObj.importJobId) {
                filter['affinityGroupMemberImportJobId'] = mongoose.Types.ObjectId(validatedObj.importJobId);
            }

            if (validatedObj.affinityGroupId) {
                filter['affinityGroupId'] = mongoose.Types.ObjectId(validatedObj.affinityGroupId);
            }

            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }

            if (validatedObj.createdDateFrom && validatedObj.createdDateTo) {
                let tDx = new Date(validatedObj.createdDateTo);
                tDx.setHours(23, 59);
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.createdDateFrom),
                    $lte: tDx
                };
            } else if (validatedObj.createdDateFrom) {
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.createdDateFrom)
                };
            } else if (validatedObj.createdDateTo) {
                let tDy = new Date(validatedObj.createdDateTo);
                tDy.setHours(23, 59);
                filter['createdOn'] = {
                    $lte: tDy
                };
            }

            if (validatedObj.regionIds) {
                filter['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantIds) {
                filter['merchantId'] = { $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }

            const total = await AffinityGroupMemberImportLog.countDocuments(filter);

            if (stream) {
                return AffinityGroupMemberImportLog.find(filter).sort({createdOn: "desc"}).cursor();
            }

            const query = AffinityGroupMemberImportLog.find(filter).sort({createdOn: "desc"}).lean();

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit);

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting affinity group member import logs', '500'));
        }
    }

}

module.exports = AffinityGroupMemberImportLogsDAO;
