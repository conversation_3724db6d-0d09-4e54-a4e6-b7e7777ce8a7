'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const Attribute = require('../models/attribute.model');
const log = logger(config.logger);
const mongoose = require('mongoose');

class AttributesDAO {

    static async getAttributes(organizationId, validatedObj) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(validatedObj.regionId)
            };

            const total = await Attribute.countDocuments(filter);
            const query = Attribute.find(filter).sort({createdOn: "desc"});

            if (validatedObj.skip) {
                query.skip(validatedObj.skip);
            }
            if (validatedObj.limit) {
                query.limit(validatedObj.limit);
            }

            const items = await query.lean();

            return {
                total,
                items,
            };

            // return Promise.resolve(result || {attributes: {}, tags: []});

        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getAttributesByRegion(organizationId, regionId) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(regionId)
            };

            const result = await Attribute.findOne(filter).lean();

            return Promise.resolve(result || {attributes: {}, tags: []});

        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = AttributesDAO;
