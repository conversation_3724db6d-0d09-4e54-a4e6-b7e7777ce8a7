const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const CardPrintJobDAO = require('./CardPrintJobDAO');
const MongooseConnector = require('./../connectors/MongooseConnector');

const organizationId = '6128e3537ed841e246e2e394';
const callerId = '613d08491df0e38df616f411';
const regionId = '61446196734bfdda56d43158';
describe('CardBatchJobDAO unit tests', () => {
    let cardBatchJobId;

    test.skip('create card batch job', async () => {
        const dataset = {
            jobType: 'DIGITAL_CARD',
            regionId: regionId,
            quantity: 10
        };
        const createdCardBatchJob = await CardPrintJobDAO.createCardBatchJob(
            dataset,
            organizationId,
            callerId,
            10000000000000010,
            10000000000000020,
            50
        );
        cardBatchJobId = createdCardBatchJob.id;
        expect(typeof createdCardBatchJob).toEqual('object');
    });

    test('get card batch job', async () => {
        const cardConfiguration = await CardPrintJobDAO.getCardBatchJob(cardBatchJobId, organizationId);
        expect(typeof cardConfiguration).toEqual('object');
    });
});
