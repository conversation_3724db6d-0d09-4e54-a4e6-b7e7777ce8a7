const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const CardConfigurationsDAO = require('./CardConfigurationsDAO');
const CardConfiguration = require('./../models/card.configuration.model');

const mongoose = require('mongoose');

const organizationId = '6128e3537ed841e246e2e394';
const callerId = '613d08491df0e38df616f411';
const regionId = '61446196734bfdda56d43158';
describe('CardConfigurationsDAO unit tests', () => {
    let cardConfigurationId;

    afterAll(async () => {
        // await CardConfiguration.deleteMany({rootUserIdpId})
        await CardConfiguration.deleteOne({ _id: cardConfigurationId });
    });

    test.skip('create card configuration', async () => {
        const cardConfigurationModel = new CardConfiguration({
            organizationId,
            createdBy: mongoose.Types.ObjectId(callerId),
            type: 'DIGITAL',
            regionId: regionId,
            rangeFrom: 6000000000000000,
            rangeTo: 6999999999999999
        });
        const createdConfiguration = await CardConfigurationsDAO.createCardConfiguration(cardConfigurationModel);
        cardConfigurationId = createdConfiguration.id;
        expect(typeof createdConfiguration).toEqual('object');
    });

    test.skip('get card configuration', async () => {
        const cardConfiguration = await CardConfigurationsDAO.getCardConfiguration(organizationId, regionId, 'DIGITAL');
        expect(typeof cardConfiguration).toEqual('object');
    });
});
