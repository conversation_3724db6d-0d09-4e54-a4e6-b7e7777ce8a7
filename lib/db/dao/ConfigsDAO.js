/**
 * Created by gane<PERSON> on 14/01/20.
 */
'use strict';
const MongoDBConnector = require('@shoutout-labs/mongodb-connector');
const config = require('./../../config');
const logger = require('./../../logger');
const log = logger(config.logger);
const secretConfig = require('./../../../config');
const MONGODB_DATABASE = secretConfig.MONGO_DB_LOYALTY_SERVICE;
const ObjectId = require('mongodb').ObjectId;
const COLLECTION = 'configs';
const CustomHttpError = require('./../../CustomHttpError');
const CacheHandler = require('./../../handlers/CacheHandler');
const Utils = require('./../../Utils');

class ConfigsDAO {

    static async createConfig(validatedConfigsObj, ownerId) {
        try {
            let db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE, ownerId);
            let collection = db.collection(COLLECTION);
            let result = await collection.insertOne(validatedConfigsObj);
            if (result && result.insertedCount === 1) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(new CustomHttpError('config could not save', '500'));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async deleteConfig(integrationIdDelete, ownerId) {
        try {
            let db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE, ownerId);
            let collection = db.collection(COLLECTION);
            let result = await collection.findOneAndDelete({ _id: ObjectId(integrationIdDelete), ownerId: ownerId });
            if (result && result.value) {
                return Promise.resolve(result.value);
            } else {
                return Promise.reject(new CustomHttpError('config could not delete', '500'));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateConfig(integrationId, ownerId, configObj) {
        try {
            let db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE, ownerId);
            let collection = db.collection(COLLECTION);
            let result = await collection.findOneAndUpdate({
                _id: ObjectId(integrationId),
                ownerId: ownerId
            }, configObj, {
                upsert: false,
                new: true,
                returnOriginal: false
            });
            if (result) {
                return Promise.resolve(result.value);
            } else {
                return Promise.reject(new CustomHttpError('config could not update', '500'));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateConfigWithNewId(ownerId) {
        try {
            let db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE, ownerId);
            let collection = db.collection(COLLECTION);
            let result = await collection.findOneAndUpdate({ ownerId: ownerId }, { $inc: { 'registrationForm.lastUserId': 1 } }, { returnOriginal: false });
            if (result && result.value) {
                return Promise.resolve(result.value);
            } else {
                return Promise.reject(new CustomHttpError('config could not update', '500'));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with generating loyalty id', '500'));
        }
    }

    static async getConfig(ownerId) {
        try {
            const key = `loyalty_configs_${ownerId}`;
            const result = await CacheHandler.get(key);
            if (!result) {
                const db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE, ownerId);
                const collection = db.collection(COLLECTION);
                const query = {
                    ownerId: ownerId
                };
                const configs = await collection.findOne(query);
                if (configs) {
                    await CacheHandler.set(key, configs);
                    return Promise.resolve(configs);
                } else {
                    return Promise.reject(new CustomHttpError('config not found', '404'));
                }
            }
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getConfigByOriginURL(originURL) {
        try {
            const urlHash = Utils.getHash(originURL);
            const urlKey = `loyalty_configs_${urlHash}`;
            const ownerIdCache = await CacheHandler.get(urlKey);
            if (!ownerIdCache) {
                const db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE);
                const collection = db.collection(COLLECTION);
                const query = {
                    "portal.originURLs": originURL
                };
                const configs = await collection.findOne(query);
                if (configs) {
                    const urlConfig = { ownerId: configs.ownerId };
                    await CacheHandler.set(urlKey, urlConfig);
                    const ownerIdKey = `loyalty_configs_${configs.ownerId}`;
                    await CacheHandler.set(ownerIdKey, configs);
                    return Promise.resolve(configs);
                } else {
                    return Promise.reject(new CustomHttpError('config not found', '404'));
                }
            } else {
                const configObj = await ConfigsDAO.getConfig(ownerIdCache.ownerId);
                return Promise.resolve(configObj);
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateConfigWithNewPrintJobId(ownerId) {
        try {
            const db = await MongoDBConnector.getDbConnection(MONGODB_DATABASE, ownerId);
            const collection = db.collection(COLLECTION);
            const result = await collection.findOneAndUpdate({ ownerId: ownerId }, { $inc: { 'cardManagement.lastPrintJobId': 1 } }, { returnOriginal: false });
            if (result && result.value) {
                return Promise.resolve(result.value);
            } else {
                return Promise.reject(new CustomHttpError('config could not update', '500'));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with generating print job id', '500'));
        }
    }
}

module.exports = ConfigsDAO;


