'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const Job = require('../models/job.model');
const { STATUS } = require('../models/enums/job.enums');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class JobsDAO {

    static async createJob(validatedObj, organizationId, callerId) {
        try {
            const JobModel = new Job({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await JobModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getJobs(organizationId, validatedObj) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(validatedObj.regionId),
                status: { $ne: STATUS.ARCHIVED }
            }

            if (validatedObj.jobTypeId) {
                filter['jobTypeId'] = mongoose.Types.ObjectId(validatedObj.jobTypeId);
            }

            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }

            if (validatedObj.processType) {
                filter['processType'] = validatedObj.processType;
            }

            if (validatedObj.scheduleType) {
                filter['scheduleType'] = validatedObj.scheduleType;
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }

            if (validatedObj.regionIds) {
                filter['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }

            const total = await Job.countDocuments(filter);
            const query = Job.find(filter).sort({createdOn: "desc"});

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit).lean();

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting jobs', '500'));
        }
    }

    static async deleteJob(id, organizationId, callerId) {
        try {
            const result = await Job.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      status: STATUS.ARCHIVED,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateJob(dataObj, id, organizationId, callerId) {
        try {
            const result = await Job.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      ...dataObj,
                      updatedBy: callerId ? mongoose.Types.ObjectId(callerId) : null
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getJobById(id, organizationId) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: STATUS.ARCHIVED },
            };
            const result = await Job.findOne(filter).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getJobsByFilter(organizationId, filter, sort, fields, skip, limit) {
        filter['organizationId'] = mongoose.Types.ObjectId(organizationId);
        try {
            const query = Job.find(filter, fields).sort(sort);
            if (skip) {
                query.skip(skip);
            }
            if (limit) {
                query.limit(limit);
            }
            return Promise.resolve(query.lean());
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = JobsDAO;
