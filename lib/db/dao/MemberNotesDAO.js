'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const MemberNote = require('../models/member.note.model');
const mongoose = require('mongoose');

class MemberNotesDAO {

    static async getMemberNotes(organizationId, memberId, skip, limit) {
        try {
            const filter = { organizationId: mongoose.Types.ObjectId(organizationId), memberId };
            const total = await MemberNote.countDocuments(filter);
            const items = await MemberNote.find(filter).sort({createdOn:-1}).skip(skip).limit(limit).lean();
            return Promise.resolve({total, items});
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMemberNoteById(id, organizationId) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId)
            };
            const memberNote = await MemberNote.findOne(filter);
            return Promise.resolve(memberNote);
        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }

    static async createMemberNote(memberNote) {
        try {
            const memberNoteModel = new MemberNote(memberNote);
            log.debug(memberNoteModel);
            await memberNoteModel.save();
            return Promise.resolve(memberNoteModel.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateMemberNote(organizationId,id, updatedBy, noteContent) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId)
            };
            const updatedMemberNote = await MemberNote.findOneAndUpdate(filter, {
                $set: {
                    content: noteContent,
                    updatedBy
                }
            }, { upsert: false })
            return Promise.resolve(updatedMemberNote);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async deleteMemberNote(organizationId,id) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId)
            };
            await MemberNote.deleteOne(filter);
            return Promise.resolve();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = MemberNotesDAO;
