const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const Member = require('../models/member.model');
const CustomHttpError = require('./../../CustomHttpError');
const mongoose = require('mongoose');
const { Status } = require('./../models/enums/member.enums');
const { assign } = require('lodash');
const RedisConnector = require('./../connectors/RedisConnector');
const { roundOffToTwoDecimals } = require('../../utils/NumberUtils');
const { getAsync, setAsync } = RedisConnector.getCommands();

// TODO: Remove if not needed.
// const COLLECTION = 'contacts';
// const MONGO_DB = secretConfig.MONGO_DB_CORE_SERVICE;

class MembersDAO {
    static async createMember(validatedObj, organizationId, callerId, opts = {}) {
        try {
            const MemberModel = new Member({
                ...validatedObj,
                organizationId,
                createdBy: callerId ? mongoose.Types.ObjectId(callerId) : null
            });
            const createdObj = await MemberModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMemberById(id, organizationId, fields, populate, filters) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: Status.ARCHIVED },
                ...filters
            };
            const member = await Member.findOne(filter, fields).populate(populate).lean();

            if (member) {
                const pointsRoundedOfResult = Object.assign(member, {
                    points: roundOffToTwoDecimals(member?.points ? Number(member?.points) : 0),
                    tierPoints: roundOffToTwoDecimals(member?.tierPoints || 0)
                });
                return Promise.resolve(pointsRoundedOfResult);
            } else {
                return Promise.reject(new CustomHttpError('member not found', '404'));
            }
        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }

    static async getMemberByIdWithFilters(id, organizationId, fields, populate, filters) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filters
            };
            const member = await Member.findOne(filter, fields).populate(populate);
            if (member) {
                const pointsRoundedOfResult = Object.assign(member, {
                    points: roundOffToTwoDecimals(member?.points ? Number(member?.points) : 0),
                    tierPoints: roundOffToTwoDecimals(member?.tierPoints || 0)
                });
                return Promise.resolve(pointsRoundedOfResult);
            } else {
                return Promise.reject(new CustomHttpError('member not found', '404'));
            }
        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }

    static async getMemberByIdpId(idpId, organizationId) {
        try {
            const cacheKey = `sub_${idpId}`;
            const memberFromCache = await getAsync(cacheKey);
            if (memberFromCache) {
                return Promise.resolve(JSON.parse(memberFromCache));
            } else {
                const filter = {
                    organizationId: mongoose.Types.ObjectId(organizationId),
                    'portalMetadata.userId': idpId
                };
                const member = await Member.findOne(filter, {
                    _id: 1,
                    regionId: 1
                });
                if (member) {
                    await setAsync(cacheKey, JSON.stringify(member), 'EX', 60 * 60 * 3);

                    const pointsRoundedOfResult = Object.assign(member, {
                        points: roundOffToTwoDecimals(member?.points ? Number(member?.points) : 0),
                        tierPoints: roundOffToTwoDecimals(member?.tierPoints || 0)
                    });

                    return Promise.resolve(pointsRoundedOfResult);
                } else {
                    return Promise.reject(new CustomHttpError('member not found', '404'));
                }
            }
        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }

    static async addPoints(
        id,
        organizationId,
        points,
        tierPoints,
        purchasesCount,
        purchasesValue,
        opts = {},
        dataObj = {}
    ) {
        try {
            let result = await Member.findOneAndUpdate(
                { _id: mongoose.Types.ObjectId(id), organizationId: mongoose.Types.ObjectId(organizationId) },
                {
                    $inc: {
                        points: points,
                        tierPoints: tierPoints,
                        purchasesCount: purchasesCount,
                        purchasesValue: purchasesValue,
                        'pointStats.collectedPoints': points
                    },
                    $set: {
                        lastTransactionOn: new Date(),
                        ...dataObj
                    }
                },
                {
                    returnOriginal: false,
                    upsert: false,
                    new: true,
                    ...opts
                }
            ).lean();

            const pointsRoundedOfResult = Object.assign(result, {
                points: roundOffToTwoDecimals(result?.points ? Number(result?.points) : 0),
                tierPoints: roundOffToTwoDecimals(result?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with collecting points', '500'));
        }
    }

    static async getMembers(organizationId, filterPayload = {}, skip, limit, projection, sort, populate, stream) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filterPayload,
                ...(filterPayload?.status ? {
                    status: {
                        ...(typeof filterPayload.status === 'string' ? { $eq: filterPayload.status } : filterPayload.status),
                        $ne: Status.ARCHIVED,
                    },
                } : { status: { $ne: Status.ARCHIVED } }),
            };

            if (stream) {
                return Member.find(filter, projection).cursor(streamBatchSize ? { batchSize: streamBatchSize } : {});
            }

            const total = Member.countDocuments(filter).collation({ locale: 'en', strength: 2 });
            const query = Member.find(filter, projection).collation({ locale: 'en', strength: 2 }).sort(sort);

            if (skip) {
                query.skip(skip);
            }
            if (limit) {
                query.limit(limit);
            }

            const items = query
                .populate(populate)
                .populate([{
                    path: 'tierData',
                    select: 'name imageUrl benefits points status',
                }])
                .lean();

            const [totalResult, itemsResult] = await Promise.all([total, items]);

            // TODO: DISCUSSION: Do we need to add "allowedRedeemablePoints" attribute value here as well?
            return {
                total: totalResult,
                items: itemsResult.map((member) => ({
                    ...member,
                    points: roundOffToTwoDecimals(member?.points ? Number(member?.points) : 0),
                    tierPoints: roundOffToTwoDecimals(member?.tierPoints || 0),
                })),
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting members list', '500'));
        }
    }

    static async getMembersFromQueryFilter(organizationId, filterPayload = {}, count, skip, limit, projection, sort, stream) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filterPayload,
                ...(filterPayload?.status
                    ? {
                        status: {
                            ...(typeof filterPayload.status === 'string'
                                ? { $eq: filterPayload.status }
                                : filterPayload.status),
                            $ne: Status.ARCHIVED
                        }
                    }
                    : { status: { $ne: Status.ARCHIVED } })
            };

            if (count) {
                return Member.countDocuments(filter).collation({ locale: 'en', strength: 2 });
            }
            const query = Member.find(filter, projection).collation({ locale: 'en', strength: 2 });

            if (sort) {
                query.sort(sort);
            }

            if (stream) {
                return query.lean().cursor();
            }

            if (skip) {
                query.skip(skip);
            }
            if (limit) {
                query.limit(limit);
            }

            const itemsResult = await query.lean();

            return {
                items: itemsResult.map((member) => ({
                    ...member,
                    points: roundOffToTwoDecimals(member?.points ? Number(member?.points) : 0),
                    tierPoints: roundOffToTwoDecimals(member?.tierPoints || 0)
                }))
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting members list', '500'));
        }
    }

    static async getMemberByFilter(filterPayload, organizationId) {
        try {
            const filter = assign(filterPayload, {
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: Status.ARCHIVED }
            });
            return await Member.findOne(filter).collation({ locale: 'en', strength: 2 }).lean();
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error getting the member', '500'));
        }
    }

    static async runAggregation(aggregation, stream = false) {
        if (stream) {
            return Member.aggregate(aggregation).allowDiskUse(true).cursor();
        }
        return Member.aggregate(aggregation).collation({ locale: 'en', strength: 1 }).allowDiskUse(true);
    }

    static async getMemberByUserId(userId, organizationId) {
        try {
            return await Member.findOne({
                organizationId: mongoose.Types.ObjectId(organizationId),
                'portalMetadata.userId': userId
            })
                .collation({ locale: 'en', strength: 2 })
                .lean();
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error getting the member', '500'));
        }
    }

    static async getMember(organizationId, id, fields, filters = {}) {
        try {
            const filter = { organizationId: mongoose.Types.ObjectId(organizationId), ...filters };

            if (id) {
                filter['_id'] = mongoose.Types.ObjectId(id);
            }

            const foundMember = await Member.findOne(filter, fields).populate([
                {
                    path: 'affinityGroup.details',
                    select: 'name'
                },
                {
                    path: 'tierData',
                    select: 'name imageUrl benefits points status'
                }
            ]).lean();

            if (!foundMember) {
                throw new CustomHttpError('member not found', '404');
            }

            const pointsRoundedOfResult = Object.assign(foundMember, {
                points: roundOffToTwoDecimals(foundMember?.points ? Number(foundMember?.points) : 0),
                tierPoints: roundOffToTwoDecimals(foundMember?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMemberNoPopulate(organizationId, fields, filters = {}) {
        try {
            const filter = { organizationId: mongoose.Types.ObjectId(organizationId), ...filters };

            const foundMember = await Member.findOne(filter, fields).lean();

            if (!foundMember) {
                throw new CustomHttpError('member not found', '404');
            }

            const pointsRoundedOfResult = Object.assign(foundMember, {
                points: roundOffToTwoDecimals(foundMember?.points || 0),
                tierPoints: roundOffToTwoDecimals(foundMember?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateMembersBulk(organizationId, dataObj, filters = {}, opts = {}) {
        filters['organizationId'] = mongoose.Types.ObjectId(organizationId);
        return Promise.resolve(
            Member.updateMany(filters, dataObj, {
                multi: true,
                ...opts
            })
        );
    }

    static async updateMember(dataObj, id, organizationId, callerId, opts = {}) {
        try {
            dataObj['updatedBy'] = callerId ? mongoose.Types.ObjectId(callerId) : null;
            const result = await Member.findOneAndUpdate(
                {
                    _id: mongoose.Types.ObjectId(id),
                    organizationId
                },
                dataObj,
                {
                    upsert: false,
                    new: true,
                    returnOriginal: false,
                    ...opts
                }
            );
            const pointsRoundedOfResult = Object.assign(result, {
                points: roundOffToTwoDecimals(result?.points ? Number(result?.points) : 0),
                tierPoints: roundOffToTwoDecimals(result?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async redeemPoints(id, organizationId, newPoints, dataObj = { $inc: {} }, opts = {}, negative = false) {
        try {
            if (!dataObj['$inc']) {
                dataObj['$inc'] = {};
            }
            dataObj['$inc']['points'] = -newPoints;
            dataObj['$inc']['pointStats.redeemedPoints'] = newPoints;
            dataObj['$currentDate'] = { updatedOn: true };
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId
            };
            if (!negative) {
                filter['points'] = { $gte: newPoints };
            }
            const result = await Member.findOneAndUpdate(
                filter,
                {
                    ...dataObj,
                    lastTransactionOn: new Date()
                },
                { returnOriginal: false, ...opts }
            ).lean();

            const pointsRoundedOfResult = Object.assign(result, {
                points: roundOffToTwoDecimals(result?.points ? Number(result?.points) : 0),
                tierPoints: roundOffToTwoDecimals(result?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('Oops! Request failed. Please try again later', '500'));
        }
    }

    static async adjustPoints(memberId, organizationId, points, dataObj, opts = {}, negative = false, subType) {
        const { getOrganizationData } = require('./../../Utils');
        if (!dataObj) {
            dataObj = { $inc: {} };
        }
        const filter = {
            _id: mongoose.Types.ObjectId(memberId),
            organizationId: mongoose.Types.ObjectId(organizationId)
        };
        if (points < 0 && !negative) {
            Object.assign(filter, { points: { $gte: points } });
        }
        try {
            if (!dataObj['$inc']) {
                dataObj['$inc'] = {};
            }
            dataObj['$inc']['points'] = points;
            if (subType) {
                const {
                    subtransactionTypeIdMap: { refundPoints }
                } = await getOrganizationData(organizationId);
                if (subType.toString() === refundPoints.toString())
                    dataObj['$inc']['pointStats.refundedPoints'] = points;
            }
            const result = await Member.findOneAndUpdate(
                filter,
                {
                    ...dataObj,
                    $set: {
                        lastTransactionOn: new Date()
                    },
                    $currentDate: {
                        updatedOn: true
                    }
                },
                { returnOriginal: false, ...opts }
            ).lean();
            const pointsRoundedOfResult = Object.assign(result, {
                points: roundOffToTwoDecimals(result?.points ? Number(result?.points) : 0),
                tierPoints: roundOffToTwoDecimals(result?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error adjusting points', '500'));
        }
    }

    static async streamData(organizationId, filter) {
        filter['organizationId'] = organizationId;
        return Member.find(filter).cursor();
    }

    static async getMemberStreamWithPopulate(filterPayload, projection = {}) {
        try {
            return Member.find(filterPayload, projection)
                .populate([
                    {
                        path: 'affinityGroup.details',
                        select: 'name'
                    },
                    {
                        path: 'tierData',
                        select: 'name imageUrl benefits points status'
                    }
                ])
                .lean()
                .cursor();
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting members list', '500'));
        }
    }

    static async bulkUpdateMembers(bulk, opts = {}) {
        try {
            const result = await Member.bulkWrite(bulk, { ...opts });
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getRecordsCount(organizationId, filters) {
        try {
            return Member.countDocuments({
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filters
            })
                .collation({ locale: 'en', strength: 1 })
                .allowDiskUse(true);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = MembersDAO;
