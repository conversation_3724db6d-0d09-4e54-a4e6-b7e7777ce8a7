const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const MemberDAO = require('./MembersDAO');

const Member = require('./../models/member.model');
const { Status, Gender, Type } = require('./../models/enums/member.enums');

const organizationId = process.env.ORGANIZATION_ID;
const callerId = process.env.USER_ID;
const regionId = process.env.REGION_ID;
const merchantLocationId = process.env.MERCHANT_LOCATION_ID;

describe('MembersDAO unit tests', () => {
    let createdObjectId;

    afterAll(async () => {
        await Member.deleteOne({ _id: createdObjectId });
    });

    test.skip('create member', async () => {
        const dataset = {
            regionId: regionId,
            merchantLocationId: merchantLocationId,
            type: Type.PRIMARY,
            loyaltyId: 10,
            identifications: [
                {
                    identificationType: 'NATIONAL_ID',
                    identificationNumber: 'string'
                }
            ],
            tags: ['string'],
            firstName: 'string',
            lastName: 'string',
            preferredName: 'string',
            mobileNumber: 'string',
            additionalPhoneNumbers: ['string'],
            companyName: 'string',
            occupation: 'string',
            countryCode: 'string',
            country: 'string',
            email: 'string',
            birthDate: '2021-11-28',
            gender: Gender.MALE,
            status: Status.ACTIVE,
            residentialAddress: {
                line1: 'string',
                line2: 'string',
                line3: 'string',
                city: 'string',
                stateOrProvince: 'string',
                zipOrPostcode: 'string'
            },
            postalAddress: {
                line1: 'string',
                line2: 'string',
                line3: 'string',
                city: 'string',
                stateOrProvince: 'string',
                zipOrPostcode: 'string'
            }
        };
        const createdObj = await MemberDAO.createMember(dataset, organizationId, callerId);
        createdObjectId = createdObj.id;
        expect(typeof createdObj).toEqual('object');
    });

    test.skip('get members', async () => {
        const result = await MemberDAO.getMembers(organizationId, {}, 0, 10, null, null);
        expect(typeof result).toEqual('object');
        expect(result.items).toBeDefined();
        expect(result.total).toBeDefined();
        expect(result.items).toBeInstanceOf(Array);
    });

    test.skip('get a member by id', async () => {
        const result = await MemberDAO.getMemberById(createdObjectId, organizationId);
        expect(typeof result).toEqual('object');
    });

    test.skip('update a member', async () => {
        const result = await MemberDAO.updateMember(
            {
                firstName: 'TEST UPDATE'
            },
            createdObjectId,
            organizationId,
            callerId
        );
        expect(typeof result).toEqual('object');
        expect(result.firstName).toBe('TEST UPDATE');
    });

    test.skip('add points to a meber', async () => {
        const result = await MemberDAO.addPoints(createdObjectId, organizationId, 100, 100, 10, 10000);
        expect(typeof result).toEqual('object');
        expect(result.points).toBe(100);
    });

    test.skip('adjust points of a meber', async () => {
        const result = await MemberDAO.adjustPoints(createdObjectId, organizationId, -5);
        expect(typeof result).toEqual('object');
        expect(result.points).toBe(95);
    });

    test.skip('redeem points from a meber', async () => {
        const result = await MemberDAO.redeemPoints(createdObjectId, organizationId, 10);
        expect(typeof result).toEqual('object');
        expect(result.points).toBe(85);
    });
});
