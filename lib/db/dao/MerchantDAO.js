'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const Merchant = require('../models/merchant.model');
const { Status } = require('../models/enums/merchant.enums');
const { STATUS: PointRuleStatus, RULE_STATE } = require('../models/enums/point.rule.enums');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class MerchantDAO {

    static async getMerchants(organizationId, validatedObj, projection) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: Status.ARCHIVED }
            };

            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }
            if (validatedObj.type) {
                filter['type'] = validatedObj.type;
            }
            if (validatedObj.visibleForNearestLocations) {
                filter['visibleForNearestLocations'] = validatedObj.visibleForNearestLocations;
            }
            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }
            if (validatedObj.merchantIds) {
                filter['_id'] = {
                    $in: validatedObj.merchantIds.map(id=>mongoose.Types.ObjectId(id))
                }
            }

            const total = await Merchant.countDocuments(filter);
            const query = Merchant.find(filter, projection);

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit).lean();

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting merchants', 500));
        }
    }

    static async getMerchantsPortal(organizationId, validatedObj, projection) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: Status.ARCHIVED }
            };

            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }
            if (validatedObj.type) {
                filter['type'] = validatedObj.type;
            }
            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }
            if (validatedObj.merchantIds) {
                filter['_id'] = {
                    $in: validatedObj.merchantIds.map(id=>mongoose.Types.ObjectId(id))
                }
            }

            const total = await Merchant.countDocuments(filter);
            const query = Merchant.find(filter, projection);

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit).populate([
                {
                    path: "pointRules",
                    match: {
                        status: PointRuleStatus.ENABLED,
                        ruleState: RULE_STATE.ACTIVE
                    },
                    select: "name description type subType ruleData status ruleState createdOn"
                }
            ]);

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting merchants', 500));
        }
    }

    static async getMerchantsByFilter(organizationId, filter, sort, fields) {
        filter['organizationId'] = mongoose.Types.ObjectId(organizationId);
        try {
            const result = await Merchant.find(filter, fields).sort(sort).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMerchant(id, organizationId) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: Status.ARCHIVED }
            };
            return Merchant.findOne(filter).lean();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createMerchant(validatedObj, organizationId, callerId) {
        try {
            const MerchantModel = new Merchant({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await MerchantModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateMerchant(dataObj, id, organizationId, callerId) {
        try {
            const result = await Merchant.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      ...dataObj,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateMerchantLocationsCount(count, id, organizationId, callerId) {
        try {
            const result = await Merchant.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $inc: {
                      locationsCount: count
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async deleteMerchant(id, organizationId, callerId) {
        try {
            const result = await Merchant.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      status: Status.ARCHIVED,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = MerchantDAO;
