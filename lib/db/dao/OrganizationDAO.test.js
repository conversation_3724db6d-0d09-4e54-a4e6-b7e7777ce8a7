const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const OrganizationDAO = require('./OrganizationDAO');
const Organization = require('./../models/organization.model');
const MongooseConnector = require('./../connectors/MongooseConnector');
const mongoose = require('mongoose');

const rootUserId = '6144551286b72fe0724ee7cd';
describe('Organization DAO unit tests', () => {
    test.skip('create organization', async () => {
        const organization = new Organization({
            rootUserId,
            organizationName: 'Massy Loyalty',
            organizationLogoImageUrl: 'https://gallery.getshoutout.com/images/58748/massy_group_logo.png',
            organizationFavicon: 'https://gallery.getshoutout.com/images/58748/favicon.ico',
            organizationAppTitle: 'Massy Loyalty Service | Admin Portal',
            address: {
                street: 'Corner Macoya Road & Churchill Roosevelt Highway',
                city: 'Trincity',
                zip: '00700'
            },
            regions: [
                {
                    regionName: 'Barbados',
                    currencyCode: 'BBD',
                    regionIconUrl: 'https://gallery.getshoutout.com/images/58748/bbd_flag.jpeg',
                    defaultCountryISO2Code: 'BB'
                },
                {
                    regionName: 'Trinidad and Tobago',
                    currencyCode: 'TTD',
                    regionIconUrl: 'https://gallery.getshoutout.com/images/58748/tt_flag.jpeg',
                    defaultCountryISO2Code: 'TT'
                },
                {
                    regionName: 'Guyana',
                    currencyCode: 'GYD',
                    regionIconUrl: 'https://gallery.getshoutout.com/images/58748/gy_flag.jpeg',
                    defaultCountryISO2Code: 'GY'
                },
                {
                    regionName: 'St. Lucia',
                    currencyCode: 'XCD',
                    regionIconUrl: 'https://gallery.getshoutout.com/images/58748/slu_flag.jpeg',
                    defaultCountryISO2Code: 'LC'
                },
                {
                    regionName: 'St. Vincent',
                    currencyCode: 'XCD',
                    regionIconUrl: 'https://gallery.getshoutout.com/images/58748/svn_flag.jpeg',
                    defaultCountryISO2Code: 'VC'
                }
            ]
        });
        const createdOrganization = await OrganizationDAO.createOrganization(organization);
        expect(typeof createdOrganization).toEqual('object');
    });

    test('get organization', async () => {
        const organization = await OrganizationDAO.getOrganization(mongoose.Types.ObjectId().toString());
        expect(typeof organization).toEqual('object');
    });
});
