'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const PointExpirationJob = require('../models/point.expiration.job.model');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class PointExpirationJobsDAO {

    static async createPointExpirationJob(validatedObj, organizationId) {
        try {
            const PointExpirationJobModel = new PointExpirationJob({
                ...validatedObj,
                organizationId
            });
            const createdObj = await PointExpirationJobModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getPointExpirationJobs(organizationId, validatedObj) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId)
            };

            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }

            const total = await PointExpirationJob.countDocuments(filter);
            const query = PointExpirationJob.find(filter);

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit).lean();

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting point expiration jobs', '500'));
        }
    }

    static async updatePointExpirationJob(dataObj, id, organizationId) {
        try {
            const result = await PointExpirationJob.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      ...dataObj
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = PointExpirationJobsDAO;
