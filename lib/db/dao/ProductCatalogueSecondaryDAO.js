'use strict';

const config = require('../../config');
const logger = require('../../logger');
const MongooseConnector = require('../connectors/MongooseConnector');
const { getProductCatalogueModel } = require('../models/product.catalogue.model');

const log = logger(config.logger);

class ProductCatalogueSecondaryDAO {
    static async #getModelWithSecondaryConnection() {
        log.debug('ProductCatalogueSecondaryDAO: getting a mongodb secondary connection...');
        const conn = await MongooseConnector.getSecondaryConnection();
        log.debug('ProductCatalogueSecondaryDAO: mongodb secondary connection established.');

        return getProductCatalogueModel(conn);
    }

    static async bulkWriteOperationsProductCatalogues(operations = [], opts = {}) {
        try {
            const modelWithSecondary = await this.#getModelWithSecondaryConnection();

            return modelWithSecondary.bulkWrite(operations, { ...opts });
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getProductCatalogueSession() {
        try {
            const modelWithSecondary = await this.#getModelWithSecondaryConnection();

            return modelWithSecondary.startSession();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = ProductCatalogueSecondaryDAO;
