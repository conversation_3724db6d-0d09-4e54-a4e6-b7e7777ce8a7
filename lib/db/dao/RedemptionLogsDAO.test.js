const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const RewardRedemptionLogsDAO = require('./RewardRedemptionLogsDAO');

const RewardRedemptionLog = require('./../models/reward.redemption.log.model');
const { STATUS, REFUND_STATUS, PROCESSING_STATUS } = require('./../models/enums/reward.redemption.log.enums');
const { TYPE: RewardType, SUB_TYPE } = require('./../models/enums/reward.enums');

const organizationId = process.env.ORGANIZATION_ID;
const callerId = process.env.USER_ID;
const merchantLocationId = process.env.MERCHANT_LOCATION_ID;
const regionId = process.env.REGION_ID;
const redemptionLogId = process.env.REDEMPTION_LOG_ITEM_ID;
const memberId = process.env.MEMBER_ID;
const rewardId = process.env.REWARD_ID;
const transactionId = process.env.TRANSACTION_ID;
describe('RedemptionLogDAO unit tests', () => {
    let createdObjectId;

    afterAll(async () => {
        await RewardRedemptionLog.deleteOne({ _id: createdObjectId });
    });

    test('create redemption log', async () => {
        const dataset = {
            organizationId,
            regionId,
            memberId,
            rewardId,
            transactionId,
            pointsRedeemed: 10,
            status: STATUS.REQUESTED,
            processingStatus: PROCESSING_STATUS.PENDING,
            rewardType: RewardType.TANGIBLE,
            rewardSubType: SUB_TYPE.VOUCHER,
            refundStatus: REFUND_STATUS.NONE,
            createdBy: callerId,
            metadata: {
                claimLocationId: merchantLocationId
            }
        };
        const createdObj = await RewardRedemptionLogsDAO.createRewardRedemptionLog(dataset, organizationId, callerId);
        createdObjectId = createdObj.id;
        expect(typeof createdObj).toEqual('object');
    });

    test.skip('get redemption logs', async () => {
        const result = await RewardRedemptionLogsDAO.getRewardRedemptionLogs(organizationId, {
            skip: 0,
            limit: 10
        });
        expect(typeof result).toEqual('object');
        expect(result.items).toBeDefined();
        expect(result.total).toBeDefined();
        expect(result.items).toBeInstanceOf(Array);
    });

    test('get a reward redemption log by id', async () => {
        const result = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, createdObjectId);
        expect(typeof result).toEqual('object');
    });

    test('update redemption log', async () => {
        const result = await RewardRedemptionLogsDAO.updateRedemptionLog(
            {
                status: STATUS.READY,
                processingStatus: PROCESSING_STATUS.COMPLETED
            },
            redemptionLogId,
            organizationId
        );
        expect(typeof result).toEqual('object');
        expect(result.processingStatus).toBe(PROCESSING_STATUS.COMPLETED);
        expect(result.status).toBe(STATUS.READY);
    });
});
