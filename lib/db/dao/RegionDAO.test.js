const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const RegionDAO = require('./RegionDAO');
const Region = require('../models/region.model');
const MongooseConnector = require('../connectors/MongooseConnector');
const mongoose = require('mongoose');
const { POINT_EXPIRY_METHOD } = require('../models/enums/regional.point.configuration.enums');

const organizationId = '6128e3537ed841e246e2e394';
const callerId = '613d08491df0e38df616f411';

describe('RegionDAO unit tests', () => {
    let regionId;

    afterAll(async () => {
        await Region.deleteMany({ organizationId });
    });

    test.skip('create region', async () => {
        const region = {
            organizationId,
            regionName: 'Barbados',
            defaultCurrencyCode: 'BBD',
            regionIconUrl: 'https://gallery.getshoutout.com/images/58748/bbd_flag.jpeg',
            defaultCountryISO2Code: 'BB',
            pointConfiguration: {
                minPointRedemptionAmount: 10,
                maxPointRedemptionAmount: 10000,
                minPointsBalanceForRedemption: 100,
                pointExpiryMethod: POINT_EXPIRY_METHOD.FIXED,
                pointExpiryStartMonth: 1,
                pointExpiryEndMonth: 12,
                //pointExpiryPeriod: 12,
                pointExpiryGracePeriod: 90,
                currencyAmountPerPoint: 0.1,
                regionalPointConversionRates: []
            },
            memberConfiguration: {
                maxSecondaryAccounts: 5
                //enrolmentForm: {}
            },
            notificationConfiguration: {
                emailConfiguration: {
                    fromAddress: '<EMAIL>'
                },
                smsConfiguration: {
                    phoneNumber: '***********'
                }
            },
            createdBy: callerId
        };
        const createdRegion = await RegionDAO.createRegion(region);
        regionId = createdRegion._id;
        expect(typeof createdRegion).toEqual('object');
    });

    test('get region', async () => {
        const region = await RegionDAO.getRegion(regionId);
        expect(typeof region).toEqual('object');
    });
});
