'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const log = logger(config.logger);
const CustomHttpError = require('./../../CustomHttpError');
const Reward = require('../models/reward.model');
const mongoose = require('mongoose');
const { STATUS, SUB_TYPE } = require('../models/enums/reward.enums');

class RewardDAO {

    static async getRewards(organizationId, validatedObj, fields, sort) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: STATUS.ARCHIVED },
                subType: { $ne: SUB_TYPE.PARTNER }
            };

            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }

            if (validatedObj.type) {
                filter['type'] = validatedObj.type;
            }

            if (validatedObj.subType) {
                filter['subType'] = validatedObj.subType;
            }

            if (validatedObj.portalVisibility) {
                filter['portalVisibility'] = validatedObj.portalVisibility;
            }

            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }

            if(validatedObj.pointsLowerMargin && validatedObj.pointsUpperMargin){
                filter['$and'] = [{
                    pointsStatic: {
                        $gte: validatedObj.pointsLowerMargin
                    }
                },{
                    pointsStatic: {
                        $lte: validatedObj.pointsUpperMargin
                    }
                }]
            } else if(validatedObj.pointsLowerMargin){
                filter['pointsStatic'] = {
                    $gte : validatedObj.pointsLowerMargin
                }
            } else if (validatedObj.pointsUpperMargin){
                filter['pointsStatic'] = {
                    $lte : validatedObj.pointsUpperMargin
                }
            }

            if (validatedObj.regionIds) {
                filter['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantIds) {
                if (validatedObj.merchantId) {
                    filter['merchantId'] = { $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
                } else {
                    filter['$or'] =
                      [
                          {'merchantId': { $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId)) } },
                          { 'merchantId': null }
                      ];
                }
            }

            const total = await Reward.countDocuments(filter);
            const query = Reward.find(filter, fields).sort(sort || {createdOn: -1, remainingCount: 1}).lean();

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit);

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting rewards', '500'));
        }
    }

    static async getReward(organizationId, rewardId, regionId) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(rewardId),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: STATUS.ARCHIVED }
            };
            if(regionId){
                filter['regionId'] = mongoose.Types.ObjectId(regionId);
            }
            const result = await Reward.findOne(filter).populate([
                {
                    path: "claimLocations",
                    select: "locationName status"
                }
            ]).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createReward(validatedObj, organizationId, callerId) {
        try {
            const RewardModel = new Reward({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await RewardModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateReward(dataObj, id, organizationId, callerId) {
        try {
            const result = await Reward.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      ...dataObj,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateRewardWithIncrement(incObj, id, organizationId, callerId) {
        try {
            const result = await Reward.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $inc: incObj,
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async deleteReward(id, organizationId, callerId) {
        try {
            const result = await Reward.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      status: STATUS.ARCHIVED,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = RewardDAO;
