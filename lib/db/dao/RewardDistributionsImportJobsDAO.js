'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const RewardDistributionsImportJob = require('../models/reward.distributions.import.job.model');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class RewardDistributionsImportJobsDAO {

    static async createRDImportJob(validatedObj, organizationId, callerId) {
        try {
            const RewardDistributionsImportJobModel = new RewardDistributionsImportJob({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await RewardDistributionsImportJobModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateRDImportJob(updatePayload, id, organizationId, filter) {
        try {
            const result = await RewardDistributionsImportJob.findOneAndUpdate(
                Object.assign({}, filter || {}, {
                    _id: mongoose.Types.ObjectId(id),
                    organizationId: mongoose.Types.ObjectId(organizationId)
                }),
                updatePayload,
                {
                    upsert: false,
                    new: true,
                    returnOriginal: false
                }
            );
            if (result) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(new CustomHttpError('Could not update reward distributions import job', 500));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = RewardDistributionsImportJobsDAO;
