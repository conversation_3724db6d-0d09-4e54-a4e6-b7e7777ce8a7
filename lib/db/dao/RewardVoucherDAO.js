'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const RewardVoucher = require('../models/reward.voucher.model');
const { STATUS } = require('../models/enums/reward.voucher.enums');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class RewardVoucherDAO {

    static async bulkCreateRewardVouchers(vouchersArray, organizationId, callerId) {
        try {
            const vouchers = vouchersArray.map((voucher)=>{
                return Object.assign(voucher,{
                    organizationId: mongoose.Types.ObjectId(organizationId),
                    createdBy: mongoose.Types.ObjectId(callerId)
                });
            })
            const createdVocuhers = await RewardVoucher.insertMany(vouchers);
            return Promise.resolve(createdVocuhers);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async reserveVoucherCode(filter, organizationId, callerId, rewardRedemptionLogId) {
        filter['organizationId'] = mongoose.Types.ObjectId(organizationId);
        try {
            const result = await RewardVoucher.findOne({
                ...filter,
                status: STATUS.OPEN
            }).lean();

            if (!result) {
                return Promise.reject(new CustomHttpError('No open vouchers found', '400'));
            }

            const updatedVoucher = await RewardVoucher.findOneAndUpdate(
              {
                  _id: result._id,
                  organizationId
              },
              {
                  $set: {
                      rewardRedemptionLogId,
                      status: STATUS.ISSUED,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );

            return Promise.resolve(updatedVoucher);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateVoucher(dataObj, id, organizationId, filters, callerId) {
        try {
            const result = await RewardVoucher.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId,
                  ...filters
              },
              {
                  $set: {
                      ...dataObj,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getVoucher(organizationId, filters) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filters
            };
            const result = await RewardVoucher.findOne(filter).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = RewardVoucherDAO;
