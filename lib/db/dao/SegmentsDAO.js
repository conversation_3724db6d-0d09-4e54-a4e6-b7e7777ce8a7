'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const Segment = require('../models/segment.model');
const log = logger(config.logger);
const mongoose = require('mongoose');

class SegmentsDAO {
    static async getSegments(organizationId, validatedObj) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId)
            };
            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }
            if (validatedObj.segmentIds) {
                filter['_id'] = { $in: validatedObj.segmentIds.map((segmentId) => mongoose.Types.ObjectId(segmentId)) };
            }
            if (validatedObj?.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                };
            }

            if (validatedObj.fromDate && validatedObj['toDate']) {
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.fromDate),
                    $lte: new Date(validatedObj['toDate'])
                };
            } else if (validatedObj.fromDate) {
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.fromDate)
                };
            } else if (validatedObj['toDate']) {
                filter['createdOn'] = {
                    $lte: new Date(validatedObj['toDate'])
                };
            }

            const total = await Segment.countDocuments(filter);
            const query = Segment.find(filter).sort({ createdOn: 'desc' });

            if (validatedObj.skip) {
                query.skip(validatedObj.skip);
            }
            if (validatedObj.limit) {
                query.limit(validatedObj.limit);
            }

            const items = await query.lean();

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getSegmentById(id, organizationId) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId)
            };
            const result = await Segment.findOne(filter).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateSegment(dataObj, id, organizationId, callerId) {
        try {
            const queryFilter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: organizationId
            };
            const update = {
                $set: dataObj,
                $currentDate: { updatedOn: true },
                updatedBy: mongoose.Types.ObjectId(callerId)
            };
            const options = {
                upsert: false,
                new: true,
                returnOriginal: false
            };
            const result = await Segment.findOneAndUpdate(queryFilter, update, options);
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async deleteSegment(id, organizationId) {
        try {
            const result = await Segment.deleteOne({
                _id: mongoose.Types.ObjectId(id),
                organizationId: organizationId
            });
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = SegmentsDAO;
