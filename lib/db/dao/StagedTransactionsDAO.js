'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const StagedTransaction = require('../models/staged.transaction.model');
const log = logger(config.logger);
const mongoose = require('mongoose');
const CustomHttpError = require('./../../CustomHttpError');

class StagedTransactionsDAO {
    static async getStagedTransaction(organizationId, id, filter = {}) {
        try {
            filter['organizationId'] = mongoose.Types.ObjectId(organizationId);
            filter['_id'] = mongoose.Types.ObjectId(id);
            return StagedTransaction.findOne(filter).lean();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createStagedTransaction(validatedObj, organizationId, callerId) {
        try {
            const stagedTransactionModel = new StagedTransaction({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await stagedTransactionModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateStagedTransaction(updatePayload, organizationId, id, callerId, optionalFilter) {
        try {
            updatePayload.updatedBy = mongoose.Types.ObjectId(callerId);
            const result = await StagedTransaction.findOneAndUpdate(
                Object.assign(optionalFilter || {}, {
                    _id: mongoose.Types.ObjectId(id),
                    organizationId: mongoose.Types.ObjectId(organizationId)
                }),
                updatePayload,
                {
                    upsert: false,
                    new: true,
                    returnOriginal: false
                }
            );
            if (result) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(new CustomHttpError('could not update staged transaction', 500));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getStagedTransactionsFilter(organizationId, validatedObj, userId) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(validatedObj.regionId)
            };
            if (validatedObj.merchantId) {
                filter['merchantId'] = mongoose.Types.ObjectId(validatedObj.merchantId);
            }
            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }
            if (validatedObj.merchantLocationId) {
                filter['merchantLocationId'] = mongoose.Types.ObjectId(validatedObj.merchantLocationId);
            }
            if (validatedObj.subType) {
                filter['subType'] = mongoose.Types.ObjectId(validatedObj.subType);
            }
            if (validatedObj.importJobId) {
                filter['importJobId'] = mongoose.Types.ObjectId(validatedObj.importJobId);
            }
            if (validatedObj.createdOn) {
                filter['createdOn'] = validatedObj.createdOn;
            }
            if (validatedObj.transactionOn) {
                filter['transactionOn'] = validatedObj.transactionOn;
            }
            if (validatedObj.cardNo) {
                filter['cardNo'] = validatedObj.cardNo;
            }

            if (validatedObj.transactionOnFromDate && validatedObj.transactionOnToDate) {
                filter['transactionOn'] = {
                    $gte: new Date(validatedObj.transactionOnFromDate),
                    $lte: new Date(validatedObj.transactionOnToDate).setHours(23, 59)
                };
            } else if (validatedObj.transactionOnFromDate) {
                filter['transactionOn'] = {
                    $gte: new Date(validatedObj.transactionOnFromDate)
                };
            } else if (validatedObj.transactionOnToDate) {
                filter['transactionOn'] = {
                    $lte: new Date(validatedObj.transactionOnToDate).setHours(23, 59)
                };
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                };
            }
            if (validatedObj.failureReason) {
                filter['failedReason'] = {
                    $regex: validatedObj.failureReason
                };
            }
            if (validatedObj.regionIds) {
                filter['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantIds) {
                filter['merchantId'] = {
                    $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId))
                };
            }
            if (validatedObj.merchantLocationIds) {
                filter['$or'] = [
                    {
                        merchantLocationId: {
                            $in: validatedObj.merchantLocationIds.map((itemId) => mongoose.Types.ObjectId(itemId))
                        }
                    },
                    { createdBy: mongoose.Types.ObjectId(userId) }
                ];
            }

            return filter;
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getStagedTransactions(organizationId, validatedObj, userId) {
        try {
            const filter = await this.getStagedTransactionsFilter(organizationId, validatedObj, userId);

            const total = await StagedTransaction.countDocuments(filter);
            const query = StagedTransaction.find(filter);

            if (validatedObj.skip) {
                query.skip(validatedObj.skip);
            }
            if (validatedObj.limit) {
                query.limit(validatedObj.limit);
            }

            const items = await query
                .populate([
                    {
                        path: 'merchant',
                        select: 'merchantName'
                    },
                    {
                        path: 'merchantLocation',
                        select: 'locationName'
                    }
                ])
                .lean();

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getStagedTransactionsByFilter(filterPayload, organizationId, fields, stream = false) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filterPayload
            };

            if (stream) {
                return StagedTransaction.find(filter, fields).cursor();
            }

            return await StagedTransaction.find(filter, fields).lean();
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error getting staged transactions', '500'));
        }
    }

    static async getStagedTransactionsStream(filters) {
        try {
            return StagedTransaction.find(filters).cursor();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = StagedTransactionsDAO;
