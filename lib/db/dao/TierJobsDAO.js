'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const TierJob = require('../models/tier.job.model');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class TierJobsDAO {

    static async createTierJob(validatedObj, organizationId) {
        try {
            const TierJobModel = new TierJob({
                ...validatedObj,
                organizationId
            });
            const createdObj = await TierJobModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTierJobs(organizationId, validatedObj) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId)
            };

            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }

            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }

            const total = await TierJob.countDocuments(filter);
            const query = TierJob.find(filter);

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit).lean();

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting tier jobs', '500'));
        }
    }

    static async updateTierJob(dataObj, id, organizationId) {
        try {
            const result = await TierJob.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId
              },
              {
                  $set: {
                      ...dataObj
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = TierJobsDAO;
