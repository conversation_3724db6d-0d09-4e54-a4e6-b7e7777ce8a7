'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const Tier = require('../models/tier.model');
const { STATUS } = require('../models/enums/tier.enums');
const CustomHttpError = require('../../CustomHttpError');
const log = logger(config.logger);
const mongoose = require('mongoose');

class TiersDAO {

    static async getZeroTier(regionId, organizationId) {
        try {
            const filter = {
                organizationId: organizationId,
                regionId: regionId,
                points: 0
            };
            const zeroTier = await Tier.findOne(filter);
            // if (!zeroTier) {
            //     return Promise.reject(new CustomHttpError('Zero tier not found', '400'));
            // }
            return Promise.resolve(zeroTier);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createTier(validatedObj, organizationId, callerId) {
        try {
            const tierModel = new Tier({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await tierModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createTiers(validatedObj, regionId, organizationId, callerId) {
        try {
            const tiers = validatedObj.map(tier => ({
                ...tier,
                organizationId,
                regionId,
                createdBy: callerId ? mongoose.Types.ObjectId(callerId) : null
        }));
            const result = await Tier.insertMany(tiers);
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTiers(organizationId, validatedObj, projection) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: STATUS.ENABLED
            };
            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }

            const total = await Tier.countDocuments(filter);
            const query = Tier.find(filter, projection);

            if (validatedObj.sort) {
                query.sort(validatedObj.sort);
            }

            if (validatedObj.skip) {
                query.skip(validatedObj.skip);
            }
            if (validatedObj.limit) {
                query.limit(validatedObj.limit);
            }

            const items = await query.lean();

            return {
                total,
                items,
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTierById(id, organizationId) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: { $ne: STATUS.ARCHIVED },
            };
            const result = await Tier.findOne(filter).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateTier(validatedDataObj, id, organizationId) {
        try {
            const result = await Tier.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId: mongoose.Types.ObjectId(organizationId)
              },
              validatedDataObj,
              {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            if (result) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(new CustomHttpError('could not update tier', 500));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateRegionTiers(organizationId, regionId, tiers, callerId) {
        const session = await mongoose.startSession();
        session.startTransaction();
        try {
            const operations = tiers.map(tier => {
                if (tier._id) {
                    if (callerId) tier.updatedBy = callerId;
                    return {
                        updateOne: {
                            filter: { _id: tier._id },
                            update: { $set: tier },
                            upsert: false
                        }
                    };
                } else {
                    if (callerId) tier.createdBy = callerId;
                    return {
                        insertOne: {
                            document: {
                                organizationId,
                                regionId,
                                ...tier
                            }
                        }
                    };
                }
            });

            const { items: retrievedTiers } = await this.getTiers(organizationId, { regionId }, { _id: 1 });
            const existingTierIds = retrievedTiers.map(tier => tier._id.toString());
            const incomingTierIds = tiers.filter(tier => tier._id).map(tier => tier._id.toString());

            const tierIdsToDelete = existingTierIds.filter(
                id => !incomingTierIds.includes(id)
            );

            if (tierIdsToDelete.length > 0) {
                operations.push({
                    updateMany: {
                        filter: { _id: { $in: tierIdsToDelete.map(id => mongoose.Types.ObjectId(id)) } },
                        update: {
                            $set: {
                                status: STATUS.ARCHIVED
                            }
                        }
                    }
                });
            }

            const result = await Tier.bulkWrite(operations, { session });

            await session.commitTransaction();
            session.endSession();
            return result;

        } catch (err) {
            await session.abortTransaction();
            session.endSession();
            log.error("Transaction aborted due to error:", err);
            throw err;
        }
    }

    static async deleteTier(id, organizationId, callerId) {
        try {
            const result = await Tier.findOneAndUpdate(
              {
                  _id: mongoose.Types.ObjectId(id),
                  organizationId,
                  points: {$ne: 0}
              },
              {
                  $set: {
                      status: STATUS.ARCHIVED,
                      updatedBy: mongoose.Types.ObjectId(callerId)
                  },
                  $currentDate: {updatedOn: true}
              }, {
                  upsert: false,
                  new: true,
                  returnOriginal: false
              }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = TiersDAO;
