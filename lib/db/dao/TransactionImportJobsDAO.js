'use strict';
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);
const TransactionImportJob = require('../models/transaction.import.job.model');
const mongoose = require('mongoose');
const CustomHttpError = require('../../CustomHttpError');

class TransactionImportJobsDAO {

    static async createTransactionImportJob(validatedObj, organizationId, callerId) {
        try {
            const TransactionImportJobModel = new TransactionImportJob({
                ...validatedObj,
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId)
            });
            const createdObj = await TransactionImportJobModel.save();
            return Promise.resolve(createdObj.toObject({ getters: true }));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateTransactionImportJob(updatePayload, id, organizationId, filter) {
        try {
            const result = await TransactionImportJob.findOneAndUpdate(
                Object.assign({}, filter || {}, {
                    _id: mongoose.Types.ObjectId(id),
                    organizationId: mongoose.Types.ObjectId(organizationId)
                }),
                updatePayload,
                {
                    upsert: false,
                    new: true,
                    returnOriginal: false
                }
            );
            if (result) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(new CustomHttpError('could not update transactions import job', 500));
            }
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTransactionImportJobs(organizationId, validatedObj) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                regionId: mongoose.Types.ObjectId(validatedObj.regionId)
            };

            if (validatedObj.merchantId) {
                filter['merchantId'] = mongoose.Types.ObjectId(validatedObj.merchantId);
            }

            if (validatedObj.merchantLocationId) {
                filter['merchantLocationId'] = mongoose.Types.ObjectId(validatedObj.merchantLocationId);
            }

            if (validatedObj.importJobId) {
                filter['_id'] = mongoose.Types.ObjectId(validatedObj.importJobId);
            }

            if (validatedObj.createdDateFrom && validatedObj.createdDateTo) {
                let tDx = new Date(validatedObj.createdDateTo);
                tDx.setHours(23, 59);
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.createdDateFrom),
                    $lte: tDx
                };
            } else if (validatedObj.createdDateFrom) {
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.createdDateFrom)
                };
            } else if (validatedObj.createdDateTo) {
                let tDy = new Date(validatedObj.createdDateTo);
                tDy.setHours(23, 59);
                filter['createdOn'] = {
                    $lte: tDy
                };
            }

            if (validatedObj.regionIds) {
                filter['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantIds) {
                filter['merchantId'] = { $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantLocationId && validatedObj.merchantLocationIds) {
                filter['merchantLocationId'] = { $in: validatedObj.merchantLocationIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }

            const total = await TransactionImportJob.countDocuments(filter);
            const query = TransactionImportJob.find(filter).sort({createdOn: "desc"}).populate([
                {
                    path: "merchant",
                    select: "merchantName"
                },
                {
                    path: "merchantLocation",
                    select: "locationName"
                }
            ]).lean();

            const items = await query.skip(validatedObj.skip).limit(validatedObj.limit);

            return {
                total,
                items
            };
        } catch (err) {
            log.error(err);
            return Promise.reject(new CustomHttpError('error with getting transaction import jobs', '500'));
        }
    }

    static async getTransactionImportJobById(id, organizationId, fields, populate, filters) {
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filters
            };
            const member = await TransactionImportJob.findOne(filter, fields).populate(populate);
            if (member) {
                return Promise.resolve(member);
            } else {
                return Promise.reject(new CustomHttpError('transaction import job not found', '404'));
            }
        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }

}

module.exports = TransactionImportJobsDAO;
