'use strict';

const config = require('./../../config');
const logger = require('./../../logger');
const TransactionProductItem = require('../models/transaction.product.item.model').getTransactionProductItemModel();

const log = logger(config.logger);

class TransactionProductItemsDAO {
    static async getTransactionProductItems(filter, sort, limit, skip, fields, countOnly = false, stream = false) {
        try {
            const query = TransactionProductItem.find(filter, fields).sort(sort).lean();
            const countQuery = TransactionProductItem.countDocuments(filter);

            if (skip) {
                query.skip(skip);
            }
            if (limit) {
                query.limit(limit);
            }

            if (countOnly) return await countQuery;

            const promises = [countQuery];
            promises.unshift(stream ? query.cursor() : query);

            const [queryResponse, total] = await Promise.all(promises);

            return { ...(stream ? { cursor: queryResponse } : { items: queryResponse }), total };
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = TransactionProductItemsDAO;
