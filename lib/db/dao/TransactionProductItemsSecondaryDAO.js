'use strict';

const config = require('./../../config');
const logger = require('./../../logger');
const MongooseConnector = require('../connectors/MongooseConnector');
const { getTransactionProductItemModel } = require('../models/transaction.product.item.model');

const log = logger(config.logger);

class TransactionProductItemsSecondaryDAO {
    static async #getModelWithSecondaryConnection() {
        log.debug('TransactionProductItemsSecondaryDAO: getting a mongodb secondary connection...');
        const conn = await MongooseConnector.getSecondaryConnection();
        log.debug('TransactionProductItemsSecondaryDAO: mongodb secondary connection established.');

        return getTransactionProductItemModel(conn);
    }

    static async getTransactionProductItems(filter, sort, limit, skip, fields) {
        try {
            const modelWithSecondary = await this.#getModelWithSecondaryConnection();

            const query = modelWithSecondary.find(filter, fields).sort(sort).lean();

            if (skip) {
                query.skip(skip);
            }
            if (limit) {
                query.limit(limit);
            }

            const items = await query;

            return { items };
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async runAggregation(aggregation, stream = false) {
        const modelWithSecondary = await this.#getModelWithSecondaryConnection();

        if (stream) return modelWithSecondary.aggregate(aggregation).allowDiskUse(true).cursor();

        return modelWithSecondary.aggregate(aggregation).collation({ locale: 'en', strength: 1 }).allowDiskUse(true);
    }
}

module.exports = TransactionProductItemsSecondaryDAO;
