'use strict';
const config = require('./../../config');
const Utils = require('./../../Utils');
const Shoutout = require('./../../services/Shoutout');
const logger = require('./../../logger');
const Transaction = require('../models/transaction.model');
const { TYPE } = require('../models/enums/transaction.enums');
const log = logger(config.logger);
const CustomHttpError = require('./../../CustomHttpError');
const mongoose = require('mongoose');

class TransactionsDAO {
    static async #getTransactionsAggregation(organizationId, validatedObj) {
        try {
            let filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
            };
            if (validatedObj.merchantId) {
                filter['merchantId'] = mongoose.Types.ObjectId(validatedObj.merchantId);
            }
            if (validatedObj.regionId) {
                filter['regionId'] = mongoose.Types.ObjectId(validatedObj.regionId);
            }
            if (validatedObj.memberId) {
                filter['memberId'] = mongoose.Types.ObjectId(validatedObj.memberId);
            }
            if (validatedObj.status) {
                filter['status'] = validatedObj.status;
            }
            if (validatedObj.transactionType) {
                filter['type'] = validatedObj.transactionType;
            }
            if (validatedObj.merchantLocationId) {
                filter['merchantLocationId'] = mongoose.Types.ObjectId(validatedObj.merchantLocationId);
            }
            if (validatedObj.subType) {
                filter['subType'] = mongoose.Types.ObjectId(validatedObj.subType);
            }
            if (validatedObj.importJobId) {
                filter['importJobId'] = mongoose.Types.ObjectId(validatedObj.importJobId);
            }
            if (validatedObj.createdOn) {
                filter['createdOn'] = validatedObj.createdOn;
            }
            if (validatedObj.transactionOn) {
                filter['transactionOn'] = validatedObj.transactionOn;
            }
            if (validatedObj.cardIds) {
                filter['cardId'] = {$in: validatedObj.cardIds};
            }
            if (validatedObj.searchKey) {
                filter['$text'] = {
                    $search: validatedObj.searchKey
                }
            }

            if (validatedObj.transactionOnFromDate && validatedObj.transactionOnToDate) {
                filter['transactionOn'] = {
                    $gte: new Date(validatedObj.transactionOnFromDate),
                    $lte: new Date(validatedObj.transactionOnToDate).setHours(23, 59)
                };
            } else if (validatedObj.transactionOnFromDate) {
                filter['transactionOn'] = {
                    $gte: new Date(validatedObj.transactionOnFromDate)
                };
            } else if (validatedObj.transactionOnToDate) {
                filter['transactionOn'] = {
                    $lte: new Date(validatedObj.transactionOnToDate).setHours(23, 59)
                };
            }

            if (validatedObj.regionIds) {
                filter['regionId'] = {$in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId))};
            }
            if (validatedObj.merchantIds) {
                filter['merchantId'] = {$in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId))};
            }
            if (validatedObj.merchantLocationIds) {
                filter['merchantLocationId'] = {$in: validatedObj.merchantLocationIds.map((itemId) => mongoose.Types.ObjectId(itemId))};
            }
            return filter;
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async createTransaction(validatedObj, organizationId, callerId, opts = {}) {
        try {
            if (isNaN(validatedObj.redeemablePoints)) {
                log.error(validatedObj)
                return Promise.reject(new CustomHttpError('redeemable points amount is not a number', '400'));
            }

            if (!validatedObj.idempotentKey) {
                validatedObj.idempotentKey = Utils.generateIdempotentKey(new Date().toISOString(), JSON.stringify(validatedObj));
            }

            const idempotentKeyInUse = await this.getTransaction(organizationId, {idempotentKey: validatedObj.idempotentKey});
            if (idempotentKeyInUse) return Promise.reject(new CustomHttpError('duplicate idempotent key', '400'));

            const transactionModel = new Transaction({
                ...validatedObj, organizationId, createdBy: callerId ? new mongoose.Types.ObjectId(callerId) : null
            });
            const createdObj = await transactionModel.save(opts);

            Shoutout.submitTransaction([createdObj]);

            return Promise.resolve(createdObj.toObject({getters: true}));
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getPortalTransactions(organizationId, validatedObj) {
        try {
            const filter = await this.#getTransactionsAggregation(organizationId, validatedObj);
            const total = await Transaction.countDocuments(filter);
            const query = Transaction.find(filter).sort({createdOn: "desc"});

            if (validatedObj.skip) {
                query.skip(validatedObj.skip);
            }
            if (validatedObj.limit) {
                query.limit(validatedObj.limit);
            }

            const items = await query.populate([{
                path: "merchant", select: "merchantName"
            }, {
                path: "merchantLocation", select: "locationName"
            }, {
                path: "card", select: "cardNo"
            }, {
                path: "transactionSubType", select: "name operationType"
            }]).lean();

            return {total, items,};
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTransactions(organizationId, validatedObj) {
        try {
            const filter = await this.#getTransactionsAggregation(organizationId, validatedObj);
            const query = Transaction.find(filter).sort({createdOn: "desc"});

            if (validatedObj.skip) {
                query.skip(validatedObj.skip);
            }
            if (validatedObj.limit) {
                query.limit(validatedObj.limit);
            }

            const items = await query.populate([{
                path: "merchant", select: "merchantName"
            }, {
                path: "merchantLocation", select: "locationName"
            }, {
                path: "card", select: "cardNo"
            }, {
                path: "transactionSubType", select: "name operationType"
            }]).lean();
            return {items};
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTransactionsCount(organizationId, validatedObj) {
        try {
            const filter = await this.#getTransactionsAggregation(organizationId, validatedObj);
            return await Transaction.countDocuments(filter);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTransactionsByFilter(organizationId, filter, sort, fields) {
        filter['organizationId'] = mongoose.Types.ObjectId(organizationId);
        try {
            const result = await Transaction.find(filter, fields).sort(sort).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getTransaction(organizationId, filter) {
        filter['organizationId'] = mongoose.Types.ObjectId(organizationId);
        try {
            const result = await Transaction.findOne(filter).lean();
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getLatestTransaction(organizationId, memberId, merchantLocationId, invoiceNumber, filter) {
        try {
            const result = await Transaction.find({
                organizationId: mongoose.Types.ObjectId(organizationId),
                memberId: mongoose.Types.ObjectId(memberId),
                merchantLocationId: mongoose.Types.ObjectId(merchantLocationId),
                'invoiceData.invoiceId': invoiceNumber,
                type: TYPE.COLLECTION,
                ...filter
            }).sort({ createdOn: -1 }).limit(1).lean();
            if (result.length > 0) return Promise.resolve(result[0]);
            return Promise.resolve(null);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async bulkCreateTransactions(organizationId, bulk, opts = {}) {
        try {
            const idempotentKeys = [];
            for (let transactionObj of bulk) {
                if (!transactionObj.idempotentKey) {
                    transactionObj.idempotentKey = Utils.generateIdempotentKey(new Date().toISOString(), JSON.stringify(transactionObj));
                }
                idempotentKeys.push(transactionObj.idempotentKey);
            }

            const idempotentKeyInUse = await this.getTransaction(organizationId, {idempotentKey: {$in: idempotentKeys}});
            if (idempotentKeyInUse) return Promise.reject(new CustomHttpError('duplicate idempotent keys in bulk', '400'));

            const result = await Transaction.insertMany(bulk, {...opts});

            Shoutout.submitTransaction(result);

            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async bulkUpdateTransactions(bulk, opts = {}) {
        try {
            const result = await Transaction.bulkWrite(bulk, { ...opts });
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async generateTransactionsFilter(organizationId, validatedObj) {
        try {
            const filter = await this.#getTransactionsAggregation(organizationId, validatedObj);
            return Promise.resolve(filter);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateTransaction(filter, dataObj, id, organizationId, callerId) {
        try {
            const result = await Transaction.findOneAndUpdate(
                {
                    _id: mongoose.Types.ObjectId(id),
                    organizationId,
                    ...filter
                },
                {
                    $set: {
                        ...dataObj,
                        updatedBy: mongoose.Types.ObjectId(callerId)
                    },
                    $currentDate: {updatedOn: true}
                }, {
                    upsert: false,
                    new: true,
                    returnOriginal: false
                }
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = TransactionsDAO;