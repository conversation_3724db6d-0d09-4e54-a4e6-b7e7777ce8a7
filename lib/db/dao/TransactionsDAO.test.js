const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const TransactionsDAO = require('./TransactionsDAO');

const mongoose = require('mongoose');

const organizationId = '6128e3537ed841e246e2e394';
const callerId = '613d08491df0e38df616f411';
describe('TransactionsDAO unit tests', () => {
    let createdObjectId;

    test('create transaction', async () => {
        const dataset = {
            regionId: mongoose.Types.ObjectId(),
            merchantId: mongoose.Types.ObjectId(),
            merchantLocationId: mongoose.Types.ObjectId(),
            memberId: mongoose.Types.ObjectId(),
            loyaltyId: '98462398479328',
            transactionOn: '2021-10-12T12:11:56.530Z',
            redeemablePoints: 10,
            tierPoints: 10,
            transactionAmount: 10,
            type: 'COLLECTION',
            subType: mongoose.Types.ObjectId(),
            importJobId: mongoose.Types.ObjectId(),
            productItems: [
                {
                    productId: '123',
                    productName: 'string',
                    productCategory: ['TestCat1', 'TestCat2', 'TestCat3'],
                    quantity: 10,
                    amount: 10
                }
            ],
            invoiceData: {
                invoiceId: 'string',
                invoiceDate: '2021-10-12T12:11:56.530Z',
                invoiceAmountWithTax: 10,
                invoiceAmountWithoutTax: 10,
                discountAmount: 10
            },
            pointRuleEvaluations: [
                {
                    pointRuleId: '6164291648d352d032b2a44c',
                    points: 10
                }
            ],
            status: 'VALID'
        };
        const createdObj = await TransactionsDAO.createTransaction(dataset, organizationId, callerId);
        createdObjectId = createdObj.id;
        expect(typeof createdObj).toEqual('object');
    });

    test.skip('get transactions', async () => {
        const result = await TransactionsDAO.getTransactions(organizationId, {
            skip: 0,
            limit: 10
        });
        expect(typeof result).toEqual('object');
    });
});
