const config = require('../../../config');
const logger = require('../../../logger');
const log = logger(config.logger);
const Member = require('../../schemas/member.schema');
//const Member2 = require('./../../models/member.model'); // TODO: REMOVE IF NOT NEEDED.
// const secretConfig = require('../../../../config'); // TODO: REMOVE IF NOT NEEDED.
const CustomHttpError = require('./../../../CustomHttpError');
const mongoose = require('mongoose');
const MongooseConnector = require('./../../connectors/MongooseConnector');
const { roundOffToTwoDecimals } = require('../../../utils/NumberUtils');

//not used for now

class MembersAnalyticsDAO {
    static async getMembersSeries(organizationId, regionId) {
        try {
            const conn = await MongooseConnector.getSecondaryConnection();
            const MemberModel = conn.model('Member', Member);
            return MemberModel.aggregate([
                {
                    $match: {
                        organizationId: mongoose.Types.ObjectId(organizationId),
                        regionId: mongoose.Types.ObjectId(regionId)
                    }
                },
                {
                    $group: {
                        _id: {
                            status: '$status'
                        },
                        total: {
                            $sum: 1
                        }
                    }
                }
            ]);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async runAggregation(aggregation, stream = false) {
        const conn = await MongooseConnector.getSecondaryConnection();
        const MemberModel = conn.model('Member', Member);
        if (stream) return MemberModel.aggregate(aggregation).allowDiskUse(true).cursor();
        return MemberModel.aggregate(aggregation).allowDiskUse(true);
    }

    static async getRecordsCount(organizationId, filters) {
        try {
            const conn = await MongooseConnector.getSecondaryConnection();
            const MemberModel = conn.model('Member', Member);
            const result = await MemberModel.countDocuments({
                organizationId: mongoose.Types.ObjectId(organizationId),
                ...filters
            });
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async runAggregationWithDateRange(aggregation, dateFrom, dateTo, streamAggregation) {
        try {
            const filter = {};
            if (dateFrom && dateTo) {
                const tDx = new Date(dateTo);
                tDx.setHours(23, 59);
                filter['registeredOn'] = {
                    $gte: new Date(dateFrom),
                    $lte: tDx
                };
            } else if (dateFrom) {
                filter['registeredOn'] = {
                    $gte: new Date(dateFrom)
                };
            } else if (dateTo) {
                const tDy = new Date(dateTo);
                tDy.setHours(23, 59);
                filter['registeredOn'] = {
                    $lte: tDy
                };
            }
            const conn = await MongooseConnector.getSecondaryConnection();
            const MemberModel = conn.model('Member', Member);
            if (Object.keys(filter).length > 0) {
                aggregation = [
                    {
                        $match: filter
                    }
                ].concat(aggregation);
            }
            if (streamAggregation) return MemberModel.aggregate(aggregation).cursor();
            return MemberModel.aggregate(aggregation);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMemberById(organizationId, id, fields, filters = {}) {
        try {
            const filter = { organizationId: mongoose.Types.ObjectId(organizationId), ...filters };

            if (id) {
                filter['_id'] = mongoose.Types.ObjectId(id);
            }

            const conn = await MongooseConnector.getSecondaryConnection();
            const MemberModel = conn.model('Member', Member);
            const foundMember = await MemberModel.findOne(filter, fields).lean();

            if (!foundMember) {
                throw new CustomHttpError('member not found', '404');
            }

            const pointsRoundedOfResult = Object.assign(foundMember, {
                points: roundOffToTwoDecimals(foundMember?.points ? Number(foundMember?.points) : 0),
                tierPoints: roundOffToTwoDecimals(foundMember?.tierPoints || 0)
            });

            return Promise.resolve(pointsRoundedOfResult);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = MembersAnalyticsDAO;
