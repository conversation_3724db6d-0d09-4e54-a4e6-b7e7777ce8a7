const config = require('../../../config');
const logger = require('../../../logger');
const log = logger(config.logger);
const Reward = require('../../schemas/reward.schema');
const mongoose = require('mongoose');
const MongooseConnector = require('./../../connectors/MongooseConnector');

class RewardsAnalyticsDAO {

    static async getTopRewards(organizationId, validatedObj) {
        try {
            const filter = {
                organizationId: mongoose.Types.ObjectId(organizationId),
                usedCount: {
                    $gt: 0
                }
            };
            if (validatedObj.dateFrom && validatedObj.dateTo) {
                const tDx = new Date(validatedObj.dateTo);
                tDx.setHours(23, 59);
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.dateFrom),
                    $lte: tDx
                };
            } else if (validatedObj.dateFrom) {
                filter['createdOn'] = {
                    $gte: new Date(validatedObj.dateFrom)
                };
            } else if (validatedObj.dateTo) {
                const tDy = new Date(validatedObj.dateTo);
                tDy.setHours(23, 59);
                filter['createdOn'] = {
                    $lte: tDy
                };
            }

            if (validatedObj.regionIds) {
                filter['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantIds) {
                filter['merchantId'] = { $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }

            const conn = await MongooseConnector.getSecondaryConnection();
            const RewardModel = conn.model('Reward', Reward);
            return RewardModel.find(filter, {
                name: 1,
                description: 1,
                imageUrls: 1,
                usedCount: 1,
                claimedCount: 1
            }).sort({usedCount: "desc"}).skip(validatedObj.skip).limit(validatedObj.limit);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

}

module.exports = RewardsAnalyticsDAO;
