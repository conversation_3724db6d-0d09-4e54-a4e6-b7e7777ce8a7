'use strict';

module.exports = {
    async up(queryInterface) {
        // Points Summary Trigger
        await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION update_points_summary_on_insert()
      RETURNS TRIGGER AS $$
      BEGIN
          INSERT INTO points_summary_by_type (
              "pointsIssued", "pointsBurned", "expirePoints", "pointsRedeemed",
              "pointsAdjustmentsPlus", "pointsAdjustmentsMinus", date,
              "organizationId", "regionId", "createdAt"
          )
          VALUES (
              CASE 
                  WHEN NEW."signedRedeemablePoints" > 0 AND (NEW."type" = 'COLLECTION' OR NEW."type" = 'ADJUSTMENT') THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."signedRedeemablePoints" < 0 AND (NEW."type" = 'REDEMPTION' OR NEW."type" = 'ADJUSTMENT') AND NEW."isExpiryPoints" = false THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."isExpiryPoints" = true THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."signedRedeemablePoints" < 0 AND NEW."type" = 'REDEMPTION' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."signedRedeemablePoints" > 0 AND NEW."type" = 'ADJUSTMENT' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."signedRedeemablePoints" < 0 AND NEW."type" = 'ADJUSTMENT' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              DATE(NEW."transactionOn"),
              NEW."organizationId",
              NEW."regionId",
              CURRENT_DATE
          )
          ON CONFLICT ("organizationId", "regionId", date)
          DO UPDATE SET
              "pointsIssued" = points_summary_by_type."pointsIssued" + EXCLUDED."pointsIssued",
              "pointsBurned" = points_summary_by_type."pointsBurned" + EXCLUDED."pointsBurned",
              "expirePoints" = points_summary_by_type."expirePoints" + EXCLUDED."expirePoints",
              "pointsRedeemed" = points_summary_by_type."pointsRedeemed" + EXCLUDED."pointsRedeemed",
              "pointsAdjustmentsPlus" = points_summary_by_type."pointsAdjustmentsPlus" + EXCLUDED."pointsAdjustmentsPlus",
              "pointsAdjustmentsMinus" = points_summary_by_type."pointsAdjustmentsMinus" + EXCLUDED."pointsAdjustmentsMinus";
      
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS update_points_summary_trigger ON transactions;
      CREATE TRIGGER update_points_summary_trigger
      AFTER INSERT ON transactions
      FOR EACH ROW
      EXECUTE FUNCTION update_points_summary_on_insert();
    `);

        // Points Overview Trigger
        await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION update_points_overview_on_insert()
      RETURNS TRIGGER AS $$
      BEGIN
          INSERT INTO points_overview (
              "pointsCollected", "pointsRedeemed", "pointsAdjustmentsPlus",
              "pointsAdjustmentsMinus", date, "organizationId", "regionId",
              "merchantId", "merchantLocationId", "createdAt"
          )
          VALUES (
              CASE 
                  WHEN NEW."type" = 'COLLECTION' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."type" = 'REDEMPTION' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."signedRedeemablePoints" > 0 AND NEW."type" = 'ADJUSTMENT' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              CASE 
                  WHEN NEW."signedRedeemablePoints" < 0 AND NEW."type" = 'ADJUSTMENT' THEN NEW."redeemablePoints"
                  ELSE 0
              END,
              DATE(NEW."transactionOn"),
              NEW."organizationId",
              NEW."regionId",
              NEW."merchantId",
              NEW."merchantLocationId",
              CURRENT_DATE
          )
          ON CONFLICT ("organizationId", "regionId", "merchantId", "merchantLocationId", date)
          DO UPDATE SET
              "pointsCollected" = points_overview."pointsCollected" + EXCLUDED."pointsCollected",
              "pointsRedeemed" = points_overview."pointsRedeemed" + EXCLUDED."pointsRedeemed",
              "pointsAdjustmentsPlus" = points_overview."pointsAdjustmentsPlus" + EXCLUDED."pointsAdjustmentsPlus",
              "pointsAdjustmentsMinus" = points_overview."pointsAdjustmentsMinus" + EXCLUDED."pointsAdjustmentsMinus";
      
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS update_points_overview_trigger ON transactions;
      CREATE TRIGGER update_points_overview_trigger
      AFTER INSERT ON transactions
      FOR EACH ROW
      EXECUTE FUNCTION update_points_overview_on_insert();
    `);

        // Points Summary By Sub Type Trigger
        await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION update_points_sub_type_summary_on_insert()
      RETURNS TRIGGER AS $$
      BEGIN
          INSERT INTO points_summary_by_sub_type (
              "type", "subType", "totalPoints", date, "organizationId",
              "regionId", "merchantId", "merchantLocationId", "createdAt"
          )
          VALUES (
              CASE 
                  WHEN NEW."type" = 'COLLECTION' OR NEW."type" = 'REDEMPTION' THEN NEW."type"
                  WHEN NEW."type" = 'ADJUSTMENT' AND NEW."signedRedeemablePoints" < 0 THEN 'ADJUSTMENT MINUS'
                  WHEN NEW."type" = 'ADJUSTMENT' AND NEW."signedRedeemablePoints" > 0 THEN 'ADJUSTMENT PLUS'
                  ELSE 'NONE'
              END, 
              NEW."subType", 
              NEW."signedRedeemablePoints",
              DATE(NEW."transactionOn"),
              NEW."organizationId",
              NEW."regionId",
              NEW."merchantId",
              NEW."merchantLocationId",
              CURRENT_DATE
          )
          ON CONFLICT ("organizationId", "regionId", "merchantId", "merchantLocationId", "type", "subType", date)
          DO UPDATE SET
              "totalPoints" = points_summary_by_sub_type."totalPoints" + EXCLUDED."totalPoints";
      
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS update_sub_type_summary_trigger ON transactions;
      CREATE TRIGGER update_sub_type_summary_trigger
      AFTER INSERT ON transactions
      FOR EACH ROW
      EXECUTE FUNCTION update_points_sub_type_summary_on_insert();
    `);
    },

    async down(queryInterface) {
        // Remove triggers first, then functions
        await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS update_points_summary_trigger ON transactions;
      DROP TRIGGER IF EXISTS update_points_overview_trigger ON transactions;
      DROP TRIGGER IF EXISTS update_sub_type_summary_trigger ON transactions;

      DROP FUNCTION IF EXISTS update_points_summary_on_insert();
      DROP FUNCTION IF EXISTS update_points_overview_on_insert();
      DROP FUNCTION IF EXISTS update_points_sub_type_summary_on_insert();
    `);
    }
};
