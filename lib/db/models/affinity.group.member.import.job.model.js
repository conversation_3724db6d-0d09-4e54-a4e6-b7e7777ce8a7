'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const FieldMappingSchema = require('./embedded/affinity.group.member.import.field.mapping.schema');
const { STATUS } = require('./enums/affinity.group.member.import.job.enums');

const AffinityGroupMemberImportJobSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantId: { type: mongoose.Types.ObjectId, required: false },
    affinityGroupId: { type: mongoose.Types.ObjectId, required: true },
    startedOn: { type: Date, required: false },
    completedOn: { type: Date, required: false },
    totalRecordsCount: { type: Number, required: false },
    processedRecordsCount: { type: Number, required: false, default: 0 },
    successRecordsCount: { type: Number, required: false, default:0 },
    failedRecordsCount: { type: Number, required: false, default: 0 },
    fileName: { type: String, required: true },
    fileId: { type: String, required: true },
    fieldMappings: { type: [FieldMappingSchema], required: true },
    status: { type: String, required: false, enum: Object.keys(STATUS), default: STATUS.PENDING },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

AffinityGroupMemberImportJobSchema.index({ organizationId: 1, regionId: 1 } );
AffinityGroupMemberImportJobSchema.index({ 'fileName': 'text' });

module.exports = mongoose.model('AffinityGroupMemberImportJob', AffinityGroupMemberImportJobSchema, 'affinity_group_member_import_jobs');
