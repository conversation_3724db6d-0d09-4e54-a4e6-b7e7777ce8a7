'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { STATUS } = require('./enums/affinity.group.enums');

const AffinityGroupSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    name: { type: String, required: true },
    description: { type: String, required: false },
    imageUrl: { type: String, required: false },
    benefits: {type: [{type: String, required: true}], required: true},
    membersCount: { type: Number, required: false, default: 0 },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId },
    status: { type: String, required: true, enum: Object.keys(STATUS), default: STATUS.ENABLED }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

AffinityGroupSchema.index({ organizationId: 1, regionId: 1 });
AffinityGroupSchema.index({ _id: 'text', name: 'text' });

module.exports = mongoose.model('AffinityGroup', AffinityGroupSchema, 'affinity_groups');
