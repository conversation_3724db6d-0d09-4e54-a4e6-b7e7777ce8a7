'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { CARD_DISTRIBUTION_JOB_STATUS, CARD_TYPES } = require('./enums/card.enums');
const History = require('./embedded/history.schema');
const CardRange = require('./embedded/card.range.schema');

const CardDistributionJobSchema = new Schema({
    regionId: { type: mongoose.Types.ObjectId, required: true },
    organizationId: { type: mongoose.Types.ObjectId , required: true },
    merchantId: { type: mongoose.Types.ObjectId, required: true },
    merchantLocationId: { type: mongoose.Types.ObjectId, required: true },
    status: {
        type: String,
        required: true,
        default: CARD_DISTRIBUTION_JOB_STATUS.PROCESSING,
        enum: [
            CARD_DISTRIBUTION_JOB_STATUS.PROCESSING,
            CARD_DISTRIBUTION_JOB_STATUS.DISPATCHED,
            CARD_DISTRIBUTION_JOB_STATUS.COMPLETED,
            CARD_DISTRIBUTION_JOB_STATUS.CANCELLED,
            CARD_DISTRIBUTION_JOB_STATUS.FAILED
        ]
    },
    jobType: {
        type: String,
        required: true,
        enum: [
          CARD_TYPES.KEY_TAG,
          CARD_TYPES.REGULAR_CARD,
          CARD_TYPES.REGULAR_CARD_AND_KEY_TAG
        ],
    },
    cardRanges: { type: [CardRange], required: true },
    quantity: { type: Number, required: true, default: 0 },
    batchId: { type: String, required: true },
    note: { type: String, required: false },
    historyEvents: {
        type: [History]
    },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

CardDistributionJobSchema.virtual('merchant', {
    ref: 'Merchant',
    localField: 'merchantId',
    foreignField: '_id',
    justOne: true
});
CardDistributionJobSchema.virtual('merchantLocation', {
    ref: 'MerchantLocation',
    localField: 'merchantLocationId',
    foreignField: '_id',
    justOne: true
});

CardDistributionJobSchema.index({ 'batchId': 'text', 'note': 'text' });

module.exports = mongoose.model('CardDistributionJob', CardDistributionJobSchema, 'card_distribution_jobs');
