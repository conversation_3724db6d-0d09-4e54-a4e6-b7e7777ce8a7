'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { CARD_BATCH_JOB_STATUS, CARD_TYPES } = require('./enums/card.enums');
const History = require('./embedded/history.schema');

const CardPrintJobSchema = new Schema({
    regionId: { type: mongoose.Types.ObjectId, required: true },
    organizationId: { type: mongoose.Types.ObjectId , required: true },
    startingCardNo: { type: Number, required: false },
    endingCardNo: { type: Number, required: false },
    quantity: { type: Number, required: true, default: 0 },
    embossCardIds: [mongoose.Types.ObjectId],
    note: { type: String, required: false },
    status: {
        type: String,
        required: true,
        enum: [CARD_BATCH_JOB_STATUS.PENDING, CARD_BATCH_JOB_STATUS.PRINTING, CARD_BATCH_JOB_STATUS.PRINTED, CARD_BATCH_JOB_STATUS.DISPATCHED, CARD_BATCH_JOB_STATUS.COMPLETED, CARD_BATCH_JOB_STATUS.FAILED]
    },
    jobType: {
        type: String,
        required: true,
        enum: [CARD_TYPES.DIGITAL_CARD, CARD_TYPES.EMBOSSED_CARD, CARD_TYPES.KEY_TAG, CARD_TYPES.REGULAR_CARD, CARD_TYPES.REGULAR_CARD_AND_KEY_TAG]
    },
    batchId: { type: String, required: true },
    historyEvents: {
        type: [History]
    },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

CardPrintJobSchema.index({ 'batchId': 'text', 'note': 'text' });

module.exports = mongoose.model('CardPrintJob', CardPrintJobSchema, 'card_print_jobs');
