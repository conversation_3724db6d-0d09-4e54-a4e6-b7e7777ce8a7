'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { CARD_REQUEST_STATUS } = require('./../../constants/Constants');
const ObjectId = require('mongoose').Types.ObjectId;

const CardRequestSchema = new Schema({
    ownerId: { type: String, required: true },
    loyaltyId: { type: String, required: true },
    status: {
        type: String,
        required: true,
        enum: [CARD_REQUEST_STATUS.NEW_REQUEST, CARD_REQUEST_STATUS.PRINTING, CARD_REQUEST_STATUS.PRINTED, CARD_REQUEST_STATUS.POSTED, CARD_REQUEST_STATUS.RETURNED]
    },
    userData: {
        type: {
            name: { type: String },
            mobileNumber: { type: String },
            email: { type: String },
            address: { type: String }
        },
        required: true
    },
    historyEvents: {
        type: [{
            eventDate: { type: Date },
            eventDetails: { type: String },
            eventBy: { type: String }
        }]
    },
    notes: { type: String },
    printBatchId: { type: ObjectId },
    createdBy: { type: String, required: true },
    modifiedBy: { type: String },
    printedOn: { type: Date },
    postedOn: { type: Date },
    returnedOn: { type: Date }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'modifiedOn' } });

module.exports = mongoose.model('CardRequest', CardRequestSchema, 'card_requests');