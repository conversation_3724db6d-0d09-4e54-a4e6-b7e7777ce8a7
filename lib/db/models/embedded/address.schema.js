'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AddressSchema = new Schema({
    line1: {type: String, required: false},
    line2: {type: String, required: false},
    line3: {type: String, required: false},
    city: {type: String, required: false},
    stateOrProvince: {type: String, required: false},
    zipOrPostcode: {type: String, required: false},
}, {timestamps: false, _id: false});


module.exports = AddressSchema;
