'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const Address = require('./address.schema');

const ContactSchema = new Schema({
    name: {type: String, required: false},
    mobileNumber: {type: String, required: false},
    email: {type: String, required: false},
    address: {type: Address, required: false},
}, {timestamps: false, _id: false});


module.exports = ContactSchema;
