'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { CARD_TYPES } = require('../enums/card.enums');

const EmbossCardSchema = new Schema({
    embossRequestedOn: { type: Date },
    embossIssuedOn: { type: Date },
    merchantId: { type: mongoose.Types.ObjectId, required: true },
    merchantLocationId: { type: mongoose.Types.ObjectId, required: true },
    printedName: { type: String, required: true },
    requestedBy: { type: mongoose.Types.ObjectId },
    previousCardType: {
        type: String,
        enum: [CARD_TYPES.DIGITAL_CARD, CARD_TYPES.KEY_TAG, CARD_TYPES.REGULAR_CARD, CARD_TYPES.REGULAR_CARD_AND_KEY_TAG]
    },
    previousPrintJobId: { type: mongoose.Types.ObjectId },
    previousPrintJobNumber: { type: String }
}, {timestamps: false, _id: false});


module.exports = EmbossCardSchema;
