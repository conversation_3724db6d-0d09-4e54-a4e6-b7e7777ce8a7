'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { SYSTEM_ATTRIBUTE_NAME } = require('../enums/transaction.import.job.enums');

const FieldMappingSchema = new Schema({
    fileColumnName: { type: String, required: true },
    systemAttributeName: { type: String, required: true, enum: Object.keys(SYSTEM_ATTRIBUTE_NAME) }
}, { timestamps: false });


module.exports = FieldMappingSchema;
