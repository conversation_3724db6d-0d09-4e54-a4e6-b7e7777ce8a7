'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const GlobalTierConfigurationSchema = new Schema({
    tierCalculationWindow: { type: Number, required: true, min: 1 },
    tierCalculationSubTransactionTypeIds: { type: [mongoose.Types.ObjectId], required: true },
    jobEnabled: { type: Boolean, required: true, default: false },
    jobFrequency: { type: String, required: false }
}, { timestamps: false, _id: false });

module.exports = GlobalTierConfigurationSchema;
