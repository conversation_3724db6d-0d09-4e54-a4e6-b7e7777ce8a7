'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const IdpMetadataSchema = new Schema({
    realm:{ type: String, required: true },
    clientId:{ type: String, required: true },
    clientSecret:{ type: String, required: true },
    certUrl:{ type: String, required: true },
    issuer:{ type: String, required: true },
    audience:{ type: String, required: true }
}, { timestamps: false, _id: false });

module.exports = IdpMetadataSchema;
