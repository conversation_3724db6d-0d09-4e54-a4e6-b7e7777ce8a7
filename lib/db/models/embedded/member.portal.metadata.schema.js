'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { Platform } = require('./../enums/member.portal.metatda.enums');

const MemberPortalMetadataSchema = new Schema({
    username: { type: String, required: false },
    userId: { type: String, required: false },
    platforms: { type: [String], required: false, enum: Object.values(Platform) },
    mobileApp: { type: Boolean, required: false },
    lastAccessedOn: { type: Date, required: false },
}, { timestamps: false, _id: false });


module.exports = MemberPortalMetadataSchema;
