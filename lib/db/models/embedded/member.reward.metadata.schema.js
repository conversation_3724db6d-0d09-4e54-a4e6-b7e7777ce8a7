'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const MemberRewardMetadataSchema = new Schema({
    rewardId: { type: mongoose.Types.ObjectId, required: true },
    refNumber: { type: String, required: false },
    refName: { type: String, required: false },
    notes: { type: String, required: false }
}, { timestamps: false, _id: false });


module.exports = MemberRewardMetadataSchema;
