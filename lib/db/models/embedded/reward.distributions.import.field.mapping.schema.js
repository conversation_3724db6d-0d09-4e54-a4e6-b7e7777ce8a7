'use strict';
const mongoose = require('mongoose');
const { SYSTEM_ATTRIBUTE_NAME } = require('../enums/reward.distributions.import.job.enums');

const Schema = mongoose.Schema;

const RewardDistributionsImportFieldMappingSchema = new Schema({
    fileColumnName: { type: String, required: true },
    systemAttributeName: { type: String, required: true, enum: Object.keys(SYSTEM_ATTRIBUTE_NAME) }
}, { timestamps: false });


module.exports = RewardDistributionsImportFieldMappingSchema;
