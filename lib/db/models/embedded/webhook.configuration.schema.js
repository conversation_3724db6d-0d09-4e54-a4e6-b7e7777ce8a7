'use strict';

const mongoose = require('mongoose');

const Schema = mongoose.Schema;

const MemberStatusUpdateJobSchema = {
    status: { type: Boolean, required: false, default: false },
    description: { type: String, required: false, default: '' }
};

const WebhookConfigurationSchema = new Schema(
    { memberStatusUpdateJob: MemberStatusUpdateJobSchema },
    { timestamps: false, _id: false }
);

module.exports = WebhookConfigurationSchema;
