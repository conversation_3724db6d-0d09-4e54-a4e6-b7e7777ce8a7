'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const MemberNoteSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    memberId: { type: String, required: true }, //{ type: mongoose.Types.ObjectId, required: true },
    content: { type: String, required: true, maxlength: 500 },
    createdBy: { type: String, required: true },
    updatedBy: { type: String, required: false },
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });


module.exports = mongoose.model('MemberNote', MemberNoteSchema, 'member_notes');
