'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const Contact = require('./embedded/contact.schema');
const LoyaltyOptions = require('./embedded/loyaltyOptions.schema');
const { Status, Type } = require('./enums/merchant.enums');

const MerchantSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantName: { type: String, required: true },
    merchantLogoImageUrl: { type: String, required: false },
    contact: { type: Contact, required: false },
    status: { type: String, required: false, enum: Object.keys(Status) },
    type: { type: String, required: false, enum: Object.keys(Type) },
    locationsCount: { type: Number, required: false, min: 0, default: 0 },
    countryISO2Code: { type: String, required: false },
    countryName:{ type: String, required: false },
    billingContacts: { type: [Contact], required: false },
    technicalContacts: { type: [Contact], required: false },
    businessRegistrationNumber: { type: String, required: false },
    options: { type: LoyaltyOptions, required: false },
    code: { type: String, required: false },
    visibleForNearestLocations: { type: Boolean, required: true, default: false },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' },
    toObject: { virtuals: true },
    toJSON: { virtuals: true }
});

MerchantSchema.index({ organizationId: 1, regionId: 1 });
MerchantSchema.index({ merchantName: 'text', 'contact.address': 'text' });

MerchantSchema.virtual('pointRules', {
    ref: 'PointRule',
    localField: '_id',
    foreignField: 'merchantId'
});

module.exports = mongoose.model('Merchant', MerchantSchema, 'merchants');
