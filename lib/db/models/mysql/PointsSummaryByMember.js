const Sequelize = require('sequelize');
module.exports = (sequelize, DataTypes) => {
    return sequelize.define(
        'PointsSummaryByMember',
        {
            id: {
                type: DataTypes.UUID,
                defaultValue: Sequelize.UUIDV1,
                primaryKey: true
            },
            organizationId: {
                type: DataTypes.STRING(50),
                allowNull: false
            },
            regionId: {
                type: DataTypes.STRING(50),
                allowNull: false
            },
            memberId: {
                type: DataTypes.STRING(50),
                allowNull: false
            },
            date: {
                type: DataTypes.DATEONLY,
                allowNull: false
            },
            transactionCount: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            redemptionCount: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            collectionsCount: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            collectionSum: {
                type: DataTypes.DOUBLE(10, 2),
                allowNull: false
            },
            redemptionSum: {
                type: DataTypes.DOUBLE(10, 2),
                allowNull: false
            },
            totalBillValue: {
                type: DataTypes.DOUBLE(10, 2),
                allowNull: false
            }
        },
        {
            tableName: 'points_summary_by_member',
            timestamps: false,
            indexes: [
                {
                    name: 'points_summary_by_member_index',
                    fields: ['organizationId', 'regionId', 'memberId', 'date'],
                    unique: true
                }
            ]
        }
    );
};
