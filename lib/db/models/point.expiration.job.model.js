'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const PointExpirationJobSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    executionStartTime: { type: Date, required: false },
    executionEndTime: { type: Date, required: false },
    membersToBeUpdated: { type: Number, required: true, default: 0 },
    updatedMembersCount: { type: Number, required: true, default: 0 },
    totalPointsExpired: { type: Number, required: true, default: 0 }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

module.exports = mongoose.model('PointExpirationJob', PointExpirationJobSchema, 'point_expiration_jobs');
