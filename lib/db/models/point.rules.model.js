'use strict'; 
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { TYPE, SUB_TYPE, STATUS, RECURRENCE, RULE_STATE } = require('./enums/point.rule.enums');
const RuleDataSchema = require('./embedded/rule.data.schema');
const moment = require('moment');
const config = require('../../config');
const logger = require('../../logger');
const log = logger(config.logger);

const PointRulesSchema = new Schema(
    {
        organizationId: { type: mongoose.Types.ObjectId, required: true },
        regionId: { type: mongoose.Types.ObjectId, required: true },
        merchantId: {
            type: mongoose.Types.ObjectId,
            required: () => {
                return this.type === TYPE.TRANSACTIONAL;
            }
        },
        name: { type: String, required: true },
        description: { type: String, required: false },
        type: { type: String, required: true, enum: Object.keys(TYPE) },
        subType: { type: String, required: true, enum: Object.keys(SUB_TYPE) },
        ruleData: { type: RuleDataSchema },
        maxPoints: { type: Number, required: false, default: null },
        createdBy: { type: mongoose.Types.ObjectId, required: true },
        updatedBy: { type: mongoose.Types.ObjectId, required: false },
        status: { type: String, required: true, enum: Object.keys(STATUS), default: STATUS.ENABLED },
        matchedCount: { type: Number, required: true, default: 0 }
    },
    {
        timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' },
        toObject: { virtuals: true },
        toJSON: { virtuals: true }
    }
);

PointRulesSchema.virtual('ruleState').get(function () {
    if ((this.subType === SUB_TYPE.SEASONAL || this.subType === SUB_TYPE.LOCATION) && this.status === STATUS.ENABLED) {
        const dayOfTheWeek = moment().format('dddd').toUpperCase();

        function checkDayOfTheWeekValidity(ruleData) {
            if (ruleData.daysOfWeek.includes(dayOfTheWeek)) {
                return RULE_STATE.ACTIVE;
            }
            return RULE_STATE.SCHEDULED;
        }

        try {
            if (this.ruleData.recurrence === RECURRENCE.FOREVER) {
                return checkDayOfTheWeekValidity(this.ruleData);
            } else if (moment(this.ruleData.fixedFromDate).isAfter(moment())) {
                return RULE_STATE.SCHEDULED;
            } else if (moment(this.ruleData.fixedToDate).isBefore(moment())) {
                return RULE_STATE.EXPIRED;
            } else if (this.ruleData.recurrence === RECURRENCE.NONE) {
                return RULE_STATE.ACTIVE;
            } else {
                return checkDayOfTheWeekValidity(this.ruleData);
            }
        } catch (e) {
            log.error(e);
        }
    } else {
        return this.status === STATUS.ENABLED ? RULE_STATE.ACTIVE : RULE_STATE.INACTIVE;
    }
});

PointRulesSchema.virtual('merchant', {
    ref: 'Merchant',
    localField: 'merchantId',
    foreignField: '_id',
    justOne: true
});

PointRulesSchema.virtual('merchantLocation', {
    ref: 'MerchantLocation',
    localField: 'ruleData.merchantLocationId',
    foreignField: '_id',
    justOne: true
});

module.exports = mongoose.model('PointRule', PointRulesSchema, 'point_rules');
