'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { STATUS } = require('./enums/reward.distribution.job.enums');
const HistorySchema = require('./embedded/history.schema');

const RewardDistributionJobSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantId: { type: mongoose.Types.ObjectId, required: false },
    rewardId: { type: mongoose.Types.ObjectId },
    batchId: { type: String, required: true },
    itemCount: {type: Number, required: true, default: 0},
    status: { type: String, required: true, enum: Object.keys(STATUS), default: STATUS.PROCESSING },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId },
    historyEvents: { type: [HistorySchema], required: false },
    successCount: {type: Number, required: true, default: 0},
    failedCount: {type: Number, required: true, default: 0}
}, {timestamps: {createdAt: 'createdOn', updatedAt: 'updatedOn'}});

RewardDistributionJobSchema.index({ 'batchId': 'text' });
RewardDistributionJobSchema.index({ organizationId: 1, regionId: 1 });

module.exports = mongoose.model('RewardDistributionJob', RewardDistributionJobSchema, 'reward_distribution_jobs');

