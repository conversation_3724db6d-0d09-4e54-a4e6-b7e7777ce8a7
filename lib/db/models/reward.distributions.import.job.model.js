'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const RewardDistributionsImportFieldMappingSchema = require('./embedded/reward.distributions.import.field.mapping.schema');
const { STATUS } = require('./enums/reward.distributions.import.job.enums');

const RewardDistributionsImportJobSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    distributionJobId: { type: mongoose.Types.ObjectId, required: true },
    startedOn: { type: Date, required: false },
    completedOn: { type: Date, required: false },
    totalRecordsCount: { type: Number, required: false },
    processedRecordsCount: { type: Number, required: false, default: 0 },
    successRecordsCount: { type: Number, required: false, default:0 },
    failedRecordsCount: { type: Number, required: false, default: 0 },
    fileName: { type: String, required: true },
    fileId: { type: String, required: true },
    fieldMappings: { type: [RewardDistributionsImportFieldMappingSchema], required: true },
    importActionName: { type: String, required: true },
    status: { type: String, required: false, enum: Object.keys(STATUS), default: STATUS.PENDING },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

RewardDistributionsImportJobSchema.index({ organizationId: 1, regionId: 1 } );
RewardDistributionsImportJobSchema.index({ 'fileName': 'text' });

module.exports = mongoose.model('RewardDistributionsImportJob', RewardDistributionsImportJobSchema, 'reward_distributions_import_jobs');
