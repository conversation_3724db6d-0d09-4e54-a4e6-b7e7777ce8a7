'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const SegmentCategorySchema = new Schema(
    {
        organizationId: { type: mongoose.Types.ObjectId, required: true },
        regionId: { type: mongoose.Types.ObjectId, required: true },
        name: { type: String, required: true },
        iconUrl: { type: String, required: true },
        isEditable: { type: Boolean, required: true, default: false },
        createdBy: { type: mongoose.Types.ObjectId, required: true },
        updatedBy: { type: mongoose.Types.ObjectId, required: false }
    },
    { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } }
);

SegmentCategorySchema.virtual('segments', {
    ref: 'Segment',
    localField: '_id',
    foreignField: 'categoryId'
});

module.exports = mongoose.model('SegmentCategory', SegmentCategorySchema, 'segment_categories');
