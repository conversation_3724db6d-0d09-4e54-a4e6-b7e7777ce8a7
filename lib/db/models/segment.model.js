'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const SegmentSchema = new Schema(
    {
        organizationId: { type: mongoose.Types.ObjectId, required: true },
        regionId: { type: mongoose.Types.ObjectId, required: true },
        name: { type: String, required: true },
        memberFilter: { type: mongoose.Mixed, required: false },
        transactionFilter: { type: mongoose.Mixed, required: false },
        categoryId: { type: mongoose.Types.ObjectId, ref: 'Category', required: true },
        createdBy: { type: mongoose.Types.ObjectId, required: false },
        updatedBy: { type: mongoose.Types.ObjectId, required: false }
    },
    { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } }
);
SegmentSchema.index({ name: 'text' });
module.exports = mongoose.model('Segment', SegmentSchema, 'segments');
