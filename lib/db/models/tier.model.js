'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { STATUS } = require('./enums/tier.enums');

const TiersSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    name: {type: String, required: true},
    imageUrl: {type: String, required: true},
    benefits: {type: [{type: String, required: true}], required: true},
    orderCount: {type: Number, required: false, default: 0},
    points: {type: Number, required: false, default: 0},
    membersCount: {type: Number, required: true, default: 0},
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId },
    status: { type: String, required: true, enum: Object.keys(STATUS),default: STATUS.ENABLED }
}, {timestamps: {createdAt: 'createdOn', updatedAt: 'updatedOn'}});

TiersSchema.index({ organizationId: 1, regionId: 1, points: 1 });

module.exports = mongoose.model('Tier', TiersSchema, 'tiers');
