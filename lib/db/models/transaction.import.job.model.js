'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const FieldMappingSchema = require('./embedded/field.mapping.schema');
//const FilePartSchema = require('./embedded/file.part.schema');
const { STATUS, MERCHANT_LOCATION_SELECTION, TRANSACTION_TYPE } = require('./enums/transaction.import.job.enums');

const TransactionImportJobSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantId: { type: mongoose.Types.ObjectId, required: true },
    merchantLocationSelection: { type: String, required: true, enum: Object.keys(MERCHANT_LOCATION_SELECTION) },
    merchantLocationId: { type: mongoose.Types.ObjectId },
    merchantLocationColumnName: { type: String },
    startedOn: { type: Date, required: false },
    completedOn: { type: Date, required: false },
    totalRecordsCount: { type: Number, required: false },
    processedRecordsCount: { type: Number, required: false, default: 0 },
    successRecordsCount: { type: Number, required: false, default:0 },
    failedRecordsCount: { type: Number, required: false, default: 0 },
    transactionType: { type: String, required: true, enum: Object.keys(TRANSACTION_TYPE) },
    transactionSubTypeId: { type: mongoose.Types.ObjectId, required: true},
    fileId: { type: String, required: true },
    //fileParts: { type: [FilePartSchema], required: true },
    fieldMappings: { type: [FieldMappingSchema], required: true },
    status: { type: String, required: false, enum: Object.keys(STATUS), default: STATUS.PENDING },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId }
}, { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } });

TransactionImportJobSchema.virtual('merchant', {
    ref: 'Merchant',
    localField: 'merchantId',
    foreignField: '_id',
    justOne: true
});

TransactionImportJobSchema.virtual('merchantLocation', {
    ref: 'MerchantLocation',
    localField: 'merchantLocationId',
    foreignField: '_id',
    justOne: true
});

module.exports = mongoose.model('TransactionImportJob', TransactionImportJobSchema, 'transaction_import_jobs');
