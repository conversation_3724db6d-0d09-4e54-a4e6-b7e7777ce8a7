'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const CardStockView = new Schema({
    totalCount: { type: Number },
    activeCardsCount: { type: Number },
    assignedCardsCount: { type: Number },
    suspendedCardsCount: { type: Number },
    deactivatedCardsCount: { type: Number },
    type: { type: String },
    merchantLocationId: { type: mongoose.Types.ObjectId },
    merchantLocationName: { type: String },
    merchantId: { type: mongoose.Types.ObjectId },
    merchantName: { type: String },
    regionId: { type: mongoose.Types.ObjectId },
    organizationId: { type: mongoose.Types.ObjectId }
})

module.exports = mongoose.model('CardStock', CardStockView, 'cards_stock');
