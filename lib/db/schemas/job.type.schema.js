'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { STATUS } = require('./../models/enums/common.enums');
const { JOB_TYPES } = require('../models/enums/job.types.enums');

const JobTypeSchema = new Schema(
    {
        organizationId: { type: mongoose.Types.ObjectId, required: true },
        name: { type: String, required: true },
        description: { type: String, required: false },
        queueName: { type: String, required: true },
        metadata: { type: Schema.Types.Mixed, required: true },
        status: { type: String, required: true, enum: Object.keys(STATUS), default: STATUS.DISABLED },
        updatedBy: { type: mongoose.Types.ObjectId, required: false },
        type: { type: String, enum: Object.keys(JOB_TYPES), required: false } //Set required false in order to backward support for the existing data
    },
    { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } }
);

JobTypeSchema.index({ organizationId: 1 });
JobTypeSchema.index({ name: 'text', description: 'text' });

module.exports = JobTypeSchema;
