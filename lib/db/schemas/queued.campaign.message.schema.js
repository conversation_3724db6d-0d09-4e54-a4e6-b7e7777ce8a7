'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const MessageSchema = new Schema({
    messageBody: { type: String, required: true },
    messageSubject: { type: String, required: false }
});

const QueuedCampaignMessageSchema = new Schema(
    {
        organizationId: { type: mongoose.Types.ObjectId, required: true },
        regionId: { type: mongoose.Types.ObjectId, required: true },
        memberId: { type: mongoose.Types.ObjectId, required: true },
        campaignId: { type: mongoose.Types.ObjectId, required: true },
        message: { type: MessageSchema, required: true },
        to: { type: String, required: true },
        senderId: { type: String, required: false },
        provider: { type: String, required: false }
    },
    { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } }
);

QueuedCampaignMessageSchema.index({ createdOn: 1 }, { expireAfterSeconds: 604800 });
QueuedCampaignMessageSchema.index({ memberId: 1, campaignId: 1 }, { unique: true });

module.exports = QueuedCampaignMessageSchema;
