'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { STATUS, PROCESSING_STATUS, REFUND_STATUS, PARTNER_STATUS } = require('./../models/enums/reward.redemption.log.enums');
const { TYPE, SUB_TYPE } = require('./../models/enums/reward.enums');
const RewardRedemptionMetadataSchema = require('./../models/embedded/reward.redemption.metadata.schema');
const HistorySchema = require('./../models/embedded/history.schema');

const ALL_STATUSES = {...STATUS, ...PARTNER_STATUS};

const RewardRedemptionLogSchema = new Schema({
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantId: { type: mongoose.Types.ObjectId, required: false },
    memberId: { type: mongoose.Types.ObjectId, required: true },
    rewardId: { type: mongoose.Types.ObjectId, required: true },
    transactionId: { type: mongoose.Types.ObjectId, required: true },
    distributionJobId: { type: mongoose.Types.ObjectId, required: false },
    pointsRedeemed: {type: Number, required: true},
    status: { type: String, required: true, enum: Object.keys(ALL_STATUSES), default: STATUS.REQUESTED },
    processingStatus: { type: String, required: true, enum: Object.keys(PROCESSING_STATUS), default: PROCESSING_STATUS.PENDING },
    rewardType: { type: String, required: true, enum: Object.keys(TYPE) },
    rewardSubType: { type: String, required: true, enum: Object.keys(SUB_TYPE) },
    refundStatus: { type: String, required: true, enum: Object.keys(REFUND_STATUS), default: REFUND_STATUS.NONE },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId },
    metadata: { type: RewardRedemptionMetadataSchema, required: false },
    historyEvents: { type: [HistorySchema], required: false },
    notes: {type: String, required: false},
    handbackFaliureReason: { type: String, required: false },
}, {timestamps: {createdAt: 'createdOn', updatedAt: 'updatedOn'},
    toObject: { virtuals: true },
    toJSON: { virtuals: true }
});

RewardRedemptionLogSchema.virtual('reward', {
    ref: 'Reward',
    localField: 'rewardId',
    foreignField: '_id',
    justOne: true
});

RewardRedemptionLogSchema.virtual('pickupLocation', {
    ref: 'MerchantLocation',
    localField: 'metadata.claimLocationId',
    foreignField: '_id',
    justOne: true
});

RewardRedemptionLogSchema.virtual('voucher', {
    ref: 'RewardVoucher',
    localField: '_id',
    foreignField: 'rewardRedemptionLogId',
    justOne: true
});

module.exports = RewardRedemptionLogSchema;

