'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {
  STATUS,
  TYPE,
  SUB_TYPE,
  POINTS_VALUE_TYPE,
  VALIDITY_PERIOD,
  DAILY_REDEMPTION_LIMIT,
  PORTAL_VISIBILITY,
} = require('./../models/enums/reward.enums');
const PointBundleSchema = require('./../models/embedded/point.bundle.schema');
const RewardMetadataSchema = require('./../models/embedded/reward.metadata.schema');
const RedemptionStepSchema = require('./../models/embedded/redemption.step.schema');
const PartnerRewardMetadataSchema = require('../models/embedded/partner.reward.metadata.schema');

const RewardSchema = new Schema(
  {
    organizationId: { type: mongoose.Types.ObjectId, required: true },
    regionId: { type: mongoose.Types.ObjectId, required: true },
    merchantId: { type: mongoose.Types.ObjectId },
    name: { type: String, required: true },
    description: { type: String, required: false },
    type: { type: String, required: true, enum: Object.keys(TYPE) },
    subType: { type: String, required: true, enum: Object.keys(SUB_TYPE) },
    imageUrls: { type: [String], required: false },
    pointValueType: {
      type: String,
      required: true,
      enum: Object.keys(POINTS_VALUE_TYPE),
    },
    pointsStatic: {
      type: Number,
      required: function () {
        return this.pointValueType === POINTS_VALUE_TYPE.STATIC;
      },
    },
    pointsBundles: {
      type: [PointBundleSchema],
      required: function () {
        return this.pointValueType === POINTS_VALUE_TYPE.BUNDLE;
      },
    },
    validityPeriod: {
      type: String,
      required: true,
      enum: Object.keys(VALIDITY_PERIOD),
    },
    validFrom: {
      type: Date,
      required: function () {
        return this.validityPeriod === VALIDITY_PERIOD.FIXED;
      },
    },
    validTo: {
      type: Date,
      required: function () {
        return this.validityPeriod === VALIDITY_PERIOD.FIXED;
      },
    },
    dailyRedemptionLimit: {
      type: String,
      required: true,
      enum: Object.keys(DAILY_REDEMPTION_LIMIT),
    },
    dailyRedemptionAmount: { type: Number, required: false },
    createdBy: { type: mongoose.Types.ObjectId, required: true },
    updatedBy: { type: mongoose.Types.ObjectId },
    status: {
      type: String,
      required: true,
      enum: Object.keys(STATUS),
      default: STATUS.ENABLED,
    },
    rewardMetadata: { type: RewardMetadataSchema, required: false },
    partnerRewardMetadata: {
      type: PartnerRewardMetadataSchema,
      required: function () {
        return this.subType === SUB_TYPE.PARTNER;
      },
    },
    portalVisibility: {
      type: String,
      required: true,
      enum: Object.keys(PORTAL_VISIBILITY),
    },
    totalCount: { type: Number, required: true, default: 0 },
    usedCount: { type: Number, required: true, default: 0 },
    claimedCount: { type: Number, required: true, default: 0 },
    invalidatedCount: { type: Number, required: true, default: 0 },
    shortDescription: { type: String, required: false },
    menuIcon: { type: String, required: false },
    redemptionSteps: { type: [RedemptionStepSchema], required: false },
  },
  {
    timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' },
    toObject: { virtuals: true },
    toJSON: { virtuals: true },
  },
);

RewardSchema.virtual('remainingCount').get(function () {
  try {
    return this.totalCount - this.usedCount;
  } catch (e) {
    log.error(e);
  }
});

RewardSchema.virtual('claimLocations', {
  ref: 'MerchantLocation',
  localField: 'rewardMetadata.claimLocations',
  foreignField: '_id',
});

RewardSchema.index({ name: 'text', description: 'text' });

module.exports = RewardSchema;
