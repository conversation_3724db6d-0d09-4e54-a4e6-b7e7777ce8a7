'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { TYPE, STATUS } = require('./../models/enums/transaction.enums');
const PointRuleEvaluation = require('./../models/embedded/point.rule.evaluation.schema');
const InvoiceDataSchema = require('./../models/embedded/invoice.data.schema');
const ConsumerMetadataSchema = require('./../models/embedded/consumer.metadata.schema');
const ProductItem = require('../models/embedded/product.item.schema');

const TransactionSchema = new Schema(
    {
        organizationId: { type: mongoose.Types.ObjectId, required: true },
        regionId: { type: mongoose.Types.ObjectId, required: true },
        merchantId: { type: mongoose.Types.ObjectId, required: true },
        merchantLocationId: { type: mongoose.Types.ObjectId, required: true },
        memberId: { type: mongoose.Types.ObjectId, required: true },
        cardId: { type: mongoose.Types.ObjectId, required: false },
        parentTransactionId: { type: mongoose.Types.ObjectId, required: false },
        cardNo: { type: String, required: false },
        transactionOn: { type: Date, required: true, default: () => new Date() },
        redeemablePoints: { type: Number, required: true, default: 0 },
        signedRedeemablePoints: { type: Number, required: true, default: 0 },
        tierPoints: { type: Number, default: 0 },
        transactionAmount: { type: Number, required: true, default: 0 },
        type: { type: String, required: false, enum: Object.keys(TYPE) },
        subType: { type: mongoose.Types.ObjectId, required: true },
        importJobId: { type: mongoose.Types.ObjectId, required: false },
        affinityGroupId: { type: mongoose.Types.ObjectId, required: false },
        tierId: { type: mongoose.Types.ObjectId, required: false },
        newMember: { type: Boolean, required: true, default: false },
        productItems: { type: [ProductItem], required: false },
        invoiceData: { type: InvoiceDataSchema, required: false },
        pointRuleEvaluations: { type: [PointRuleEvaluation], required: false },
        notes: { type: String, required: false },
        idempotentKey: { type: String, required: false },
        createdBy: { type: mongoose.Types.ObjectId, required: false },
        updatedBy: { type: mongoose.Types.ObjectId },
        status: { type: String, required: true, enum: Object.keys(STATUS) },
        consumerMetadata: { type: ConsumerMetadataSchema, required: true, default: () => ({}) }
    },
    { timestamps: { createdAt: 'createdOn', updatedAt: 'updatedOn' } }
);

TransactionSchema.index({ organizationId: 1, regionId: 1 });
TransactionSchema.index({ organizationId: 1, regionId: 1, status: 1 });
TransactionSchema.index({ organizationId: 1, idempotentKey: 1 });
TransactionSchema.index({ organizationId: 1, regionId: 1, memberId: 1, createdOn: -1 });
TransactionSchema.index({ cardNo: 'text' });
TransactionSchema.index({ organizationId: 1, regionId: 1, transactionOn: -1 });
TransactionSchema.index({ organizationId: 1, regionId: 1, createdOn: -1, status: 1 });
TransactionSchema.virtual('merchant', {
    ref: 'Merchant',
    localField: 'merchantId',
    foreignField: '_id',
    justOne: true
});
TransactionSchema.virtual('merchantLocation', {
    ref: 'MerchantLocation',
    localField: 'merchantLocationId',
    foreignField: '_id',
    justOne: true
});
TransactionSchema.virtual('card', {
    ref: 'Card',
    localField: 'cardId',
    foreignField: '_id',
    justOne: true
});
TransactionSchema.virtual('transactionSubType', {
    ref: 'SubTransactionType',
    localField: 'subType',
    foreignField: '_id',
    justOne: true
});

module.exports = TransactionSchema;
