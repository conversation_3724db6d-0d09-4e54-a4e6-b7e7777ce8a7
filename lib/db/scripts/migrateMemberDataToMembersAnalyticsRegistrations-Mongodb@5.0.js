[
    { $sort: { registeredOn: 1 } },
    {
        $project: {
            registeredOn: 1,
            organizationId: 1,
            registerMethod: 1,
            merchantLocationId: 1,
            regionId: 1,
            type: 1,
            gender: 1,
            birthDate: 1,
            key: {
                $concat: [{ $toString: '$organizationId' }, '_', { $toString: '$regionId' }, '_', { $toString: '$_id' }]
            },
            _id: 0
        }
    },
    { $out: { db: 'loyalty_service', coll: 'members_analytics_registrations' } }
];
