'use strict';
const ejs = require('ejs');
const TemplatesDAO = require('./../db/dao/TemplatesDAO');
const ObjectId = require('mongoose').Types.ObjectId;

const render = (string, data) => {
    return ejs.render(string, data, {
        async: true
    });
};

class MessageFactory {

    static async buildMessage(dataObj, organizationId, regionId, templateId) {
        try {
            const regionalTemplate = await TemplatesDAO.getTemplateByFilter(organizationId, { regionId: ObjectId(regionId), templateId });
            const { emailTemplate: { bodyHtml, bodyText, subject: subjectFromData }, smsTemplate: { body } } = regionalTemplate || await TemplatesDAO.getTemplateByFilter(organizationId, { templateId });
            const html = await render(JSON.parse(bodyHtml), dataObj);
            const text = await render(JSON.parse(bodyText), dataObj);
            const subject = await render(JSON.parse(subjectFromData), dataObj);
            const smsBody = await render(JSON.parse(body), dataObj);
            return Promise.resolve({ html, text, subject, smsBody });
        } catch (e) {
            console.error(e);
            return Promise.reject(e);
        }
    }

    static async buildTextMessage(dataObj, organizationId, regionId, templateId) {
        try {
            const regionalTemplate = await TemplatesDAO.getTemplateByFilter(organizationId, { regionId: ObjectId(regionId), templateId });
            const { smsTemplate: { body } } = regionalTemplate || await TemplatesDAO.getTemplateByFilter(organizationId, { templateId });
            const smsBody = await render(JSON.parse(body), dataObj);
            return Promise.resolve({ smsBody });
        } catch (e) {
            console.error(e);
            return Promise.reject(e);
        }
    }

    static async buildEmail(dataObj, organizationId, regionId, templateId) {
        try {
            const regionalTemplate = await TemplatesDAO.getTemplateByFilter(organizationId, { regionId: ObjectId(regionId), templateId });
            const { emailTemplate: { bodyHtml, bodyText, subject: subjectFromData } } = regionalTemplate || await TemplatesDAO.getTemplateByFilter(organizationId, { templateId });
            const html = await render(JSON.parse(bodyHtml), dataObj);
            const text = await render(JSON.parse(bodyText), dataObj);
            const subject = await render(JSON.parse(subjectFromData), dataObj);
            return Promise.resolve({ html, text, subject });
        } catch (e) {
            console.error(e);
            return Promise.reject(e);
        }
    }

    // static async createVerifyAccountURLEmail(name, verifyUrl, organizationId, regionId) {
    //     try {
    //         const regionalTemplate = await TemplatesDAO.getTemplateByFilter(organizationId, { regionId: ObjectId(regionId), templateId: TEMPLATE_ID.VERIFY_ACCOUNT_URL });
    //         const { emailTemplate: { bodyHtml, bodyText, subject: subjectFromData } } = regionalTemplate || await TemplatesDAO.getTemplateByFilter(organizationId, { templateId: TEMPLATE_ID.VERIFY_ACCOUNT_URL });
    //         const dataObj = {
    //             data: {
    //                 userDisplayName: name,
    //                 verifyUrl: verifyUrl
    //             }
    //         };
    //         const html = await render(bodyHtml, dataObj);
    //         const text = await render(bodyText, dataObj);
    //         const subject = await render(subjectFromData, dataObj);
    //         return Promise.resolve({ html, text, subject });
    //     } catch (e) {
    //         console.error(e);
    //         return Promise.reject(e);
    //     }
    // }

    static async createVerifyEmail(name, verifyUrl) {
        try {
            const templateDir = path.join(__dirname, 'email_templates', 'email_verify');
            const dataObj = {
                data: {
                    userDisplayName: name,
                    verifyUrl: verifyUrl
                }
            };
            const html = await render(templateDir + '/html.ejs', dataObj);
            const text = await render(templateDir + '/text.ejs', dataObj);
            const subject = await render(templateDir + '/subject.ejs', dataObj);
            return Promise.resolve({ html, text, subject });
        } catch (e) {
            console.error(e);
            return Promise.reject(e);
        }
    }

}

module.exports = MessageFactory;
