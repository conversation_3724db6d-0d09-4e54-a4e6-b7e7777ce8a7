'use strict';
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const AffinityGroupValidator = require('../validators/AffinityGroupValidator');
const AffinityGroupsDAO = require('../db/dao/AffinityGroupsDAO');
const MembersDAO = require('../db/dao/MembersDAO');
const RegionDAO = require('../db/dao/RegionDAO');
const { STATUS } = require('../db/models/enums/affinity.group.enums');
const { OPERATION } = require('./../db/models/enums/user.enums');
const AffinityGroup = require('../db/models/affinity.group.model');
const CustomHttpError = require('./../CustomHttpError');
const mongoose = require('mongoose');
const _ = require('lodash');
const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        LOYALTY_SERVICE: {
            AFFINITY_GROUP
        }
    }
}, validateAbility } = require('@shoutout-labs/authz-utils');
const {Type, MemberType} = require("../db/models/enums/member.enums");
const TransactionsDAO = require("../db/dao/TransactionsDAO");
const {roundOffToTwoDecimals} = require("../utils/NumberUtils");
const {COLLECT_POINTS_CALLER_TYPES, SYSTEM_EVENTS} = require("../constants/Constants");
const {formatDateTimeToReadableRegionalTimeZone} = require("../utils/DateUtils");
const Shoutout = require("../services/Shoutout");

class AffinityGroupsHandler {

    static async createAffinityGroup(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: AFFINITY_GROUP.MODULE_ID,
            action: AFFINITY_GROUP.ACTIONS.CREATE_AFFINITY_GROUP,
            operation: OPERATION.CREATE,
            auditMessage: 'affinity group creation successful',
        }
        try {
            if (ability) {
                const { regionIds } = await validateAbility(AFFINITY_GROUP.ACTIONS.CREATE_AFFINITY_GROUP, AFFINITY_GROUP.MODULE_ID, dataObj.regionId, boundary, ability, true);
                if (regionIds) {
                    if (!regionIds.includes(dataObj.regionId)) {
                        throw new CustomHttpError('unauthorized action', '403');
                    }
                }
            }
            const validatedObj = await AffinityGroupValidator.isValid(dataObj);
            const createdObj = await AffinityGroupsDAO.createAffinityGroup(validatedObj, organizationId, callerId);
            return Promise.resolve({
                result: createdObj,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async getAffinityGroups(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                const { regionIds } = await validateAbility(AFFINITY_GROUP.ACTIONS.LIST_AFFINITY_GROUPS, AFFINITY_GROUP.MODULE_ID, validatedObj.regionId, boundary, ability, true, true);
                if (regionIds) {
                    validatedObj.regionIds = _.intersection(regionIds, [validatedObj.regionId]);
                }
            }
            const result = await AffinityGroupsDAO.getAffinityGroups(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getAffinityGroupById(id, organizationId) {
        try {
            const result = await AffinityGroupsDAO.getAffinityGroupById(id, organizationId);
            if (!result) {
                throw new CustomHttpError('Affinity group not found', '404');
            }
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async updateAffinityGroup(id, dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: AFFINITY_GROUP.MODULE_ID,
            action: AFFINITY_GROUP.ACTIONS.UPDATE_AFFINITY_GROUP,
            operation: OPERATION.UPDATE,
            auditMessage: 'affinity group update successful',
        }
        try {
            const validatedDataObj = await AffinityGroupValidator.isValidUpdate(dataObj);
            const affinityGroup = await AffinityGroupsDAO.getAffinityGroupById(id, organizationId);
            if (!affinityGroup) {
                throw new CustomHttpError('Affinity group not found', '404');
            }
            if (ability) {
                const { regionIds } = await validateAbility(AFFINITY_GROUP.ACTIONS.UPDATE_AFFINITY_GROUP, AFFINITY_GROUP.MODULE_ID, null, boundary, ability, true);
                if (regionIds) {
                    if (!regionIds.includes(affinityGroup.regionId.toString())) {
                        throw new CustomHttpError('unauthorized action', '403');
                    }
                }
            }
            const { memberConfiguration: { defaultAffinityGroupId } } = await RegionDAO.getRegion(affinityGroup.regionId);
            if (defaultAffinityGroupId.toString() === id) {
                throw new CustomHttpError('Default affinity group cannot be modified', '400');
            }

            const updatedDoc = await AffinityGroupsDAO.updateAffinityGroup({
                ...validatedDataObj,
                updatedBy:  mongoose.Types.ObjectId(callerId),
            }, id, organizationId, callerId);
            return Promise.resolve({
                result: updatedDoc,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async deleteAffinityGroup(id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: AFFINITY_GROUP.MODULE_ID,
            action: AFFINITY_GROUP.ACTIONS.DELETE_AFFINITY_GROUP,
            operation: OPERATION.DELETE,
            auditMessage: 'affinity group deletion successful',
        }
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: {$ne: STATUS.ARCHIVED}
            };

            let [affinityGroup, membersCount] = await Promise.all([
                AffinityGroup.findOne(filter),
                MembersDAO.getRecordsCount(organizationId, {
                    'affinityGroup.affinityGroupId': mongoose.Types.ObjectId(id),
                    organizationId: mongoose.Types.ObjectId(organizationId),
                    status: {$ne: STATUS.ARCHIVED},
                    type :{ $in: [Type.PRIMARY,Type.SECONDARY] }
                }),
            ]);
            if (!affinityGroup) {
                throw new CustomHttpError('Affinity group not found', '404');
            }
            if (ability) {
                const {regionIds} = await validateAbility(AFFINITY_GROUP.ACTIONS.DELETE_AFFINITY_GROUP, AFFINITY_GROUP.MODULE_ID, null, boundary, ability, true);
                if (regionIds) {
                    if (!regionIds.includes(affinityGroup.regionId.toString())) {
                        throw new CustomHttpError('unauthorized action', '403');
                    }
                }
            }

            const {memberConfiguration: {defaultAffinityGroupId}} = await RegionDAO.getRegion(affinityGroup.regionId);
            if (defaultAffinityGroupId.toString() !== id) {
                await MembersDAO.updateMembersBulk(organizationId, {
                    'affinityGroup.affinityGroupId': mongoose.Types.ObjectId(defaultAffinityGroupId),
                    'affinityGroup.joinedDate': new Date(),
                    'affinityGroup.expiryDate': null
                }, {
                    regionId: mongoose.Types.ObjectId(affinityGroup.regionId),
                    'affinityGroup.affinityGroupId': mongoose.Types.ObjectId(id)
                });

                if (membersCount > 0) {
                    const session = await AffinityGroup.startSession();
                    await session.withTransaction(async () => {
                        const opts = { session };
                        await AffinityGroupsDAO.bulkUpdateAffinityGroups(
                            [
                                {
                                    updateOne: {
                                        filter: {_id: mongoose.Types.ObjectId(defaultAffinityGroupId)},
                                        update: {
                                            $inc: {
                                                membersCount: membersCount,
                                            },
                                        }
                                    }
                                },
                                {
                                    updateOne: {
                                        filter: {_id: mongoose.Types.ObjectId(id)},
                                        update: {
                                            $inc: {
                                                membersCount: -membersCount,
                                            },
                                        }
                                    }
                                },
                            ],opts
                        );
                    });
                    if(session){
                        session.endSession();
                    }
                }
            } else {
                throw new CustomHttpError('Default affinity group cannot be archived', '400');
            }

            const result = await AffinityGroupsDAO.deleteAffinityGroup(id, organizationId, callerId);
            return Promise.resolve({
                result,
                auditData
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: {...auditData, auditMessage: e.message}
            });
        }
    }

    static async validateAffinityGroup(id, organizationId) {
        try {
            const result = await AffinityGroupsDAO.getAffinityGroupsByFilter(organizationId, {
                _id: mongoose.Types.ObjectId(id),
                status: STATUS.ENABLED
            }, null, null);
            if (result.length > 0) {
                return Promise.resolve(result[0]);
            }
            return Promise.reject(new CustomHttpError('Invalid affinity group', '400'));
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

}

module.exports = AffinityGroupsHandler;
