'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const log = logger(config.logger);
const Utils = require('./../../Utils');
const CardsAnalyticsDAO = require('./../../db/dao/analytics/CardsAnalyticsDAO');
const { BOUNDARY, OPERATION } = require('./../../db/models/enums/user.enums');
const { CardsSummaryByMemberTypeAggregation, ProcessingStatusStatsAggregation, CardActivationReportAggregation } = require('./../../constants/analyticsAggregations/CardsAggregations');
const mongoose = require('mongoose');
const _ = require('lodash');
const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        ANALYTIC_SERVICE: {
            CARD_ANALYTICS
        }
    }
}, validateAbility } = require('@shoutout-labs/authz-utils');

class AnalyticsCardsHandler {

    static async getCardsSummaryReport(organizationId, validatedObj, ability, boundary) {
        try {
            if (validatedObj.regionId) validatedObj.regionIds = [validatedObj.regionId];
            if (ability && boundary !== BOUNDARY.ROOT) {
                const { regionIds } = await validateAbility(CARD_ANALYTICS.ACTIONS.GET_CARD_SUMMARY_REPORT, CARD_ANALYTICS.MODULE_ID, null, boundary, ability, false, true);
                if (validatedObj.regionId) {
                    validatedObj.regionIds = _.intersection([validatedObj.regionId], regionIds)
                } else {
                    validatedObj.regionIds = regionIds
                }
            }

            const filters = {};
            if (validatedObj.regionIds) {
                filters['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.type) {
                filters['type'] = {
                    $in: validatedObj.type
                };
            }

            const pipeline = CardsSummaryByMemberTypeAggregation(mongoose.Types.ObjectId(organizationId), filters);

            const result = await CardsAnalyticsDAO.runAggregationWithDateRange(pipeline, validatedObj.dateFrom, validatedObj.dateTo);
            return Promise.resolve({data: result});
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getProcessingStatusStatsReport(organizationId, validatedObj, ability, boundary) {
        try {
            if (validatedObj.regionId) validatedObj.regionIds = [validatedObj.regionId];
            if (ability && boundary !== BOUNDARY.ROOT) {
                const { regionIds } = await validateAbility(CARD_ANALYTICS.ACTIONS.VIEW_CARD_PROCESSING_STATUS_REPORT, CARD_ANALYTICS.MODULE_ID, null, boundary, ability, false, true);
                if (validatedObj.regionId) {
                    validatedObj.regionIds = _.intersection([validatedObj.regionId], regionIds)
                } else {
                    validatedObj.regionIds = regionIds
                }
            }

            const filters = {};
            if (validatedObj.regionIds) {
                filters['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.type) {
                filters['type'] = {
                    $in: validatedObj.type
                };
            }

            const pipeline = ProcessingStatusStatsAggregation(mongoose.Types.ObjectId(organizationId), filters);

            const result = await CardsAnalyticsDAO.runAggregationWithDateRange(pipeline, validatedObj.dateFrom, validatedObj.dateTo);
            return Promise.resolve({data: result});
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getActivationSummaryReport(organizationId, validatedObj, exportReport, ability, boundary) {
        try {
            if (validatedObj.regionId) validatedObj.regionIds = [validatedObj.regionId];
            if (ability && boundary !== BOUNDARY.ROOT) {
                const { regionIds } = await validateAbility(exportReport ? CARD_ANALYTICS.ACTIONS.EXPORT_CARD_ACTIVATION_REPORT : CARD_ANALYTICS.ACTIONS.VIEW_CARD_ACTIVATION_REPORT, CARD_ANALYTICS.MODULE_ID, null, boundary, ability, false, true);
                if (validatedObj.regionId) {
                    validatedObj.regionIds = _.intersection([validatedObj.regionId], regionIds)
                } else {
                    validatedObj.regionIds = regionIds
                }
            }

            const filters = {};
            if (validatedObj.regionIds) {
                filters['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.type) {
                filters['type'] = {
                    $in: validatedObj.type
                };
            }

            const pipeline = CardActivationReportAggregation(mongoose.Types.ObjectId(organizationId), filters, true, validatedObj.dateBucket);

            if (exportReport) {
                const aggregationCursor = await CardsAnalyticsDAO.runAggregationWithDateRange(pipeline, validatedObj.dateFrom, validatedObj.dateTo, true, 'assignedDate');
                const headers = [
                    {id: 'totalCount', title: 'TOTAL_COUNT'},
                    {id: 'digitalCards', title: 'DIGITAL_CARDS'},
                    {id: 'keyTagCards', title: 'KEY_TAG_CARDS'},
                    {id: 'RegularCards', title: 'REGULAR_CARDS'},
                    {id: 'keyTagAndRegularCards', title: 'KEY_TAG_AND_REGULAR_CARDS'},
                    {id: 'dateBucketKey', title: 'DATE_BUCKET_KEY'},
                ];
                const { url } = await Utils.exportToCSV(aggregationCursor, organizationId, `cards_activation_report`, headers, 'analytics');
                return Promise.resolve({ url });
            } else {
                const result = await CardsAnalyticsDAO.runAggregationWithDateRange(pipeline, validatedObj.dateFrom, validatedObj.dateTo, false, 'assignedDate');
                return Promise.resolve({data: result});
            }
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

}

module.exports = AnalyticsCardsHandler;
