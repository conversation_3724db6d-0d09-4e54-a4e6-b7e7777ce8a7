'use strict';
const config = require('./../../config');
const logger = require('./../../logger');
const Utils = require('./../../Utils');
const log = logger(config.logger);
const RewardsAnalyticsDAO = require('./../../db/dao/analytics/RewardsAnalyticsDAO');
const RewardRedemptionLogsAnalyticsDAO = require('./../../db/dao/analytics/RewardRedemptionLogsAnalyticsDAO');
const { RewardsAggregation, RewardsRedemptionByRewardAggregation } = require('./../../constants/analyticsAggregations/RewardsAggregations');
const { BOUNDARY, OPERATION } = require('./../../db/models/enums/user.enums');
const { COMMON: { DATE_BUCKET }, REWARDS: { GROUP_BY } } = require('../../constants/AnalyticsEnums');
const mongoose = require('mongoose');
const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        ANALYTIC_SERVICE: {
            REWARD_ANALYTICS
        }
    }
}, validateGet } = require('@shoutout-labs/authz-utils');

const getResponseType = (exportReport, series) => {
    return {
        prefix: exportReport ? 'EXPORT' : 'VIEW',
        postfix: series ? 'SERIES' : 'COUNT'
    }
}

class AnalyticsRewardsHandler {

    static async getTopRewards(organizationId, validatedObj, ability, boundary) {
        try {
            validatedObj.regionIds = [validatedObj.regionId];
            if (ability && boundary !== BOUNDARY.ROOT) {
                validatedObj = await validateGet(REWARD_ANALYTICS.ACTIONS.VIEW_TOP_REWARDS, REWARD_ANALYTICS.MODULE_ID, validatedObj.regionId, boundary, ability, validatedObj);
            }

            const result = await RewardsAnalyticsDAO.getTopRewards(organizationId, validatedObj);
            return Promise.resolve({ data: result});
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getRedemptionReport(organizationId, validatedObj, series, exportReport, ability, boundary) {
        
        const responseType = getResponseType(exportReport, series);

        try {
            
            validatedObj.regionIds = [validatedObj.regionId];
            if (ability && boundary !== BOUNDARY.ROOT) {
                validatedObj = await validateGet(REWARD_ANALYTICS.ACTIONS[`${responseType.prefix}_REDEEMED_REWARDS_${responseType.postfix}`], REWARD_ANALYTICS.MODULE_ID, validatedObj.regionId, boundary, ability, validatedObj);
            }

            const filters = {};
            if (validatedObj.regionIds) {
                filters['regionId'] = { $in: validatedObj.regionIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.merchantIds) {
                filters['merchantId'] = { $in: validatedObj.merchantIds.map((itemId) => mongoose.Types.ObjectId(itemId)) };
            }
            if (validatedObj.rewardId) {
                filters['rewardId'] = mongoose.Types.ObjectId(validatedObj.rewardId);
            }

            let pipeline = [], headers = [{id: 'count', title: 'COUNT'}];
            switch (validatedObj.groupBy) {
                case GROUP_BY.REWARD:
                    pipeline = RewardsRedemptionByRewardAggregation(mongoose.Types.ObjectId(organizationId), filters, series, validatedObj.dateBucket);
                    headers = [
                        {id: 'rewardName', title: 'REWARD_NAME'},
                        {id: 'redeemedCount', title: 'TOTAL_REDEEMED_COUNT'},
                        {id: 'pendingCount', title: 'PENDING_COUNT'},
                        {id: 'readyCount', title: 'READY_COUNT'},
                        {id: 'claimedCount', title: 'CLAIMED_COUNT'},
                        {id: 'cancelledCount', title: 'CANCELLED_COUNT'}
                    ];
                    break;
                default:
                    pipeline = RewardsAggregation(mongoose.Types.ObjectId(organizationId), filters, series, validatedObj.dateBucket);
            }


            if (exportReport) {
                const aggregationCursor = await RewardRedemptionLogsAnalyticsDAO.runAggregationWithDateRange(pipeline, validatedObj.dateFrom, validatedObj.dateTo, true);

                if (series) headers.push({id: 'dateBucketKey', title: `${validatedObj.dateBucket.toUpperCase()}`});

                const {url} = await Utils.exportToCSV(aggregationCursor, organizationId, `rewards_redemption_report`, headers, 'analytics');

                return Promise.resolve({url});
            } else {
                const result = await RewardRedemptionLogsAnalyticsDAO.runAggregationWithDateRange(pipeline, validatedObj.dateFrom, validatedObj.dateTo);
                return Promise.resolve({data: result});
            }
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

}

module.exports = AnalyticsRewardsHandler;
