'use strict';
const CampaignsDAO = require('./../db/dao/CampaignsDAO');
const mongoose = require('mongoose');
const CustomHttpError = require('./../CustomHttpError');
const moment = require('moment');
const { CAMPAIGN_PROGRESS_TRACKER_DELAY } = require('./../../config');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const CampaignsValidator = require('../validators/CampaignsValidator');
const MessageService = require('../services/MessageService');
const { OPERATION } = require('../db/models/enums/user.enums');
const Campaign = require('../db/models/campaign.model');
const Utils = require('../Utils');
const { CHANNEL, STATUS, VISIBILITY } = require('../db/models/enums/campaign.enums');

const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            CAMPAIGN_SERVICE: { CAMPAIGN }
        }
    },
    validateAbility
} = require('@shoutout-labs/authz-utils');

const campaignQueue = require('../../workers/processors/campaign.sms.job.processor.mq').getQueue();
const campaignProgressTrackerQueue = require('../../workers/processors/campaign.progress.tracker.mq').getQueue();

class CampaignsHandler {
    static async createCampaign(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CAMPAIGN.MODULE_ID,
            action: CAMPAIGN.ACTIONS.CREATE_CAMPAIGN,
            operation: OPERATION.CREATE,
            auditMessage: 'campaign creation successful'
        };
        try {
            const validatedObj = await CampaignsValidator.isValid(dataObj);

            if (ability) {
                await validateAbility(
                    CAMPAIGN.ACTIONS.CREATE_CAMPAIGN,
                    CAMPAIGN.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability
                );
            }
            const createdObj = await CampaignsDAO.createCampaign(validatedObj, organizationId, callerId);

            const { regionId, channel, senderId, message, segmentFilters, providerName } = createdObj;
            const campaignId = createdObj._id.toString();

            if (validatedObj.scheduleOn && validatedObj.channel !== CHANNEL.BANNER) {
                const nextJobInLocaleTime = moment(validatedObj.scheduleOn);
                const msUntilNextJob = nextJobInLocaleTime.diff(moment(), 'millisecond', true);

                await campaignQueue.add(
                    channel,
                    {
                        message,
                        organizationId,
                        regionId,
                        channel,
                        senderId,
                        segmentFilters,
                        campaignId,
                        providerName
                    },
                    {
                        jobId: `${organizationId}:${validatedObj.regionId}:${campaignId}`,
                        delay: msUntilNextJob
                    }
                );

                const campaignUpdateObj = Utils.getEventUpdateObj(callerId, 'status changed to SCHEDULED', {
                    status: STATUS.SCHEDULED
                });

                await CampaignsDAO.updateCampaign(null, campaignId, organizationId, callerId, null, campaignUpdateObj);

                await campaignProgressTrackerQueue.add(
                    channel,
                    {
                        organizationId,
                        campaignId,
                        totalMessagesCount: 0,
                        successCount: 0,
                        failedCount: 0
                    },
                    {
                        jobId: `${organizationId}:${validatedObj.regionId}:${campaignId}`,
                        delay: msUntilNextJob + CAMPAIGN_PROGRESS_TRACKER_DELAY
                    }
                );
            } else {
                await campaignQueue.add(
                    channel,
                    {
                        message,
                        organizationId,
                        regionId,
                        channel,
                        senderId,
                        segmentFilters,
                        campaignId,
                        providerName
                    },
                    {
                        jobId: `${organizationId}:${validatedObj.regionId}:${campaignId}`
                    }
                );

                await campaignProgressTrackerQueue.add(
                    channel,
                    {
                        organizationId,
                        campaignId,
                        totalMessagesCount: 0,
                        successCount: 0,
                        failedCount: 0
                    },
                    {
                        jobId: `${organizationId}:${validatedObj.regionId}:${campaignId}`,
                        delay: CAMPAIGN_PROGRESS_TRACKER_DELAY
                    }
                );
            }

            return Promise.resolve({
                result: createdObj,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async getCampaignRequest(organizationId, id, ability, boundary) {
        try {
            const campaign = await CampaignsDAO.getCampaignById(organizationId, id);

            if (ability) {
                await validateAbility(
                    CAMPAIGN.ACTIONS.LIST_CAMPAIGNS,
                    CAMPAIGN.MODULE_ID,
                    campaign.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (!campaign) {
                throw new CustomHttpError('campaign not found', '404');
            }
            return Promise.resolve(campaign);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getCampaignReport(organizationId, regionId, categoryId, boundary, ability) {
        try {
            if (ability) {
                await validateAbility(
                    CAMPAIGN.ACTIONS.GET_CAMPAIGN_REPORT,
                    CAMPAIGN.MODULE_ID,
                    regionId,
                    boundary,
                    ability
                );
            }
            const campaign = await CampaignsDAO.getCampaignById(organizationId, categoryId);

            if (!campaign) {
                throw new CustomHttpError('campaign not found', '404');
            }

            const reportResponse = await MessageService.getMessageLogsReportById(organizationId, regionId, categoryId);

            if (!reportResponse) {
                throw new CustomHttpError('message logs not found', '404');
            }

            const createdObj = {
                reportData: reportResponse,
                ...(campaign?.failedCount ? { failedCount: campaign?.failedCount } : { failedCount: '0' }),
                ...(campaign?.successCount ? { successCount: campaign?.successCount } : { successCount: '0' }),
                ...(campaign?.totalMessages ? { totalMessages: campaign?.totalMessages } : { totalMessages: '0' })
            };

            return Promise.resolve(createdObj);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getCampaigns(organizationId, dataObj, ability, boundary) {
        try {
            if (ability) {
                await validateAbility(
                    CAMPAIGN.ACTIONS.LIST_CAMPAIGNS,
                    CAMPAIGN.MODULE_ID,
                    dataObj.regionId,
                    boundary,
                    ability
                );
            }
            const validatedObj = await CampaignsValidator.isValidGetCampaign(dataObj);
            const result = await CampaignsDAO.getCampaigns(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getPublicBannerCampaigns(organizationId, dataObj) {
        try {
            const { regionId, limit, skip } = await CampaignsValidator.isValidGetBannerCampaigns(dataObj);
            const result = await CampaignsDAO.getCampaigns(
                organizationId,
                {
                    regionId,
                    limit,
                    skip,
                    channel: CHANNEL.BANNER,
                    visibility: VISIBILITY.PUBLIC,
                    status: STATUS.RUNNING
                },
                {
                    $and: [
                        {
                            $or: [{ scheduleOn: null }, { scheduleOn: { $lte: new Date() } }]
                        },
                        {
                            $or: [{ endOn: null }, { endOn: { $gte: new Date() } }]
                        }
                    ]
                }
            );
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getPrivateBannerCampaigns(organizationId, memberId, dataObj) {
        try {
            const { regionId, limit, skip } = await CampaignsValidator.isValidGetBannerCampaigns(dataObj);
            const result = await CampaignsDAO.getCampaigns(
                organizationId,
                {
                    regionId,
                    limit,
                    skip,
                    channel: CHANNEL.BANNER,
                    visibility: VISIBILITY.PRIVATE,
                    status: STATUS.RUNNING
                },
                {
                    memberId: mongoose.Types.ObjectId(memberId),
                    $and: [
                        {
                            $or: [{ scheduleOn: null }, { scheduleOn: { $lte: new Date() } }]
                        },
                        {
                            $or: [{ endOn: null }, { endOn: { $gte: new Date() } }]
                        }
                    ]
                }
            );
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async deleteCampaign(id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CAMPAIGN.MODULE_ID,
            action: CAMPAIGN.ACTIONS.DELETE_CAMPAIGN,
            operation: OPERATION.DELETE,
            auditMessage: 'campaign deletion successful'
        };
        try {
            const filter = {
                _id: mongoose.Types.ObjectId(id),
                organizationId,
                status: { $ne: STATUS.ARCHIVED }
            };
            const campaign = await Campaign.findOne(filter);
            if (!campaign) {
                throw new CustomHttpError('Campaign not found', '404');
            }
            if (ability) {
                await validateAbility(
                    CAMPAIGN.ACTIONS.DELETE_CAMPAIGN,
                    CAMPAIGN.MODULE_ID,
                    campaign.regionId.toString(),
                    boundary,
                    ability
                );
            }

            if (
                campaign.channel !== CHANNEL.BANNER &&
                campaign.scheduleOn &&
                moment(campaign.scheduleOn).isAfter(moment())
            ) {
                const jobs = await campaignQueue.getJobs(['delayed']);
                let jobToDelete;
                jobToDelete = jobs.find(
                    (delayedJob) =>
                        delayedJob.opts.jobId ===
                        `${organizationId}:${campaign.regionId.toString()}:${campaign._id.toString()}`
                );
                if (jobToDelete) {
                    await jobToDelete.remove();
                }
            }

            const result = await CampaignsDAO.deleteCampaign(id, organizationId, callerId);

            return Promise.resolve({
                result,
                auditData
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }
}

module.exports = CampaignsHandler;
