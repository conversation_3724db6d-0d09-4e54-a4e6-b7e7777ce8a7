'use strict';
const CardConfigurationsDAO = require('./../db/dao/CardConfigurationsDAO');
const config = require('./../config');
const logger = require('./../logger');
const _ = require('lodash');
const log = logger(config.logger);
const CardConfigurationsValidator = require('../validators/CardConfigurationValidator');
const CardConfiguration = require('../db/models/card.configuration.model');
const { OPERATION } = require('./../db/models/enums/user.enums');
const mongoose = require('mongoose');
const CustomHttpError = require('../CustomHttpError');

const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        LOYALTY_SERVICE: {
            CARD
        }
    }
}, validateAbility } = require('@shoutout-labs/authz-utils');

class CardConfigurationsHandler {

    static async createCardConfiguration(organizationId, callerId, dataset, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.CREATE_CARD_CONFIGURATION,
            operation: OPERATION.CREATE,
            auditMessage: 'card configuration creation successful',
        }
        try {
            const validatedDataset = await CardConfigurationsValidator.cardConfigurationCreateValidation(dataset);
            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.CREATE_CARD_CONFIGURATION, CARD.MODULE_ID, dataset.regionId, boundary, ability, true);
                if (regionIds) {
                    if (!regionIds.includes(dataset.regionId)) {
                        throw new CustomHttpError('unauthorized action', '403');
                    }
                }
            }
            const cardConfiguration = await CardConfigurationsDAO.getCardConfigurationByFilter({
                regionId: mongoose.Types.ObjectId(validatedDataset.regionId),
                type: validatedDataset.type
            }, organizationId);
            if (cardConfiguration) {
                throw new CustomHttpError(`this region already has a card configuration of type ${validatedDataset.type}`, '400');
            }
            await this.validateFullRange(validatedDataset, {
                lastCardNumber: 0,
                regionId: validatedDataset.regionId
            }, organizationId);
            const cardConfigurationModel = new CardConfiguration({
                organizationId,
                createdBy: mongoose.Types.ObjectId(callerId),
                ...validatedDataset
            });
            const createdCardConfiguration = await CardConfigurationsDAO.createCardConfiguration(cardConfigurationModel);
            return Promise.resolve({
                result: createdCardConfiguration,
                auditData: {
                    ...auditData
                }
            });
        } catch (err) {
            log.error(err);
            return Promise.reject({
                err: err,
                auditData: { ...auditData, auditMessage: err.message }
            });
        }
    }

    static async getCardConfigurations(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.LIST_CARD_CONFIGURATIONS, CARD.MODULE_ID, validatedObj.regionId, boundary, ability, true, true);
                if (regionIds) {
                    if (validatedObj.regionId) {
                        validatedObj.regionIds = _.intersection(regionIds, [validatedObj.regionId]);
                    } else {
                        validatedObj.regionIds = regionIds;
                    }
                }
            }
            const cardConfigurationsGetResult = await CardConfigurationsDAO.getCardConfigurations(organizationId, validatedObj);
            return Promise.resolve(cardConfigurationsGetResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async updateCardConfiguration(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.UPDATE_CARD_CONFIGURATION,
            operation: OPERATION.UPDATE,
            auditMessage: 'card configuration update successful',
        }
        try {
            const validatedDataObj = await CardConfigurationsValidator.cardConfigurationUpdateValidation(dataObj);
            const existingCardConfiguration = await CardConfigurationsDAO.getCardConfigurationById(organizationId, id);
            if (!existingCardConfiguration) {
                throw new CustomHttpError('Card configuration not found', 404);
            }
            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.UPDATE_CARD_CONFIGURATION, CARD.MODULE_ID, null, boundary, ability, true);
                if (regionIds) {
                    if (!regionIds.includes(existingCardConfiguration.regionId.toString())) {
                        throw new CustomHttpError('unauthorized action', '403');
                    }
                }
            }
            if (validatedDataObj.rangeFrom || validatedDataObj.rangeTo) {
                if (validatedDataObj.rangeFrom && validatedDataObj.rangeTo === undefined) {
                    validatedDataObj.rangeTo = existingCardConfiguration.rangeTo;
                }
                if (validatedDataObj.rangeTo && validatedDataObj.rangeFrom === undefined) {
                    validatedDataObj.rangeFrom = existingCardConfiguration.rangeFrom;
                }
                await this.validateFullRange(validatedDataObj, existingCardConfiguration, organizationId);
            }

            const updatedResult = await CardConfigurationsDAO.updateCardConfiguration({
                updatedBy: callerId,
                rangeFrom: validatedDataObj.rangeFrom,
                rangeTo: validatedDataObj.rangeTo,
                replacementFee: validatedDataObj.replacementFee
            }, id, organizationId);

            return Promise.resolve({
                result: updatedResult,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async validateFullRange(validatedRanges, existingCardConfiguration, organizationId) {
        if (validatedRanges.rangeFrom >= validatedRanges.rangeTo) {
            return Promise.reject(new CustomHttpError('Range from must be less than range to', 400));
        }
        if (existingCardConfiguration.lastCardNumber !== 0) {
            if (validatedRanges.rangeFrom > existingCardConfiguration.lastCardNumber) {
                return Promise.reject(new CustomHttpError('Range from must be less than last card number', 400));
            }
            if (validatedRanges.rangeTo < existingCardConfiguration.lastCardNumber) {
                return Promise.reject(new CustomHttpError('Range to must be greater than last card number', 400));
            }
        }
        const existingRanges = await CardConfigurationsDAO.getCardConfigurationsByFilter({
            _id: { $ne: existingCardConfiguration._id ? existingCardConfiguration._id : null },
            organizationId,
            regionId: existingCardConfiguration.regionId,
            $or: [ { rangeFrom: { $lte: validatedRanges.rangeFrom }, rangeTo: { $gte: validatedRanges.rangeFrom }, }, { rangeFrom: { $lte: validatedRanges.rangeTo },
                rangeTo: { $gte: validatedRanges.rangeTo } }, { rangeFrom: { $gte: validatedRanges.rangeFrom, $lte: validatedRanges.rangeTo } } ]
        });
        if (existingRanges.length > 0) {
            log.debug(existingRanges);
            return Promise.reject(new CustomHttpError('Range is already in use', 400));
        }
    }

}

module.exports = CardConfigurationsHandler;
