'use strict';
const CardPrintJobDAO = require('../db/dao/CardPrintJobDAO');
const CardDAO = require('./../db/dao/CardDAO');
const CardConfigurationsDAO = require('./../db/dao/CardConfigurationsDAO');
const config = require('./../config');
const secretConfig = require('./../../config');
const logger = require('./../logger');
const Utils = require('./../Utils');
const {intersection,snakeCase} = require('lodash');
const log = logger(config.logger);
const Shoutout = require('./../../lib/services/Shoutout');
const CardPrintJobsValidator = require('../validators/CardPrintJobsValidator');
const { CARD_CONFIGURATION_TYPES, CARD_STATUS, CARD_TYPES, CARD_BATCH_JOB_STATUS, CARD_PROCESSING_STATUS } = require('../db/models/enums/card.enums');
const Card = require('../db/models/card.model');
const CustomHttpError = require('./../CustomHttpError');
const IdentityService = require('./../services/IdentityService');
const mongoose = require('mongoose');
const { addJob: addCardStockRefreshJob } = require('./../../workers/processors/card.stock.view.refresh.job.processor');
const { OPERATION } = require('../db/models/enums/user.enums');

const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        LOYALTY_SERVICE: {
            CARD
        }
    }
}, validateAbility
} = require('@shoutout-labs/authz-utils');

const fs = require("fs");
const OrganizationDAO = require('../db/dao/OrganizationDAO');
const AddressUtils = require('../utils/AddressUtils');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

const dbRequestChunkSize = Number(process.env.WORKER_DB_REQUEST_CHUNK_SIZE || 500);

const getFileUploadPath = (organizationId, jobId) => {
    return organizationId + "/loyalty_cards/print_batch_jobs/" + jobId + ".csv";
};

const getEventUpdateObj = (callerId, eventDetails, setObj) => {
    return {
        $set: setObj,
        $push: {
            historyEvents: {
                $each: [
                    {
                        eventDate: new Date(),
                        eventDetails,
                        eventBy: callerId
                    }
                ],
                $sort: { eventDate: -1 }
            }
        },
        $currentDate: { modifiedOn: true },
        updatedBy: callerId
    };
};

const publishEmbossedCardReadyActivities = async (organizationId, regionId, cardPrintJobId, printJobStatus) => {
    const cardsCursor = await CardDAO.getEmbossedCardsWithMerchantLocations(cardPrintJobId, organizationId, printJobStatus);
    let activities = [];
    for (let card = await cardsCursor.next(); card != null; card = await cardsCursor.next()) {
        const { memberId, cardNo, merchantLocation } = card;
        activities.push({
            data: {
                memberId: memberId.toString(),
                activityName: 'Embossed Card Ready',
                organizationId,
                regionId: regionId,
                createdOn: new Date(),
                activityData: {
                    cardNo,
                    locationName: merchantLocation.locationName
                }
            },
            opts: {
                jobId: mongoose.Types.ObjectId().toString()
            }
        });
        if (activities.length % dbRequestChunkSize === 0 ) {
            Shoutout.produceActivityToTopic(activities);
            activities = [];
        }
    }
    if (activities.length > 0 ) {
        Shoutout.produceActivityToTopic(activities);
    }
}

class CardPrintJobsHandler {

    static async createCardPrintJob(organizationId, callerId, dataset, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.CREATE_CARD_BATCH_JOB,
            operation: OPERATION.CREATE,
            auditMessage: 'card print job successful',
        }
        try {
            const validatedDataset = await CardPrintJobsValidator.isValid(dataset);

            const { configuration: { cardConfiguration: { allowManualCardGeneration } } } = await OrganizationDAO.getOrganization(organizationId);
            if (allowManualCardGeneration && validatedDataset.jobType !== CARD_TYPES.EMBOSSED_CARD) throw new CustomHttpError(`Manual card generation is allowed`, '400');

            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.CREATE_CARD_BATCH_JOB, CARD.MODULE_ID, validatedDataset.regionId, boundary, ability, true);
                if (regionIds && !regionIds.includes(validatedDataset.regionId)) throw new CustomHttpError('unauthorized region', '403');
            }

            const typeData = this.getTypeData(validatedDataset.jobType);
            const cardConfiguration = await CardConfigurationsDAO.getCardConfiguration(organizationId, validatedDataset.regionId, typeData.configurationType);

            if (validatedDataset.jobType !== CARD_TYPES.EMBOSSED_CARD) {
                const newStatingCardNo = cardConfiguration.lastCardNumber === 0 ? cardConfiguration.rangeFrom : Number(cardConfiguration.lastCardNumber) + 1;
                const newLastCardNo = (newStatingCardNo - 1) + Number(validatedDataset.quantity);
                if (newLastCardNo > cardConfiguration.rangeTo) {
                    throw new CustomHttpError('Insufficient range', 400);
                }
                const updatedCardConfiguration = await CardConfigurationsDAO.incrementConfiguration(cardConfiguration._id.toString(), organizationId, newLastCardNo);
                const printJobNumber = `${typeData.prefix}-${updatedCardConfiguration.lastBatchId}`;
                const statusObj = this.getBatchAndCardStatus(dataset.jobType);
                const createdCardBatchJob = await CardPrintJobDAO.createCardBatchJob(validatedDataset, organizationId, callerId, newStatingCardNo, newLastCardNo, printJobNumber, statusObj.batchJobStatus);
                await this.generateCards(newStatingCardNo, newLastCardNo, organizationId, createdCardBatchJob.id, printJobNumber, callerId, { jobType: dataset.jobType, regionId: dataset.regionId, cardStatus: statusObj.cardStatus, processingStatus: statusObj.batchJobStatus })
                addCardStockRefreshJob(organizationId, validatedDataset.regionId);
                
                return Promise.resolve({
                    result: createdCardBatchJob,
                    auditData
                });
            } else {
                const invalidCards = await CardDAO.validateEmbossRequests(validatedDataset.embossCardIds, organizationId);
                if (invalidCards.length > 0) {
                    throw new CustomHttpError(`Following cards doesnt contain a emboss request, ${invalidCards.map((card) => card.id + ',')}`, 400);
                }
                const updatedCardConfiguration = await CardConfigurationsDAO.incrementConfiguration(cardConfiguration._id.toString(), organizationId, validatedDataset.embossCardIds.length);
                validatedDataset.quantity = validatedDataset.embossCardIds.length;
                const printJobNumber = `${typeData.prefix}-${updatedCardConfiguration.lastBatchId}`;
                const createdBatchJob = await CardPrintJobDAO.createCardBatchJob(validatedDataset, organizationId, callerId, 0, 0, `${typeData.prefix}-${updatedCardConfiguration.lastBatchId}`, CARD_BATCH_JOB_STATUS.PENDING);

                const cardBulkUpdateObj = getEventUpdateObj(callerId, `Processing status changed to PENDING`, {
                    processingStatus: CARD_PROCESSING_STATUS.PENDING,
                    printJobId: createdBatchJob.id,
                    printJobNumber
                });
                await CardDAO.updateCardBulkStatus(cardBulkUpdateObj, {
                    _id: { $in: validatedDataset.embossCardIds },
                    organizationId: mongoose.Types.ObjectId(organizationId)
                });

                addCardStockRefreshJob(organizationId, validatedDataset.regionId);
                return Promise.resolve({
                    result: createdBatchJob,  
                    auditData: { ...auditData }
                });
            }
        } catch (err) {
            log.error(err);
            return Promise.reject({
                err: err,
                auditData: { ...auditData, auditMessage: err.message }
            });
        }
    }

    static async getCardPrintJobs(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.LIST_CARD_BATCH_JOBS, CARD.MODULE_ID, validatedObj.regionId, boundary, ability, true);
                if (regionIds) validatedObj.regionIds = intersection(regionIds, [validatedObj.regionId]);
            }
            let projection;
            if (validatedObj.fields && validatedObj.fields.length > 0) {
                projection = Utils.buildProjection(validatedObj.fields);
            }
            const result = await CardPrintJobDAO.getCardBatchJobs(organizationId, validatedObj, projection, validatedObj.regionId);
            if (result.items.length === 0) {
                return Promise.resolve(result);
            }
            const userNames = await IdentityService.getUsersByIds(result.items.map(item => item.createdBy));
            result.items.forEach((item, i) => {
                result.items[i]['createdByName'] = userNames[item.createdBy?.toString()] || null
            });
            return Promise.resolve(result);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getCardPrintJobById(organizationId, id, ability, boundary) {
        try {
            const result = await CardPrintJobDAO.getCardBatchJob(id, organizationId);
            if (!result) {
                throw new CustomHttpError('card print job not found', 404);
            }

            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.LIST_CARD_BATCH_JOBS, CARD.MODULE_ID, result.regionId?.toString(), boundary, ability, true);
                if (regionIds && !regionIds.includes(result.regionId?.toString())) throw new CustomHttpError('unauthorized region', '403');
            }

            const userNames = await IdentityService.getUsersByIds([result.createdBy]);
            result['createdByName'] = userNames[result.createdBy?.toString()] || null
            if (result.historyEvents?.length > 0){
                result['historyEvents'] = await Utils.getHistoryEventUserNames(result.historyEvents);
            }
            return Promise.resolve(result);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async generateCards(startingCardNo, lastCardNo, organizationId, cardPrintJobId, lastBatchId, callerId, data) {
        let cardsList = [];
        for (let i = startingCardNo; i <= lastCardNo; i++) {
            cardsList.push(new Card({
                regionId: mongoose.Types.ObjectId(data.regionId),
                cardNo: i,
                cardNoStr: i.toString(),
                organizationId: mongoose.Types.ObjectId(organizationId),
                printJobId: cardPrintJobId,
                printJobNumber: lastBatchId,
                status: data.cardStatus,
                processingStatus: data.processingStatus,
                type: data.jobType,
                createdBy: mongoose.Types.ObjectId(callerId),
                historyEvents: [
                    {
                        eventDate: new Date(),
                        eventDetails: `Batch job ID: ${cardPrintJobId}`,
                        eventBy: callerId
                    }
                ]
            }));
            if (cardsList.length % dbRequestChunkSize === 0 ) {
                await CardDAO.createCardBatch(cardsList);
                cardsList = [];
            }
        }

        if (cardsList.length > 0) {
            await CardDAO.createCardBatch(cardsList);
            cardsList = [];
        }
        return Promise.resolve();
    }

    static getTypeData(jobType) {
        let typeData;
        switch (jobType) {
            case CARD_TYPES.DIGITAL_CARD:
                typeData = {
                    configurationType: CARD_CONFIGURATION_TYPES.DIGITAL,
                    prefix: 'DGT'
                }
                break;
            case CARD_TYPES.EMBOSSED_CARD:
                typeData = {
                    configurationType: CARD_CONFIGURATION_TYPES.EMBOSSED,
                    prefix: 'EMB'
                }
                break;
            default:
                typeData = {
                    configurationType: CARD_CONFIGURATION_TYPES.INSTANT,
                    prefix: 'INT'
                }
        }
        return typeData;
    }

    static async getCardBatchJob(id, organizationId) {
        try {
            return await CardPrintJobDAO.getCardBatchJob(id, organizationId);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async updateCardBatchJob(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID, 
            action: CARD.ACTIONS.UPDATE_CARD_BATCH_JOB,
            operation: OPERATION.UPDATE,
            auditMessage: 'card batch job update successful', 
        }
        try {
            const validatedDataObj = await CardPrintJobsValidator.isValidUpdate(dataObj);
            const existingCardBatchJob = await this.getCardBatchJob(id, organizationId);
            if (!existingCardBatchJob) {
                throw new CustomHttpError('Card print job not found', 404);
            }

            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.UPDATE_CARD_BATCH_JOB, CARD.MODULE_ID, existingCardBatchJob.regionId?.toString(), boundary, ability, true);
                if (regionIds && !regionIds.includes(existingCardBatchJob.regionId?.toString())) throw new CustomHttpError('unauthorized region', '403');
            }

            await this.validateStateChange(existingCardBatchJob.status, validatedDataObj.status, existingCardBatchJob.jobType === CARD_TYPES.EMBOSSED_CARD);

            let eventObj;
            switch (validatedDataObj.status) {
                case CARD_BATCH_JOB_STATUS.COMPLETED:
                    if (existingCardBatchJob.jobType !== CARD_TYPES.EMBOSSED_CARD) {
                        eventObj = getEventUpdateObj(callerId, `Status changed to READY, Processing status changed to PRINTED`, {
                            status: CARD_STATUS.READY,
                            processingStatus: CARD_PROCESSING_STATUS.PRINTED
                        });
                    } else {
                        eventObj = getEventUpdateObj(callerId, `Embossed card issued, Processing status changed to COMPLETED`, {
                            processingStatus: validatedDataObj.status,
                            // 'embossCard.embossIssuedOn': new Date(),
                        });
                        await publishEmbossedCardReadyActivities(organizationId, existingCardBatchJob.regionId.toString(), id, existingCardBatchJob.status);
                    }
                    break;
                case CARD_BATCH_JOB_STATUS.FAILED:
                    if (existingCardBatchJob.jobType !== CARD_TYPES.EMBOSSED_CARD) {
                        eventObj = getEventUpdateObj(callerId, `Processing status changed to ${validatedDataObj.status}, Status changed to DEACTIVATED`, {
                            processingStatus: validatedDataObj.status,
                            status: CARD_STATUS.DEACTIVATED
                        });
                    } else {
                        eventObj = [
                            {
                                $set: {
                                    "processingStatus": CARD_PROCESSING_STATUS.REQUESTED,
                                    "printJobId": '$embossCard.previousPrintJobId',
                                    "printJobNumber": '$embossCard.previousPrintJobNumber',
                                    "historyEvents": {
                                        $concatArrays: [[{
                                            eventDate: new Date(),
                                            eventDetails: 'Processing status changed to REQUESTED, Print job ID and number reversed.',
                                            eventBy: callerId
                                        }], "$historyEvents"],
                                    },
                                    "updatedBy": callerId,
                                    "updatedOn": new Date()
                                }
                            }];
                    }
                    break;
                default:
                    eventObj = getEventUpdateObj(callerId, `Processing status changed to ${validatedDataObj.status}`, {
                        processingStatus: validatedDataObj.status
                    });
            }

            const cardsCount = await CardDAO.getCountByFilter(organizationId, {
                printJobId: mongoose.Types.ObjectId(id)
            });
            if (existingCardBatchJob.quantity !== cardsCount) {
                throw new CustomHttpError('card bulk is not ready yet. please try again in a few seconds', '400');
            }

            await CardDAO.updateCardBulkStatus(eventObj, {
                printJobId: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                processingStatus: existingCardBatchJob.status
            });
            const update = getEventUpdateObj(callerId, `Status changed from ${existingCardBatchJob.status} to ${validatedDataObj.status}`, validatedDataObj);
            const updatedResult = await CardPrintJobDAO.updateCardBatchJob(update, id, organizationId);

            addCardStockRefreshJob(organizationId, existingCardBatchJob.regionId.toString());
            return Promise.resolve({
                result: updatedResult, 
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static validateStateChange(currentState, newState, embossedCard = false) {
        if (embossedCard) {
            switch (currentState) {
                case CARD_BATCH_JOB_STATUS.PENDING:
                    if ((newState !== CARD_BATCH_JOB_STATUS.FAILED) && (newState !== CARD_BATCH_JOB_STATUS.PRINTING)) {
                        return Promise.reject(new CustomHttpError('Valid states - PRINTING, FAILED', 400));
                    }
                    break;
                case CARD_BATCH_JOB_STATUS.PRINTING:
                    if ((newState !== CARD_BATCH_JOB_STATUS.FAILED) && (newState !== CARD_BATCH_JOB_STATUS.PRINTED)) {
                        return Promise.reject(new CustomHttpError('Valid states - PRINTED, FAILED', 400));
                    }
                    break;
                case CARD_BATCH_JOB_STATUS.PRINTED:
                    if ((newState !== CARD_BATCH_JOB_STATUS.FAILED) && (newState !== CARD_BATCH_JOB_STATUS.DISPATCHED)) {
                        return Promise.reject(new CustomHttpError('Valid states - DISPATCHED, FAILED', 400));
                    }
                    break;
                case CARD_BATCH_JOB_STATUS.DISPATCHED:
                    if ((newState !== CARD_BATCH_JOB_STATUS.FAILED) && (newState !== CARD_BATCH_JOB_STATUS.COMPLETED)) {
                        return Promise.reject(new CustomHttpError('Valid states - COMPLETED, FAILED', 400));
                    }
                    break;
                default:
                    return Promise.reject(new CustomHttpError(`Invalid state change, Print job is in ${currentState} state`, 400));
            }
        } else {
            switch (currentState) {
                case CARD_BATCH_JOB_STATUS.PENDING:
                    if ((newState !== CARD_BATCH_JOB_STATUS.FAILED) && (newState !== CARD_BATCH_JOB_STATUS.PRINTING)) {
                        return Promise.reject(new CustomHttpError('Valid states - PRINTING, FAILED', 400));
                    }
                    break;
                case CARD_BATCH_JOB_STATUS.PRINTING:
                    if ((newState !== CARD_BATCH_JOB_STATUS.FAILED) && (newState !== CARD_BATCH_JOB_STATUS.COMPLETED)) {
                        return Promise.reject(new CustomHttpError('Valid states - COMPLETED, FAILED', 400));
                    }
                    break;
                default:
                    return Promise.reject(new CustomHttpError(`Invalid state change, Print job is in ${currentState} state`, 400));
            }
        }
    }

    static getBatchAndCardStatus(jobType) {
        if (jobType === CARD_TYPES.DIGITAL_CARD) {
            return {
                batchJobStatus: CARD_BATCH_JOB_STATUS.COMPLETED,
                cardStatus: CARD_STATUS.ACTIVE
            }
        } else {
            return {
                batchJobStatus: CARD_BATCH_JOB_STATUS.PENDING,
                cardStatus: CARD_STATUS.PENDING
            }
        }
    }

    static async exportCardPrintBatchToCSV(organizationId, printJobId,memberFields=[], ability, boundary) {
        const auditData = {
            moduleId: CARD.MODULE_ID,
            action: CARD.ACTIONS.LIST_CARD_BATCH_JOBS, 
            operation: OPERATION.READ,
            auditMessage: 'export card print batch to csv successful',
        }
        try {
            const batchJob = await this.getCardBatchJob(printJobId, organizationId);
            if (!batchJob) {
                throw new CustomHttpError('Card batch job not found', 404);
            }

            if (ability) {
                const { regionIds } = await validateAbility(CARD.ACTIONS.LIST_CARD_BATCH_JOBS, CARD.MODULE_ID, batchJob.regionId?.toString(), boundary, ability, true);
                if (regionIds && !regionIds.includes(batchJob.regionId?.toString())) throw new CustomHttpError('unauthorized region', '403');
            }

            const cursor = await CardDAO.streamData(organizationId, {
                printJobId
            }, 
            memberFields
            );

            let exportResult;
            if (batchJob.jobType === CARD_TYPES.EMBOSSED_CARD) {
                const headers = [
                    { id: 'cardNo', title: 'CARD_NO' },
                    { id: 'printedName', title: 'PRINTED_NAME' },
                    { id: 'locationName', title: 'LOCATION_NAME' },
                    ...(memberFields.map(item=>({id:item, title:snakeCase(item).toUpperCase()})))
                ]
                exportResult  = await Utils.exportToCSV(cursor, organizationId, `card_print_job_${batchJob.batchId}`, headers, 'cards', true, async (dataCursor, csvWriter) => {
                    for (let doc = await cursor.next(); doc != null; doc = await cursor.next()) {
                        const csvData = [{
                            cardNo: doc.cardNo,
                            printedName: doc.embossCard.printedName,
                            locationName: '-',
                            ...(memberFields.reduce((result,item)=>{

                                if(doc.member&&doc.member[item]){
                                    if(item==='postalAddress'||item==="residentialAddress"){
                                        result[item]=AddressUtils.constructAddress(doc.member[item]);
                                    }else{
                                        result[item]=doc.member[item]; 
                                    }
                                }else{
                                    result[item]="-";
                                }

                                return result;
                            },{}))
                        }]
                        await csvWriter.writeRecords(csvData);
                    }
                });
            } else {
                const headers = [
                    { id: 'cardNo', title: 'CARD_NO' },
                    ...(memberFields.map(item=>({id:`member.${item}`,title:snakeCase(item).toUpperCase()})))
                ]
                exportResult = await Utils.exportToCSV(cursor, organizationId, `card_print_job_${batchJob.batchId}`, headers, 'cards', true, async (dataCursor, csvWriter) => {
                    for (let doc = await cursor.next(); doc != null; doc = await cursor.next()) {
                        const csvData = [{
                            cardNo: doc.cardNo,
                            ...(memberFields.reduce((result,item)=>{
                                if(doc.member){
                                    result[item]=doc.member[item];    
                                }
                                return result;
                            },{}))
                        }]
                        await csvWriter.writeRecords(csvData);
                    }
                });
            }
            return Promise.resolve({
                result: exportResult,
                auditData
            });
        } catch (e) {
            console.log(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }
}

module.exports = CardPrintJobsHandler;
