'use strict';
const ConfigsDAO = require('./../db/dao/ConfigsDAO');
const utils = require('./../Utils');
const ConfigsValidator = require('./../validators/ConfigsValidator');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);

class ConfigHandler {
    static getConfig(ownerId, projectedAttributes) {
        return ConfigsDAO.getConfig(ownerId);
    }

    static async createConfig(createPayload, ownerId) {
        try {
            let validatedConfigsObj = await ConfigsValidator.isValid(createPayload);
            validatedConfigsObj.ownerId = ownerId;
            validatedConfigsObj.createdOn = utils.getCurrentDateISO();
            validatedConfigsObj.updatedOn = validatedConfigsObj.createdOn;
            return ConfigsDAO.createConfig(validatedConfigsObj, ownerId);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static deleteConfig(integrationIdDelete, ownerId) {
        return ConfigsDAO.deleteConfig(integrationIdDelete, ownerId);
    }

    static async updateConfig(updatePayload, ownerId, integrationId) {
        try {
            let validatedConfigsObj = await ConfigsValidator.isValid(updatePayload);
            let configObj = {
                $set: validatedConfigsObj
            };
            return ConfigsDAO.updateConfig(integrationId, ownerId, configObj);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }
}

module.exports = ConfigHandler;
