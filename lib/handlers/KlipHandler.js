'use strict';
const { assign } = require('lodash');
const mongoose = require('mongoose');
const Utils = require('./../Utils');
const Shoutout = require('./../services/Shoutout');
const secretConfig = require('./../../config');
const RewardDAO = require('./../db/dao/RewardDAO');
const RegionDAO = require('./../db/dao/RegionDAO');
const RewardRedemptionLogsDAO = require('./../db/dao/RewardRedemptionLogsDAO');
const RewardVoucherDAO = require('./../db/dao/RewardVoucherDAO');
const MembersDAO = require('./../db/dao/MembersDAO');
const MerchantLocationsDAO = require('../db/dao/MerchantLocationsDAO');
const { Type } = require('./../db/models/enums/member.enums');
const { STATUS: VOUCHER_STATUS } = require('./../db/models/enums/reward.voucher.enums');
const { STATUS } = require('./../db/models/enums/reward.redemption.log.enums');
const { SUB_TYPE } = require('./../db/models/enums/reward.enums');
const PointsHandler = require('./../handlers/PointsHandler');
const MerchantsHandler = require('./../handlers/MerchantsHandler');
const RewardRedeemHandler = require('./../handlers/RewardRedeemHandler');
const MembersHandler = require('./../handlers/MembersHandler');
const RedemptionLogsHandler = require('./../handlers/RedemptionLogsHandler');
const CollectPointsValidator = require('./../validators/CollectPointsValidator');
const ClaimRewardValidator = require('./../validators/ClaimRewardValidator');
const RedeemRewardValidator = require('./../validators/RedeemRewardValidator');
const CustomHttpError = require('./../CustomHttpError');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const { OPERATION, BOUNDARY } = require('../db/models/enums/user.enums');
const { ForbiddenError } = require('@casl/ability');

const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { KLIP }
        }
    },
    validateAction,
    validateGet
} = require('@shoutout-labs/authz-utils');

class KlipHandler {
    static async redeemPoints(organizationId, payload, callerId, ability) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.REDEEM_POINTS_WITHOUT_OTP,
            operation: OPERATION.CREATE,
            auditMessage: 'KLIP - Redeem points successful'
        };

        if (ability) {
            ForbiddenError.from(ability).throwUnlessCan(KLIP.ACTIONS.REDEEM_POINTS_WITHOUT_OTP, KLIP.MODULE_ID);
        }

        const { transactionId, transactionSubType, memberId, balancePoints, points } = await PointsHandler.redeemPoints(
            organizationId,
            payload,
            callerId
        );

        return Promise.resolve({
            result: {
                transactionId,
                transactionSubType,
                memberId,
                balancePoints,
                points
            },
            auditData: {
                ...auditData
            }
        });
    }

    static async redeemPointsOtpRequest(organizationId, payload, callerId, ability) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.REDEEM_POINTS,
            operation: OPERATION.CREATE,
            auditMessage: 'Redeem OTP request successful'
        };

        if (ability) {
            ForbiddenError.from(ability).throwUnlessCan(KLIP.ACTIONS.REDEEM_POINTS, KLIP.MODULE_ID);
        }

        const { result } = await PointsHandler.redeemOtpRequest(organizationId, payload, callerId);

        return Promise.resolve({
            result,
            auditData: {
                ...auditData
            }
        });
    }

    static async redeemPointsWithOtp(organizationId, payload, caller, ability) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.REDEEM_POINTS,
            operation: OPERATION.CREATE,
            auditMessage: 'Redeem points with OTP successful'
        };

        try {
            if (ability) {
                ForbiddenError.from(ability).throwUnlessCan(KLIP.ACTIONS.REDEEM_POINTS, KLIP.MODULE_ID);
            }
            const { result } = await PointsHandler.redeemPointsWithOtp(organizationId, payload, caller);
            return Promise.resolve({
                result,
                auditData: {
                    ...auditData
                }
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async adjustPointsRequest(
        organizationId,
        payload,
        callerId,
        ability,
        importJobId,
        systemAdjustment,
        boundary
    ) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.ADJUST_POINTS,
            operation: OPERATION.CREATE,
            auditMessage: 'Points adjustment successful'
        };

        try {
            const { transactionId, transactionSubType, memberId, totalPoints, points } =
                await PointsHandler.adjustPoints(
                    organizationId,
                    payload,
                    callerId,
                    ability,
                    importJobId,
                    systemAdjustment,
                    boundary
                );
            return Promise.resolve({
                result: {
                    transactionId,
                    transactionSubType,
                    memberId,
                    totalPoints,
                    points
                },
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async collectPointsBill(organizationId, payload, callerId, ability, boundary) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.COLLECT_POINTS_BILL,
            operation: OPERATION.CREATE,
            auditMessage: 'KLIP - Collect points (bill) successful'
        };

        try {
            const validatedObj = await CollectPointsValidator.isValidManualBill(payload);
            const merchant = await MerchantsHandler.validateMerchant(validatedObj.merchantId, organizationId);
            if (!merchant) {
                throw new CustomHttpError('invalid merchant', '400');
            }
            const merchantLocation = await PointsHandler.getMerchantLocation(
                validatedObj.merchantLocationCode,
                validatedObj.merchantLocationId,
                validatedObj.merchantId,
                organizationId
            );
            if (!merchantLocation) {
                throw new CustomHttpError('invalid merchant location', '400');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                await validateAction(
                    KLIP.ACTIONS.COLLECT_POINTS_BILL,
                    KLIP.MODULE_ID,
                    merchantLocation.regionId.toString(),
                    boundary,
                    ability,
                    validatedObj.merchantId,
                    merchantLocation._id.toString()
                );
            }

            const result = await PointsHandler.collectPointsBill(organizationId, payload, callerId, null, null);

            return Promise.resolve({
                result,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async collectPoints(organizationId, payload, callerId, ability, boundary) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.COLLECT_POINTS_AMOUNT,
            operation: OPERATION.CREATE,
            auditMessage: 'KLIP - Collect points successful'
        };

        try {
            const validatedObj = await CollectPointsValidator.isValidPoints(payload);
            const merchant = await MerchantsHandler.validateMerchant(validatedObj.merchantId, organizationId);
            if (!merchant) {
                throw new CustomHttpError('invalid merchant', '400');
            }
            const merchantLocation = await PointsHandler.getMerchantLocation(
                validatedObj.merchantLocationCode,
                validatedObj.merchantLocationId,
                validatedObj.merchantId,
                organizationId
            );
            if (!merchantLocation) {
                throw new CustomHttpError('invalid merchant location', '400');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                await validateAction(
                    KLIP.ACTIONS.COLLECT_POINTS_AMOUNT,
                    KLIP.MODULE_ID,
                    merchantLocation.regionId.toString(),
                    boundary,
                    ability,
                    validatedObj.merchantId,
                    merchantLocation._id.toString()
                );
            }

            const result = await PointsHandler.collectPoints(organizationId, payload, callerId, null, null, null, {
                merchant,
                merchantLocation
            });

            return Promise.resolve({
                result,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async getRewards(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                validatedObj = await validateGet(
                    KLIP.ACTIONS.LIST_REWARDS,
                    KLIP.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    validatedObj
                );
            }
            const result = await RewardDAO.getRewards(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async redeemReward(organizationId, payload, callerId, ability, boundary) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.REDEEM_REWARD,
            operation: OPERATION.CREATE,
            auditMessage: 'KLIP - Redeem reward successful'
        };

        try {
            const validatedObj = await RedeemRewardValidator.isValid(payload, organizationId);
            const { regionId, merchantLocationId, rewardId } = validatedObj;

            const region = await RegionDAO.getRegion(regionId);
            if (!region) {
                return Promise.reject(new CustomHttpError('Region not found', '404'));
            }

            const merchantLocation = await RewardRedeemHandler.getMerchantLocation(
                merchantLocationId,
                organizationId,
                region.defaultMerchantLocationId
            );

            const reward = await RewardDAO.getReward(organizationId, rewardId);
            if (!reward) {
                return Promise.reject(new CustomHttpError('Reward not found', '404'));
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !validatedObj.merchantId)
                    validatedObj.merchantId = merchantLocation.merchantId.toString();
                await validateAction(
                    KLIP.ACTIONS.REDEEM_REWARD,
                    KLIP.MODULE_ID,
                    regionId,
                    boundary,
                    ability,
                    reward.merchantId?.toString()
                );
                await validateAction(
                    KLIP.ACTIONS.REDEEM_REWARD,
                    KLIP.MODULE_ID,
                    regionId,
                    boundary,
                    ability,
                    merchantLocation.merchantId?.toString(),
                    merchantLocationId
                );
            }

            const result = await RewardRedeemHandler.redeemReward(organizationId, payload, callerId);

            return Promise.resolve({
                result,
                auditData
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async getRedemptionLogs(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability && boundary !== BOUNDARY.ROOT) {
                validatedObj = await validateGet(
                    KLIP.ACTIONS.LIST_REWARD_REDEMPTION_LOGS,
                    KLIP.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    validatedObj
                );
            }

            const result = await RedemptionLogsHandler.getRedemptionLogs(organizationId, validatedObj);

            return Promise.resolve(result);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getMembers(
        organizationId,
        regionId,
        queryPayload,
        skip,
        limit,
        ability,
        filterPayload = {},
        type,
        exportToCSV,
        boundary
    ) {
        try {
            if (ability) {
                assign(
                    filterPayload,
                    await validateGet(
                        KLIP.ACTIONS.LIST_MEMBERS,
                        KLIP.MODULE_ID,
                        regionId,
                        boundary,
                        ability,
                        filterPayload
                    )
                );
                if (filterPayload) {
                    delete filterPayload.merchantIds;
                }
            }
            const result = await MembersHandler.getMembersAggregation(
                organizationId,
                regionId,
                queryPayload,
                skip,
                limit,
                null,
                filterPayload,
                type,
                exportToCSV
            );
            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMembersCount(organizationId, regionId, queryPayload, ability, filterPayload = {}, type, boundary) {
        try {
            if (ability) {
                assign(
                    filterPayload,
                    await validateGet(
                        KLIP.ACTIONS.LIST_MEMBERS,
                        KLIP.MODULE_ID,
                        regionId,
                        boundary,
                        ability,
                        filterPayload
                    )
                );
                if (filterPayload) {
                    delete filterPayload.merchantIds;
                }
            }
            queryPayload.countMembers = true;
            const result = await MembersHandler.countMembers(
                organizationId,
                regionId,
                queryPayload,
                null,
                filterPayload,
                type
            );
            return Promise.resolve(result?.count ?? 0);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMember(organizationId, id, ability, fields, boundary) {
        try {
            const member = await MembersDAO.getMember(
                organizationId,
                id,
                fields ? [...fields, 'merchantLocationId', 'regionId'] : null,
                { type: { $in: [Type.PRIMARY, Type.SECONDARY] } }
            );
            if (!member) {
                throw new CustomHttpError('Charity not found', '404');
            }
            if (ability) {
                await validateAction(
                    KLIP.ACTIONS.GET_MEMBER,
                    KLIP.MODULE_ID,
                    member.regionId.toString(),
                    boundary,
                    ability
                );
            }
            return Promise.resolve(member);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async claimReward(organizationId, callerId, payload, ability, boundary) {
        const auditData = {
            moduleId: KLIP.MODULE_ID,
            action: KLIP.ACTIONS.CLAIM_REWARD,
            operation: OPERATION.CREATE,
            auditMessage: 'KLIP - Claim reward successful'
        };

        try {
            const validatedObj = await ClaimRewardValidator.isValidVoucherCode(payload);

            let reward, rewardRedemptionLog;
            const rewardVoucher = await RewardVoucherDAO.getVoucher(organizationId, {
                voucherCode: Utils.encrypt(validatedObj.voucherCode, secretConfig.DATA_ENCRYPTION_SECRET)
            });

            if (!rewardVoucher) {
                return Promise.reject(new CustomHttpError('invalid voucher code', 400));
            }

            const { status } = rewardVoucher;
            if (status === VOUCHER_STATUS.CLAIMED) {
                return Promise.reject(new CustomHttpError('reward voucher already claimed', '400'));
            }

            rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(
                organizationId,
                rewardVoucher.rewardRedemptionLogId?.toString()
            );
            reward = await RewardDAO.getReward(organizationId, rewardRedemptionLog.rewardId.toString());

            const redemptionLogId = rewardRedemptionLog._id.toString();

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId)
                    return Promise.reject(new CustomHttpError('unauthorized action', '403'));
                const claimLocation = await MerchantLocationsDAO.getMerchantLocation(
                    rewardRedemptionLog.metadata?.claimLocationId,
                    organizationId
                );
                if (!claimLocation) return Promise.reject(new CustomHttpError('claim location not found', '404'));
                await validateAction(
                    KLIP.ACTIONS.CLAIM_REWARD,
                    KLIP.MODULE_ID,
                    rewardRedemptionLog.regionId?.toString(),
                    boundary,
                    ability,
                    rewardRedemptionLog.merchantId?.toString(),
                    claimLocation._id?.toString()
                );
            }

            if (rewardRedemptionLog.status === STATUS.CANCELLED) {
                return Promise.reject(new CustomHttpError('reward redemption was cancelled', '400'));
            }

            if (rewardRedemptionLog.status === STATUS.CLAIMED) {
                return Promise.reject(new CustomHttpError('reward already claimed', '400'));
            }

            if (rewardRedemptionLog.status !== STATUS.READY) {
                return Promise.reject(new CustomHttpError('reward is not ready yet', '400'));
            }

            const { name, description, type, points, imageUrls } = reward;
            const redemptionLogUpdate = { status: STATUS.CLAIMED };

            const { _id, voucherCode } = rewardVoucher;

            const updatedVoucher = await RewardVoucherDAO.updateVoucher(
                {
                    status: VOUCHER_STATUS.CLAIMED
                },
                _id.toString(),
                organizationId,
                {
                    rewardRedemptionLogId: mongoose.Types.ObjectId(redemptionLogId),
                    status: VOUCHER_STATUS.ISSUED
                },
                callerId
            );
            await RewardRedemptionLogsDAO.updateRedemptionLog(redemptionLogUpdate, redemptionLogId, organizationId);
            await RewardDAO.updateRewardWithIncrement(
                {
                    claimedCount: 1
                },
                rewardRedemptionLog.rewardId.toString(),
                organizationId,
                callerId
            );

            //reward log item id
            const activityData = {
                reward_name: name,
                reward_id: rewardRedemptionLog.rewardId.toString(),
                voucher_code: voucherCode //NOT DECRYPTED
            };

            await Shoutout.produceActivityToTopic({
                memberId: rewardRedemptionLog.memberId.toString(),
                activityName: reward.subType === SUB_TYPE.PARTNER ? `Claim ${name}` : 'Claim Reward',
                organizationId,
                regionId: rewardRedemptionLog.regionId,
                createdOn: new Date(),
                activityData
            });
            return Promise.resolve({
                result: {
                    rewardId: rewardRedemptionLog.rewardId,
                    voucherCode: voucherCode,
                    status: updatedVoucher.status,
                    reward: {
                        name,
                        description,
                        type,
                        points,
                        imageUrls
                    }
                },
                auditData
            });
        } catch (e) {
            log.error('error\n', e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }
}

module.exports = KlipHandler;
