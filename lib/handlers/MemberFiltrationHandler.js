const mongoose = require('mongoose');
const MembersDAO = require('../db/dao/MembersDAO');
const TransactionProductItemDAO = require('../db/dao/TransactionProductItemDAO');
const PointsSummaryByMemberDAO = require('../db/dao/PointsSummaryByMemberDAO');
const { roundOffToTwoDecimals } = require('../utils/NumberUtils');

const modifyMemberResultsToRoundOfPoints = (results = []) => {
    return results.map((member) => ({
        ...member,
        points: roundOffToTwoDecimals(member?.points ? Number(member?.points) : 0),
        tierPoints: roundOffToTwoDecimals(member?.tierPoints || 0),
    }));
};

const parseDatesAndObjectIds = (filter) => {
    const dateAttributes = ["transactionOn", "birthDate", "registeredOn", "createdOn", "lastSeenOn", "lastTransactionOn", "date"];
    const objectIdAttributes = ["organizationId", "regionId", "parentMemberId", "merchantLocationId", "createdBy", "updatedBy", "lastTransactionLocation"];

    for (const attribute of dateAttributes) {
        if (filter.hasOwnProperty(attribute)) {
            const value = filter[attribute];
            if (typeof value === 'string') {
                filter[attribute] = new Date(value);
            } else if (typeof value === 'object') {
                for (const operator in value) {
                    if (value.hasOwnProperty(operator)) {
                        const dateString = value[operator];
                        filter[attribute][operator] = new Date(dateString);
                    }
                }
            }
        }
    }

    for (const attribute of objectIdAttributes) {
        if (filter.hasOwnProperty(attribute)) {
            const value = filter[attribute];
            if (typeof value === 'string') {
                if (mongoose.Types.ObjectId.isValid(value)) {
                    filter[attribute] = new mongoose.Types.ObjectId(value);
                }
            }
        }
    }

    return filter;
};

class MemberFiltrationHandler {
     static async filterMembersCollection(organizationId, regionId, filterPayload, count, skip, limit, stream, exportMembers) {
        const { filter, projection, sort } = filterPayload;
        if (exportMembers) {
            return {
                filter,
                projection
            };
        }
        if (count) {
            return await MembersDAO.getRecordsCount(organizationId, filter);
        }
        const result = await MembersDAO.getMembersFromQueryFilter(organizationId, filter, false, skip, limit, projection, sort, stream);
        return Promise.resolve(result);
    }

    static async filterMembersWithSummaryMetrics(organizationId, regionId, filterPayload, count, skip, limit, stream, exportMembers) {
        const { filter: membersFilter, projection, sort } = filterPayload.membersFilter;
        delete membersFilter.organizationId;
        delete membersFilter.regionId;
        const parsedMembersFilter = parseDatesAndObjectIds(membersFilter);
        const parsedSummaryMetrics = parseDatesAndObjectIds(filterPayload.summaryMetrics)
        const summaryMetricsDateRange = parseDatesAndObjectIds(filterPayload.summaryMetricsDateRange)
        const filter = {
            organizationId: mongoose.Types.ObjectId(organizationId),
            regionId: mongoose.Types.ObjectId(regionId),
            ...summaryMetricsDateRange
        }

        const summaryMetricsProjection = {
            memberId: 1
        };
        const summaryMetricsAggregations = {};

        for (const attribute in parsedSummaryMetrics) {
            summaryMetricsProjection[attribute] = 1;
            summaryMetricsAggregations[attribute] = {
                '$sum': `$${attribute}`
            }
        }

        const aggregationPipeline = [{
            '$match': filter,
        }, {
            '$project': summaryMetricsProjection,
        }, {
            '$group': {
                '_id': '$memberId',
                ...summaryMetricsAggregations
            }
        }, {
            '$match': {
                ...parsedSummaryMetrics
            }
        }, {
            '$lookup': {
                'from': 'members',
                'localField': '_id',
                'foreignField': '_id',
                'pipeline': [{
                    '$match': {
                        ...parsedMembersFilter,
                    },
                }, projection ? {
                    '$project': {
                        ...projection,
                    },
                } : {}],
                'as': 'member',
            },
        }, {
            '$unwind': {
                'path': '$member',
                'includeArrayIndex': '0',
                'preserveNullAndEmptyArrays': false,
            },
        }];

        if (count) {
            aggregationPipeline.push({
                $count: 'count',
            });
            const [result] = await PointsSummaryByMemberDAO.runAggregation(aggregationPipeline);
            return result?.count;
        }

        aggregationPipeline.push({
            '$replaceRoot': {
                'newRoot': {
                    '$mergeObjects': [
                        '$$ROOT', '$member'
                    ]
                }
            }
        }, {
            '$project': {
                'member': 0
            }
        });

        if (sort) {
            aggregationPipeline.push({
                $sort: sort,
            });
        }

        if (stream) {
            return PointsSummaryByMemberDAO.runAggregation(aggregationPipeline, true);
        }

        aggregationPipeline.push({
            $facet: {
                items: [{ $skip: skip }, { $limit: limit }],
            },
        });

        const [result] = await PointsSummaryByMemberDAO.runAggregation(aggregationPipeline);
        return {
            ...result, items: Array.isArray(result?.items) ? modifyMemberResultsToRoundOfPoints(result.items) : []
        };
    }

    static async filterMembersWithTransactions(organizationId, regionId, filterPayload, count, skip, limit, stream, exportMembers) {
        const { filter: membersFilter, projection, sort } = filterPayload.membersFilter;
        delete membersFilter.organizationId;
        delete membersFilter.regionId;
        const parsedMembersFilter = parseDatesAndObjectIds(membersFilter);
        const transactionsFilter = parseDatesAndObjectIds(filterPayload.transactionsFilter);
        const filter = {
            organizationId: mongoose.Types.ObjectId(organizationId),
            regionId: mongoose.Types.ObjectId(regionId),
            ...transactionsFilter
        }

        const aggregationPipeline = [{
            '$match': filter,
        }, {
            '$project': {
                memberId: 1
            },
        }, {
            '$group': {
                '_id': '$memberId'
            }
        }, {
            '$lookup': {
                'from': 'members',
                'localField': '_id',
                'foreignField': '_id',
                'pipeline': [{
                    '$match': {
                        ...parsedMembersFilter,
                    },
                }, projection ? {
                    '$project': {
                        ...projection,
                    },
                } : {}],
                'as': 'member',
            },
        }, {
            '$unwind': {
                'path': '$member',
                'includeArrayIndex': '0',
                'preserveNullAndEmptyArrays': false,
            },
        }];

        if (count) {
            aggregationPipeline.push({
                $count: 'count',
            });
            const [result] = await TransactionProductItemDAO.runAggregation(aggregationPipeline);
            return result?.count;
        }

        aggregationPipeline.push({
            '$replaceRoot': {
                'newRoot': {
                    '$mergeObjects': [
                        '$$ROOT', '$member'
                    ]
                }
            }
        }, {
            '$project': {
                'member': 0
            }
        });

        if (sort) {
            aggregationPipeline.push({
                $sort: sort,
            });
        }

        if (stream) {
            return TransactionProductItemDAO.runAggregation(aggregationPipeline, true);
        }

        aggregationPipeline.push({
            $facet: {
                items: [{ $skip: skip }, { $limit: limit }],
            },
        });

        const [result] = await TransactionProductItemDAO.runAggregation(aggregationPipeline);
        return {
            ...result, items: Array.isArray(result?.items) ? modifyMemberResultsToRoundOfPoints(result.items) : []
        };
    }
}

module.exports = MemberFiltrationHandler;