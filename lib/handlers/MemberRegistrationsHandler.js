'use strict';
const CustomHttpError = require('./../CustomHttpError');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const { OPERATION } = require('../db/models/enums/user.enums');
const { Type, BonusType } = require('../db/models/enums/member.enums');
const MembersValidator = require('../validators/MembersValidator');
const Utils = require('../Utils');
const MerchantLocationsHandler = require('./MerchantLocationsHandler');
const CardDAO = require('../db/dao/CardDAO');
const MembersHandler = require('./MembersHandler');
const PointsHandler = require('./PointsHandler');
const PointRuleDAO = require('../db/dao/PointRuleDAO');
const Shoutout = require('../services/Shoutout');

const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { MEMBER }
        }
    },
    validateAction
} = require('@shoutout-labs/authz-utils');
const { addJob: addCardStockRefreshJob } = require('../../workers/processors/card.stock.view.refresh.job.processor');
const CardsHandler = require('./CardsHandler');
const Member = require('../db/models/member.model');
const MessageFactory = require('../factories/MessageFactory');
const { TEMPLATE_ID } = require('../db/models/enums/template.enums');
const { CATEGORY } = require('../../workers/constants');
const moment = require('moment');
const mongoose = require('mongoose');
const VoucherCodes = require('voucher-code-generator');
const secretConfig = require('./../../config');

const MessagesProcessor = require('../../workers/processors/messages.processor');

const messageQueue = MessagesProcessor.getQueue();

const memberRegistrationPreValidations = async (
    organizationId,
    regionId,
    { merchantLocationCode, merchantLocationId }
) => {
    try {
        const [merchantLocation, zeroTier, region] = await Promise.all([
            MembersHandler.getMerchantLocation(merchantLocationCode, merchantLocationId, organizationId, regionId),
            Utils.getZeroTier(organizationId, regionId),
            Utils.getRegionData(organizationId, regionId)
        ]);

        if (!merchantLocation) throw new CustomHttpError('Merchant location not found', '404', '150103');

        if (!region) throw new CustomHttpError('Region not found', '404', '150005');

        MerchantLocationsHandler.validateLoyaltyOptionWithLocation(merchantLocation, 'enroll');

        return { merchantLocation, zeroTier, region };
    } catch (err) {
        log.error('Failed member registration pre-validations', err);
        return Promise.reject(err);
    }
};

class MemberRegistrationsHandler {
    static async registerMemberWithoutOtp(dataObj, organizationId, verifyEmail, callerId, ability, boundary) {
        const auditData = {
            moduleId: MEMBER.MODULE_ID,
            action: MEMBER.ACTIONS.REGISTER_MEMBER,
            operation: OPERATION.CREATE,
            auditMessage: 'Member registration successful'
        };

        try {
            const validatedObj = await MembersValidator.isValidMemberRegistration(dataObj);

            // * Run member registration pre-validations.
            const { merchantLocation, region, zeroTier } = await memberRegistrationPreValidations(
                organizationId,
                validatedObj.regionId,
                {
                    merchantLocationCode: validatedObj?.merchantLocationCode,
                    merchantLocationId: validatedObj?.merchantLocationId
                }
            );

            if (ability) {
                await validateAction(
                    MEMBER.ACTIONS.REGISTER_MEMBER,
                    MEMBER.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    merchantLocation.merchantId?.toString(),
                    merchantLocation._id?.toString()
                );
            }

            const registerMemberResponse = await this.#registerMember(
                validatedObj,
                organizationId,
                verifyEmail,
                { region, merchantLocation, zeroTier },
                callerId
            );

            return { result: registerMemberResponse, auditData };
        } catch (error) {
            error.errorCode = error?.errorCode || '150400';
            log.error('error\n', error);

            return Promise.reject({ err: error, auditData: { ...auditData, auditMessage: error.message } });
        }
    }

    static async registerMemberWithOtpRequest(payload, organizationId, verifyEmail, _callerId, ability, boundary) {
        const auditData = {
            moduleId: MEMBER.MODULE_ID,
            action: MEMBER.ACTIONS.REGISTER_MEMBER_WITH_OTP,
            operation: OPERATION.CREATE,
            auditMessage: 'Member registration with otp request successful'
        };

        try {
            // ? Register endpoint currently only support manual card generation.
            // * Check if the organization allows manual card generation.
            const {
                configuration: {
                    cardConfiguration: { allowManualCardGeneration }
                }
            } = await Utils.getOrganizationData(organizationId);

            if (!allowManualCardGeneration)
                throw new CustomHttpError('Manual card generation is not allowed', '400', '180001');

            const validatedObj = await MembersValidator.isValidMemberRegistration(payload);

            // * Validate organization primary attribute logic.
            await MembersHandler.validateMemberCreation(organizationId, {
                email: validatedObj?.email,
                mobileNumber: validatedObj?.mobileNumber
            });

            // * If "autoGenerateCard = false", check if the card number to be generated is already in use.
            if (!validatedObj.autoGenerateCard) {
                const existingCard = await CardDAO.validateCardNumbersUniqueness(
                    [validatedObj.cardNumber],
                    organizationId
                );

                if (existingCard)
                    throw new CustomHttpError(
                        `The card number '${existingCard.cardNo}' is already in use`,
                        '400',
                        '180002'
                    );
            }

            // * Run member registration pre-validations.
            const { merchantLocation, region } = await memberRegistrationPreValidations(
                organizationId,
                validatedObj.regionId,
                {
                    merchantLocationCode: validatedObj?.merchantLocationCode,
                    merchantLocationId: validatedObj?.merchantLocationId
                }
            );

            if (ability) {
                await validateAction(
                    MEMBER.ACTIONS.REGISTER_MEMBER_WITH_OTP,
                    MEMBER.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    merchantLocation.merchantId?.toString(),
                    merchantLocation._id?.toString()
                );
            }

            // ! This logic could conflict with organizations that have configured "email" as "primaryAttribute", might have to change it in the future.
            if (!validatedObj?.mobileNumber)
                throw new CustomHttpError('Mobile number is required for create member with otp', '400', '150107');

            const mobileNumber = validatedObj.mobileNumber;

            const otpCode = VoucherCodes.generate({
                length: 5,
                count: 1,
                charset: VoucherCodes.charset('numbers')
            })[0];

            const { smsBody } = await MessageFactory.buildTextMessage(
                {
                    data: {
                        otpCode: !isNaN(otpCode) ? otpCode.toString() : otpCode
                    }
                },
                organizationId,
                validatedObj.regionId.toString(),
                TEMPLATE_ID.OTP // TODO: [SHTT-1530] - DISCUSSION: Should we introduce a new template for this?
            );

            const {
                notificationConfiguration: { smsConfiguration }
            } = region;

            // TODO: [SHTT-1530] - DISCUSSION: Should we add to "emailMessagesQueue" or "messageQueue" queue based on the organization's "primaryAttribute"?
            messageQueue.add(
                {
                    organizationId,
                    regionId: validatedObj.regionId.toString(),
                    memberId: '',
                    body: smsBody,
                    from: smsConfiguration.phoneNumber,
                    to: mobileNumber,
                    category: CATEGORY.SYSTEM
                },
                {
                    jobId: mongoose.Types.ObjectId().toString()
                }
            );

            if (verifyEmail) validatedObj['verifyEmail'] = verifyEmail;

            validatedObj['otpCode'] = otpCode;
            validatedObj['otpExpiry'] = moment().add(10, 'minutes');

            const encryptedRequest = Utils.encrypt(JSON.stringify(validatedObj), secretConfig.VERIFY_TOKEN_SECRET); // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?

            await Utils.setCache(organizationId, `member_registrations:${mobileNumber}`, encryptedRequest);

            return Promise.resolve({
                result: { registerMemberToken: encryptedRequest },
                auditData
            });
        } catch (error) {
            error.errorCode = error?.errorCode || '150400';
            log.error('error\n', error);

            return Promise.reject({ err: error, auditData: { ...auditData, auditMessage: error.message } });
        }
    }

    static async registerMemberWithOtp(payload, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: MEMBER.MODULE_ID,
            action: MEMBER.ACTIONS.REGISTER_MEMBER_WITH_OTP,
            operation: OPERATION.CREATE,
            auditMessage: 'Member registration with otp successful'
        };

        try {
            const validatedObj = await MembersValidator.isValidMemberRegistrationWithOtp(payload);

            const decryptedRequest = JSON.parse(
                Utils.decrypt(validatedObj.registerMemberToken, secretConfig.VERIFY_TOKEN_SECRET) // TODO: [SHTT-1530] - DISCUSSION: Should we have a new token secret "VERIFY_TOKEN_SECRET_MEMBER_REGISTER"?
            );

            if (moment(decryptedRequest.otpExpiry).isBefore(moment())) {
                throw new CustomHttpError('Token expired', '400', '150108');
            }

            const cachedToken = await Utils.getCache(
                organizationId,
                `member_registrations:${decryptedRequest?.mobileNumber}`
            );

            if (!cachedToken || validatedObj.registerMemberToken !== cachedToken) {
                throw new CustomHttpError('Invalid token', '400', '150109');
            }

            if (validatedObj.otpCode !== decryptedRequest.otpCode) {
                throw new CustomHttpError('Invalid OTP code', '400', '150110');
            }

            await Utils.setCache(organizationId, `member_registrations:${decryptedRequest?.mobileNumber}`, null);

            delete decryptedRequest.otpCode;
            delete decryptedRequest.otpExpiry;

            // * Run member registration pre-validations.
            const { merchantLocation, region, zeroTier } = await memberRegistrationPreValidations(
                organizationId,
                decryptedRequest?.regionId,
                {
                    merchantLocationCode: decryptedRequest?.merchantLocationCode,
                    merchantLocationId: decryptedRequest?.merchantLocationId
                }
            );

            if (ability) {
                await validateAction(
                    MEMBER.ACTIONS.REGISTER_MEMBER_WITH_OTP,
                    MEMBER.MODULE_ID,
                    decryptedRequest?.regionId,
                    boundary,
                    ability,
                    merchantLocation.merchantId?.toString(),
                    merchantLocation._id?.toString()
                );
            }

            const { verifyEmail, ...restMemberRegistrationPayload } = decryptedRequest;

            const registerMemberResponse = await this.#registerMember(
                restMemberRegistrationPayload,
                organizationId,
                verifyEmail,
                { region, merchantLocation, zeroTier },
                callerId
            );

            return { result: registerMemberResponse, auditData };
        } catch (error) {
            error.errorCode = error?.errorCode || '150400';
            log.error('error\n', error);

            return Promise.reject({ err: error, auditData: { ...auditData, auditMessage: error.message } });
        }
    }

    static async #registerMember(
        validatedObj = {},
        organizationId,
        verifyEmail,
        { region = {}, merchantLocation = {}, zeroTier = {} },
        callerId
    ) {
        let session;

        try {
            const { regionId, affinityGroup, cardNumber, autoGenerateCard } = validatedObj;

            const merchantLocationId = merchantLocation._id.toString();

            session = await Member.startSession();

            let createdUser,
                card,
                affinityGroupId =
                    affinityGroup?.affinityGroupId?.toString() ||
                    region.memberConfiguration.defaultAffinityGroupId?.toString();

            await session.withTransaction(async () => {
                const opts = { session };

                if (autoGenerateCard) {
                    const { result: cards } = await CardsHandler.generateCardsManually(
                        { regionId, autoGenerate: autoGenerateCard },
                        organizationId,
                        callerId,
                        null,
                        null,
                        opts
                    );
                    card = cards[0];

                    const loyaltyProfile = await MembersHandler.createLoyaltyProfile(
                        organizationId,
                        { ...validatedObj, type: Type.PRIMARY },
                        region._id.toString(),
                        null,
                        affinityGroupId,
                        affinityGroup?.expiryDate,
                        zeroTier?._id || null,
                        merchantLocationId,
                        callerId,
                        card?.cardNoStr,
                        opts
                    );
                    createdUser = loyaltyProfile;
                } else {
                    const { result: cards } = await CardsHandler.generateCardsManually(
                        { regionId, cardNumbers: [cardNumber] },
                        organizationId,
                        callerId,
                        null,
                        null,
                        opts
                    );
                    const loyaltyProfile = await MembersHandler.createLoyaltyProfile(
                        organizationId,
                        { ...validatedObj, type: Type.PRIMARY },
                        region._id.toString(),
                        null,
                        affinityGroupId,
                        affinityGroup?.expiryDate,
                        zeroTier?._id || null,
                        merchantLocationId,
                        callerId,
                        cardNumber,
                        opts
                    );

                    card = cards[0];
                    createdUser = loyaltyProfile;
                }

                await CardDAO.assignCard(
                    card._id.toString(),
                    organizationId,
                    callerId,
                    createdUser._id.toString(),
                    opts
                );
            });

            await Promise.all([
                this.collectBonusPoints(organizationId, regionId, createdUser, callerId),
                this.createActivities(organizationId, regionId, validatedObj, createdUser, cardNumber, card),
                this.sendVerificationEmail(verifyEmail, createdUser, region, organizationId)
            ]);

            return createdUser;
        } catch (error) {
            if (session) {
                try {
                    await session.abortTransaction();
                    session.endSession();
                } catch (e) {
                    log.error('error\n', e);
                }
            }

            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async collectBonusPoints(organizationId, regionId, createdUser, callerId) {
        try {
            const {
                selectedPointRule: { pointRuleId, points: pointsFromRule }
            } = await MembersHandler.calculateBonusPoints(organizationId, regionId);
            if (pointsFromRule > 0) {
                await PointsHandler.collectPointsForBonus(
                    organizationId,
                    callerId,
                    pointsFromRule,
                    new Date(),
                    regionId,
                    BonusType.SIGNUP,
                    createdUser._id.toString(),
                    `ntr-${BonusType.SIGNUP}-${createdUser._id.toString()}`
                );
                PointRuleDAO.updatePointRule(
                    { $inc: { matchedCount: 1 } },
                    pointRuleId,
                    organizationId,
                    createdUser._id.toString()
                );
            }
        } catch (e) {
            log.error('Error while collecting bonus points', e);
        }
    }

    static async createActivities(organizationId, regionId, validatedObj, createdUser, cardNumber, card) {
        try {
            await Promise.all([
                Shoutout.produceActivityToTopic({
                    memberId: createdUser._id.toString(),
                    activityName: 'Register Loyalty',
                    organizationId,
                    regionId: regionId.toString(),
                    createdOn: new Date(),
                    activityData: {
                        ...validatedObj,
                        register_on: createdUser.register_on,
                        user_id: createdUser.user_id,
                        type: Type.PRIMARY
                    }
                }),
                Shoutout.produceActivityToTopic({
                    memberId: createdUser._id.toString(),
                    activityName: 'Assign Loyalty Card',
                    organizationId,
                    regionId: regionId.toString(),
                    createdOn: new Date(),
                    activityData: { cardNo: cardNumber, cardType: card.type }
                }),
                addCardStockRefreshJob(organizationId, card.regionId.toString())
            ]);
        } catch (e) {
            log.error('Error while creating activities or refreshing card stock', e);
        }

        try {
            await MembersHandler.publishLoyaltyProfileMetadata(organizationId, regionId.toString(), createdUser);
        } catch (e) {
            log.error('Error while publishing loyalty profile metadata', e);
        }
    }

    static async sendVerificationEmail(verifyEmail, createdUser, region, organizationId) {
        if (verifyEmail && createdUser.email) {
            try {
                const {
                    notificationConfiguration: { emailConfiguration }
                } = region;
                await MembersHandler.sendEmailVerification(
                    createdUser.email,
                    createdUser._id.toString(),
                    region._id.toString(),
                    emailConfiguration,
                    organizationId,
                    createdUser.firstName
                );
            } catch (e) {
                log.error('Error while sending the member verification email', e);
            }
        }
    }
}

module.exports = MemberRegistrationsHandler;
