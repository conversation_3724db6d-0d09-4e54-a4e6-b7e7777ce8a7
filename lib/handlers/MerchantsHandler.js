'use strict';
const MerchantDAO = require('./../db/dao/MerchantDAO');
const MerchantLocationsDAO = require('./../db/dao/MerchantLocationsDAO');
const PointRuleDAO = require('./../db/dao/PointRuleDAO');
const RegionDAO = require('./../db/dao/RegionDAO');
const { Status } = require('./../db/models/enums/merchant.enums');
const { STATUS: MerchantLocationStatus } = require('./../db/models/enums/merchant.location.enums');
const { STATUS: PointRuleStatus, TYPE } = require('./../db/models/enums/point.rule.enums');
const { OPERATION } = require('./../db/models/enums/user.enums');
const MerchantLocationEnums = require('./../db/models/enums/merchant.location.enums');
const CustomHttpError = require('./../CustomHttpError');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const MerchantsValidator = require('../validators/MerchantsValidator');
const mongoose = require('mongoose');
const _ = require('lodash');
const Utils = require('./../Utils');
const { getWrapperForCache, promiseRaceAsync } = Utils;
const RedisConnector = require('./../db/connectors/RedisConnector');
const { getAsync, delAsync } = RedisConnector.getCommands();
const { addJob: addCardStockRefreshJob } = require('./../../workers/processors/card.stock.view.refresh.job.processor');

const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { MERCHANT }
        }
    },
    validateAbility,
    validateAction,
    validateGet
} = require('@shoutout-labs/authz-utils');

const invalidateMerchantCache = async (organizationId, merchantId) => {
    await delAsync(`merchants:${organizationId}:${merchantId}`);
};

const validateMerchantLoyaltyOptionsUpdate = async (merchantId, loyaltyOptions, organizationId) => {
    const optionsUpdateObj = {};
    _.forEach(loyaltyOptions, (val, option) => {
        if (!val) {
            optionsUpdateObj[`options.${option}`] = false;
        }
    });
    if (Object.keys(optionsUpdateObj).length > 0) {
        await MerchantLocationsDAO.updateBulk(
            { $set: optionsUpdateObj },
            {
                merchantId: mongoose.Types.ObjectId(merchantId),
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: Status.ACTIVE
            }
        );
    }
};

const validateDefaultMerchantModifications = (updateObj = {}) => {
    if (updateObj?.options) throw new CustomHttpError('Default merchant "options" cannot be modified.', '400');
    if (updateObj?.status) throw new CustomHttpError('Default merchant "status" cannot be modified.', '400');
    if (updateObj?.type) throw new CustomHttpError('Default merchant "type" cannot be modified.', '400');
};

class MerchantsHandler {
    static async createMerchant(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: MERCHANT.MODULE_ID,
            action: MERCHANT.ACTIONS.CREATE_MERCHANT,
            operation: OPERATION.CREATE,
            auditMessage: 'merchant creation successful'
        };
        try {
            const validatedObj = await MerchantsValidator.isValid(dataObj);
            if (ability) {
                const { regionIds } = await validateAbility(
                    MERCHANT.ACTIONS.CREATE_MERCHANT,
                    MERCHANT.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    true
                );
                if (regionIds && !regionIds.includes(validatedObj.regionId))
                    throw new CustomHttpError('unauthorized action', '403');
            }
            const createdObj = await MerchantDAO.createMerchant(validatedObj, organizationId, callerId);
            return Promise.resolve({
                result: createdObj,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async getMerchantById(id, organizationId, ability, boundary) {
        try {
            const result = await MerchantDAO.getMerchant(id, organizationId);
            if (!result) {
                throw new CustomHttpError('Merchant not found', '404');
            }

            if (ability) {
                await validateAction(
                    MERCHANT.ACTIONS.GET_MERCHANT,
                    MERCHANT.MODULE_ID,
                    result.regionId.toString(),
                    boundary,
                    ability,
                    result._id.toString()
                );
            }

            return Promise.resolve(result);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static async getMerchantsPortal(organizationId, regionId, validatedObj) {
        try {
            validatedObj['status'] = Status.ACTIVE;
            validatedObj['regionId'] = regionId;
            const merchant = await MerchantDAO.getMerchantsPortal(organizationId, validatedObj, {
                merchantName: 1,
                merchantLogoImageUrl: 1,
                contact: 1,
                type: 1,
                countryISO2Code: 1,
                countryName: 1,
                createdOn: 1
            });
            return Promise.resolve(merchant);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getMerchants(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                validatedObj = await validateGet(
                    MERCHANT.ACTIONS.LIST_MERCHANTS,
                    MERCHANT.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    validatedObj
                );
            }
            const result = await MerchantDAO.getMerchants(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async getMerchantsSystem(organizationId, validatedObj) {
        try {
            let projection;
            if (validatedObj.fields) {
                projection = Utils.buildProjection(validatedObj.fields);
            }
            const result = await MerchantDAO.getMerchants(organizationId, validatedObj, projection);
            return Promise.resolve(result);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async updateMerchant(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: MERCHANT.MODULE_ID,
            action: MERCHANT.ACTIONS.UPDATE_MERCHANT,
            operation: OPERATION.UPDATE,
            auditMessage: 'merchant update successful'
        };
        try {
            const validatedDataObj = await MerchantsValidator.isValidUpdate(dataObj);
            const merchant = await MerchantDAO.getMerchant(id, organizationId);
            if (!merchant) {
                throw new CustomHttpError('Merchant not found', '404');
            }

            if (ability) {
                await validateAction(
                    MERCHANT.ACTIONS.UPDATE_MERCHANT,
                    MERCHANT.MODULE_ID,
                    merchant.regionId.toString(),
                    boundary,
                    ability,
                    merchant._id.toString()
                );
            }

            const { defaultMerchantId } = await RegionDAO.getRegion(merchant.regionId.toString());

            if (defaultMerchantId?.toString() === id) validateDefaultMerchantModifications(validatedDataObj);

            if (validatedDataObj.status) {
                if (validatedDataObj.status === Status.ACTIVE) {
                    const { items } = await MerchantLocationsDAO.getMerchantLocations(organizationId, {
                        merchantId: id,
                        status: MerchantLocationStatus.ACTIVE
                    });
                    if (items.length === 0) {
                        throw new CustomHttpError(
                            'to activate the merchant, at least one active merchant location is required.',
                            '400'
                        );
                    }

                    const pointRules = await PointRuleDAO.getPointRulesByFilter(organizationId, {
                        merchantId: mongoose.Types.ObjectId(id),
                        type: TYPE.TRANSACTIONAL,
                        status: PointRuleStatus.ENABLED
                    });
                    if (pointRules.length === 0) {
                        throw new CustomHttpError(
                            'to activate the merchant, at least one enabled point rule is required.',
                            '400'
                        );
                    }
                }
            }

            if (validatedDataObj.options) {
                await validateMerchantLoyaltyOptionsUpdate(id, validatedDataObj.options, organizationId);
            }
            const updatedDocument = await MerchantDAO.updateMerchant(validatedDataObj, id, organizationId, callerId);
            if (validatedDataObj.merchantName) {
                addCardStockRefreshJob(organizationId, merchant.regionId.toString());
            }

            await invalidateMerchantCache(organizationId, id);

            return Promise.resolve({ result: updatedDocument, auditData });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async deleteMerchant(id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: MERCHANT.MODULE_ID,
            action: MERCHANT.ACTIONS.DELETE_MERCHANT,
            operation: OPERATION.DELETE,
            auditMessage: 'merchant deletion successful'
        };
        try {
            const merchant = await MerchantDAO.getMerchant(id, organizationId);
            if (!merchant) {
                throw new CustomHttpError('Merchant not found', '404');
            }

            if (ability) {
                await validateAction(
                    MERCHANT.ACTIONS.DELETE_MERCHANT,
                    MERCHANT.MODULE_ID,
                    merchant.regionId.toString(),
                    boundary,
                    ability,
                    merchant._id.toString()
                );
            }

            const { defaultMerchantId } = await RegionDAO.getRegion(merchant.regionId.toString());
            if (defaultMerchantId.toString() === id) {
                throw new CustomHttpError('the default merchant cannot be archived', '400');
            }
            const result = await MerchantDAO.deleteMerchant(id, organizationId, callerId);
            await MerchantLocationsDAO.updateBulk(
                {
                    status: MerchantLocationEnums.STATUS.ARCHIVED
                },
                {
                    merchantId: mongoose.Types.ObjectId(id),
                    organizationId: mongoose.Types.ObjectId(organizationId)
                }
            );

            await invalidateMerchantCache(organizationId, id);

            return Promise.resolve({
                result,
                auditData
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async validateMerchant(id, organizationId) {
        try {
            const cacheKey = `merchants:${organizationId}:${id}`;
            const result = await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(MerchantDAO.getMerchant, [id, organizationId], true)
                ],
                cacheKey
            );
            if (!result) {
                return Promise.reject(new CustomHttpError('Invalid merchant', '400'));
            }
            return result;
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }
}

module.exports = MerchantsHandler;
