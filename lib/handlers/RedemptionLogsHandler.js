/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/29/16.
 */
'use strict';
const RewardRedemptionLogsDAO = require('./../db/dao/RewardRedemptionLogsDAO');
const RewardDistributionJobsDAO = require('./../db/dao/RewardDistributionJobsDAO');
const TransactionsDAO = require('./../db/dao/TransactionsDAO');
const RewardVoucherDAO = require('./../db/dao/RewardVoucherDAO');
const OrganizationDAO = require('./../db/dao/OrganizationDAO');
const RewardDAO = require('./../db/dao/RewardDAO');
const PointsHandler = require('./../handlers/PointsHandler');
const IdentityService = require('../services/IdentityService');
const { STATUS, PROCESSING_STATUS, REFUND_STATUS, PARTNER_STATUS } = require('./../db/models/enums/reward.redemption.log.enums');
const { BOUNDARY, OPERATION } = require('./../db/models/enums/user.enums');
const { STATUS: VOUCHER_STATUS } = require('./../db/models/enums/reward.voucher.enums');
const { SUB_TYPE } = require('./../db/models/enums/reward.enums');
const config = require('./../config');
const logger = require('./../logger');
const Utils = require('./../../lib/Utils');
const mongoose = require('mongoose');
const log = logger(config.logger);
const secretConfig = require('../../config');
const RedemptionLogsValidator = require('../validators/RedemptionLogsValidator');
const CustomHttpError = require('../CustomHttpError');
const Shoutout = require('../services/Shoutout');
const RedemptionLogsBulkRefundJobsDAO = require('../db/dao/RedemptionLogsBulkRefundJobsDAO');
const RewardRedemptionLogsBulkRefundJobProcessor = require('../../workers/processors/reward.redemption.logs.bulk.refund.job.processor');

const { AUTH_CONSTANTS: {
    AUTH_MODULE_ACTIONS: {
        LOYALTY_SERVICE: {
            REWARD
        }
    }
}, validateGet, validateAction } = require('@shoutout-labs/authz-utils');

const getEventUpdateObj = (callerId, eventDetails, setObj) => {
    return {
        $set: setObj,
        $push: {
            historyEvents: {
                $each: [
                    {
                        eventDate: new Date(),
                        eventDetails,
                        eventBy: callerId
                    }
                ],
                $sort: { eventDate: -1 }
            }
        },
        $currentDate: { modifiedOn: true },
        updatedBy: callerId
    };
};

class RedemptionLogsHandler {

    static async getRedemptionLogs(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability && boundary !== BOUNDARY.ROOT) {
                validatedObj = await validateGet(REWARD.ACTIONS.LIST_REWARD_REDEMPTION_LOGS, REWARD.MODULE_ID, validatedObj.regionId, boundary, ability, validatedObj);
            }
            const result = await RewardRedemptionLogsDAO.getRewardRedemptionLogs(organizationId, validatedObj);
            if (result.items.length === 0) {
                return Promise.resolve(result);
            }
            const userNames = await IdentityService.getUsersByIds(result.items.map(item => item.createdBy));
            result.items.forEach((item, i) => {
                result.items[i]['createdByName'] = userNames[item.createdBy?.toString()] || null
            });
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getRedemptionLog(id, organizationId, ability, boundary) {
        try {
            const result = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, id);
            if (!result) {
                throw new CustomHttpError('Reward redemption log not found', '404');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !result.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.GET_REWARD_REDEMPTION_LOG, REWARD.MODULE_ID, result.regionId?.toString(), boundary, ability, result.merchantId?.toString());
            }

            const userNames = await IdentityService.getUsersByIds([result.createdBy]);
            result['createdByName'] = userNames[result.createdBy?.toString()] || null
            if (result.historyEvents?.length > 0){
                result['historyEvents'] = await Utils.getHistoryEventUserNames(result.historyEvents);
            }
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getRedemptionLogsPortal(organizationId, validatedObj, memberId, regionId) {
        try {
            validatedObj['memberId'] = memberId;
            validatedObj['regionId'] = regionId;
            validatedObj['statusArray'] = validatedObj.status;
            delete validatedObj['status'];
            const { items, total } = await RewardRedemptionLogsDAO.getRewardRedemptionLogs(organizationId, validatedObj, {
                updatedOn: 0,
                createdBy: 0,
                updatedBy: 0,
                memberId: 0,
                regionId: 0,
                organizationId: 0
            });
            if (total > 0) {
                for (const logItem of items) {
                    if (logItem.voucher?.voucherCode) {
                        logItem.voucher.voucherCode = Utils.decrypt(logItem.voucher.voucherCode, secretConfig.DATA_ENCRYPTION_SECRET);
                    }
                }
            }
            return Promise.resolve({
                items,
                total
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async cancelLogItem(payload, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.CANCEL_LOG_ITEM,
            operation: OPERATION.UPDATE,
            auditMessage: 'Successfully canceled the redmeption log item',
        }
        try {
            const { notes } = await RedemptionLogsValidator.isValidRefund(payload);
            const rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, id);
            if (!rewardRedemptionLog) {
                throw new CustomHttpError('Redemption log not found', '404');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId) throw new CustomHttpError('Unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.CANCEL_LOG_ITEM, REWARD.MODULE_ID, rewardRedemptionLog.regionId?.toString(), boundary, ability, rewardRedemptionLog.merchantId?.toString());
            }

            if (rewardRedemptionLog.refundStatus === REFUND_STATUS.REFUNDED) {
                throw new CustomHttpError('Redemption has already been refunded', '400');
            }

            if (rewardRedemptionLog.rewardSubType !== SUB_TYPE.PARTNER && rewardRedemptionLog.status !== STATUS.REQUESTED) {
                throw new CustomHttpError('Reward redemption log must be in REQUESTED state', '400');
            }

            const transaction = await TransactionsDAO.getTransaction(organizationId, {
                _id: mongoose.Types.ObjectId(rewardRedemptionLog.transactionId)
            });
            if (!transaction) {
                throw new CustomHttpError('Transaction not found', '404');
            }

            const reward = await RewardDAO.getReward(organizationId, rewardRedemptionLog.rewardId.toString());

            if (
                rewardRedemptionLog.rewardSubType === SUB_TYPE.PARTNER &&
                !~[PROCESSING_STATUS.PENDING, PROCESSING_STATUS.PROCESSING].indexOf(
                    rewardRedemptionLog.processingStatus,
                )
            ) {
                throw new CustomHttpError(
                    `${reward?.name || 'Partner reward'} redemption log must be in PENDING or PROCESSING state`,
                    '400',
                );
            }

            const { subtransactionTypeIdMap: {
                refundPoints
            } } = await OrganizationDAO.getOrganization(organizationId);

            const result = await PointsHandler.adjustPoints(organizationId, {
                memberId: rewardRedemptionLog.memberId.toString(),
                merchantId: transaction.merchantId.toString(),
                merchantLocationId: transaction.merchantLocationId.toString(),
                pointsAmount: rewardRedemptionLog.pointsRedeemed,
                transactionDate: Date.now(),
                transactionSubTypeId: refundPoints?.toString()
            }, callerId, null, null, true);

            let event = 'Redemption canceled';

            if (rewardRedemptionLog.rewardSubType === SUB_TYPE.PARTNER) {
                event = `Canceled ${reward?.name || 'partner reward'} redemption`
                event += `, Status changed from ${rewardRedemptionLog.status} to ${STATUS.CANCELLED}`;
                event += `, Points refunded`;
                event += `, Status changed from ${STATUS.CANCELLED} to ${REFUND_STATUS.REFUNDED}`;
                event += `, Processing status changed from ${rewardRedemptionLog.processingStatus} to ${PROCESSING_STATUS.FAILED}`;
            };

            const eventObj = getEventUpdateObj(callerId, event, {
                refundStatus: REFUND_STATUS.REFUNDED,
                status:
                    rewardRedemptionLog.rewardSubType === SUB_TYPE.PARTNER
                        ? REFUND_STATUS.REFUNDED
                        : STATUS.CANCELLED,
                processingStatus: PROCESSING_STATUS.FAILED,
                notes,
            });
            const updatedResult = await RewardRedemptionLogsDAO.updateRedemptionLog(eventObj, id, organizationId, callerId);
            log.info('Canceled redemption', updatedResult);

            if (rewardRedemptionLog.rewardSubType === SUB_TYPE.VOUCHER) {
                const rewardVoucher = await RewardVoucherDAO.getVoucher(organizationId, {
                    rewardRedemptionLogId: mongoose.Types.ObjectId(id)
                });
                await RewardVoucherDAO.updateVoucher({
                    status: VOUCHER_STATUS.INVALIDATED
                }, rewardVoucher._id.toString(), organizationId, {
                    rewardRedemptionLogId: mongoose.Types.ObjectId(id),
                    status: VOUCHER_STATUS.ISSUED
                }, callerId);
                await RewardDAO.updateRewardWithIncrement({
                    invalidatedCount: 1
                }, reward._id.toString(), organizationId, callerId);
                // await RewardDAO.updateRewardWithIncrement({
                //     usedCount: -1
                // }, rewardRedemptionLog.rewardId.toString(), organizationId, callerId);
            }

            const activityData = {
                reward_name: reward.name,
                reward_id: reward._id.toString()
            };
            await Shoutout.produceActivityToTopic({
                memberId: rewardRedemptionLog.memberId.toString(),
                activityName:
                    reward.subType === SUB_TYPE.PARTNER
                    ? `Cancel ${reward.name || 'Partner Reward'} Redemption`
                    : 'Cancel Reward Redemption',
                organizationId,
                regionId: rewardRedemptionLog.regionId,
                createdOn: new Date(),
                activityData,
            });

            return Promise.resolve({ 
                result, 
                auditData 
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            })
        }
    }

    static async refundLogItem(payload, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID, 
            action: REWARD.ACTIONS.REFUND_LOG_ITEM,
            operation: OPERATION.UPDATE,
            auditMessage: 'refund log item successful', 
        }
        try {
            const { notes } = await RedemptionLogsValidator.isValidRefund(payload);
            const rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, id);
            if (!rewardRedemptionLog) {
                throw new CustomHttpError('Reward redemption log not found', '404');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.REFUND_LOG_ITEM, REWARD.MODULE_ID, rewardRedemptionLog.regionId?.toString(), boundary, ability, rewardRedemptionLog.merchantId?.toString());
            }

            if (rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER) {
                if (rewardRedemptionLog.status !== PARTNER_STATUS.FAILED) {
                    throw new CustomHttpError('Partner reward redemption log is not in FAILED state', '400');
                }               
            }
            else {
                if (rewardRedemptionLog.status !== STATUS.CANCELLED) {
                    throw new CustomHttpError('Reward redemption log is not in CANCELLED state', '400');
                }                
            }

            if (rewardRedemptionLog.refundStatus === REFUND_STATUS.REFUNDED || (rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER && rewardRedemptionLog.status === PARTNER_STATUS.REFUNDED)) {
                throw new CustomHttpError('Already refunded', '400');
            }

            const reward = await RewardDAO.getReward(organizationId, rewardRedemptionLog.rewardId.toString());

            const transaction = await TransactionsDAO.getTransaction(organizationId, {
                _id: mongoose.Types.ObjectId(rewardRedemptionLog.transactionId)
            });
            if (!transaction) {
                throw new CustomHttpError('Transaction not found', '404');
            }

            const { subtransactionTypeIdMap: {
                refundPoints
            } } = await OrganizationDAO.getOrganization(organizationId);

            log.debug(transaction);

            const adjustObj = {
                memberId: rewardRedemptionLog.memberId?.toString(),
                merchantId: transaction.merchantId?.toString(),
                merchantLocationId: transaction.merchantLocationId?.toString(),
                pointsAmount: rewardRedemptionLog.pointsRedeemed,
                transactionDate: Date.now(),
                transactionSubTypeId: refundPoints?.toString()
            };
            let event = 'Points refunded. ';

            if (reward.subType === SUB_TYPE.PARTNER) event += `Status changed from ${rewardRedemptionLog.status} to ${PARTNER_STATUS.REFUNDED}`;

            const result = await PointsHandler.adjustPoints(organizationId, adjustObj, callerId, null, null, true);

            const eventObj = getEventUpdateObj(
                callerId, 
                event, 
                {
                    ...(reward.subType === SUB_TYPE.PARTNER ? 
                        { status: PARTNER_STATUS.REFUNDED } 
                        : {}
                    ),
                    refundStatus: REFUND_STATUS.REFUNDED,
                    notes
                }
            );
            const updatedResult = await RewardRedemptionLogsDAO.updateRedemptionLog(eventObj, id, organizationId, callerId);

            const activityData = {
                reward_name: reward.name,
                reward_id: reward._id.toString()
            };
            await Shoutout.produceActivityToTopic({
                memberId: rewardRedemptionLog.memberId.toString(),
                activityName: reward.subType === SUB_TYPE.PARTNER ? `Refund ${reward.name} Redemption` : 'Refund Reward Redemption',
                organizationId,
                regionId: rewardRedemptionLog.regionId,
                createdOn: new Date(),
                activityData
            });

            return Promise.resolve({ 
                result, 
                auditData 
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async bulkRefundPartnerRewardLogItems(payload, organizationId, callerId, ability, boundary) {
        // TODO: [MLS-3426] Implement audit logs for the funtion 'bulkRefundPartnerRewardLogItems'.
        // const auditData = {
        //     moduleId: REWARD.MODULE_ID,
        //     action: REWARD.ACTIONS.UPDATE_LOG_ITEM,
        //     operation: OPERATION.UPDATE,
        //     auditMessage: 'reward redemption log update successful',
        // }
        try {
            // TODO: [MLS-3427] Implement permissions for the function 'bulkRefundPartnerRewardLogItems'.
            // if (ability && boundary !== BOUNDARY.ROOT) {
            // }

            const { distributionJobId, notes } = await RedemptionLogsValidator.isValidBulkRefundPartnerRewards(payload);
            const rewardDistributionJob = await RewardDistributionJobsDAO.getRewardDistributionJobById(organizationId, distributionJobId);

            if (!rewardDistributionJob) {
                throw new CustomHttpError('Reward distribution job not found', '404');
            }

            if (rewardDistributionJob?.status !== PROCESSING_STATUS.FAILED) {
                throw new CustomHttpError('Must be a FAILED reward distribution job.', '400');
            }

            const filters = {
                distributionJobId: mongoose.Types.ObjectId(distributionJobId),
                status: { $in: [PARTNER_STATUS.PROCESSING, PARTNER_STATUS.FAILED] },
                $or: [
                    { refundStatus: { $exists: false } }, 
                    { refundStatus: { $exists: true, $eq: REFUND_STATUS.NONE } }
                ],
            };
            const eligibleRefundCount = await RewardRedemptionLogsDAO.eligibleRedemptionsForRefunding(organizationId, filters) || 0;

            if (eligibleRefundCount === 0) {
                throw new CustomHttpError('No refund eligible redemptions found', '404');
            } else {
                log.debug('No. of redemptions to be refunded: ', eligibleRefundCount);

                const { subtransactionTypeIdMap: { refundPoints } } = await OrganizationDAO.getOrganization(organizationId);  

                const createJobValidObj = {
                    organizationId,
                    regionId: rewardDistributionJob.regionId,
                    distributionJobId, 
                    refundPointsSubTransactionId: refundPoints,
                    eligibleRefundCount, 
                    notes, 
                    createdBy: callerId
                };

                const createdRLBulkRefundJob = await RedemptionLogsBulkRefundJobsDAO.createRedemptionLogsBulkRefundJob(createJobValidObj, organizationId, callerId);
                log.debug('Created redemption logs bulk refund job', createdRLBulkRefundJob);

                RewardRedemptionLogsBulkRefundJobProcessor.addJob(createdRLBulkRefundJob, createdRLBulkRefundJob._id.toString());
            }

            return Promise.resolve({
                result: { eligibleRefundCount },
                auditData: {}, // TODO: [MLS-3426] Implement audit logs for the funtion 'bulkRefundPartnerRewardLogItems'.
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: {} // TODO: [MLS-3426] Implement audit logs for the funtion 'bulkRefundPartnerRewardLogItems'.
            });
            
        }
    };

    static async updatePartnerRewardLogItem(
        payload,
        id,
        organizationId,
        callerId,
        ability,
        boundary,
    ) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.UPDATE_LOG_ITEM,
            operation: OPERATION.UPDATE,
            auditMessage: 'Successfully updated partner reward redemption log.',
        };

        try {
            const validatedDataObj =
                await RedemptionLogsValidator.isValidPartnerRewardLogUpdate(payload);

            const rewardRedemptionLog =
                await RewardRedemptionLogsDAO.getRewardRedemptionLog(
                    organizationId,
                    id,
                );

            if (!rewardRedemptionLog) {
                throw new CustomHttpError(
                    'Partner reward redemption log not found',
                    '404',
                );
            }

            if (rewardRedemptionLog?.rewardSubType !== SUB_TYPE.PARTNER) {
                throw new CustomHttpError('Not a partner reward log item', '400');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId) throw new CustomHttpError('Unauthorized region', '403');
                await validateAction(
                    REWARD.ACTIONS.UPDATE_LOG_ITEM,
                    REWARD.MODULE_ID,
                    rewardRedemptionLog.regionId?.toString(),
                    boundary,
                    ability,
                    rewardRedemptionLog.merchantId?.toString(),
                );
            }

            if (
                !~[PROCESSING_STATUS.PENDING, PROCESSING_STATUS.PROCESSING].indexOf(
                    rewardRedemptionLog.processingStatus,
                )
            ) {
                throw new CustomHttpError(
                    'Partner reward redemption must be in PENDING or PROCESSING state to be updated',
                    '400',
                );
            }

            let event = 'Redemption log was updated.';

            if (
                validatedDataObj?.partnerRefNumber &&
                !validatedDataObj?.partnerRefName
            ) {
                event = `${
                    validatedDataObj?.rewardName || 'Partner'
                } reference number was updated from '${
                    rewardRedemptionLog?.metadata?.partnerRefNumber
                }' to '${validatedDataObj?.partnerRefNumber}'`;
            }

            if (
                !validatedDataObj?.partnerRefNumber &&
                validatedDataObj?.partnerRefName
            ) {
                event = `${
                    validatedDataObj?.rewardName || 'Partner'
                } reference name was updated from '${
                    rewardRedemptionLog?.metadata?.partnerRefName
                }' to '${validatedDataObj?.partnerRefName}'`;
            }

            if (
                validatedDataObj?.partnerRefNumber &&
                validatedDataObj?.partnerRefName
            ) {
                event = `${
                    validatedDataObj?.rewardName || 'Partner'
                } reference number was updated from '${
                    rewardRedemptionLog?.metadata?.partnerRefNumber
                }' to '${validatedDataObj?.partnerRefNumber}'`;
                event += `, ${
                    validatedDataObj?.rewardName || 'Partner'
                } reference name was updated from '${
                    rewardRedemptionLog?.metadata?.partnerRefName
                }' to '${validatedDataObj?.partnerRefName}'`;
            }

            let eventObj = getEventUpdateObj(callerId, event, {
                metadata: {
                    partnerRefNumber:
                        validatedDataObj?.partnerRefNumber ||
                        rewardRedemptionLog?.metadata?.partnerRefNumber ||
                        'Unknown',
                    partnerRefName:
                        validatedDataObj?.partnerRefName ||
                        rewardRedemptionLog?.metadata?.partnerRefName ||
                        'Unknown',
                    partnerNotes:
                        validatedDataObj?.partnerNotes ||
                        rewardRedemptionLog?.metadata?.partnerNotes ||
                        '',
                },
            });
    
            const updatedResult = await RewardRedemptionLogsDAO.updateRedemptionLog(
                eventObj,
                id,
                organizationId,
                callerId,
            );
            return Promise.resolve({ result: updatedResult, auditData });
        } catch (err) {
            log.error('Error', err);
            return Promise.reject({
                err,
                auditData: { ...auditData, auditMessage: err.message },
            });
        }
    }

    static async reprocessFailedLogItem(payload, id, organizationId, callerId, ability, boundary) {
        // TODO: [MLS-3362] Implement audit logs for the funtion 'reprocessFailedLogItem'.
        // const auditData = {
        //     moduleId: REWARD.MODULE_ID,
        //     action: REWARD.ACTIONS.UPDATE_LOG_ITEM,
        //     operation: OPERATION.UPDATE,
        //     auditMessage: 'reward redemption log update successful',
        // }
        try {
            const validatedDataObj = await RedemptionLogsValidator.isValidReprocess(payload);

            const rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, id);
            if (!rewardRedemptionLog) {
                throw new CustomHttpError('Partner reward redemption log not found', '404');
            }

            if (rewardRedemptionLog?.rewardSubType !== SUB_TYPE.PARTNER) {
                throw new CustomHttpError('Not a partner reward log item', '400');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.UPDATE_LOG_ITEM, REWARD.MODULE_ID, rewardRedemptionLog.regionId?.toString(), boundary, ability, rewardRedemptionLog.merchantId?.toString());
            }

            if (rewardRedemptionLog.status !== PARTNER_STATUS.FAILED) {
                throw new CustomHttpError('Partner reward redemption log is not in FAILED state', '400');
            }

            const event = `Status changed from ${rewardRedemptionLog.status} to ${PARTNER_STATUS.PROCESSING}`;
    
            let eventObj = getEventUpdateObj(callerId, event, {
                metadata: {
                    partnerRefNumber: 
                        validatedDataObj?.partnerRefNumber || 
                        rewardRedemptionLog?.metadata?.partnerRefNumber || 
                        'Unknown',
                    partnerRefName: 
                        validatedDataObj?.partnerRefName || 
                        rewardRedemptionLog?.metadata?.partnerRefName || 
                        'Unknown',
                    partnerNotes: 
                        validatedDataObj?.partnerNotes || 
                        rewardRedemptionLog?.metadata?.partnerNotes || 
                        '',
                    bundleValue: 
                        validatedDataObj?.bundleValue || 
                        rewardRedemptionLog?.metadata?.bundleValue || 
                        0,
                },
                status: PARTNER_STATUS.PROCESSING,
                processingStatus: PROCESSING_STATUS.PROCESSING,
            });  

            if (rewardRedemptionLog.handbackFaliureReason) {
                eventObj = {
                    ...eventObj,
                    $unset: { handbackFaliureReason: '' },
                }
            }

            const updatedResult = await RewardRedemptionLogsDAO.updateRedemptionLog(eventObj, id, organizationId, callerId);
            if (rewardRedemptionLog.distributionJobId) {
                const rewardDistributionJob = await RewardDistributionJobsDAO.getRewardDistributionJobById(organizationId, rewardRedemptionLog.distributionJobId?.toString());
                let failedCount = 0;

                if (rewardDistributionJob.failedCount > 0) {
                    failedCount = -1;
                }
                const updatedDistributionJob = await RewardDistributionJobsDAO.updateRewardDistributionJob({
                    $inc: {
                        failedCount
                    }
                }, rewardRedemptionLog.distributionJobId.toString(), organizationId, callerId);
            }
            return Promise.resolve({
                result: updatedResult, 
                auditData: {}, // TODO: [MLS-3362] Implement audit logs for the funtion 'reprocessFailedLogItem'.
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: {} // TODO: [MLS-3362] Implement audit logs for the funtion 'reprocessFailedLogItem'.
            });
        }
    }

    static async updateRewardRedemptionLogWithAuditLogs(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.UPDATE_LOG_ITEM,
            operation: OPERATION.UPDATE,
            auditMessage: 'reward redemption log update successful',
        }

        try {
            const result = await this.updateRewardRedemptionLog(dataObj, id, organizationId, callerId, ability, boundary);
            return Promise.resolve({ result, auditData });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error,
                auditData: { ...auditData, auditMessage: error.message }
            });
        }
    }

    static async updateRewardRedemptionLog(dataObj, id, organizationId, callerId, ability, boundary) {
        try {
            const validatedDataObj = await RedemptionLogsValidator.isValidUpdate(dataObj);

            const rewardRedemptionLog = await RewardRedemptionLogsDAO.getRewardRedemptionLog(organizationId, id);
            if (!rewardRedemptionLog) {
                throw new CustomHttpError('Reward redemption log not found', '404');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardRedemptionLog.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.UPDATE_LOG_ITEM, REWARD.MODULE_ID, rewardRedemptionLog.regionId?.toString(), boundary, ability, rewardRedemptionLog.merchantId?.toString());
            }

            let event = 'Redemption details updated.';
            if (rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER) {
                if (validatedDataObj.status) {
                    if (rewardRedemptionLog.status === validatedDataObj.status) {
                        throw new CustomHttpError(`Redundant status update. Status is already in ${validatedDataObj.status} state.`, '400');
                    }
                    if (rewardRedemptionLog.status === PARTNER_STATUS.PROCESSING) {
                        if (!~ [PARTNER_STATUS.COMPLETED, PARTNER_STATUS.FAILED].indexOf(validatedDataObj.status)) {
                            throw new CustomHttpError('Valid update states for PROCESSING partner reward redemptions - COMPLETED, FAILED', '400');
                        }
                    }
                    if (rewardRedemptionLog.status === PARTNER_STATUS.COMPLETED) {
                        throw new CustomHttpError('Completed partner reward redemption logs are non-reversible', '400');
                    }
                    if (rewardRedemptionLog.status === PARTNER_STATUS.REFUNDED) {
                        throw new CustomHttpError('Refunded partner reward redemption logs are non-reversible', '400');
                    }

                    event = `Status changed from ${rewardRedemptionLog.status} to ${validatedDataObj.status}`;
                }
            }
            else {
                if (validatedDataObj.status) {
                    if (rewardRedemptionLog.status === STATUS.CANCELLED) {
                        throw new CustomHttpError('Cancelled redemption logs are non-reversible', '400');
                    }
                    if (validatedDataObj.status !== STATUS.CANCELLED) {
                        throw new CustomHttpError('Valid states - CANCELLED', '400');
                    }
                    event = `Status changed from ${rewardRedemptionLog.status} to ${STATUS.CANCELLED}`;
                }
                if (validatedDataObj.processingStatus) {
                    await this.validateProcessingStateChange(rewardRedemptionLog.processingStatus, validatedDataObj.processingStatus);
                    event += `Status changed from ${rewardRedemptionLog.processingStatus} to ${PROCESSING_STATUS.FAILED}`;
                    if (validatedDataObj.processingStatus === PROCESSING_STATUS.FAILED) {
                        validatedDataObj.status = STATUS.CANCELLED;
                    }
                }              
            }

            let eventObj = getEventUpdateObj(callerId, event, {
                status:
                    rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER
                        ? validatedDataObj.status
                        : STATUS.CANCELLED,
                processingStatus:
                    rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER
                        ? PROCESSING_STATUS.PROCESSING
                        : PROCESSING_STATUS.FAILED,
                ...(rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER &&
                    validatedDataObj.handbackFaliureReason
                    ? { handbackFaliureReason: validatedDataObj.handbackFaliureReason }
                    : {}
                ),
            });

            if (rewardRedemptionLog.status !== PARTNER_STATUS.FAILED && rewardRedemptionLog.handbackFaliureReason) {
                eventObj = {
                    ...eventObj,
                    $unset: { handbackFaliureReason: '' },
                }
            }

            const updatedResult = await RewardRedemptionLogsDAO.updateRedemptionLog(eventObj, id, organizationId, callerId);
            if (rewardRedemptionLog.distributionJobId) {
                let failedCount = 0;
                let successCount = 0;

                if (rewardRedemptionLog?.rewardSubType === SUB_TYPE.PARTNER) {
                    if (validatedDataObj.status === PARTNER_STATUS.FAILED) {
                        failedCount = 1;
                    }
                    else if (validatedDataObj.status === PARTNER_STATUS.COMPLETED) {
                        successCount = 1;
                    }
                    else {
                        failedCount = 0;
                        successCount = 0;
                    }
                }
                else {
                    failedCount = 1;
                }

                await RewardDistributionJobsDAO.updateRewardDistributionJob({
                    $inc: { failedCount, successCount }
                }, rewardRedemptionLog.distributionJobId.toString(), organizationId, callerId);

                await this.updateRewardDistributionJobStatus(organizationId, rewardRedemptionLog.distributionJobId.toString());
            }
            return Promise.resolve(updatedResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static validateProcessingStateChange(currentState, newState) {
        switch (currentState) {
            case PROCESSING_STATUS.FAILED:
                return Promise.reject(new CustomHttpError('Failed redemption logs are non-reversible', 400));
            default:
                if (newState !== PROCESSING_STATUS.FAILED) {
                    return Promise.reject(new CustomHttpError(`Processing state is in ${currentState} state, Valid states - FAILED`, 400));
                }
        }
    }

    static async updateRewardDistributionJobStatus(organizationId, distributionJobId) {
        try {
            const { items } = await RewardRedemptionLogsDAO.getRewardRedemptionLogs(organizationId, {
                distributionJobId,
                processingStatus: {
                    $ne: PROCESSING_STATUS.FAILED,
                },
            });
            if (items.length <= 0) {
                const updateObj = getEventUpdateObj(null, `Status changed to FAILED`, {
                    status: PROCESSING_STATUS.FAILED
                });
                await RewardDistributionJobsDAO.updateRewardDistributionJob(updateObj, distributionJobId, organizationId, null);
            }
        } catch (err) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }
}

module.exports = RedemptionLogsHandler;
