'use strict';
const _ = require('lodash');
const config = require('./../config');
const secretConfig = require('./../../config');
const logger = require('./../logger');
const lodash = require('lodash');
const log = logger(config.logger);
const RewardDistributionJobsValidator = require('../validators/RewardDistributionJobsValidator');
const RewardDistributionJobsDAO = require('./../db/dao/RewardDistributionJobsDAO');
const RegionDAO = require('./../db/dao/RegionDAO');
const RewardDAO = require('./../db/dao/RewardDAO');
const RewardRedemptionLogsDAO = require('./../db/dao/RewardRedemptionLogsDAO');
const {
    SUB_TYPE, PARTNER_REWARD_CONFIG,
} = require('./../db/models/enums/reward.enums');
const {
    STATUS, PROCESSING_STATUS, PARTNER_STATUS,
} = require('./../db/models/enums/reward.redemption.log.enums');
const { BOUNDARY, OPERATION } = require('./../db/models/enums/user.enums');
const CustomHttpError = require('./../CustomHttpError');
const Shoutout = require('./../../lib/services/Shoutout');
const mongoose = require('mongoose');
const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { REWARD },
        },
    }, validateAbility, validateAction, validateGet,
} = require('@shoutout-labs/authz-utils');
const Utils = require('../Utils');
const StorageWrapper = require('../wrappers/StorageWrapper');
const RewardDistributionsImportJobsValidator = require('../validators/RewardDistributionsImportJobsValidator');
const RewardDistributionsImportJobsDAO = require('../db/dao/RewardDistributionsImportJobsDAO');
const RewardDistributionsImportJobProcessor = require('../../workers/processors/reward.distributions.import.job.processor');
const { fileActionMapping } = require('./partnerRewardConfig');
const RewardDistributionJobsQueryValidator = require('../validators/QueryValidators/RewardDistributionJobsQueryValidator');

const getEventUpdateObj = (callerId, eventDetails, setObj) => {
    return {
        $set: setObj, $push: {
            historyEvents: {
                $each: [{
                    eventDate: new Date(), eventDetails, eventBy: callerId,
                }], $sort: { eventDate: -1 },
            },
        }, $currentDate: { modifiedOn: true }, updatedBy: callerId,
    };
};

const dbRequestChunkSize = Number(process.env.WORKER_DB_REQUEST_CHUNK_SIZE || 500);
const publishRewardsReadyActivities = async (organizationId, regionId, distributionJobId, distributionJobStatus) => {
    const redemptionLogsCursor = await RewardRedemptionLogsDAO.getRedemptionLogsWithRewardsAndLocations(distributionJobId, organizationId, distributionJobStatus);
    let activities = [];
    for (let redemptionLog = await redemptionLogsCursor.next(); redemptionLog != null; redemptionLog = await redemptionLogsCursor.next()) {
        const { memberId, reward, claimLocation } = redemptionLog;
        activities.push({
            data: {
                memberId: memberId.toString(),
                activityName: 'Reward Ready To Claim',
                organizationId,
                regionId: regionId,
                createdOn: new Date(),
                activityData: {
                    rewardName: reward.name, claimLocation: claimLocation.locationName,
                },
            }, opts: {
                jobId: mongoose.Types.ObjectId().toString(),
            },
        });
        if (activities.length % dbRequestChunkSize === 0) {
            Shoutout.produceActivityToTopic(activities);
            activities = [];
        }
    }
    if (activities.length > 0) {
        Shoutout.produceActivityToTopic(activities);
    }
};
const publishPartnerRewardsRedemptionCompleteActivities = async (organizationId, regionId, distributionJobId, distributionJobStatus, rewardId) => {
    const redemptionLogsCursor = await RewardRedemptionLogsDAO.streamData(organizationId, {
        distributionJobId: mongoose.Types.ObjectId(distributionJobId),
        organizationId: mongoose.Types.ObjectId(organizationId),
        processingStatus: distributionJobStatus,
    });
    let activities = [];
    const reward = await RewardDAO.getReward(organizationId, rewardId);
    for (let redemptionLog = await redemptionLogsCursor.next(); redemptionLog != null; redemptionLog = await redemptionLogsCursor.next()) {
        const { memberId } = redemptionLog;
        activities.push({
            data: {
                memberId: memberId.toString(),
                activityName: `${reward.name} Redemption Complete`,
                organizationId,
                regionId: regionId,
                createdOn: new Date(),
                activityData: {
                    rewardName: reward.name,
                },
            }, opts: {
                jobId: mongoose.Types.ObjectId().toString(),
            },
        });
        if (activities.length % dbRequestChunkSize === 0) {
            Shoutout.produceActivityToTopic(activities);
            activities = [];
        }
    }
    if (activities.length > 0) {
        Shoutout.produceActivityToTopic(activities);
    }
};

const validateDistributionJobCreationAbility = async (regionId, merchantId, boundary, ability) => {
    const returnObj = {};
    const {
        regionIds,
        merchantIds,
    } = await validateAbility(REWARD.ACTIONS.CREATE_REWARD_DISTRIBUTION_JOB, REWARD.MODULE_ID, regionId, boundary, ability);
    if (regionIds && !regionIds.includes(regionId)) return Promise.reject(new CustomHttpError('unauthorized region', '403'));
    if (boundary === BOUNDARY.MERCHANT && !merchantId) {
        returnObj.merchantId = _.first(merchantIds);
        returnObj.merchantIds = merchantIds;
    } else {
        returnObj.merchantIds = merchantIds;
        if (merchantIds && merchantId && !merchantIds.includes(merchantId)) return Promise.reject(new CustomHttpError('unauthorized merchant', '403'));
    }
    return returnObj;
};

class RewardDistributionJobsHandler {
    static async createRewardDistributionJob(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.CREATE_REWARD_DISTRIBUTION_JOB,
            operation: OPERATION.CREATE,
            auditMessage: 'reward distribution job creation successful',
        };
        try {
            const validatedObj = await RewardDistributionJobsValidator.isValid(dataObj);

            if (ability && boundary !== BOUNDARY.ROOT) {
                _.assign(validatedObj, await validateDistributionJobCreationAbility(validatedObj.regionId, validatedObj.merchantId, boundary, ability));
            }

            const redemptionLogsValidationFilter = {
                _id: { $in: validatedObj.redemptionLogIds },
                regionId: mongoose.Types.ObjectId(validatedObj.regionId),
                status: STATUS.REQUESTED,
                processingStatus: PROCESSING_STATUS.PENDING,
            };
            if (validatedObj.merchantIds) redemptionLogsValidationFilter['merchantId'] = {
                $in: validatedObj.merchantIds,
            };
            const validRedemptionLogItems = await RewardRedemptionLogsDAO.getValidRedemptionLogIds(redemptionLogsValidationFilter, organizationId);
            if (validRedemptionLogItems.length !== validatedObj.redemptionLogIds.length) {
                const validRedemptionLogIds = validRedemptionLogItems.map((item) => item._id.toString());
                const intersectionOfArrays = lodash.intersection(validRedemptionLogIds, validatedObj.redemptionLogIds);
                const invalidRedemptionLogIds = lodash.difference(validatedObj.redemptionLogIds, intersectionOfArrays);
                throw new CustomHttpError(`Following Ids are invalid, ${invalidRedemptionLogIds.map((logItemId) => logItemId + ',')}`, '400');
            }

            const validatedRewardIds = await RewardRedemptionLogsDAO.validateRewardIds(organizationId, validatedObj.redemptionLogIds);
            if (validatedRewardIds.length > 1) {
                throw new CustomHttpError(`Reward redemption logs must contain the same reward`, '400');
            }

            const reward = await RewardDAO.getReward(organizationId, validatedRewardIds[0]._id);

            const region = await RegionDAO.getRegion(validatedObj.regionId);
            if (!region) {
                throw new CustomHttpError('Region not found', '404');
            }
            const regionUpdateResult = await RegionDAO.updateRegion({
                $inc: {
                    'rewardConfiguration.lastBatchId': 1,
                },
            }, validatedObj.regionId, organizationId);

            const distributionJobObj = {
                regionId: mongoose.Types.ObjectId(validatedObj.regionId),
                batchId: regionUpdateResult.rewardConfiguration.lastBatchId,
                itemCount: validRedemptionLogItems.length,
            };
            if (validatedObj.merchantId) distributionJobObj.merchantId = mongoose.Types.ObjectId(validatedObj.merchantId);
            if (reward.subType === SUB_TYPE.PARTNER) {
                distributionJobObj['rewardId'] = reward._id;
            }
            const createdObj = await RewardDistributionJobsDAO.createRewardDistributionJob(distributionJobObj, organizationId, callerId);
            const bulkUpdateObj = getEventUpdateObj(callerId, `Distribution job assigned`, {
                distributionJobId: createdObj._id, ...(reward.subType === SUB_TYPE.PARTNER ? { status: PARTNER_STATUS.PROCESSING } : {}),
                processingStatus: PROCESSING_STATUS.PROCESSING,
            });
            await RewardRedemptionLogsDAO.updateRedemptionLogBulk(bulkUpdateObj, {
                _id: { $in: validatedObj.redemptionLogIds },
                organizationId: mongoose.Types.ObjectId(organizationId),
                status: STATUS.REQUESTED,
                processingStatus: PROCESSING_STATUS.PENDING,
            });
            return Promise.resolve({
                result: createdObj, auditData: {
                    ...auditData,
                },
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e, auditData: { ...auditData, auditMessage: e.message },
            });
        }
    }

    static async updateRewardDistributionJob(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.UPDATE_REWARD_DISTRIBUTION_JOB,
            operation: OPERATION.UPDATE,
            auditMessage: 'reward distribition job update successful',
        };
        let processingRedemptionsOfBatch;

        try {
            const validatedDataObj = await RewardDistributionJobsValidator.isValidUpdate(dataObj);
            const rewardDistributionJob = await RewardDistributionJobsDAO.getRewardDistributionJob(organizationId, id);
            if (!rewardDistributionJob) {
                throw new CustomHttpError('Reward distribution job not found', '404');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardDistributionJob.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.UPDATE_REWARD_DISTRIBUTION_JOB, REWARD.MODULE_ID, rewardDistributionJob.regionId?.toString(), boundary, ability, rewardDistributionJob.merchantId?.toString());
            }

            await this.validateStateChange(rewardDistributionJob.status, validatedDataObj.status, rewardDistributionJob.rewardId);

            if (rewardDistributionJob.rewardId) {
                processingRedemptionsOfBatch = await RewardRedemptionLogsDAO.getRewardRedemptionLogs(organizationId, {
                    distributionJobId: id,
                    status: PARTNER_STATUS.PROCESSING,
                });
            }

            const updateFilters = {
                distributionJobId: mongoose.Types.ObjectId(id),
                organizationId: mongoose.Types.ObjectId(organizationId),
                processingStatus: rewardDistributionJob.status,
            };
            let updateManyObjs = [];
            let eventObj;
            let distributionJobObj;
            switch (validatedDataObj.status) {
                case PROCESSING_STATUS.COMPLETED:
                    if (processingRedemptionsOfBatch && processingRedemptionsOfBatch.items.length !== 0) {
                        throw new CustomHttpError('Partner reward distribution batch cannot contain any PROCESSING redemptions', '400');
                    }

                    eventObj = getEventUpdateObj(callerId, `${!rewardDistributionJob.rewardId ? 'Status changed to READY, ' : ''}Processing status changed to COMPLETED`, {
                        ...(!rewardDistributionJob.rewardId ? { status: STATUS.READY } : {}),
                        processingStatus: validatedDataObj.status,
                    });

                    distributionJobObj = {
                        ...(!rewardDistributionJob.rewardId ? {
                            successCount: rewardDistributionJob.itemCount - rewardDistributionJob.failedCount,
                        } : {}),
                    };

                    if (rewardDistributionJob.rewardId) {
                        await publishPartnerRewardsRedemptionCompleteActivities(organizationId, rewardDistributionJob.regionId.toString(), id, rewardDistributionJob.status, rewardDistributionJob.rewardId);
                    } else {
                        await publishRewardsReadyActivities(organizationId, rewardDistributionJob.regionId.toString(), id, rewardDistributionJob.status);
                    }
                    break;
                case PROCESSING_STATUS.FAILED:
                    let updateStatus = {};
                    let eventDetails = 'Processing status changed to FAILED';

                    if (rewardDistributionJob.rewardId) {
                        if (processingRedemptionsOfBatch && processingRedemptionsOfBatch.items.length !== 0) {
                            eventDetails += `, status changed to ${PARTNER_STATUS.FAILED}`;

                            updateManyObjs = [{
                                updateMany: {
                                    filter: {
                                        ...updateFilters, status: PARTNER_STATUS.PROCESSING,
                                    }, update: getEventUpdateObj(callerId, eventDetails, {
                                        processingStatus: validatedDataObj.status,
                                        status: PARTNER_STATUS.FAILED,
                                        updatedOn: new Date(),
                                    }),
                                },
                            }, {
                                updateMany: {
                                    filter: { ...updateFilters },
                                    update: getEventUpdateObj(callerId, `Processing status changed to ${validatedDataObj.status}`, {
                                        processingStatus: validatedDataObj.status, updatedOn: new Date(),
                                    }),
                                },
                            }];
                        }

                        distributionJobObj = {
                            failedCount: rewardDistributionJob.itemCount - rewardDistributionJob.successCount,
                        };
                    } else {
                        updateStatus = { status: STATUS.CANCELLED };
                        eventDetails += `, status changed to CANCELLED`;

                        distributionJobObj = {
                            successCount: 0, failedCount: rewardDistributionJob.itemCount,
                        };
                    }

                    eventObj = [{
                        $set: {
                            processingStatus: validatedDataObj.status, ...updateStatus, historyEvents: {
                                $concatArrays: [[{
                                    eventDate: new Date(), eventDetails, eventBy: callerId,
                                }], '$historyEvents'],
                            }, updatedBy: mongoose.Types.ObjectId(callerId), updatedOn: new Date(),
                        },
                    }];
                    break;
                default:
                    eventObj = getEventUpdateObj(callerId, `Processing status changed to ${validatedDataObj.status}`, { processingStatus: validatedDataObj.status });
            }

            const updateObj = getEventUpdateObj(callerId, `Status changed from ${rewardDistributionJob.status} to ${validatedDataObj.status}`, {
                status: validatedDataObj.status, ...distributionJobObj,
            });
            const updatedResult = await RewardDistributionJobsDAO.updateRewardDistributionJob(updateObj, id, organizationId, callerId);

            if (rewardDistributionJob.rewardId && processingRedemptionsOfBatch && processingRedemptionsOfBatch.items.length !== 0) {
                log.debug(updateManyObjs);
                await RewardRedemptionLogsDAO.updateRedemptionLogBulkWrite(updateManyObjs);
            } else {
                log.debug(eventObj);
                await RewardRedemptionLogsDAO.updateRedemptionLogBulk(eventObj, {
                    ...updateFilters,
                });
            }

            return Promise.resolve({
                result: updatedResult, auditData,
            });
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject({
                err: error, auditData: { ...auditData, auditMessage: error.message },
            });
        }
    }

    static validateStateChange(currentState, newState, rewardId) {
        switch (currentState) {
            case PROCESSING_STATUS.PENDING:
                if (newState !== PROCESSING_STATUS.FAILED && newState !== PROCESSING_STATUS.PROCESSING) {
                    return Promise.reject(new CustomHttpError('Valid states - PROCESSING, FAILED', '400'));
                }
                break;
            case PROCESSING_STATUS.PROCESSING:
                if (rewardId) {
                    if (newState !== PROCESSING_STATUS.FAILED && newState !== PROCESSING_STATUS.COMPLETED) {
                        return Promise.reject(new CustomHttpError('Valid states - COMPLETED, FAILED', '400'));
                    }
                } else {
                    if (newState !== PROCESSING_STATUS.FAILED && newState !== PROCESSING_STATUS.DISPATCHED) {
                        return Promise.reject(new CustomHttpError('Valid states - DISPATCHED, FAILED', '400'));
                    }
                }
                break;
            case PROCESSING_STATUS.DISPATCHED:
                if (newState !== PROCESSING_STATUS.FAILED && newState !== PROCESSING_STATUS.COMPLETED) {
                    return Promise.reject(new CustomHttpError('Valid states - COMPLETED, FAILED', '400'));
                }
                break;
            default:
                return Promise.reject(new CustomHttpError(`Invalid state change, distribution job is in ${currentState} state`, '400'));
        }
    }

    static async getRewardDistributionJobs(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability && boundary !== BOUNDARY.ROOT) {
                validatedObj = await validateGet(REWARD.ACTIONS.LIST_REWARD_DISTRIBUTION_JOBS, REWARD.MODULE_ID, validatedObj.regionId, boundary, ability, validatedObj);
            }
            const result = await RewardDistributionJobsDAO.getRewardDistributionJobs(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async exportDistributionJobToCSV(organizationId, validatedObj, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.LIST_REWARD_DISTRIBUTION_JOBS,
            operation: OPERATION.READ,
            auditMessage: 'export distribution job to csv successful',
        };
        try {
            const rewardDistributionJob = await RewardDistributionJobsDAO.getRewardDistributionJob(organizationId, validatedObj.distributionJobId);
            if (!rewardDistributionJob) {
                throw new CustomHttpError('Reward distribution job not found', '404');
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardDistributionJob.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.LIST_REWARD_DISTRIBUTION_JOBS, REWARD.MODULE_ID, rewardDistributionJob.regionId?.toString(), boundary, ability, rewardDistributionJob.merchantId?.toString());
            }

            if (!rewardDistributionJob.rewardId && validatedObj?.redemptionStatus) {
                throw new CustomHttpError('Must be a partner reward distribution to filter by redemption status', '400');
            }

            const cursor = await RewardRedemptionLogsDAO.streamData(organizationId, {
                distributionJobId: validatedObj.distributionJobId, ...(validatedObj?.redemptionStatus ? { status: validatedObj?.redemptionStatus } : {}),
            }, !rewardDistributionJob.rewardId);

            const folderName = 'distribution_jobs';
            let headers;
            let reportName = 'reward_distributions';
            if (rewardDistributionJob.rewardId) {
                const batchRef = rewardDistributionJob?.batchId || rewardDistributionJob._id;

                const currentRegionData = await RegionDAO.getRegion(rewardDistributionJob?.regionId);

                if (!currentRegionData) {
                    throw new CustomHttpError(`No region found for reward distribution job - ${batchRef}`, '400');
                }

                if (validatedObj?.customExporter && validatedObj?.customExporter !== PARTNER_REWARD_CONFIG.DEFAULT) {
                    const { url } = await fileActionMapping[validatedObj.customExporter].customExportFunction(cursor, organizationId, currentRegionData?.defaultCountryISO2Code, batchRef, folderName);

                    return Promise.resolve({
                        result: { url }, auditData,
                    });
                }

                headers = [{ id: '_id', title: 'REDEMPTION_ID' }, {
                    id: 'memberId',
                    title: 'MEMBER_ID',
                }, { id: 'pointsRedeemed', title: 'POINTS_AMOUNT' }, {
                    id: 'partnerRefNumber',
                    title: 'PARTNER_REF_NUMBER',
                }, { id: 'partnerRefName', title: 'PARTNER_REF_NAME' }, {
                    id: 'partnerNotes',
                    title: 'PARTNER_NOTES',
                }, { id: 'status', title: 'STATUS' }];
            } else {
                reportName = `batch_${rewardDistributionJob?.batchId || rewardDistributionJob._id}_${reportName}`;

                headers = [{ id: 'memberId', title: 'MEMBER_ID' }, {
                    id: 'rewardName',
                    title: 'REWARD_NAME',
                }, { id: 'pointsRedeemed', title: 'POINTS_AMOUNT' }];
            }

            const { url } = await Utils.exportToCSV(cursor, organizationId, reportName, headers, folderName, true, async (dataCursor, csvWriter) => {
                for (let record = await dataCursor.next(); record != null; record = await dataCursor.next()) {
                    const csvData = [{
                        _id: record._id,
                        memberId: record.memberId,
                        rewardId: record.rewardId,
                        pointsRedeemed: record.pointsRedeemed,
                        status: record.status, ...(rewardDistributionJob.rewardId ? {
                            partnerRefNumber: record.metadata.partnerRefNumber || '',
                            partnerRefName: record.metadata.partnerRefName || '',
                            partnerNotes: record.metadata.partnerNotes || '',
                        } : { rewardName: record?.reward?.name || '' }),
                    }];
                    await csvWriter.writeRecords(csvData);
                }
            });

            return Promise.resolve({
                result: { url }, auditData,
            });
        } catch (e) {
            console.log(e);
            return Promise.reject({
                err: e, auditData: { ...auditData, auditMessage: e.message },
            });
        }
    }

    static async generateFileUploadTokenForImportRewardDistributions(organizationId, userId, payload, {
        mimetype,
        size,
        key,
        bucket,
        originalFileName,
    }, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.CREATE_REWARD_DISTRIBUTION_IMPORT_JOB,
            operation: OPERATION.CREATE,
            auditMessage: 'file upload token creation successful',
        };
        try {
            const rewardDistributionJob = await RewardDistributionJobsDAO.getRewardDistributionJob(organizationId, payload.distributionJobId);
            if (!rewardDistributionJob) {
                throw new CustomHttpError('Reward distribution job not found', 404);
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardDistributionJob.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.CREATE_REWARD_DISTRIBUTION_IMPORT_JOB, REWARD.MODULE_ID, rewardDistributionJob.regionId?.toString(), boundary, ability, rewardDistributionJob.merchantId?.toString());
            }

            const { regionId, distributionJobId } = payload;

            const partnerReward = await RewardDAO.getReward(organizationId, rewardDistributionJob?.rewardId, regionId);

            if (!partnerReward) {
                throw new CustomHttpError('Partner reward not found', 404);
            }

            const importActionName = await RewardDistributionJobsQueryValidator.isValidCustomImport(partnerReward?.partnerRewardMetadata?.partnerRewardConfig);
            let headers = [];
            if (importActionName !== PARTNER_REWARD_CONFIG.DEFAULT) {
                // headers = fileActionMapping[importActionName].getImportConfigData().fileHeaders; // TODO: Uncomment if all headers of CAL Miles' handback file are needed.
                headers = fileActionMapping[importActionName].getImportConfigData().loyaltySupportedFileHeaders;
            } else {
                const firstLine = await StorageWrapper.getFirstLineFromFile(bucket, key);
                headers = firstLine.split(',');
            }
            const result = {
                fileToken: Utils.encrypt(JSON.stringify({
                    fileId: key, organizationId, regionId, distributionJobId, originalFileName, importActionName,
                }), secretConfig.VERIFY_TOKEN_SECRET), headers,
            };
            return Promise.resolve({
                result, auditData,
            });
        } catch (e) {
            console.log(e);
            return Promise.reject({
                err: e, auditData: { ...auditData, auditMessage: e.message },
            });
        }
    }

    static async createRewardDistributionsImportJob(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: REWARD.MODULE_ID,
            action: REWARD.ACTIONS.CREATE_REWARD_DISTRIBUTION_IMPORT_JOB,
            operation: OPERATION.CREATE,
            auditMessage: 'import job creation successful',
        };
        try {
            const { fieldMappings, fileToken } = await RewardDistributionsImportJobsValidator.isValid(dataObj);
            const {
                fileId,
                organizationId: organizationIdFromToken,
                regionId,
                distributionJobId,
                originalFileName,
                importActionName,
            } = JSON.parse(Utils.decrypt(fileToken, secretConfig.VERIFY_TOKEN_SECRET));

            const rewardDistributionJob = await RewardDistributionJobsDAO.getRewardDistributionJob(organizationId, distributionJobId);
            if (!rewardDistributionJob) {
                throw new CustomHttpError('Reward distribution job not found', 404);
            }

            if (ability && boundary !== BOUNDARY.ROOT) {
                if (boundary === BOUNDARY.MERCHANT && !rewardDistributionJob.merchantId) throw new CustomHttpError('unauthorized region', '403');
                await validateAction(REWARD.ACTIONS.CREATE_REWARD_DISTRIBUTION_IMPORT_JOB, REWARD.MODULE_ID, rewardDistributionJob.regionId?.toString(), boundary, ability, rewardDistributionJob.merchantId?.toString());
            }

            if (organizationId !== organizationIdFromToken) {
                throw new CustomHttpError('File token organization mismatch', '400');
            }

            const createdRDImportJob = await RewardDistributionsImportJobsDAO.createRDImportJob({
                regionId,
                distributionJobId,
                status: STATUS.PENDING,
                fileName: originalFileName,
                fileId,
                fieldMappings,
                importActionName,
            }, organizationId, callerId);

            log.debug('Created import job', createdRDImportJob);
            RewardDistributionsImportJobProcessor.addJob(createdRDImportJob, createdRDImportJob._id.toString());
            return Promise.resolve({
                result: createdRDImportJob, auditData,
            });
        } catch (e) {
            console.log(e);
            return Promise.reject({
                err: e, auditData: { ...auditData, auditMessage: e.message },
            });
        }
    }
}

module.exports = RewardDistributionJobsHandler;
