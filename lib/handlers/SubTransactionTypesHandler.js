'use strict';
const SubTransactionTypesDAO = require('./../db/dao/SubTransactionTypesDAO');
const TransactionsDAO = require('./../db/dao/TransactionsDAO');
const RegionDAO = require('./../db/dao/RegionDAO');
const CustomHttpError = require('./../CustomHttpError');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const IdentityService = require('../services/IdentityService');
const SubTransactionTypesValidator = require('../validators/SubTransactionTypesValidator');
const { STATUS, TYPE } = require('../db/models/enums/sub.transaction.type.enums');
const { OPERATION, BOUNDARY } = require('../db/models/enums/user.enums');
const ObjectId = require('mongoose').Types.ObjectId;
const SubTransactionType = require('../db/models/sub.transaction.type.model');
const RedisConnector = require('./../db/connectors/RedisConnector');
const { getAsync, delAsync } = RedisConnector.getCommands();
const { getWrapperForCache, promiseRaceAsync } = require('./../Utils');

const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { SUB_TRANSACTION_TYPE }
        }
    },
    validateAbility
} = require('@shoutout-labs/authz-utils');
const CoreService = require('../services/CoreService');
const { SYSTEM_EVENTS } = require('../constants/Constants');

const invalidateSubTransactionTypeCache = async (organizationId, subTransactionTypeId) => {
    await delAsync(`subTransactionTypes:${organizationId}:${subTransactionTypeId}`);
};

const createEventsForSubType = async (organizationId, subTypeName) => {
    const { items: regions } = await RegionDAO.getRegions(organizationId, null, null, { _id: 1 });
    if (regions && regions.length > 0) {
        const regionIds = regions.map((region) => region._id);
        await CoreService.createEvent(organizationId, {
            regionIds: regionIds,
            title: `${SYSTEM_EVENTS.ADJUST_POINTS} - ${subTypeName}`,
            attributes: {
                points: {
                    label: 'Points',
                    type: 'number'
                },
                total_points: {
                    label: 'Total Points',
                    type: 'number'
                },
                transactionDate: {
                    label: 'Transaction Date',
                    type: 'string'
                }
            }
        });
    }
};

class SubTransactionTypesHandler {
    static async createSubTransactionType(dataObj, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: SUB_TRANSACTION_TYPE.MODULE_ID,
            action: SUB_TRANSACTION_TYPE.ACTIONS.CREATE_SUB_TRANSACTION_TYPE,
            operation: OPERATION.CREATE,
            auditMessage: 'sub transaction type creation successful'
        };
        try {
            if (ability) {
                if (boundary !== BOUNDARY.ROOT && boundary !== BOUNDARY.GLOBAL) {
                    throw new CustomHttpError('unauthorized action', '403');
                }
                await validateAbility(
                    SUB_TRANSACTION_TYPE.ACTIONS.CREATE_SUB_TRANSACTION_TYPE,
                    SUB_TRANSACTION_TYPE.MODULE_ID,
                    null,
                    boundary,
                    ability
                );
            }
            const validatedObj = await SubTransactionTypesValidator.isValid(dataObj);
            const createdObj = await SubTransactionTypesDAO.createSubTransactionType(
                validatedObj,
                organizationId,
                callerId
            );

            if (validatedObj.transactionType === TYPE.ADJUSTMENT) {
                try {
                    await createEventsForSubType(organizationId, validatedObj.name);
                } catch (e) {
                    log.error(e);
                }
            }

            return Promise.resolve({
                result: createdObj,
                auditData: {
                    ...auditData
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async getSubTransactionTypes(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                await validateAbility(
                    SUB_TRANSACTION_TYPE.ACTIONS.LIST_SUB_TRANSACTION_TYPES,
                    SUB_TRANSACTION_TYPE.MODULE_ID,
                    null,
                    boundary,
                    ability,
                    true,
                    true
                );
            }
            const result = await SubTransactionTypesDAO.getSubTransactionTypes(organizationId, validatedObj);
            if (result.items.length === 0) {
                return Promise.resolve(result);
            }
            const userNames = await IdentityService.getUsersByIds(result.items.map((item) => item.createdBy));
            result.items.forEach((item, i) => {
                result.items[i]['createdByName'] = userNames[item.createdBy?.toString()] || null;
            });
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async deleteSubTransactionType(id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: SUB_TRANSACTION_TYPE.MODULE_ID,
            action: SUB_TRANSACTION_TYPE.ACTIONS.DELETE_SUB_TRANSACTION_TYPE,
            operation: OPERATION.DELETE,
            auditMessage: 'sub transaction type deletion successful'
        };
        try {
            const filter = {
                _id: ObjectId(id),
                organizationId,
                status: { $ne: STATUS.ARCHIVED }
            };
            const subTransaction = await SubTransactionType.findOne(filter);
            if (!subTransaction) {
                throw new CustomHttpError('Sub transaction type not found', '404');
            }
            if (ability) {
                if (boundary !== BOUNDARY.ROOT && boundary !== BOUNDARY.GLOBAL) {
                    throw new CustomHttpError('unauthorized action', '403');
                }
                await validateAbility(
                    SUB_TRANSACTION_TYPE.ACTIONS.DELETE_SUB_TRANSACTION_TYPE,
                    SUB_TRANSACTION_TYPE.MODULE_ID,
                    null,
                    boundary,
                    ability,
                    true
                );
            }
            if (!subTransaction.isVisibleToUser) {
                throw new CustomHttpError('system sub transaction types cannot be archived', '400');
            }
            const result = await SubTransactionTypesDAO.deleteSubTransactionType(id, organizationId, callerId);

            await invalidateSubTransactionTypeCache(organizationId, id);

            return Promise.resolve({
                result,
                auditData
            });
        } catch (e) {
            log.error(e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async updateSubTransactionType(dataObj, id, organizationId, callerId, ability, boundary) {
        const auditData = {
            moduleId: SUB_TRANSACTION_TYPE.MODULE_ID,
            action: SUB_TRANSACTION_TYPE.ACTIONS.UPDATE_SUB_TRANSACTION_TYPE,
            operation: OPERATION.UPDATE,
            auditMessage: 'sub transaction type update successful'
        };
        try {
            if (ability) {
                if (boundary !== BOUNDARY.ROOT && boundary !== BOUNDARY.GLOBAL) {
                    throw new CustomHttpError('unauthorized action', '403');
                }
                await validateAbility(
                    SUB_TRANSACTION_TYPE.ACTIONS.UPDATE_SUB_TRANSACTION_TYPE,
                    SUB_TRANSACTION_TYPE.MODULE_ID,
                    null,
                    boundary,
                    ability,
                    true
                );
            }
            const validatedDataObj = await SubTransactionTypesValidator.isValidUpdate(dataObj);
            const filter = {
                _id: ObjectId(id),
                organizationId,
                status: { $ne: STATUS.ARCHIVED }
            };
            const subTransaction = await SubTransactionType.findOne(filter);
            if (!subTransaction) {
                throw new CustomHttpError('Sub transaction type not found', '404');
            }

            if (!subTransaction.isVisibleToUser) {
                throw new CustomHttpError('system sub transaction types cannot be updated', '400');
            }

            const transactions = await TransactionsDAO.getTransactionsByFilter(
                organizationId,
                {
                    subType: ObjectId(id)
                },
                null,
                null
            );
            if (transactions.length > 0) {
                throw new CustomHttpError('Sub transaction type is already in use', '400');
            }

            const updatedDocument = await SubTransactionTypesDAO.updateSubTransactionType(
                validatedDataObj,
                id,
                organizationId,
                callerId
            );

            await invalidateSubTransactionTypeCache(organizationId, id);

            return Promise.resolve({
                result: updatedDocument,
                auditData
            });
        } catch (e) {
            log.error('error\n', e);
            return Promise.reject({
                err: e,
                auditData: { ...auditData, auditMessage: e.message }
            });
        }
    }

    static async validateSubTransactionType(organizationId, filter) {
        try {
            const result = await SubTransactionTypesDAO.getSubTransactionTypesByFilter(
                organizationId,
                {
                    ...filter,
                    status: STATUS.ENABLED
                },
                null,
                null
            );
            if (result.length > 0) {
                return Promise.resolve(result[0]);
            }
            return Promise.reject(new CustomHttpError('Invalid sub transaction type', '400'));
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }

    static async validateSubTransactionTypeById(organizationId, subTransactionTypeId) {
        try {
            const cacheKey = `subTransactionTypes:${organizationId}:${subTransactionTypeId}`;
            const result = await promiseRaceAsync(
                [
                    getWrapperForCache(getAsync, [cacheKey]),
                    getWrapperForCache(
                        SubTransactionTypesDAO.getSubTransactionTypesById,
                        [organizationId, subTransactionTypeId],
                        true
                    )
                ],
                cacheKey
            );
            if (!result || result?.status !== STATUS.ENABLED) {
                return Promise.reject(new CustomHttpError('Invalid sub transaction type', '400'));
            }
            return result;
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }
}

module.exports = SubTransactionTypesHandler;
