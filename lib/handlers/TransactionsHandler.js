'use strict';
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const TransactionsDAO = require('../db/dao/TransactionsDAO');
const JobsHandler = require('./JobsHandler');
const { JOB_TYPES } = require('../db/models/enums/job.types.enums');
const { PROCESS_TYPE, STATUS: JOB_STATUS } = require('../db/models/enums/job.enums');

const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { TRANSACTION }
        }
    },
    validateGet
} = require('@shoutout-labs/authz-utils');

class TransactionsHandler {
    static async getTransactions(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                validatedObj = await validateGet(
                    TRANSACTION.ACTIONS.LIST_TRANSACTIONS,
                    TRANSACTION.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    validatedObj
                );
            }
            const result = await TransactionsDAO.getTransactions(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async exportTransactions(organizationId, validatedObj, ability, boundary, callerId) {
        try {
            if (ability) {
                validatedObj = await validateGet(
                    TRANSACTION.ACTIONS.LIST_TRANSACTIONS,
                    TRANSACTION.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    validatedObj
                );
            }

            const {
                result: { _id }
            } = await JobsHandler.createJob(
                {
                    regionId: validatedObj.regionId,
                    name: 'Transactions list export',
                    description: 'Transactions list export',
                    notificationEmails: validatedObj.notificationEmails,
                    type: JOB_TYPES.EXPORT_TRANSACTIONS,
                    metadata: {
                        validatedObj,
                        stagedTransactions: false
                    },
                    processType: PROCESS_TYPE.IMMEDIATE
                },
                organizationId,
                callerId
            );

            await JobsHandler.updateJob(
                {
                    status: JOB_STATUS.ENABLED
                },
                _id.toString(),
                organizationId,
                callerId
            );

            return Promise.resolve({
                message: 'Transactions export job creation successful'
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getTransactionsCount(organizationId, validatedObj, ability, boundary) {
        try {
            if (ability) {
                validatedObj = await validateGet(
                    TRANSACTION.ACTIONS.LIST_TRANSACTIONS,
                    TRANSACTION.MODULE_ID,
                    validatedObj.regionId,
                    boundary,
                    ability,
                    validatedObj
                );
            }
            const result = await TransactionsDAO.getTransactionsCount(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async getTransactionsPortal(organizationId, regionId, memberId, validatedObj) {
        try {
            validatedObj['regionId'] = regionId;
            validatedObj['memberId'] = memberId;
            const result = await TransactionsDAO.getPortalTransactions(organizationId, validatedObj);
            return Promise.resolve(result);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }
}

module.exports = TransactionsHandler;
