const config = require('../config');
const logger = require('../logger');
const CustomHttpError = require('../CustomHttpError');
const { OPERATION } = require('../db/models/enums/user.enums');
const MembersDAO = require('../db/dao/MembersDAO');
const {
    AUTH_CONSTANTS: {
        AUTH_MODULE_ACTIONS: {
            LOYALTY_SERVICE: { MEMBER }
        }
    }
} = require('@shoutout-labs/authz-utils');
const Shoutout = require('../services/Shoutout');
const log = logger(config.logger);

class EventHandler {
    static async updateMemberLastAccessedDate(validatedObj) {
        const auditData = {
            moduleId: MEMBER.MODULE_ID,
            action: MEMBER.ACTIONS.UPDATE_LAST_SEEN_ON,
            operation: OPERATION.UPDATE,
            auditMessage: 'member updated successfully'
        };
        try {
            const member = await MembersDAO.getMemberByUserId(
                validatedObj?.identityProviderId,
                validatedObj?.organizationId
            );
            if (!member) {
                return Promise.reject(new CustomHttpError('invalid identity provider Id!', '400'));
            }
            const { organizationId, _id, regionId, firstName, lastName, lastSeenOn } = member;

            if (Date.parse(lastSeenOn) < Date.parse(validatedObj?.loginDate) || !lastSeenOn) {
                const activityData = {
                    member_name: `${firstName} ${lastName}`,
                    member_id: _id.toString()
                };
                const [updatedMember] = await Promise.all([
                    MembersDAO.updateMember(
                        {
                            lastSeenOn: validatedObj?.loginDate
                        },
                        _id,
                        organizationId
                    ),
                    Shoutout.produceActivityToTopic({
                        memberId: _id.toString(),
                        activityName: 'Member Login',
                        organizationId,
                        regionId: regionId,
                        createdOn: new Date(),
                        activityData
                    })
                ]);
                return Promise.resolve({
                    result: updatedMember,
                    auditData: {
                        ...auditData
                    }
                });
            } else {
                log.error('error: duplicate date');
            }
        } catch (err) {
            log.error(err);
            return Promise.reject({
                err: err,
                auditData: { ...auditData, auditMessage: err.message }
            });
        }
    }
}

module.exports = EventHandler;
