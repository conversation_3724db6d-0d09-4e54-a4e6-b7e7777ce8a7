'use strict';
const AnnouncementsDAO = require('./../../db/dao/portal/PortalAnnouncementsDAO');
const config = require('./../../config');
const logger = require('./../../logger');
const log = logger(config.logger);

class PortalAnnouncementsHandler {
    static async getAnnouncements(ownerId, contactId) {
        try {
            const date = new Date();
            const announcementsGetResult = await AnnouncementsDAO.getAnnouncements(ownerId, contactId, date);
            return Promise.resolve(announcementsGetResult);
        } catch (error) {
            log.error('error\n', error);
            return Promise.reject(error);
        }
    }
}

module.exports = PortalAnnouncementsHandler;
