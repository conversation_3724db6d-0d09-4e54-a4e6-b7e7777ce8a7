'use strict';

const CustomHttpError = require('../CustomHttpError');

function validateRequest(schemas) {
    return async (req, res, next) => {
        try {
            const validationPromises = [];

            if (schemas.body) {
                validationPromises.push(
                    schemas.body.validateAsync(req.body).then((validatedBody) => {
                        req.body = validatedBody;
                    })
                );
            }

            if (schemas.query) {
                validationPromises.push(
                    schemas.query.validateAsync(req.query).then((validatedQuery) => {
                        req.query = validatedQuery;
                    })
                );
            }

            // Wait for all validations to complete
            await Promise.all(validationPromises);

            // Store response schema and status code for swagger generation
            if (schemas.response) {
                req.responseSchema =
                    typeof schemas.response === 'object' && schemas.response.schema
                        ? schemas.response.schema
                        : schemas.response;
                req.responseStatus =
                    typeof schemas.response === 'object' && schemas.response.status ? schemas.response.status : 200;
            }

            next();
        } catch (error) {
            next(new CustomHttpError(error.message, '400'));
        }
    };
}

// Export both the middleware and the schemas for swagger generation
function createValidator(schemas, routeConfig) {
    const validator = validateRequest(schemas);
    validator.schemas = schemas;
    validator.routeConfig = routeConfig;
    return validator;
}

module.exports = createValidator;
