const OrganizationDAO = require('../db/dao/OrganizationDAO');
const Utils = require('./../../lib/Utils');
const secretConfig = require('./../../config');

module.exports = async function (req, res, next) {
  const host = req.get('origin') || req.get('Origin');
  if (!host) {
    return res.status(400).send({
      error: 'Invalid Origin'
    });
  }
  const organization = await OrganizationDAO.getOrganizationByOrigin(host);
  if (!organization) {
    return res.status(400).send({
      error: 'Invalid Origin'
    });
  }
  req['organizationId'] = organization._id.toString();
  req['idpMetadata'] = Utils.encrypt(JSON.stringify(organization.configuration.portalConfiguration.idpMetadata), secretConfig.DATA_ENCRYPTION_SECRET);
  next();
};
