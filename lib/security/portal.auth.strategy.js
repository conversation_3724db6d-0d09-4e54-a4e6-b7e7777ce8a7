const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const jwksClient = require('jwks-rsa');
const jwt = require('jsonwebtoken');
const LoyaltyService = require('../services/LoyaltyService');
const secretConfig = require('./../../config');
const Utils = require('./../../lib/Utils');

const secretOrKeyProvider = () => {
  return async (request, rawJwtToken, done) => {
    try {
      const { certUrl, clientSecret } = JSON.parse(Utils.decrypt(request.idpMetadata, secretConfig.DATA_ENCRYPTION_SECRET));
      const client = jwksClient({
        jwksUri: certUrl,
        cache: true
      });
      const { header: { kid } } = jwt.decode(rawJwtToken, { complete: true });
      if (kid) {
        client.getSigningKey(kid, function (err, key) {
          const signingKey = key.publicKey || key.rsaPublicKey;
          done(null, signingKey);
        });
      } else {
        done(null, clientSecret);
      }
    } catch (e) {
      done(e);
    }
  }
}

/*
* @param {object} passport - passport instance
* @param {object} req - request object
* */
module.exports = (passport) => {
  const passportOpts = {
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKeyProvider: secretOrKeyProvider(),
    // issuer: extractIssuer(),
    // audience: extractAudience(),
    passReqToCallback: true
  };

  const strategyId = 'jwt-portal-auth';

  const strategyFunction = async (req, jwtPayload, done) => {
    try {
      const member = await LoyaltyService.getMemberByIdpId(req.organizationId, jwtPayload['sub']);
      done(null, {
        id: member._id.toString(),
        regionId: member.regionId.toString(),
        organizationId: req.organizationId
      });
    } catch (e) {
      done(e);
    }
  };

  passport.use(strategyId, new JwtStrategy(passportOpts, strategyFunction));
}
