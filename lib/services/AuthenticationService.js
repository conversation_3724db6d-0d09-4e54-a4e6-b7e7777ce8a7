const axios = require('axios');
const querystring = require('query-string');
const logger = require('../logger');
const config = require("../../config");
const log = logger(config.logger);




const KEYCLOAK_REALM = process.env.KEYCLOAK_REALM;
const KEYCLOAK_CLIENT_ID = process.env.KEYCLOAK_CLIENT_ID;
const KEYCLOAK_CLIENT_SECRET = process.env.KEYCLOAK_CLIENT_SECRET;
const KEYCLOAK_BASE_URL = process.env.KEYCLOAK_BASE_URL;


class AuthenticationService {
    static async getAccessToken() {
        try {
            const data = querystring.stringify({
                'grant_type': 'client_credentials',
                'client_id': KEYCLOAK_CLIENT_ID,
                'client_secret': KEYCLOAK_CLIENT_SECRET
            }, { arrayFormat: 'bracket' });
            const url = `${KEYCLOAK_BASE_URL}/realms/${KEYCLOAK_REALM}/protocol/openid-connect/token`;
            const configAxios = {
                method: 'post',
                url,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data
            };
            const response = await axios(configAxios);
            return Promise.resolve(response.data);
        } catch (error) {
            log.error('error getting keycloak access token', error);
            return Promise.reject(new Error('keycloak access token get'));
        }
    }
}

module.exports = AuthenticationService;