const axios = require('axios');
const querystring = require('query-string');
const AuthTokenService = require('./AuthTokenService');

const CORE_SERVICE_BASE_URL = process.env.CORE_SERVICE_BASE_URL;

const getSegments = async (organizationId, regionId, segmentIds) => {
    try {
        const tokenResponse = await AuthTokenService.getAccessToken();
        const token = tokenResponse.access_token;
        const query = querystring.stringify({
            limit: 100,
            skip: 0,
            organizationId,
            regionId,
            segmentIds
        }, { arrayFormat: 'bracket' });
        const configAxios = {
            method: 'get',
            url: `${CORE_SERVICE_BASE_URL}/system/segments?${query}`,
            headers: {
                'Authorization': 'Bearer ' + token
            }
        };
        const response = await axios(configAxios);
        if (response.status === 200) {
            if (response.data) {
                return Promise.resolve(response.data);
            } else {
                return Promise.reject(new Error('system member not found'));
            }
        } else {
            return Promise.reject(new Error('error getting system member'));
        }
    } catch (error) {
        return Promise.reject(error);
    }
}

class CoreService {

    static async getSegmentsByIds(organizationId, regionId, segmentIds) {
        try {
            const results = await getSegments(organizationId, regionId, segmentIds);
            return Promise.resolve(results);
        } catch (error) {
            return Promise.reject(error);
        }
    }

}

module.exports = CoreService;
