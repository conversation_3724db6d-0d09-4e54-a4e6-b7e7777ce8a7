const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const KeycloakService = require('./KeycloakService');

const userId = '02469205-308b-40e7-8577-2e23f9cd8032';

beforeAll(async () => {});

afterAll(async () => {});

it.skip('create group admin', async () => {
    const events = await KeycloakService.getEventsForUserId(userId, 0, 5);
    expect(Array.isArray(events)).toEqual(true);
});

it.skip('reset password', async () => {
    const result = await KeycloakService.resetPassword('a1c76112-970b-4654-b960-a7fbf6493ce1', 'test@1234', true);
    expect(result).toEqual(undefined);
});
