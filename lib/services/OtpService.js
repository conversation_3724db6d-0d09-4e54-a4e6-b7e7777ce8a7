'use strict';
const axios = require('axios');
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const CustomHttpError = require('./../CustomHttpError');

class OtpService {

    static async sendOtp(mobileNumber, senderId, apiKey, otpMessageTemplate) {
        try {

            const data = JSON.stringify({
                "source": senderId,
                "transport": "sms",
                "content": { "sms": otpMessageTemplate ? otpMessageTemplate : "Use code {{code}}" },
                "destination": mobileNumber
            });

            const configAxios = {
                method: 'post',
                url: 'https://api.getshoutout.com/otpservice/send',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + apiKey
                },
                data: data
            };

            const response = await axios(configAxios);
            if (response && response.status === 200 && response.data.messageResult.status === "1001") {
                return Promise.resolve(response.data);
            } else {
                log.error(response.status);
                return Promise.reject(new CustomHttpError('otp sending failed.', 500));
            }

        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }


    static async verifyOtp(otpCode, referenceId, apiKey) {
        try {
            const data = JSON.stringify({ "code": otpCode, "referenceId": referenceId });

            const configAxios = {
                method: 'post',
                url: 'https://api.getshoutout.com/otpservice/verify',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + apiKey
                },
                data: data
            };

            const response = await axios(configAxios);

            if (response && response.status === 200 && response.data.statusCode === '1000') {
                return Promise.resolve(response.data);
            } else {
                return Promise.reject(new CustomHttpError('otp verification failure', '400'));
            }

        } catch (error) {
            log.error(error);
            return Promise.reject(error);
        }
    }

}

module.exports = OtpService;
