'use strict';
const { S3Client, GetObjectCommand, PutObjectCommand } = require('@aws-sdk/client-s3');
const { AWS_REGION_DEFAULT } = require('../../config');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const logger = require('../logger');
const config = require('../config');
const log = logger(config.logger);
class S3 {
    constructor(opts = {}) {
        const options = { region: AWS_REGION_DEFAULT, ...opts };
        this.s3 = new S3Client(options);
    }

    async upload(body, bucket, key) {
        try {
            const s3Client = this.s3;
            const putObjectParams = {
                Body: body,
                Bucket: bucket,
                Key: key
            };
            const putObjectCommand = new PutObjectCommand(putObjectParams);
            const uploadResponse = await s3Client.send(putObjectCommand);
            return Promise.resolve(uploadResponse);
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    async getSignedUrl(bucket, key, expires) {
        try {
            const s3Client = this.s3;
            const getObjectParams = { Bucket: bucket, Key: key };
            const getObjectCommand = new GetObjectCommand(getObjectParams);
            const url = await getSignedUrl(s3Client, getObjectCommand, { expiresIn: expires });
            return Promise.resolve(url);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    async getObject(bucket, key) {
        try {
            const s3Client = this.s3;
            const getObjectParams = {
                Bucket: bucket,
                Key: key
            };
            const getObjectCommand = new GetObjectCommand(getObjectParams);
            const data = await s3Client.send(getObjectCommand);
            return Promise.resolve(data);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    async getObjectAsStream(params) {
        try {
            const s3Client = this.s3;
            const getObjectCommand = new GetObjectCommand(params);
            const data = await s3Client.send(getObjectCommand);
            return Promise.resolve(data.Body);
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }
}

module.exports = S3;
