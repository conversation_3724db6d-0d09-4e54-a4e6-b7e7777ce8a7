'use strict';

const {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const config = require('../config');
const logger = require('../logger');
const { AWS_REGION_DEFAULT } = require('../../config');

const log = logger(config.logger);

class S3V2 {
  constructor(opts = {}) {
    const options = { region: AWS_REGION_DEFAULT, ...opts };
    this.s3 = new S3Client(options);
  }

  static async upload({ Bucket = '', Key = '', Body = '' }) {
    try {
      const ctx = this;
      const s3Client = new ctx().s3;
      const putObjectParams = { Bucket, Key, Body };
      const putObjectCommand = new PutObjectCommand(putObjectParams);
      const uploadResponse = await s3Client.send(putObjectCommand);
      return Promise.resolve(uploadResponse);
    } catch (e) {
      log.error(e);
      return Promise.reject(e);
    }
  }

  static async getSignedUrl({ Bucket = '', Key = '', expiresIn }) {
    try {
      const ctx = this;
      const s3Client = new ctx().s3;
      const getObjectParams = { Bucket, Key };
      const getObjectCommand = new GetObjectCommand(getObjectParams);
      const url = await getSignedUrl(s3Client, getObjectCommand, { expiresIn });
      return Promise.resolve(url);
    } catch (e) {
      log.error(e);
      return Promise.reject(e);
    }
  }
}

module.exports = S3V2;
