const axios = require('axios');
const config = require('../../config');
const logger = require('../logger');
const log = logger(config.logger);

const uri= config.URL_SHORTENER_BASE_URL + '/shorten';
class ShoutOUTUrlShortenerService {
    static async shortenUrls(payload) {
        try {

            const response = await axios.post(uri,payload);
            if (response.status === 200) {
                if (response?.data?.length>0) {
                    return Promise.resolve(response?.data[0]?.shortenUrl);
                }
            } else {
                return Promise.reject(new Error('something went wrong'));
            }
        } catch (error) {
            log.error('error shortening url\n', error);
            return Promise.reject(error);
        }
    }

}

module.exports = ShoutOUTUrlShortenerService;