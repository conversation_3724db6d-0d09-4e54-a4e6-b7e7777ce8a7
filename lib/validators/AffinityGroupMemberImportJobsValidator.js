'use strict';
let Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('./../CustomHttpError');
const { SYSTEM_ATTRIBUTE_NAME } = require('./../db/models/enums/affinity.group.member.import.job.enums');

const schema = Joi.object().keys({
    fieldMappings: Joi.array().min(1).items(Joi.object().keys({
        fileColumnName: Joi.string().required(),
        systemAttributeName: Joi.string().valid(...Object.values(SYSTEM_ATTRIBUTE_NAME)).required()
    })).required(),
    fileToken: Joi.string().required(),
});

const uploadSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    affinityGroupId: Joi.objectId().required()
});

class AffinityGroupMemberImportJobsValidator {

    static isValid(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

    static isValidUpload(obj) {
        const { value, error } = uploadSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

}

module.exports = AffinityGroupMemberImportJobsValidator;
