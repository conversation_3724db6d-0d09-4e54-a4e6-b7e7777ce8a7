'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const { COMMON: { DATE_BUCKET } } = require('../../constants/AnalyticsEnums');
const CustomHttpError = require('../../CustomHttpError');

const dateRangeSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    dateFrom: Joi.date(),
    dateTo: Joi.date(),
    dateBucket: Joi.string().valid(...Object.keys(DATE_BUCKET)).default('DAY'),
});

const dateRangeWithFiltersSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    merchantId: Joi.objectId(),
    merchantLocationId: Joi.objectId(),
    dateFrom: Joi.date(),
    dateTo: Joi.date(),
    dateBucket: Joi.string().valid(...Object.keys(DATE_BUCKET)).default('DAY'),
});

const dateRangeWithFiltersExportSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    merchantId: Joi.objectId(),
    merchantLocationId: Joi.objectId(),
    dateFrom: Joi.date(),
    dateTo: Joi.date(),
    dateBucket: Joi.string().valid(...Object.keys(DATE_BUCKET)).default('DAY'),
    notificationEmails: Joi.array().items(Joi.string().email()).min(1).required()
});

class PointsQueryValidator {

    static isValidDateRange(obj) {
        const { value, error } = dateRangeSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidDateRangeWithFilters(obj) {
        const { value, error } = dateRangeWithFiltersSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidExportDateRangeWithFilters(obj) {
        const { value, error } = dateRangeWithFiltersExportSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = PointsQueryValidator;
