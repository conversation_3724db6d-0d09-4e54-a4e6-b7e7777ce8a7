'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const { COMMON: { DATE_BUCKET }, REWARDS: { GROUP_BY } } = require('../../constants/AnalyticsEnums');
const CustomHttpError = require('../../CustomHttpError');

const dateRangeSchema = Joi.object().keys({
    limit: Joi.number().default(10),
    skip: Joi.number().default(0),
    regionId: Joi.objectId().required(),
    dateFrom: Joi.date(),
    dateTo: Joi.date()
});

const redemptionReportSchema = Joi.object().keys({
    rewardId: Joi.objectId(),
    regionId: Joi.objectId().required(),
    groupBy: Joi.string().valid(...Object.keys(GROUP_BY)),
    dateFrom: Joi.date(),
    dateTo: Joi.date(),
    dateBucket: Joi.string().valid(...Object.keys(DATE_BUCKET)).default('DAY'),
});

class MembersQueryValidator {

    static isValidDateRange(obj) {
        const { value, error } = dateRangeSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidRedemptionGet(obj) {
        const { value, error } = redemptionReportSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = MembersQueryValidator;
