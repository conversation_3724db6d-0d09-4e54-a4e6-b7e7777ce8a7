'use strict';
let Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('./../CustomHttpError');
const { CHANNEL, VISIBILITY, TYPE } = require('../db/models/enums/campaign.enums');

const schema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    name: Joi.string().required(),
    description: Joi.string(),
    segmentFilters: Joi.array().items(
        Joi.object().keys({
            membersFilter: Joi.string().optional(),
            transactionsFilter: Joi.string().optional()
        })
    ),
    scheduleOn: Joi.date(),
    channel: Joi.string()
        .valid(...Object.values(CHANNEL))
        .required(),
    type: Joi.string()
        .valid(...Object.values(TYPE))
        .required(),
    visibility: Joi.string()
        .valid(...Object.values(VISIBILITY))
        .default(VISIBILITY.PRIVATE),
    senderId: Joi.string().when('channel', { not: CHANNEL.BANNER, then: Joi.string().required() }),
    provider: Joi.string(),
    endOn: Joi.date().when('channel', { not: CHANNEL.BANNER, then: Joi.forbidden() }),
    message: Joi.object().keys({
        messageBody: Joi.string(),
        messageSubject: Joi.string()
    })
});

const getCampaignByIdSchema = Joi.object().keys({
    id: Joi.objectId().required()
});

const getCampaignSchema = Joi.object().keys({
    skip: Joi.number().default(0).integer().min(0),
    limit: Joi.number().default(50).integer().min(1),
    regionId: Joi.objectId().required(),
    searchKey: Joi.string(),
    channel: Joi.string(),
    status: Joi.string(),
    fromDate: Joi.string(),
    toDate: Joi.string()
});

const getCampaignReportByIdSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    categoryId: Joi.objectId().required()
});

const getBannerCampaignsSchema = Joi.object().keys({
    skip: Joi.number().default(0).integer().min(0),
    limit: Joi.number().default(50).integer().min(1),
    regionId: Joi.objectId().required()
});

class CampaignsValidator {
    static isValid(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }

        if (value.type === TYPE.PROMOTIONAL) {
            if (Array.isArray(value.segmentFilters) && value.segmentFilters.length > 0) {
                for (let segmentFilter of value.segmentFilters) {
                    if (segmentFilter.membersFilter) {
                        segmentFilter = JSON.parse(segmentFilter.membersFilter);
                        const allowPromotionalNotifications =
                            segmentFilter['notificationPreference.allowPromotionalNotifications'];
                        if (
                            allowPromotionalNotifications &&
                            (allowPromotionalNotifications === true || allowPromotionalNotifications === 'true')
                        ) {
                            return Promise.resolve(value);
                        } else {
                            return Promise.reject(
                                new CustomHttpError(
                                    "'notificationPreference.allowPromotionalNotifications' must be true in all the segment filters for PROMOTIONAL campaigns",
                                    400
                                )
                            );
                        }
                    } else {
                        return Promise.reject(
                            new CustomHttpError(
                                "'notificationPreference.allowPromotionalNotifications' must be true in all the segment filters for PROMOTIONAL campaigns",
                                400
                            )
                        );
                    }
                }
            }
            return Promise.reject(new CustomHttpError('Segment Filters Required', 400));
        }

        return Promise.resolve(value);
    }

    static isValidGetCampaign(obj) {
        const { value, error } = getCampaignSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

    static isValidGetCampaignById(obj) {
        const { value, error } = getCampaignByIdSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

    static isValidGetCampaignReportById(obj) {
        const { value, error } = getCampaignReportByIdSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidGetBannerCampaigns(obj) {
        const { value, error } = getBannerCampaignsSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = CampaignsValidator;
