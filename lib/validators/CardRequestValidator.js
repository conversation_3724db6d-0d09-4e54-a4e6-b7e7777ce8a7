'use strict';
const config = require('./../config');
const logger = require('./../logger');
const log = logger(config.logger);
const Joi = require('joi');
const CustomHttpError = require('./../CustomHttpError');
const { CARD_REQUEST_STATUS } = require('./../constants/Constants');

const schemaCreate = Joi.object().keys({
    loyaltyId: Joi.string().required(),
    userData: Joi.object().keys({
        name: Joi.string().required(),
        mobileNumber: Joi.string().required(),
        email: Joi.string().required(),
        address: Joi.string().required()
    })
});

const schemaUpdate = Joi.object().keys({
    status: Joi.string().valid(CARD_REQUEST_STATUS.NEW_REQUEST, CARD_REQUEST_STATUS.POSTED, CARD_REQUEST_STATUS.RETURNED),
    notes: Joi.when('status', {
        not: CARD_REQUEST_STATUS.RETURNED,
        then: Joi.string(),
        otherwise: Joi.string().required()
    }),
    userData: Joi.when('status', {
        not: CARD_REQUEST_STATUS.NEW_REQUEST,
        then: Joi.forbidden(),
        otherwise: Joi.object().keys({
            name: Joi.string().required(),
            mobileNumber: Joi.string().required(),
            email: Joi.string().required(),
            address: Joi.string().required()
        })
    })
});

const schemaPost = Joi.object().keys({
    cardRequestIds: Joi.array().items(Joi.string()).required().min(1)
});

class CardRequestValidator {
    static isValidCreate(obj) {
        const { value, error } = schemaCreate.validate(obj);
        if (error) {
            log.error('Error\n', error);
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidUpdate(obj) {
        const { value, error } = schemaUpdate.validate(obj);
        if (error) {
            log.error('Error\n', error);
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidPost(obj) {
        const { value, error } = schemaPost.validate(obj);
        if (error) {
            log.error('Error\n', error);
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = CardRequestValidator;