'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const { CARD_STATUS, CARD_PROCESSING_STATUS } = require('../db/models/enums/card.enums');
const CustomHttpError = require('../CustomHttpError');

const schema = Joi.object().keys({
    status: Joi.string().valid(
        CARD_STATUS.SUSPENDED,
        CARD_STATUS.DEACTIVATED,
        CARD_STATUS.ASSIGNED,
        CARD_STATUS.ACTIVE
    ),
    waiveCardReplacementFee: Joi.boolean(),
    processingStatus: Joi.string().valid(
        CARD_PROCESSING_STATUS.PENDING,
        CARD_PROCESSING_STATUS.PRINTING,
        CARD_PROCESSING_STATUS.PRINTED,
        CARD_PROCESSING_STATUS.DISPATCHED,
        CARD_PROCESSING_STATUS.COMPLETED,
        CARD_PROCESSING_STATUS.FAILED
    ),
    note: Joi.string().when('status', { not: CARD_STATUS.DEACTIVATED, then: Joi.required() }),
    embossCard: Joi.object().keys({ embossIssuedOn: Joi.string() })
});

const assignCardSchema = Joi.object()
    .keys({
        memberId: Joi.objectId().required(),
        cardNumber: Joi.number(),
        cardNumberStr: Joi.string(),
        waiveCardReplacementFee: Joi.boolean()
    })
    .or('cardNumber', 'cardNumberStr');

const assignDigitalCardSchema = Joi.object().keys({
    memberId: Joi.objectId().required()
});

const embossRequestCardSchema = Joi.object().keys({
    printedName: Joi.string().required(),
    merchantLocationId: Joi.objectId().required()
});

const cardsManualGenerationSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    autoGenerate: Joi.boolean().default(false),
    cardNumbers: Joi.when('autoGenerate', {
        is: false,
        then: Joi.array().items(Joi.string()).min(1).max(1000),
        otherwise: Joi.forbidden()
    })
});

class CardsValidator {
    static cardsCreateValidation(obj) {
        return schema.validateAsync(obj);
    }

    static cardsUpdateValidation(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static cardsManualGenerationValidation(obj) {
        const { value, error } = cardsManualGenerationSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static cardsAssignValidation(obj) {
        const { value, error } = assignCardSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static digitalCardAssignValidation(obj) {
        const { value, error } = assignDigitalCardSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static cardsEmbossRequestValidation(obj) {
        const { value, error } = embossRequestCardSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = CardsValidator;
