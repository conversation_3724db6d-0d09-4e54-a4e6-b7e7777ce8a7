'use strict';
let Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('./../CustomHttpError');
const { STATUS } = require('./../db/models/enums/common.enums');

const updateSchema = Joi.object().keys({
    status: Joi.string().valid(STATUS.ENABLED, STATUS.DISABLED).required()
});

class JobTypesValidator {

    static isValidUpdate(obj) {
        const { value, error } = updateSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

}

module.exports = JobTypesValidator;
