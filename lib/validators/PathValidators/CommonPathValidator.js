'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
Joi.objectId = require('joi-objectid')(Joi);

const schema = Joi.object().keys({
    id: Joi.objectId().required()
});

const regionSchema = Joi.object().keys({
    id: Joi.objectId().required(),
    regionId: Joi.objectId().required(),
    fromDate: Joi.date(),
    toDate: Joi.date()
});

class CommonPathValidator {

    static isValid(obj) {
        let {value, error} = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidRegion(obj) {
        let {value, error} = regionSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = CommonPathValidator;
