'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const { SORT_DIRECTION } = require('@shoutout-labs/constants');
const { ProductCatalogueSortFields } = require('../db/models/enums/product.catalogue.enums');
const CustomHttpError = require('../CustomHttpError');

const commonSchema = {
    regionId: Joi.objectId().required().description('Region ID to filter members'),
    sortBy: Joi.string()
        .valid(...Object.values(ProductCatalogueSortFields))
        .description('Field to sort by'),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC).description('Sort direction'),
    merchantId: Joi.objectId().description('Merchant ID filter'),
    searchKey: Joi.string().description('Search keyword: Search by "referenceId" or "name"'),
    lastSoldOnFromDate: Joi.date(),
    lastSoldOnToDate: Joi.date(),
    createdOnFromDate: Joi.date(),
    createdOnToDate: Joi.date()
};

const getSchema = Joi.object().keys({
    limit: Joi.number().required().default(50).integer().min(1).description('Number of records to return'),
    skip: Joi.number().required().default(0).integer().min(0).description('Number of records to skip'),
    ...commonSchema
});

const exportSchema = Joi.object().keys({
    ...commonSchema,
    notificationEmails: Joi.array().items(Joi.string().email()).min(1).required()
});

// * Response schema for product catalogue.
const productCatalogueGetResponseSchema = Joi.object({
    limit: Joi.number().default(50).integer().min(1).description('Number of records to return'),
    skip: Joi.number().default(0).integer().min(0).description('Number of records to skip'),
    total: Joi.number().description('Total number of records returned'),
    items: Joi.array()
        .items(
            Joi.object().keys({
                organizationId: Joi.objectId(),
                regionId: Joi.objectId(),
                referenceId: Joi.string(),
                name: Joi.string(),
                unitPrice: Joi.string(),
                totalSoldQuantity: Joi.number(),
                totalSales: Joi.number(),
                totalTransactions: Joi.number(),
                lastSoldOn: Joi.date(),
                merchantId: Joi.objectId(),
                productCategory: Joi.array().items(Joi.string())
            })
        )
        .description('Total records returned')
});

const productCatalogueExportResponseSchema = Joi.object({ message: Joi.string().description('Success message') });

class ProductCatalogueValidator {
    static isValidGet(obj) {
        const { value, error } = getSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
    static isValidExport(obj) {
        const { value, error } = exportSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

// * Export schemas and configurations.
ProductCatalogueValidator.productCatalogueGetSchema = getSchema;
ProductCatalogueValidator.productCatalogueExportSchema = exportSchema;
ProductCatalogueValidator.productCatalogueGetResponseSchema = productCatalogueGetResponseSchema;
ProductCatalogueValidator.productCatalogueExportResponseSchema = productCatalogueExportResponseSchema;

module.exports = ProductCatalogueValidator;
