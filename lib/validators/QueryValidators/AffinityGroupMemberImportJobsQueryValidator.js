'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
const { STATUS } = require('../../db/models/enums/affinity.group.member.import.job.enums');
Joi.objectId = require('joi-objectid')(Joi);

const schema = Joi.object().keys({
    skip: Joi.number().required().default(0),
    limit: Joi.number().required().default(10),
    regionId: Joi.objectId().required(),
    affinityGroupId: Joi.objectId(),
    importJobId: Joi.objectId(),
    status: Joi.string().valid(...Object.values(STATUS)),
    createdDateFrom: Joi.date(),
    createdDateTo: Joi.date(),
    searchKey: Joi.string()
});

const schemaLogsExport = Joi.object().keys({
    regionId: Joi.objectId().required(),
    affinityGroupId: Joi.objectId(),
    importJobId: Joi.objectId(),
    status: Joi.string().valid(...Object.values(STATUS)),
    createdDateFrom: Joi.date(),
    createdDateTo: Joi.date()
});

class AffinityGroupMemberImportJobsQueryValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidLogsExport(obj) {
        const { value, error } = schemaLogsExport.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = AffinityGroupMemberImportJobsQueryValidator;
