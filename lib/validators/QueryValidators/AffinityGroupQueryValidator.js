'use strict';
let Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('../../CustomHttpError');

let schema = Joi.object().keys({
    skip: Joi.number().default(0).required(),
    limit: Joi.number().default(10).required(),
    regionId: Joi.objectId().required(),
    searchKey: Joi.string(),
});

class AffinityGroupQueryValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = AffinityGroupQueryValidator;
