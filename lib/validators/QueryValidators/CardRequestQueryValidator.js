'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
const { CARD_REQUEST_STATUS } = require('./../../constants/Constants');

const schemaGetCardRequests = Joi.object().keys({
    skip: Joi.number().default(0),
    limit: Joi.number().default(50),
    fromDate: Joi.date().required(),
    toDate: Joi.date().default(new Date()).min(Joi.ref('fromDate')),
    printBatchId: Joi.string(),
    status: Joi.string().valid(CARD_REQUEST_STATUS.NEW_REQUEST, CARD_REQUEST_STATUS.PRINTING, CARD_REQUEST_STATUS.PRINTED, CARD_REQUEST_STATUS.POSTED, CARD_REQUEST_STATUS.RETURNED),
    searchKey: Joi.string()
});

const schemaGetCardRequestsCounts = Joi.object().keys({
    fromDate: Joi.date().required(),
    toDate: Joi.date().default(new Date()).min(Joi.ref('fromDate'))
});

const schemaGetCardRequestsSeries = Joi.object().keys({
    fromDate: Joi.date().required(),
    toDate: Joi.date().default(new Date()).min(Joi.ref('fromDate'))
});

class CardRequestQueryValidator {
    static isValidGet(obj) {
        const { value, error } = schemaGetCardRequests.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidGetCardRequestsCounts(obj) {
        const { value, error } = schemaGetCardRequestsCounts.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidGetCardRequestsSeries(obj) {
        const { value, error } = schemaGetCardRequestsSeries.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = CardRequestQueryValidator;
