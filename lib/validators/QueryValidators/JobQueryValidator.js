'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
const { STATUS, PROCESS_TYPE, SCHEDULE_TYPE } = require('../../db/models/enums/job.enums');
Joi.objectId = require('joi-objectid')(Joi);

const schema = Joi.object().keys({
    skip: Joi.number().required().default(0),
    limit: Joi.number().required().default(10),
    regionId: Joi.objectId().required(),
    jobTypeId: Joi.objectId(),
    searchKey: Joi.string(),
    processType: Joi.string().valid(...Object.values(PROCESS_TYPE)),
    scheduleType: Joi.string().valid(...Object.values(SCHEDULE_TYPE)),
    status: Joi.string().valid(STATUS.ENABLED, STATUS.DISABLED, STATUS.COMPLETED)
});

class JobQueryValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = JobQueryValidator;
