'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
Joi.objectId = require('joi-objectid')(Joi);
const { STATUS } = require('../../db/models/enums/merchant.location.enums');

const schema = Joi.object().keys({
    skip: Joi.number().required().default(0),
    limit: Joi.number().required().default(10),
    regionId: Joi.objectId().required(),
    merchantId: Joi.objectId(),
    merchantIds: Joi.array().items(Joi.objectId()),
    searchKey: Joi.string(),
    locationCode: Joi.string(),
    isPickupLocation: Joi.boolean(),
    nearestLocations: Joi.boolean(),
    locationIds: Joi.array().items(Joi.objectId()),
    status: Joi.string().valid(STATUS.DRAFT, STATUS.ACTIVE, STATUS.SUSPENDED).optional(),
});

const systemSchema = Joi.object().keys({
    skip: Joi.number().required().default(0),
    limit: Joi.number().required().default(10),
    regionId: Joi.objectId(),
    merchantId: Joi.objectId(),
    locationIds: Joi.array().items(Joi.objectId()),
    organizationId: Joi.objectId().required(),
    searchKey: Joi.string(),
    isPickupLocation: Joi.boolean(),
    status: Joi.string().valid(STATUS.DRAFT, STATUS.ACTIVE, STATUS.SUSPENDED, STATUS.ARCHIVED).optional(),
    fields: Joi.array().items(Joi.string())
});

class MerchantLocationQueryValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidSystemGet(obj) {
        const { value, error } = systemSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = MerchantLocationQueryValidator;
