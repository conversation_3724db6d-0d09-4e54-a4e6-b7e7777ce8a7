'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
Joi.objectId = require('joi-objectid')(Joi);
const { STATUS, TYPE, SUB_TYPE } = require('../../db/models/enums/point.rule.enums');

const schemaGet = Joi.object().keys({
    skip: Joi.number().default(0).required(),
    limit: Joi.number().default(10).required(),
    merchantId: Joi.objectId(),
    regionId: Joi.objectId().required(),
    status: Joi.string().valid(STATUS.ENABLED, STATUS.DISABLED, STATUS.ARCHIVED).optional(),
    type: Joi.string().valid(TYPE.TRANSACTIONAL, TYPE.NON_TRANSACTIONAL).optional(),
    subType: Joi.string()
        .valid(
            SUB_TYPE.GENERAL,
            SUB_TYPE.SEASONAL,
            SUB_TYPE.LOCATION,
            SUB_TYPE.AFFINITY,
            SUB_TYPE.ENROLL,
            SUB_TYPE.SIGNUP,
            SUB_TYPE.PROFILE_COMPLETION,
            SUB_TYPE.BIRTHDAY
        )
        .optional()
});

class PointRuleQueryValidator {
    static isValidGet(obj) {
        const { value, error } = schemaGet.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = PointRuleQueryValidator;
