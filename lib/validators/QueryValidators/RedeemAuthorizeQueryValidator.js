'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
const AUTH_STATUS = require('@shoutout-labs/constants').AUTH_STATUS;

const schemaRedeemAuthorize = Joi.object().keys({
    status: Joi.string().valid(AUTH_STATUS.APPROVED, AUTH_STATUS.PENDING, AUTH_STATUS.REJECTED)
});

class RedeemAuthorizeQueryValidator {
    static isValid(obj) {
        let {value, error} = schemaRedeemAuthorize.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = RedeemAuthorizeQueryValidator;
