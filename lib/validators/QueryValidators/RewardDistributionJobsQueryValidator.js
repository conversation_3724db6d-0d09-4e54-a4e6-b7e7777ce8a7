'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
Joi.objectId = require('joi-objectid')(Joi);
const { STATUS } = require('../../db/models/enums/reward.distribution.job.enums');
const { PARTNER_REWARD_CONFIG } = require('../../db/models/enums/reward.enums');
const { PARTNER_STATUS } = require('../../db/models/enums/reward.redemption.log.enums');

const schemaExport = Joi.object().keys({
    distributionJobId: Joi.objectId().required(),
    redemptionStatus: Joi.string()
        .valid(
            PARTNER_STATUS.PROCESSING,
            PARTNER_STATUS.COMPLETED,
            PARTNER_STATUS.FAILED,
            PARTNER_STATUS.REFUNDED,
        )
        .optional(),
    customExporter: Joi.string()
        .valid(
            PARTNER_REWARD_CONFIG.CAL_MILES_CONFIGS,
            PARTNER_REWARD_CONFIG.DEFAULT
        )
        .optional(),
});

const schemaGet = Joi.object().keys({
    skip: Joi.number().default(0).required(),
    limit: Joi.number().default(10).required(),
    batchId: Joi.number().optional(),
    rewardId: Joi.objectId().optional(),
    regionId: Joi.objectId().required(),
    searchKey: Joi.string(),
    status: Joi.string().valid(STATUS.PENDING,STATUS.PROCESSING,STATUS.DISPATCHED,STATUS.COMPLETED,STATUS.FAILED).optional(),
    createdOnFrom: Joi.date(),
    createdOnTo: Joi.date().when('createdOnFrom', { not: undefined, then: Joi.date().min(Joi.ref('createdOnFrom')) })
});

const validImport = Joi
    .string()
    .valid(
        PARTNER_REWARD_CONFIG.CAL_MILES_CONFIGS, 
        PARTNER_REWARD_CONFIG.DEFAULT
    )
    .required();

class RewardDistributionJobsQueryValidator {
    static isValidCsvGet(obj) {
        const { value, error } = schemaExport.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
    static isValidGet(obj) {
        const { value, error } = schemaGet.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
    static isValidCustomImport(customImporter) {
        const { value, error } = validImport.validate(customImporter);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = RewardDistributionJobsQueryValidator;
