'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
const { METHOD, STATUS } = require('../../db/models/enums/reward.topup.enums');
Joi.objectId = require('joi-objectid')(Joi);

const schema = Joi.object().keys({
    skip: Joi.number().required().default(0),
    limit: Joi.number().required().default(10),
    regionId: Joi.objectId().required(),
    merchantId: Joi.objectId(),
    rewardId: Joi.objectId(),
    method: Joi.string().valid(METHOD.GENERATED,METHOD.MANUAL,METHOD.UPLOAD).optional(),
    status: Joi.string().valid(STATUS.SUCCESS,STATUS.FAILED).optional()
});

const uploadSchema = Joi.object().keys({
    rewardId: Joi.objectId().required()
});

class RewardTopUpQueryValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidUpload(obj) {
        const { value, error } = uploadSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

}

module.exports = RewardTopUpQueryValidator;
