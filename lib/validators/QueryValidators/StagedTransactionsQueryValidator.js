'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
Joi.objectId = require('joi-objectid')(Joi);
const { STATUS } = require('../../db/models/enums/staged.transaction.enums');

const getStagedTransactionsSchema = Joi.object().keys({
    skip: Joi.number().default(0).required(),
    limit: Joi.number().default(10).required(),
    regionId: Joi.objectId().required(),
    status: Joi.string().valid(STATUS.PENDING_ACTION, STATUS.RESOLVED, STATUS.DISCARDED).optional(),
    merchantId: Joi.objectId(),
    merchantLocationId: Joi.objectId(),
    subType: Joi.objectId(),
    importJobId: Joi.objectId(),
    createdOn: Joi.date(),
    transactionOn: Joi.date(),
    cardNo: Joi.string(),
    searchKey: Joi.string(),
    failureReason: Joi.string()
});

const exportStagedTransactionsSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    status: Joi.string().valid(STATUS.PENDING_ACTION, STATUS.RESOLVED, STATUS.DISCARDED).optional(),
    merchantId: Joi.objectId(),
    merchantLocationId: Joi.objectId(),
    subType: Joi.objectId(),
    importJobId: Joi.objectId(),
    createdOn: Joi.date(),
    transactionOn: Joi.date(),
    cardNo: Joi.string(),
    searchKey: Joi.string(),
    failureReason: Joi.string(),
    transactionOnFromDate: Joi.date(),
    transactionOnToDate: Joi.date(),
    notificationEmails: Joi.array().items(Joi.string().email()).min(1).required()
});

class StagedTransactionsQueryValidator {

    static isValidGet(obj) {
        let { value, error } = getStagedTransactionsSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidExport(obj) {
        let { value, error } = exportStagedTransactionsSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = StagedTransactionsQueryValidator;
