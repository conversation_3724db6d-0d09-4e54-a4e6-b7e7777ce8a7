'use strict';
const Joi = require('joi');
const CustomHttpError = require('../../CustomHttpError');
const { TYPE } = require('../../db/models/enums/sub.transaction.type.enums');
Joi.objectId = require('joi-objectid')(Joi);

const schema = Joi.object().keys({
    skip: Joi.number().required().default(0),
    limit: Joi.number().required().default(10),
    searchKey: Joi.string(),
    withSystemSubTransactionTypes: Joi.boolean(),
    transactionType: Joi.string().valid(TYPE.COLLECTION, TYPE.ADJUSTMENT, TYPE.REDEMPTION).optional(),
    createdBy: Joi.objectId()
});

class SubTransactionTypeQueryValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

module.exports = SubTransactionTypeQueryValidator;
