'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('../../CustomHttpError');
const { Type, SearchFields } = require('../../db/models/enums/member.enums');
const { SORT_DIRECTION } = require('@shoutout-labs/constants');

const schemaSkipLimitRegion = Joi.object().keys({
    skip: Joi.number().default(0).integer().min(0).description('Number of records to skip'),
    limit: Joi.number().default(50).integer().min(1).description('Number of records to return'),
    regionId: Joi.objectId().required().description('Region ID to filter members'),
    affinityGroupId: Joi.objectId().description('Affinity group ID filter'),
    tierId: Joi.objectId().description('Tier ID filter'),
    sortBy: Joi.string().description('Field to sort by'),
    parentMemberId: Joi.objectId().description('Parent member ID filter'),
    memberIds: Joi.array().items(Joi.objectId()).min(1).description('Array of member IDs'),
    searchKey: Joi.string().description('Search keyword'),
    searchField: Joi.string()
        .valid(...Object.keys(SearchFields))
        .description('Field to search in'),
    loyaltyId: Joi.string().description('Loyalty ID filter'),
    fields: Joi.array().items(Joi.string()).description('Fields to include in the response'),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC).description('Sort direction'),
    type: Joi.string().valid(Type.PRIMARY, Type.SECONDARY).description('Member type filter')
});

const memberFiltrationCountSchema = Joi.object().keys({
    regionId: Joi.objectId().required().description('Region ID to filter members')
});

const memberFiltrationQuerychema = memberFiltrationCountSchema.keys({
    skip: Joi.number().default(0).integer().min(0).description('Number of records to skip'),
    limit: Joi.number().default(50).integer().min(1).description('Number of records to return'),
    sortBy: Joi.string().description('Field to sort by'),
    fields: Joi.array().items(Joi.string()).description('Fields to include in the response'),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC).description('Sort direction'),
});

const memberSchema = Joi.object().keys({
    parentMemberId: Joi.any(),
    merchantLocationId: Joi.any(),
    type: Joi.any(),
    status: Joi.any(),
    description: Joi.any(),
    points: Joi.any(),
    identifications: Joi.any(),
    tierPoints: Joi.any(),
    purchasesCount: Joi.any(),
    purchasesValue: Joi.any(),
    tags: Joi.any(),
    lastSeenOn: Joi.any(),
    lastTransactionOn: Joi.any(),
    lastTransactionLocation: Joi.any(),
    registeredOn: Joi.any(),
    firstName: Joi.any(),
    lastName: Joi.any(),
    preferredName: Joi.any(),
    mobileNumber: Joi.any(),
    additionalPhoneNumbers: Joi.any(),
    companyName: Joi.any(),
    occupation: Joi.any(),
    countryCode: Joi.any(),
    country: Joi.any(),
    email: Joi.any(),
    isValidEmail: Joi.any(),
    isValidMobileNumber: Joi.any(),
    birthDate: Joi.any(),
    gender: Joi.any(),
    residentialAddress: Joi.any(),
    postalAddress: Joi.any(),
    createdBy: Joi.any(),
    registerMethod: Joi.any(),
    affinityGroup: Joi.any(),
    tier: Joi.any(),
    stats: Joi.any(),
    cardReplacementCount: Joi.any(),
    portalMetadata: Joi.any(),
    cardNumber: Joi.any(),
    customAttributes: Joi.any(),
    notificationPreference: Joi.any(),
    memberInsight: Joi.any(),
    pointStats: Joi.any(),
    pointsToExpire: Joi.any(),
}).unknown(true);

const memberFiltrationBodySchema = Joi.object().keys({
    membersFilter: memberSchema.keys({
        'summaryMetrics.transactionCount': Joi.alternatives([Joi.number(), Joi.object()]),
        'summaryMetrics.redemptionCount': Joi.alternatives([Joi.number(), Joi.object()]),
        'summaryMetrics.collectionCount': Joi.alternatives([Joi.number(), Joi.object()]),
        'summaryMetrics.collectionSum': Joi.alternatives([Joi.number(), Joi.object()]),
        'summaryMetrics.redemptionSum': Joi.alternatives([Joi.number(), Joi.object()]),
        'summaryMetrics.totalBillValue': Joi.alternatives([Joi.number(), Joi.object()]),
        'summaryMetrics.date': Joi.object().keys({
            $gt: Joi.date(),
            $gte: Joi.date(),
            $lt: Joi.date(),
            $lte: Joi.date(),
        }).xor('$gt', '$gte').xor('$lt', '$lte')
    }).custom((value, helpers) => {
        const metricFields = [
            'summaryMetrics.transactionCount',
            'summaryMetrics.redemptionCount',
            'summaryMetrics.collectionCount',
            'summaryMetrics.collectionSum',
            'summaryMetrics.redemptionSum',
            'summaryMetrics.totalBillValue'
        ];

        const hasMetrics = metricFields.some(field => value[field] !== undefined);

        if (hasMetrics && !value['summaryMetrics.date']) {
            throw new CustomHttpError(`When filtering with summary metrics 'summaryMetrics.date' is required`);
        }

        return value;
    }).optional().description('Members filter'),
    transactionsFilter: Joi.object().keys({
        transactionOn: Joi.alternatives([Joi.number(), Joi.object()]),
        productId: Joi.string(),
        productName: Joi.string(),
        productCategory: Joi.alternatives([Joi.string(), Joi.object()]),
        quantity: Joi.alternatives([Joi.number(), Joi.object()]),
        amount: Joi.alternatives([Joi.number(), Joi.object()]),
    }).optional().description('Transactions filter'),
});

const schemaSkipLimitRegionSystem = Joi.object().keys({
    skip: Joi.number().default(0).integer().min(0),
    limit: Joi.number().default(50).integer().min(1),
    regionId: Joi.objectId().required(),
    organizationId: Joi.objectId().required(),
    affinityGroupId: Joi.objectId(),
    tierId: Joi.objectId(),
    sortBy: Joi.string(),
    parentMemberId: Joi.objectId(),
    memberIds: Joi.array().items(Joi.objectId()).min(1),
    searchKey: Joi.string(),
    searchField: Joi.string().valid(...Object.keys(SearchFields)),
    loyaltyId: Joi.string(),
    fields: Joi.array().items(Joi.string()),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC),
    type: Joi.string().valid(Type.PRIMARY, Type.SECONDARY)
});

const systemGetMembersSchema = Joi.object().keys({
    organizationId: Joi.objectId().required(),
    skip: Joi.number().default(0).integer().min(0),
    limit: Joi.number().default(50).integer().min(1),
    regionId: Joi.objectId().required(),
    affinityGroupId: Joi.objectId(),
    tierId: Joi.objectId(),
    sortBy: Joi.string(),
    parentMemberId: Joi.objectId(),
    memberIds: Joi.array().items(Joi.objectId()).min(1),
    searchKey: Joi.string(),
    loyaltyId: Joi.string(),
    fields: Joi.array().items(Joi.string()),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC),
    type: Joi.string().valid(Type.PRIMARY, Type.SECONDARY)
});

const membersExportSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    affinityGroupId: Joi.objectId(),
    sortBy: Joi.string(),
    parentMemberId: Joi.objectId(),
    searchKey: Joi.string(),
    fields: Joi.array().min(1).items(Joi.string()).required(),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC),
    type: Joi.string().valid(Type.PRIMARY, Type.SECONDARY)
});

const filterMembersExportSchema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    sortBy: Joi.string(),
    fields: Joi.array().min(1).items(Joi.string()).required(),
    sortDirection: Joi.string().valid(SORT_DIRECTION.ASC, SORT_DIRECTION.DESC),
    notificationEmails: Joi.array().items(Joi.string().email()).min(1).required()
});

const schemaRegionId = Joi.object().keys({
    fields: Joi.array().items(Joi.string())
});

const schemaMemberCreateQuery = Joi.object().keys({
    verifyEmail: Joi.boolean().default(true)
});

const schemaVerificationQuery = Joi.object().keys({
    verificationToken: Joi.string().required()
});

const systemGetByIdSchema = Joi.object().keys({
    organizationId: Joi.objectId().required(),
    fields: Joi.array().items(Joi.string())
});

class UsersQueryValidator {
    static isValid(obj) {
        const { value, error } = schemaSkipLimitRegion.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidSystem(obj) {
        const { value, error } = schemaSkipLimitRegionSystem.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidExport(obj) {
        const { value, error } = membersExportSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidFilterExport(obj) {
        const { value, error } = filterMembersExportSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidMemberCreateQuery(obj) {
        const { value, error } = schemaMemberCreateQuery.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidFields(obj) {
        const { value, error } = schemaRegionId.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidSystemGet(obj) {
        const { value, error } = systemGetByIdSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidSystemGetMembers(obj) {
        const { value, error } = systemGetMembersSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidVerificationToken(obj) {
        const { value, error } = schemaVerificationQuery.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidMemberCountObject(obj) {
        const { value, error } = memberFiltrationCountSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = UsersQueryValidator;

UsersQueryValidator.memberSchema = memberSchema;
UsersQueryValidator.memberFiltrationCountSchema = memberFiltrationCountSchema;
UsersQueryValidator.memberFiltrationQuerychema = memberFiltrationQuerychema;
UsersQueryValidator.memberFiltrationBodySchema = memberFiltrationBodySchema;
UsersQueryValidator.membersSearchQueryParamsSchema = schemaSkipLimitRegion;
