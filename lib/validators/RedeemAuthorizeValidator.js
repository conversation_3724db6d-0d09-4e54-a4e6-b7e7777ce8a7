'use strict';
const Joi = require('joi');
const {AUTH_STATUS} = require('@shoutout-labs/constants');
const CustomHttpError = require('./../CustomHttpError');

const schema = Joi.object().keys({
    _id: Joi.string().required(),
    status: Joi.string().valid(AUTH_STATUS.APPROVED, AUTH_STATUS.REJECTED).required()
});

class RedeemAuthorizeValidator {
    static isValid(obj) {
        let {value, error} = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = RedeemAuthorizeValidator;