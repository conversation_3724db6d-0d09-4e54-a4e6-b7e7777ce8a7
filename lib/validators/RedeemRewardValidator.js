'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('./../CustomHttpError');

const schema = Joi.object().keys({
    memberId: Joi.string(),
    rewardId: Joi.objectId().required(),
    regionId: Joi.objectId().required(),
    merchantId: Joi.objectId(),
    pointsBundleId: Joi.objectId(),
    metadata: Joi.object(),
    merchantLocationId: Joi.objectId()
});

const pointsBundleSchema = Joi.object().keys({
    memberId: Joi.string(),
    rewardId: Joi.objectId().required(),
    regionId: Joi.objectId().required(),
    pointsBundleId: Joi.objectId().required(),
    metadata: Joi.object().required(),
    merchantLocationId: Joi.objectId()
});

const partnerMetaDataSchema = Joi.object().keys({
    partnerRefNumber: Joi.string(),
    partnerRefName: Joi.string(),
    partnerNotes: Joi.string()
});

const metaDataSchema = Joi.object().keys({
    claimLocationId: Joi.objectId().required()
});

const otpSchema = Joi.object().keys({
    redemptionToken: Joi.string().required(),
    otpCode: Joi.string().required()
});

class RedeemRewardValidator {
    static async isValid(obj) {
        try {
            const value = await schema.validateAsync(obj);
            return Promise.resolve(value);
        } catch (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
    }

    static async isValidPointBundle(obj) {
        try {
            const value = await pointsBundleSchema.validateAsync(obj);
            return Promise.resolve(value);
        } catch (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
    }

    static async isValidMetaData(obj) {
        try {
            const value = await metaDataSchema.validateAsync(obj);
            return Promise.resolve(value);
        } catch (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
    }

    static async isValidPartnerMetaData(obj) {
        try {
            const value = await partnerMetaDataSchema.validateAsync(obj);
            return Promise.resolve(value);
        } catch (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
    }

    static isValidOtpRedeem(obj) {
        let {value, error} = otpSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = RedeemRewardValidator;
