'use strict';
let Joi = require('joi');
const JoiTimezone = require('joi-tz');
Joi.objectId = require('joi-objectid')(Joi);
Joi = Joi.extend(JoiTimezone);
Joi = require('joi-cron-expression')(Joi);
const CustomHttpError = require('../CustomHttpError');
const { POINT_EXPIRY_METHOD } = require('../db/models/enums/regional.point.configuration.enums');
const { countries, currencies } = require('@aerapass/country-data');
const { webhookConfigurationSchema } = require('./common');

const schema = Joi.object().keys({
    regionName: Joi.string().required(),
    regionIconUrl: Joi.string().required(),
    defaultCountryISO2Code: Joi.string()
        .valid(...countries.all.map((obj) => obj.alpha2))
        .required(),
    defaultCurrencyCode: Joi.string()
        .valid(...currencies.all.map((obj) => obj.code))
        .required(),
    defaultMerchantId: Joi.objectId(),
    defaultMerchantLocationId: Joi.objectId(),
    pointConfiguration: Joi.object().keys({
        minPointRedemptionAmount: Joi.number().required(),
        maxPointRedemptionAmount: Joi.number().required(),
        minPointsBalanceForRedemption: Joi.number().required(),
        pointExpiryMethod: Joi.string().valid(POINT_EXPIRY_METHOD.ROLLING, POINT_EXPIRY_METHOD.FIXED).required(),
        pointExpiryStartMonth: Joi.number().required(),
        pointExpiryEndMonth: Joi.number().required(),
        pointExpiryPeriod: Joi.number().required(),
        pointExpiryGracePeriod: Joi.number().required(),
        currencyAmountPerPoint: Joi.number().required(),
        regionalPointConversionRates: Joi.array()
            .items(
                Joi.object().keys({
                    destinationRegionId: Joi.objectId().required(),
                    rate: Joi.number().required()
                })
            )
            .required(),
        jobEnabled: Joi.boolean(),
        jobFrequency: Joi.when('pointConfiguration.jobEnabled', {
            is: true,
            then: Joi.string().cron().required(),
            otherwise: Joi.string().cron()
        }),
        pointExpiryCalculationJobFrequency: Joi.when('pointConfiguration.jobEnabled', {
            is: true,
            then: Joi.string().cron().required(),
            otherwise: Joi.string().cron()
        }),
        pointExpiryCalculationSubTransactionTypeIds: Joi.array().items(Joi.objectId())
    }),
    memberConfiguration: Joi.object().keys({
        maxSecondaryAccounts: Joi.number().required().default(2),
        enrolmentForm: Joi.object().unknown(),
        defaultAffinityGroupId: Joi.objectId(),
        affinityGroupExpiryJobEnabled: Joi.boolean().default(false),
        affinityGroupExpiryJobFrequency: Joi.when('memberConfiguration.jobEnabled', {
            is: true,
            then: Joi.string().cron().required(),
            otherwise: Joi.string().cron()
        }),
        segmentsMemberStatsGeneratorJobEnabled: Joi.boolean().default(true),
    }),
    insightCalculations: Joi.object().keys({
        jobEnabled: Joi.boolean().default(false),
        insightCalculationJobFrequency: Joi.when('insightCalculations.insightCalculationJobEnabled', {
            is: true,
            then: Joi.string().cron().required(),
            otherwise: Joi.string().cron()
        })
    }),
    transactionConfigurations: Joi.object().keys({
        analyticsTransactionSyncJobEnabled: Joi.boolean().default(false),
        transactionSyncRangeInDays: Joi.number().default(8)
    }),
    notificationConfiguration: Joi.object().keys({
        emailConfiguration: Joi.object().keys({
            fromAddress: Joi.string().required()
        }),
        smsConfiguration: Joi.object().keys({
            phoneNumber: Joi.string().required()
        }),
        preBirthdayNotifications: Joi.object().keys({
            enabled: Joi.boolean().default(false),
            notifyDaysBefore: Joi.when('notificationConfiguration.preBirthdayNotifications.enabled', {
                is: true,
                then: Joi.number().required(),
                otherwise: Joi.number()
            })
        }),
        birthdayNotifications: Joi.object().keys({
            enabled: Joi.boolean().default(false)
        })
    }),
    providerConfiguration: Joi.object()
        .keys({
            emailProvidersList: Joi.array().required(),
            smsProvidersList: Joi.array().required()
        })
        .required(),
    webhookConfiguration: webhookConfigurationSchema,
    supportInfo: Joi.object()
        .keys({
            phoneNumbers: Joi.array().items(Joi.string()).min(1).required(),
            email: Joi.string().email().required(),
            whatsappNumber: Joi.string().required()
        })
        .required(),
    timeZone: Joi.timezone().required()
});

const updateSchema = Joi.object().keys({
    regionName: Joi.string(),
    regionIconUrl: Joi.string(),
    defaultCountryISO2Code: Joi.string().valid(...countries.all.map((obj) => obj.alpha2)),
    defaultCurrencyCode: Joi.string().valid(...currencies.all.map((obj) => obj.code)),
    defaultMerchantId: Joi.objectId(),
    defaultMerchantLocationId: Joi.objectId(),
    pointConfiguration: Joi.object().keys({
        minPointRedemptionAmount: Joi.number(),
        maxPointRedemptionAmount: Joi.number(),
        minPointsBalanceForRedemption: Joi.number(),
        pointExpiryMethod: Joi.string().valid(POINT_EXPIRY_METHOD.ROLLING, POINT_EXPIRY_METHOD.FIXED),
        pointExpiryStartMonth: Joi.number(),
        pointExpiryEndMonth: Joi.number(),
        pointExpiryPeriod: Joi.number(),
        pointExpiryGracePeriod: Joi.number(),
        currencyAmountPerPoint: Joi.number(),
        regionalPointConversionRates: Joi.array().items(
            Joi.object().keys({
                destinationRegionId: Joi.objectId().required(),
                rate: Joi.number().required()
            })
        ),
        jobFrequency: Joi.string().cron(),
        pointExpiryCalculationJobFrequency: Joi.string().cron(),
        jobEnabled: Joi.boolean(),
        pointExpiryCalculationSubTransactionTypeIds: Joi.array().items(Joi.objectId())
    }),
    memberConfiguration: Joi.object().keys({
        maxSecondaryAccounts: Joi.number().required().default(2),
        enrolmentForm: Joi.object().unknown(),
        defaultAffinityGroupId: Joi.objectId(),
        affinityGroupExpiryJobEnabled: Joi.boolean(),
        affinityGroupExpiryJobFrequency: Joi.string().cron(),
        segmentsMemberStatsGeneratorJobEnabled: Joi.boolean()
    }),
    affinityGroupConfiguration: Joi.object().keys({
        jobKey: Joi.string(),
        jobEnabled: Joi.boolean(),
        jobFrequency: Joi.string().cron()
    }),
    notificationConfiguration: Joi.object().keys({
        emailConfiguration: Joi.object().keys({
            fromAddress: Joi.string().required()
        }),
        smsConfiguration: Joi.object().keys({
            phoneNumber: Joi.string().required()
        }),
        preBirthdayNotifications: Joi.object().keys({
            enabled: Joi.boolean(),
            notifyDaysBefore: Joi.when('notificationConfiguration.preBirthdayNotifications.enabled', {
                is: true,
                then: Joi.number().required(),
                otherwise: Joi.number()
            })
        }),
        birthdayNotifications: Joi.object().keys({
            enabled: Joi.boolean()
        })
    }),
    providerConfiguration: Joi.object().keys({
        emailProvidersList: Joi.array(),
        smsProvidersList: Joi.array()
    }),
    webhookConfiguration: webhookConfigurationSchema,
    insightCalculations: Joi.object().keys({
        jobEnabled: Joi.boolean(),
        insightCalculationJobFrequency: Joi.string().cron()
    }),
    transactionConfigurations: Joi.object().keys({
        analyticsTransactionSyncJobEnabled: Joi.boolean(),
        transactionSyncRangeInDays: Joi.number()
    }),
    supportInfo: Joi.object().keys({
        phoneNumbers: Joi.array().items(Joi.string()).min(1),
        email: Joi.string().email(),
        whatsappNumber: Joi.string()
    }),
    timeZone: Joi.timezone()
});

class RegionsValidator {
    static isValid(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

    static isValidUpdate(obj) {
        const { value, error } = updateSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }
}

module.exports = RegionsValidator;
