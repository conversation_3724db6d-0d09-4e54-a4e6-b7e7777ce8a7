'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('../CustomHttpError');

const schema = Joi.object().keys({
    name: Joi.string().required(),
    regionId: Joi.objectId().required(),
    iconUrl: Joi.string()
});

const updateSchema = Joi.object().keys({
    name: Joi.string().required(),
    iconUrl: Joi.string()
});

class SegmentCategoryValidator {
    static isValid(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

    static isValidUpdate(obj) {
        const { value, error } = updateSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }
}

module.exports = SegmentCategoryValidator;
