'use strict';
let Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('../CustomHttpError');
const { productItemSchema } = require('./common');

let updateSchema = Joi.object().keys({
    merchantLocationId: Joi.objectId(),
    memberId: Joi.objectId(),
    transactionOn: Joi.date(),
    cardNo: Joi.string(),
    notes: Joi.string(),
    transactionAmount: Joi.number(),
    productItems: Joi.array().items(productItemSchema),
    invoiceData: Joi.object().keys({
        invoiceId: Joi.string(),
        invoiceDate: Joi.date(),
        invoiceAmountWithTax: Joi.number(),
        invoiceAmountWithoutTax: Joi.number(),
        discountAmount: Joi.number()
    })
});

class StagedTransactionsValidator {
    static isValidUpdate(obj) {
        const { value, error } = updateSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }
}

module.exports = StagedTransactionsValidator;
