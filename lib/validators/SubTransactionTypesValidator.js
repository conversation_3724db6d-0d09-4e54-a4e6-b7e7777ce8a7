'use strict';
let Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('./../CustomHttpError');
const { TYPE, OPERATION_TYPE } = require('../db/models/enums/sub.transaction.type.enums');

let schema = Joi.object().keys({
    transactionType: Joi.string().valid(TYPE.COLLECTION, TYPE.ADJUSTMENT, TYPE.REDEMPTION).required(),
    operationType: Joi.string().valid(OPERATION_TYPE.ADD,OPERATION_TYPE.SUBTRACT).required(),
    isVisibleToUser: Joi.boolean().default(true),
    name: Joi.string().required(),
    description: Joi.string(),
    referenceId: Joi.string().required(),
});

let updateSchema = Joi.object().keys({
    name: Joi.string(),
    description: Joi.string(),
    referenceId: Joi.string(),
    isVisibleToUser: Joi.boolean(),
});

class SubTransactionTypesValidator {

    static isValid(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

    static isValidUpdate(obj) {
        const { value, error } = updateSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, 400));
        }
        return Promise.resolve(value);
    }

}

module.exports = SubTransactionTypesValidator;
