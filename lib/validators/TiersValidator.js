'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('./../CustomHttpError');
const { STATUS } = require('../db/models/enums/tier.enums');

const schema = Joi.object().keys({
    regionId: Joi.objectId().required(),
    tiers: Joi.array().items(Joi.object().keys({
        name: Joi.string().required(),
        benefits: Joi.array().items().required(),
        imageUrl: Joi.string().required(),
        points: Joi.number().default(0),
        orderCount: Joi.number().default(0)
    }))
});

const updateSchema = Joi.object().keys({
    regionId: Joi.string().required(),
    tiers: Joi.array().items(Joi.object().keys({
        _id: Joi.objectId().optional(),
        name: Joi.string().required(),
        benefits: Joi.array().items().required(),
        imageUrl: Joi.string().required(),
        points: Joi.number().default(0),
        orderCount: Joi.number().default(0)
    }))
});

const tierSchema = Joi.object({
    organizationId: Joi.string().required().description('MongoDB ObjectId of the organization'),
    regionId: Joi.string().required().description('MongoDB ObjectId of the region'),
    name: Joi.string().required().description('Name of the tier'),
    imageUrl: Joi.string().uri().required().description('Image URL of the tier'),
    benefits: Joi.array().items(Joi.string().required()).required().description('List of benefits'),
    orderCount: Joi.number().default(0).description('Total number of orders'),
    points: Joi.number().default(0).description('Points associated with the tier'),
    membersCount: Joi.number().default(0).required().description('Count of members in this tier'),
    createdBy: Joi.string().required().description('MongoDB ObjectId of the creator'),
    updatedBy: Joi.string().optional().description('MongoDB ObjectId of the last updater'),
    status: Joi.string().valid(...Object.values(STATUS)).default(STATUS.ENABLED).required().description('Status of the tier'),
    createdOn: Joi.date().optional().description('Timestamp when the tier was created'),
    updatedOn: Joi.date().optional().description('Timestamp when the tier was last updated')
});

const tierCreationResponseSchema = Joi.array().items(tierSchema);

class TiersValidator {

    static isValid(obj) {
        let {value, error} = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static isValidUpdate(obj) {
        let {value, error} = updateSchema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

}

TiersValidator.createTiersSchema = schema;
TiersValidator.tiersUpdateSchema = updateSchema;
TiersValidator.tierCreationResponseSchema = tierCreationResponseSchema;
TiersValidator.tierUpdateResponseSchema = tierCreationResponseSchema;

module.exports = TiersValidator;
