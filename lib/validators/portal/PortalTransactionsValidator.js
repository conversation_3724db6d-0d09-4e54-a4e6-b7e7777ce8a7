'use strict';
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);
const CustomHttpError = require('../../CustomHttpError');
const { TYPE } = require('../../db/models/enums/transaction.enums');

const schema = Joi.object().keys({
    skip: Joi.number().default(0).required(),
    limit: Joi.number().default(10).required(),
    transactionType: Joi.string().valid(TYPE.COLLECTION,TYPE.REDEMPTION,TYPE.ADJUSTMENT).optional(),
    transactionOnFromDate: Joi.date(),
    transactionOnToDate: Joi.date()
});

class PortalTransactionsValidator {

    static isValidGet(obj) {
        const { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = PortalTransactionsValidator;
