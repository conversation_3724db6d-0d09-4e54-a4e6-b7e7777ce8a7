'use strict';
const Joi = require('joi');
const CustomHttpError = require('./../../CustomHttpError');

const schema = Joi.object().keys({
    loyaltyId: Joi.string().required(),
    mobileNumber: Joi.string().required(),
    password: Joi.string()
});

const schemaCreate = Joi.object().keys({
    token: Joi.string().required(),
    otpCode: Joi.string().required()
});

const schemaPasswordResetRequest = Joi.object().keys({
    username: Joi.string().required()
});

const schemaPasswordReset = Joi.object().keys({
    token: Joi.string().required(),
    otpCode: Joi.string().required(),
    password: Joi.string().required()
});

const schemaLoginRequest = Joi.object().keys({
    mobileNumber: Joi.string().required()
});

const schemaLoginValidate = Joi.object().keys({
    token: Joi.string().required(),
    otpCode: Joi.string().required()
});

class PortalUserValidator {
    static createAccountRequestValidation(obj) {
        let { value, error } = schema.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static createAccountValidation(obj) {
        let { value, error } = schemaCreate.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static passwordResetRequestValidation(obj) {
        let { value, error } = schemaPasswordResetRequest.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static passwordResetValidation(obj) {
        let { value, error } = schemaPasswordReset.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static loginRequestValidation(obj) {
        let { value, error } = schemaLoginRequest.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }

    static loginValidateValidation(obj) {
        let { value, error } = schemaLoginValidate.validate(obj);
        if (error) {
            return Promise.reject(new CustomHttpError(error.message, '400'));
        }
        return Promise.resolve(value);
    }
}

module.exports = PortalUserValidator;
