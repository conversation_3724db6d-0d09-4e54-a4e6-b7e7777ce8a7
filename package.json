{"name": "shoutout-loyalty-customer-portal", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build:beta": "cp .env.beta.local .env.local && tsc && vite build", "build:production": "cp .env.production.local .env.local && tsc && vite build", "build:production:celeste": "cp .env.production.local .env.local && npm run prepare-for-build:celeste && tsc && vite build", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare-for-build:celeste": "echo 'Copying Celeste Privacy Policy...' && cp src/markdown/customer_templates/Celeste/privacy_policy.md src/markdown/privacy_policy.md && echo 'Copied Celeste Privacy Policy to markdown.' && echo 'Copying Celeste T&C...' && cp src/markdown/customer_templates/Celeste/terms_and_conditions.md src/markdown/terms_and_conditions.md && echo 'Copied Celeste T&C to markdown.'", "celeste-convert-docx-to-raw-md:privacy": "pandoc -f docx -t commonmark src/markdown/customer_templates/Celeste/'Privacy Policy - Loyalty Program - General.docx' -o src/markdown/customer_templates/Celeste/RAW_privacy_policy.md", "celeste-convert-docx-to-raw-md:terms": "pandoc -f docx -t commonmark src/markdown/customer_templates/Celeste/'Terms and Conditions - Loyalty Program - General.docx' -o src/markdown/customer_templates/Celeste/RAW_terms_and_conditions.md"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.8.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "date-fns": "^4.1.0", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^8.0.4", "intl-tel-input": "^25.3.0", "keycloak-js": "^22.0.5", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-barcode": "^1.5.3", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-i18next": "^13.5.0", "react-markdown": "^10.0.0", "react-router-dom": "^6.20.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^4.4.6"}, "devDependencies": {"@types/node": "^20.17.23", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}