{"name": "linkedin-profile-scraper-api", "version": "1.0.0", "main": "dist/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "watch": "tsc --watch", "postinstall": "npm run build", "login": "ts-node login.ts", "check-login": "ts-node check_linkedin_login.ts", "screenshot": "ts-node linkedin_profile_screenshot.ts"}, "keywords": ["linkedin", "scraper", "api", "azure"], "author": "", "license": "ISC", "description": "API for LinkedIn profile page screenshots with Azure Blob Storage integration", "dependencies": {"express": "^4.18.2", "@azure/storage-blob": "^12.14.0", "playwright": "^1.53.1", "dotenv": "^16.0.3", "body-parser": "^1.20.2"}, "devDependencies": {"@playwright/test": "^1.53.1", "@types/body-parser": "^1.19.2", "@types/express": "^4.17.17", "@types/node": "^18.15.11", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}