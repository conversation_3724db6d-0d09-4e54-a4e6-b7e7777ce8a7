{"name": "shoutout-loyalty-integration", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "dev-so": "shopify app dev -c so-loyalty", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "prisma generate && prisma db push", "app:deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma db push", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite", "db:test": "npx prisma db push --preview-feature"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@aws-sdk/client-sqs": "^3.679.0", "@prisma/client": "^5.20.0", "@remix-run/dev": "^2.7.1", "@remix-run/node": "^2.15.2", "@remix-run/react": "^2.7.1", "@remix-run/serve": "^2.7.1", "@shopify/app-bridge-react": "^4.1.2", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.0.2", "@shopify/shopify-app-session-storage-mongodb": "^4.0.10", "@shopify/shopify-app-session-storage-prisma": "^5.2.1", "aws-sdk": "^2.1691.0", "isbot": "^5.1.0", "joi": "^17.13.3", "jwt-decode": "^4.0.0", "node-cache": "^5.1.2", "pino": "^9.4.0", "prisma": "^5.22.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sqs-consumer": "^11.1.0", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@remix-run/eslint-config": "^2.7.1", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^8.40.0", "@types/node": "^22.2.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "retry": "^0.13.1", "typescript": "^5.2.2", "vite": "^5.1.3"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {}, "overrides": {}, "author": "HP"}