{"name": "shoutout-enterprice-loyalty-dashboard", "version": "1.1.16", "private": true, "dependencies": {"@datadog/browser-rum": "^3.8.0", "@iconscout/react-unicons": "^1.1.6", "@react-keycloak/web": "^3.4.0", "@shoutout-labs/react-awesome-query-builder-shoutout": "^5.0.24", "@shoutout-labs/reactbootstrap-wizard-rb": "^1.0.17", "@shoutout-labs/shoutout-message-editor-enterprise": "^3.3.19", "@shoutout-labs/shoutout-themes-enterprise": "^2.2.9", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.2", "@testing-library/user-event": "^12.2.2", "country-state-city": "^3.0.1", "cronstrue": "^2.28.0", "date-fns": "2.14.0", "dompurify": "^3.0.1", "draft-js": "^0.11.7", "echarts": "^5.3.2", "echarts-for-react": "^3.0.2", "immutability-helper": "^3.1.1", "immutable": "^3.8.2", "jwt-decode": "^3.0.0-beta.2", "keycloak-js": "^11.0.3", "lodash": "^4.17.20", "moment": "^2.27.0", "mustache": "^4.0.1", "numeral": "^2.0.6", "prop-types": "15.x", "qs": "^6.7.0", "react": "^16.14.0", "react-avatar": "^3.10.0", "react-avatar-editor": "^12.0.0", "react-bootstrap": "^1.4.0", "react-bootstrap-table-next": "^4.0.3", "react-bootstrap-table2-overlay": "^2.0.0", "react-bootstrap-table2-paginator": "^2.1.2", "react-bootstrap-table2-toolkit": "^2.1.3", "react-bootstrap-typeahead": "^5.2.0", "react-copy-to-clipboard": "^5.0.4", "react-country-flag": "^3.0.2", "react-csv": "^2.0.3", "react-date-range": "1.4.0", "react-dom": "^16.14.0", "react-dropzone": "10.x", "react-event-timeline": "^1.6.3", "react-heatmap-grid": "^0.8.2", "react-infinite-scroll-component": "^6.1.0", "react-intl-tel-input": "8.x", "react-jsonschema-form": "^1.8.1", "react-notify-toast": "^0.5.1", "react-router-dom": "^5.2.0", "react-scripts": "^4.0.3", "react-toastify": "^8.0.2", "react-virtualized": "^9.22.2", "react-window": "^1.8.10", "recharts": "^2.1.8", "uuid": "^9.0.1", "web-vitals": "^1.0.1"}, "scripts": {"preinstall": "npx npm-force-resolutions", "start": "set PORT=8080 && set \"REACT_APP_BUILD_VARIANT=dev\" && npm i && react-scripts start", "start:with-openssl-legacy": "set PORT=8080 && set \"REACT_APP_BUILD_VARIANT=dev\" && npm i && react-scripts --openssl-legacy-provider start", "start:dilmah": "set PORT=8080 && set \"REACT_APP_BUILD_VARIANT=dilmah\" && react-scripts start", "build:staging": "set \"REACT_APP_BUILD_VARIANT=dev\" && react-scripts build", "build:staging-with-openssl-legacy": "set \"REACT_APP_BUILD_VARIANT=dev\" && react-scripts --openssl-legacy-provider build", "build": "react-scripts --max_old_space_size=8192 build", "build:with-openssl-legacy": "react-scripts --max_old_space_size=8192 --openssl-legacy-provider build", "build:staging:github": "CI=false && react-scripts --max_old_space_size=8192 build", "build:dilmah": "set \"REACT_APP_BUILD_VARIANT=dilmah\" && react-scripts --max_old_space_size=8192 build", "test": "react-scripts test --verbose false --coverage", "eject": "react-scripts eject", "deploy:staging": "firebase deploy --only hosting:staging", "deploy:dev": "firebase deploy --only hosting:dev", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/dom": "^7.31.2", "husky": "^7.0.0", "jira-prepare-commit-msg": "^1.6.2", "npm-install-peers": "^1.2.2", "puppeteer": "^10.2.0", "react-error-overlay": "6.0.9", "react-test-renderer": "^17.0.2", "sass": "^1.38.2", "sass-loader": "^10.2.0"}, "resolutions": {"react-error-overlay": "6.0.9"}, "jira-prepare-commit-msg": {"messagePattern": "[$J] $M", "jiraTicketPattern": "([A-Z]+-\\d+)", "commentChar": "#", "isConventionalCommit": false, "allowEmptyCommitMessage": false, "gitRoot": ""}, "engines": {"npm": ">=8.0.0 <9.0.0", "node": ">=16.0.0 <17.0.0"}}