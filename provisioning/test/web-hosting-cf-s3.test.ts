import { expect as expectCDK, matchTemplate, MatchStyle } from '@aws-cdk/assert';
import * as cdk from '@aws-cdk/core';
import * as WebHostingCfS3 from '../lib/web-hosting-cf-s3-stack';

test('Empty Stack', () => {
  const app = new cdk.App();

  const ENV = { account: '************', region: 'us-east-2' };

  const DIST_NAME = '';
  const BUCKET_NAME = '';
  const CERTIFICATE_ARN = '';
  const DOMAIN_NAMES = [''];
  // WHEN
  const stack = new WebHostingCfS3.WebHostingCfS3Stack(app, 'MyTestStack',BUCKET_NAME,CERTIFICATE_ARN,DOMAIN_NAMES,DIST_NAME);
  // THEN
  expectCDK(stack).to(matchTemplate({
    "Resources": {}
  }, MatchStyle.EXACT))
});
