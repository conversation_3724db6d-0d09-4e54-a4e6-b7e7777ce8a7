{"name": "cx_forge_customer_portal_keycloak_theme", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare-theme-configs:celeste": "cp ./../customer_configs/celeste/customer.configs.json ./theme.configs.json && cp ./../customer_configs/celeste/customer-logo-favicon.png ./src/assets/customer-logo-favicon.png && cp ./../customer_configs/celeste/customer-logo.png ./src/assets/customer-logo.png", "prepare-theme-configs:pns": "cp ./../customer_configs/pns/customer.configs.json ./theme.configs.json && cp ./../customer_configs/pns/customer-logo-favicon.png ./src/assets/customer-logo-favicon.png && cp ./../customer_configs/pns/customer-logo.png ./src/assets/customer-logo.png", "build-keycloak-theme": "npm run build && keycloakify build", "build-docker-image": "docker compose build && docker compose up", "build-keycloak-and-deploy:celeste:local": "npm run prepare-theme-configs:celeste && npm i && npm run build-keycloak-theme && npm run build-docker-image", "build-keycloak-and-deploy:pns:local": "npm run prepare-theme-configs:pns && npm i && npm run build-keycloak-theme && npm run build-docker-image"}, "dependencies": {"intl-tel-input": "^25.3.0", "keycloakify": "^11.8.14", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tss-react": "^4.9.15"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}