import { FC } from "react";
import type { I18n } from "../keycloak-theme/login/i18n";
import { House } from "lucide-react";
import { useStyles } from "../makeStyle";
import logoImage from "../assets/customer-logo.png";
import { LANDING_PAGE, CUSTOMER_NAME } from "../../constants";

const navigator: Navigator = window.navigator;
const userAgent: string = navigator.userAgent.toLowerCase();
// const standalone: boolean = navigator?.standalone; // * Throws a TS error.
const standalone: boolean = "standalone" in navigator;

const isIos: boolean = /ip(ad|hone|od)/.test(userAgent) && navigator.maxTouchPoints > 1;
const isAndroid: boolean = /android/.test(userAgent);
const isSafari: boolean = /safari/.test(userAgent);
const isWebview: boolean =
  (isAndroid && /; wv\)/.test(userAgent)) ||
  (isIos && !standalone && !isSafari);

interface CommonHeaderProps {
  i18n: I18n;
  disabled?: boolean;
  showMsg?: boolean;
}

const CommonHeader: FC<CommonHeaderProps> = ({ i18n, disabled = false, showMsg = true }) => {
  const { msg } = i18n;
  const { css, cx, theme } = useStyles();

  return (
    <div className="header">
      {!isWebview && (
        <a
          className={cx(disabled ? "no-pointer-event" : "", css(theme.btnLink))}
          href={LANDING_PAGE}
        >
          <House />
        </a>
      )}
      <div className="logo-image">
        <img src={logoImage} alt={CUSTOMER_NAME || "CUSTOMER LOGO"} />
      </div>
      {showMsg && msg("doLogIn")}
    </div>
  );
};

export default CommonHeader;