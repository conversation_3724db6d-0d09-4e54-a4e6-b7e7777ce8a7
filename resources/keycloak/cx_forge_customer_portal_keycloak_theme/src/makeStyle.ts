import { createMakeStyles } from "tss-react";
import { THEME } from "../constants";

const defaults = {
  background: THEME.BACKGROUND,
  foreground: THEME.FOREGROUND,
  fontSize: THEME.FONT_SIZE,
  fontFamily: THEME.FONT_FAMILY,
  borderRadius: THEME.BORDER_RADIUS,
  btnBorderRadius: THEME.BTN_BORDER_RADIUS,
  primary: THEME.PRIMARY,
  primaryForeground: THEME.PRIMARY_FOREGROUND,
  secondary: THEME.SECONDARY,
  secondaryForeground: THEME.SECONDARY_FOREGROUND,
  tertiary: THEME.TERTIARY,
  tertiaryForeground: THEME.TERTIARY_FOREGROUND,
};

const theme = {
  defaultBackground: defaults.background,
  defaultFontSize: defaults.fontSize,
  foreground: defaults.foreground,
  defaultFontFamily: defaults.fontFamily,
  defaultCardBorderRadius: "21px",
  defaultBorderRadius: defaults.borderRadius,
  defaultInputBorder: `1px solid ${defaults.secondary}`,
  primary: defaults.primary,
  primaryForeground: defaults.primaryForeground,
  secondary: defaults.secondary,
  secondaryForeground: defaults.secondaryForeground,
  tertiary: defaults.tertiary,
  tertiaryForeground: defaults.tertiaryForeground,
  btnPrimary: {
    fontSize: defaults.fontSize,
    color: defaults.secondary,
    background: defaults.primary,
    borderRadius: defaults.btnBorderRadius,
    "&:hover": {
      background: defaults.primaryForeground,
    },
    "&:focus": {
      background: defaults.primaryForeground,
    },
  },
  btnLink: {
    fontSize: defaults.fontSize,
    color: defaults.primary,
    background: "transparent",
    border: "none",
    "&:hover": {
      color: defaults.primaryForeground,
    },
    "&:focus": {
      color: defaults.primaryForeground,
    },
    "&:active": {
      color: defaults.primaryForeground,
    },
  },
};

function useTheme() {
  return theme;
}

export const {
  makeStyles,
  useStyles, // ? <- To use when you need css or cx but don't have custom classes.
} = createMakeStyles({ useTheme });
