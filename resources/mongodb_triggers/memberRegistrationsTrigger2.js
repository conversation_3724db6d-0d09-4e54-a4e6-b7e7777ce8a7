const getRegisteredOnDate = ({ registeredOn, createdOn  }) => {
    try {
        if (!registeredOn && !createdOn) throw new Error('`registeredOn` and `createdOn` dates not found!');

        if (registeredOn) {
            return new Date(registeredOn);
        } else if (createdOn) return new Date(createdOn);
    } catch (error) {
        console.error('Failed to get registered on date', error);
        throw error;
    }
};

const getMerchantLocationId = async ({ locationId, serviceName, database, organizationId, regionId }) => {
    if (!locationId) {
        try {
            const regionCollection = context.services.get(serviceName).db(database).collection('regions');

            const membersRegionData = await regionCollection.findOne(
                { organizationId, _id: regionId },
                { defaultMerchantLocationId: 1 }
            );

            if (membersRegionData?.defaultMerchantLocationId) {
                console.log(
                    `No location id found. Default merchant location id - '${membersRegionData.defaultMerchantLocationId}' will be assigned.`
                );
                return membersRegionData.defaultMerchantLocationId;
            }

            return null;
        } catch (error) {
            console.error('Failed to get merchant location id', error);
            return null;
        }
    }

    return locationId;
};

exports = async function (changeEvent) {
    const serviceName = 'Development';
    const database = 'loyalty_service';

    try {
        if (changeEvent.operationType === 'insert') {
            const membersAnalyticsRegistrationsCollection = context.services
                .get(serviceName)
                .db(database)
                .collection('members_analytics_registrations');

            const {
                _id,
                organizationId,
                regionId,
                createdOn,
                registeredOn,
                merchantLocationId: locationId,
                type,
                registerMethod,
                birthDate,
                gender 
            } = changeEvent.fullDocument;

            if (!organizationId) throw new Error('`organizationId` not found!');

            if (!regionId) throw new Error('`regionId` not found!');

            const registeredOnDate = getRegisteredOnDate({
                registeredOn,
                createdOn
            });

            const merchantLocationId = await getMerchantLocationId({
                locationId,
                serviceName,
                database,
                organizationId,
                regionId
            });

            const key = `${organizationId}_${regionId}_${_id}`;
            const analyticsDocument = {
                registeredOn: registeredOnDate,
                organizationId,
                regionId,
                key,
                merchantLocationId,
                type: type || null,
                gender: gender || null,
                birthDate: birthDate || null,
                registerMethod: registerMethod || null
            };

            await membersAnalyticsRegistrationsCollection.insertOne(analyticsDocument);

            console.log('Successfully inserted document to `members_analytics_registrations` collection.');
        }
    } catch (err) {
        console.error('Failed to insert document: ', err.message);
    }
};
