[{"$project": {"_id": 0, "distributionJobId": {"$ifNull": ["$distributionJobId", null]}, "type": 1, "status": 1, "regionId": 1, "organizationId": 1}}, {"$lookup": {"from": "card_distribution_jobs", "let": {"distributionJobId": "$distributionJobId"}, "pipeline": [{"$match": {"$expr": {"$eq": ["$$distributionJobId", "$_id"]}}}, {"$project": {"merchantLocationId": 1, "merchantId": 1, "_id": 0}}], "as": "batchJob"}}, {"$unwind": {"path": "$batchJob", "includeArrayIndex": "0", "preserveNullAndEmptyArrays": true}}, {"$addFields": {"readyCards": {"$cond": [{"$and": [{"$eq": ["$status", "READY"]}, {"$eq": ["$distributionJobId", null]}]}, 1, 0]}, "distributedCards": {"$cond": [{"$and": [{"$eq": ["$status", "READY"]}, {"$ne": ["$distributionJobId", null]}]}, 1, 0]}, "activeCards": {"$cond": [{"$eq": ["$status", "ACTIVE"]}, 1, 0]}, "assignedCards": {"$cond": [{"$eq": ["$status", "ASSIGNED"]}, 1, 0]}, "suspendedCards": {"$cond": [{"$eq": ["$status", "SUSPENDED"]}, 1, 0]}, "deactivatedCards": {"$cond": [{"$eq": ["$status", "DEACTIVATED"]}, 1, 0]}}}, {"$group": {"_id": {"type": "$type", "merchantId": "$batchJob.merchantId", "merchantLocation": "$batchJob.merchantLocationId", "regionId": "$regionId", "organizationId": "$organizationId"}, "totalCount": {"$sum": 1}, "readyCards": {"$sum": "$readyCards"}, "distributedCards": {"$sum": "$distributedCards"}, "activeCount": {"$sum": "$activeCards"}, "assignedCount": {"$sum": "$assignedCards"}, "suspendedCount": {"$sum": "$suspendedCards"}, "deactivatedCount": {"$sum": "$deactivatedCards"}}}, {"$lookup": {"from": "merchants", "let": {"merchantId": "$_id.merchantId"}, "pipeline": [{"$match": {"$expr": {"$eq": ["$$merchantId", "$_id"]}}}, {"$project": {"merchantName": 1, "_id": 1}}], "as": "merchant"}}, {"$unwind": {"path": "$merchant", "includeArrayIndex": "0", "preserveNullAndEmptyArrays": true}}, {"$lookup": {"from": "merchant_locations", "let": {"merchantLocationId": "$_id.merchantLocation"}, "pipeline": [{"$match": {"$expr": {"$eq": ["$$merchantLocationId", "$_id"]}}}, {"$project": {"locationName": 1, "_id": 1}}], "as": "merchantLocations"}}, {"$set": {"merchantLocationName": {"$arrayElemAt": ["$merchantLocations.locationName", 0]}}}, {"$replaceRoot": {"newRoot": {"totalCount": "$totalCount", "readyCardsCount": "$readyCards", "distributedCardsCount": "$distributedCards", "activeCardsCount": "$activeCount", "assignedCardsCount": "$assignedCount", "suspendedCardsCount": "$suspendedCount", "deactivatedCardsCount": "$deactivatedCount", "type": "$_id.type", "merchantId": "$merchant._id", "merchantName": "$merchant.merchantName", "merchantLocationId": "$_id.merchantLocation", "merchantLocationName": "$merchantLocationName", "regionId": "$_id.regionId", "organizationId": "$_id.organizationId"}}}, {"$merge": {"into": "cards_stock"}}]