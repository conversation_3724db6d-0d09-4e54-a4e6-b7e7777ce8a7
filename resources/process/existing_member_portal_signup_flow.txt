title Member Portal Signup - Existing Loyalty Member

group Validate card
group (A) /accounts/validatecard [POST]
Loyalty Service<-Member Portal: Card number
note right of Loyalty Service:Validate card number
Member Portal<-Loyalty Service:Card status with member info(masked)
note over Member Portal,Loyalty Service:**200 - Valid Card, Member available**\n204 - Valid Card, Member not available\n400 - Invalid card
end
end

group Get account
group (B-1) /accounts/verifytokenexisting [GET]
Member Portal->Loyalty Service: Card number
note right of Loyalty Service:Validate card number\nSend OTP to email and mobile number\nGenerate **account verify token**
Member Portal<--Loyalty Service:Email and SMS with account verify code
Member Portal<-Loyalty Service: Account verify token
end

group (C) /accounts/verify [POST]
Member Portal->Loyalty Service:Account verify token & OTP code
note right of Loyalty Service:Verify OTP code\nLoad member details\nGenerate **account create token**
Member Portal<-Loyalty Service:Account details & account create token 
end
end

group Create account
group (D) /accounts [POST]
Member Portal->Loyalty Service:Account create token & password
note right of Loyalty Service:Create keycloak user\nUpdate member profile with keycloak info (portalMetadata)
Member Portal<-Loyalty Service:Account create status
end
end