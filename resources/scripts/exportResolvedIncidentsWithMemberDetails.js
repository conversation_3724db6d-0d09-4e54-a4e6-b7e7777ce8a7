'use strict';

const fs = require('fs');
const mongoose = require('mongoose');
const path = require('path');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const Location = require('../../lib/db/models/merchant.location.model');
const Member = require('../../lib/db/models/member.model');
const Transaction = require('../../lib/db/models/transaction.model');

const fileName = '.json';
const fileNameToExportTo = '.csv';

// const mongoUrl = ''; // * Staging Url.
// const organizationId = ''; // * Staging Org.
// const merchantId = ''; // * Staging Org.

// ! PROD CONFIGS.
const mongoUrl = '';
const organizationId = '';
const merchantId = '';
// ! PROD CONFIGS.

const incidentDataToExport = [];
let primaryConn;
let processedCount = 0;

const readJSONDataFromFile = (file) => {
    try {
        return JSON.parse(fs.readFileSync(file, 'utf8'));
    } catch (err) {
        console.error(err);
        throw err;
    }
};

const writeToCSVFile = async (filePath, data = []) => {
    console.log(`Writing to report '${filePath}'...`);
    const csvWriter = createCsvWriter({
        path: filePath,
        header: [
            { id: 'incidentId', title: 'Incident Id' },
            { id: 'mobileNumber', title: 'Mobile Number' },
            { id: 'incidentCreatedOn', title: 'Incident Reported On' },
            { id: 'transactionCount', title: 'Incident Transaction Count' },
            { id: 'outlet', title: 'Outlet' },
            { id: 'incidentResolvedOn', title: 'Incident Resolved On' },
            { id: 'reasonToResolve', title: 'Reason To Resolve' },
            { id: 'incidentResolvedByUserId', title: 'Incident Resolved By User Id' }
        ]
    });

    await csvWriter.writeRecords(data);
    console.log(`Finished writing to report '${filePath}'.`);
    return Promise.resolve();
};

const initializePrimary = async () => {
    try {
        primaryConn = mongoose.connection;
        primaryConn.on('error', () => {
            console.error('mongodb primary connection error');
        });
        primaryConn.once('open', () => {
            console.info('mongodb primary connected');
        });
        return mongoose.connect(mongoUrl, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            maxPoolSize: 10
        });
    } catch (err) {
        console.error(err);
        return Promise.reject(err);
    }
};

const closePrimary = async () => {
    try {
        console.log('Closing db connection...');
        await primaryConn.close();
        console.log('Closed db connection.');
        return;
    } catch (err) {
        console.error(err);
        return Promise.reject(err);
    }
};

const loadAllActiveOfMerchantLocations = async () => {
    try {
        console.log('Loading all ACTIVE locations...');
        return Location.find({
            organizationId: mongoose.Types.ObjectId(organizationId),
            merchantId: mongoose.Types.ObjectId(merchantId),
            status: 'ACTIVE'
        });
    } catch (err) {
        console.error('Failed to load all merchant locations', err);
        return Promise.reject(err);
    }
};

const fetchMemberData = async (memberId) => {
    try {
        return Member.findOne(
            { organizationId: mongoose.Types.ObjectId(organizationId), _id: mongoose.Types.ObjectId(memberId) },
            ['mobileNumber']
        );
    } catch (err) {
        console.error('Failed to fetch member data', err);
        return [];
    }
};

const fetchTransactionData = async (transactionIds = []) => {
    try {
        return Transaction.find({
            organizationId: mongoose.Types.ObjectId(organizationId),
            _id: { $in: transactionIds.map((tI) => mongoose.Types.ObjectId(tI)) }
        });
    } catch (err) {
        console.error('Failed to fetch transaction data', err);
        return [];
    }
};

(async () => {
    try {
        const filePath = `${path.join(__dirname, 'data')}/${fileName}`;
        const fileData = readJSONDataFromFile(filePath);
        console.log('fileData', fileData.length);

        await initializePrimary();

        const merchantLocations = await loadAllActiveOfMerchantLocations();
        const merchantLocationMap = merchantLocations.reduce((result, location) => {
            result[location._id] = location;
            return result;
        }, {});

        console.log('Merchant location map', merchantLocationMap, merchantLocations.length);

        for (const incident of fileData) {
            const memberData = await fetchMemberData(incident?.memberId);
            const mobileNumber = memberData?.mobileNumber;
            console.log('Mobile number and incident count', mobileNumber, incident?.defectsCount);

            const transactionData = await fetchTransactionData(incident?.defectIds || []);

            const outlet =
                [
                    ...new Set(
                        transactionData.map((tD) => {
                            const location = merchantLocationMap[tD.merchantLocationId] || {};
                            return `${location?.code || '~unknown'}-${location?.locationName || '~unknown'}`;
                        })
                    )
                ][0] || '~unknown';

            const modifiedIncidentData = {
                incidentId: incident?._id || '~unknown',
                mobileNumber: memberData?.mobileNumber || null,
                incidentCreatedOn: incident?.createdOn ? new Date(incident?.createdOn).toString() : null,
                transactionCount: incident?.defectsCount || 0,
                outlet,
                incidentResolvedOn: incident?.updatedOn ? new Date(incident?.updatedOn).toString() : null,
                reasonToResolve: incident?.note || '~unknown',
                incidentResolvedByUserId: incident?.updatedBy || '~unknown'
            };

            incidentDataToExport.push(modifiedIncidentData);
            processedCount++;
            console.log(`Current processed ${processedCount} of ${fileData.length}.\n`);
        }
        console.log(`Total processed ${processedCount} of ${fileData.length}.`);

        console.log('Incident data to export', incidentDataToExport, incidentDataToExport.length);
        const filePathToWrite = `${path.join(__dirname, 'data/reports')}/${fileNameToExportTo}`;
        await writeToCSVFile(filePathToWrite, incidentDataToExport);

        console.log('Successfully processed and wirtten to file. Terminating app...');

        await closePrimary();
    } catch (err) {
        console.error(err);
        await closePrimary();
        process.exit(1);
    }
})();
