'use strict';
const express = require('express');
const router = express.Router();
const AffinityGroupsHandler = require('../lib/handlers/AffinityGroupsHandler');
const AffinityGroupQueryValidator = require('../lib/validators/QueryValidators/AffinityGroupQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');


/**
 * @openapi
 *
 * definitions:
 *      AffinityGroup:
 *          type: object
 *          properties:
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              regionId:
 *                  type: string
 *                  required: true
 *              name:
 *                  type: string
 *                  required: true
 *              description:
 *                  type: string
 *                  required: false
 *              imageUrl:
 *                  type: string
 *              benefits:
 *                  required: true
 *                  type: array
 *                  items:
 *                      type: string
 *              membersCount:
 *                  type: number
 *                  readOnly: true
 *              createdBy:
 *                  type: string
 *                  required: true
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  required: true
 *                  readOnly: true
 *              status:
 *                  type: string
 *                  enum: [ENABLED,DISABLED,ARCHIVED]
 *                  required: true
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *
 *      AffinityGroupsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/AffinityGroup'
 */
module.exports = (authorizer) => {
      /**
       * @openapi
       *
       * /affinitygroups:
       *      get:
       *          summary: Get a list of affinity groups
       *          produces:
       *              - application/json
       *          parameters:
       *              - name: limit
       *                in: query
       *                required: true
       *                type: number
       *              - name: skip
       *                in: query
       *                required: true
       *                type: number
       *              - name: searchKey
       *                in: query
       *                required: false
       *                type: string
       *              - name: regionId
       *                in: query
       *                required: true
       *                type: string
       *          tags:
       *              - Affinity Groups
       *          security:
       *              - Token: []
       *          responses:
       *              200:
       *                  description: Get affinity groups is successful.
       *                  content:
       *                      application/json:
       *                          schema:
       *                              $ref: '#/definitions/AffinityGroupsResponse'
       */
      router.get('/', authorizer, async (req, res, next) => {
        try {
          const organizationId = req.user['organizationId'];
          const ability = req.user['ability'];
          const boundary = req.user['boundary'];
          const validatedObj = await AffinityGroupQueryValidator.isValidGet(req.query);
          const result = await AffinityGroupsHandler.getAffinityGroups(organizationId, validatedObj, ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
          next(err);
        }
      });

      /**
       * @openapi
       *
       * /affinitygroups/{id}:
       *      get:
       *          summary: Get an affinity group by id
       *          produces:
       *              - application/json
       *          parameters:
       *              - name: id
       *                type: string
       *                in: path
       *                required: true
       *          tags:
       *              - Affinity Groups
       *          security:
       *              - Token: []
       *          responses:
       *              200:
       *                  description: Affinity group get is successful.
       *                  content:
       *                      application/json:
       *                          schema:
       *                              $ref: '#/definitions/AffinityGroup'
       */
      router.get('/:id', authorizer, async (req, res, next) => {
          try {
              const { id } = await CommonPathValidator.isValid(req.params);
              const organizationId = req.user['organizationId'];
              const ability = req.user['ability'];
              const boundary = req.user['boundary'];
              const result = await AffinityGroupsHandler.getAffinityGroupById(id, organizationId, ability, boundary);
              res.status(200).send(result);
          } catch (err) {
            next(err);
          }
      });

      /**
       * @openapi
       * /affinitygroups:
       *      post:
       *          produces:
       *              - application/json
       *          tags:
       *              - Affinity Groups
       *          requestBody:
       *              content:
       *                  application/json:
       *                      schema:
       *                          $ref: '#/definitions/AffinityGroup'
       *          security:
       *              - Token: []
       *          summary: Create an affinity group
       *          responses:
       *              201:
       *                  description: Affinity group created
       *                  content:
       *                      application/json:
       *                          schema:
       *                              $ref: '#/definitions/AffinityGroup'
       */
      router.post('/', authorizer, async (req, res, next) => {
        try {
          const userId = req.user['id'];
          const organizationId = req.user['organizationId'];
          const ability = req.user['ability'];
          const payload = req.body;
          const boundary = req.user['boundary'];
          const { result, ...rest } = await AffinityGroupsHandler.createAffinityGroup(payload, organizationId, userId, ability, boundary);
          next({
            response: { status: 201, body: result },
            ...rest
          });
        } catch (err) {
          next(err);
        }
      });

      /**
       * @openapi
       *
       * /affinitygroups/{id}:
       *      put:
       *          summary: Edit an affinity group
       *          produces:
       *              - application/json
       *          tags:
       *              - Affinity Groups
       *          parameters:
       *              - name: id
       *                type: string
       *                in: path
       *          requestBody:
       *              content:
       *                  application/json:
       *                      schema:
       *                          type: object
       *                          properties:
       *                            name:
       *                                type: string
       *                                required: true
       *                            description:
       *                                type: string
       *                                required: false
       *                            imageUrl:
       *                                type: string
       *                                required: true
       *                            benefits:
       *                                required: true
       *                                type: array
       *                                items:
       *                                    type: string
       *                            status:
       *                              type: string
       *                              enum: [ENABLED,DISABLED]
       *          security:
       *              - Token: []
       *          responses:
       *              200:
       *                  description: Update affinity group success.
       *                  content:
       *                      application/json:
       *                          schema:
       *                              $ref: '#/definitions/AffinityGroup'
       */
      router.put('/:id', authorizer, async (req, res, next) => {
        try {
          const { id } = await CommonPathValidator.isValid(req.params);
          const userId = req.user['id'];
          const organizationId = req.user['organizationId'];
          const ability = req.user['ability'];
          const payload = req.body;
          const boundary = req.user['boundary'];
          const { result, ...rest } = await AffinityGroupsHandler.updateAffinityGroup(id, payload, organizationId, userId, ability, boundary);
          next({
            response: { status: 200, body: result },
            ...rest
          }); 
        } catch (err) {
          next(err);
        }
      });

      /**
       * @openapi
       *
       * /affinitygroups/{id}:
       *      delete:
       *          summary: Delete an affinity group
       *          produces:
       *              - application/json
       *          tags:
       *              - Affinity Groups
       *          parameters:
       *              - name: id
       *                type: string
       *                in: path
       *          security:
       *              - Token: []
       *          responses:
       *              200:
       *                  description: Delete affinity group success.
       *                  content:
       *                      application/json:
       *                          schema:
       *                              $ref: '#/definitions/AffinityGroup'
       */
      router.delete('/:id', authorizer, async (req, res, next) => {
        try {
          const { id } = await CommonPathValidator.isValid(req.params);
          const userId = req.user['id'];
          const organizationId = req.user['organizationId'];
          const ability = req.user['ability'];
          const boundary = req.user['boundary'];
          const { result, ...rest } = await AffinityGroupsHandler.deleteAffinityGroup(id, organizationId, userId, ability, boundary);
          next({
            response: { status: 200, body: result },
            ...rest
          });           
        } catch (err) {
          next(err);
        }
      });

    return router;
}
