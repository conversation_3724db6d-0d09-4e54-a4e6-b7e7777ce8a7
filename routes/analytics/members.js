'use strict';
const express = require('express');
const router = express.Router();
const AnalyticsMembersHandler = require('../../lib/handlers/Analytics/AnalyticsMembersHandler');
const MembersQueryValidator = require('../../lib/validators/Analytics/MembersQueryValidator');

module.exports = (authorizer) => {

    /**
     * @openapi
     * /analytics/members/registration/counts:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Member registration report as count
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: groupBy
     *                in: query
     *                required: true
     *                schema:
     *                  type: string
     *                  enum: [REGISTRATION_METHOD, AGE, TYPE, BRANCH, GENDER]
     *              - name: ageRange
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [0-18, 18-24, 25-34, 35-44, 45-54, 55-64, 65+]
     *              - name: registrationMethod
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [ADMIN_PORTAL, CUSTOMER_PORTAL]
     *              - name: gender
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [MALE, FEMALE, OTHER]
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          label:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *
     */
    router.get('/registration/counts', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidRegistrationGet(req.query);
            const result = await AnalyticsMembersHandler.getCountReport(organizationId, validatedObj,  false, false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /analytics/members/registration/counts/export:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Export member registration report as count
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: groupBy
     *                in: query
     *                required: true
     *                schema:
     *                  type: string
     *                  enum: [REGISTRATION_METHOD, AGE, TYPE, BRANCH, GENDER]
     *              - name: ageRange
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [0-18, 18-24, 25-34, 35-44, 45-54, 55-64, 65+]
     *              - name: registrationMethod
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [ADMIN_PORTAL, CUSTOMER_PORTAL]
     *              - name: gender
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [MALE, FEMALE, OTHER]
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  url:
     *                                    type: string
     *
     */
    router.get('/registration/counts/export', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidRegistrationGet(req.query);
            const result  = await AnalyticsMembersHandler.getCountReport(organizationId, validatedObj,  false, true, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /analytics/members/registration/series:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Member registration report as series
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: groupBy
     *                in: query
     *                required: true
     *                schema:
     *                  type: string
     *                  enum: [REGISTRATION_METHOD, AGE, TYPE, BRANCH, GENDER]
     *              - name: ageRange
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [0-18, 18-24, 25-34, 35-44, 45-54, 55-64, 65+]
     *              - name: registrationMethod
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [ADMIN_PORTAL, CUSTOMER_PORTAL]
     *              - name: gender
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [MALE, FEMALE, OTHER]
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          label:
     *                                              type: string
     *                                          dateBucketKey:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *
     */
    router.get('/registration/series', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidRegistrationGet(req.query);
            const result = await AnalyticsMembersHandler.getCountReport(organizationId, validatedObj, true, false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/members/affinitygroup/counts:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Member affinity groups report as count
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: groupBy
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [TIER]
     *              - name: tierId
     *                in: query
     *                schema:
     *                  type: string
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          affinityGroup:
     *                                              type: string
     *                                          tier:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *                                          percentage:
     *                                              type: number
     *
     */
    router.get('/affinitygroup/counts', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidAffinityGroupReportGet(req.query);
            const result = await AnalyticsMembersHandler.getAffinityGroupsReport(organizationId, validatedObj,  false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/members/affinitygroup/series:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Member affinity groups report as series
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: groupBy
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [TIER]
     *              - name: tierId
     *                in: query
     *                schema:
     *                  type: string
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          affinityGroup:
     *                                              type: string
     *                                          tier:
     *                                              type: string
     *                                          dateBucketKey:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *                                          percentage:
     *                                              type: number
     *
     */
    router.get('/affinitygroup/series', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidAffinityGroupReportGet(req.query);
            const result = await AnalyticsMembersHandler.getAffinityGroupsReport(organizationId, validatedObj,  true, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/members/tiers/counts:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Member tiers report as count
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          tier:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *
     */
    router.get('/tiers/counts', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidDateRange(req.query);
            const result  = await AnalyticsMembersHandler.getTiersReport(organizationId, validatedObj,  false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/members/tiers/series:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Member tiers report as series
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *              - name: registrationsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: registrationsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          tier:
     *                                              type: string
     *                                          dateBucketKey:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *
     */
    router.get('/tiers/series', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidDateRange(req.query);
            const result = await AnalyticsMembersHandler.getTiersReport(organizationId, validatedObj,  true, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /analytics/members/new-return/counts:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Get new and return members report as count
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: transactionsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  newMembers:
     *                                      type: number
     *                                  returnMembers:
     *                                      type: number
     *
     */
    router.get('/new-return/counts', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidNewReturnMemberGet(req.query);
            const result = await AnalyticsMembersHandler.getNewReturnMembersCountsReport(organizationId, validatedObj,  false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /analytics/members/new-return/series:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Get new and return members report as a series
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *              - name: transactionsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          dateBucketKey:
     *                                              type: string
     *                                          newMembers:
     *                                              type: number
     *                                          returnMembers:
     *                                              type: number
     *
     */
    router.get('/new-return/series', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidNewReturnMemberGet(req.query);
            const result = await AnalyticsMembersHandler.getNewReturnMembersSeriesReport(organizationId, validatedObj,  false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /analytics/members/new-return/series/export:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Export new and return members report as a series
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *              - name: transactionsFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionsTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  url:
     *                                    type: string
     *
     */
    router.get('/new-return/series/export', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidNewReturnMemberGet(req.query);
            const result = await AnalyticsMembersHandler.getNewReturnMembersSeriesReport(organizationId, validatedObj,  true, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        } 
    });

    /**
     * @openapi
     * /analytics/members/top-shoppers:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Get top shoppers list
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: fromDate
     *                in: query
     *                required: true
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: toDate
     *                in: query
     *                required: true
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  newMembers:
     *                                      type: number
     *                                  returnMembers:
     *                                      type: number
     *
     */
    router.get('/top-shoppers', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidTopShopperGet(req.query);
            const result = await AnalyticsMembersHandler.getTopShoppersReport(organizationId, validatedObj,  ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /analytics/members/points-subtransactiontypes/counts:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Members
     *          security:
     *              - Token: []
     *          summary: Get top shoppers list
     *          parameters:
     *              - name: limit
     *                in: query
     *                type: number
     *                required: true
     *              - name: skip
     *                in: query
     *                type: number
     *                required: true
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: memberId
     *                in: query
     *                type: string
     *                required: true
     *              - name: fromDate
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: toDate
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionType
     *                in: query
     *                schema:
     *                   type: string
     *                   enum: [COLLECTION,ADJUSTMENT,REDEMPTION]
     *              - name: subtransactiontypes
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  subTypeId:
     *                                      type: string
     *                                  name:
     *                                      type: string
     *                                  transactionType:
     *                                      type: string
     *                                  isVisibleToUser:
     *                                      type: string
     *                                  totalTransactions:
     *                                      type: number
     *                                  totalPoints:
     *                                      type: number
     *
     */
    router.get('/points-subtransactiontypes/counts', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await MembersQueryValidator.isValidSubTransactionWisePointsGet(req.query);
            const result = await AnalyticsMembersHandler.getSubTransactionWiseMemberPointsReport(organizationId, validatedObj,  ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    return router;
};
