'use strict';
const express = require('express');
const router = express.Router();
const AnalyticsRewardsHandler = require('../../lib/handlers/Analytics/AnalyticsRewardsHandler');
const RewardsQueryValidator = require('../../lib/validators/Analytics/RewardsQueryValidator');

module.exports = (authorizer) => {

    /**
     * @openapi
     * /analytics/rewards/top:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Rewards
     *          security:
     *              - Token: []
     *          summary: Top rewards list
     *          parameters:
     *              - name: limit
     *                in: query
     *                type: number
     *              - name: skip
     *                in: query
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: dateFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  limit:
     *                                      type: number
     *                                  skip:
     *                                      type: number
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          _id:
     *                                              type: string
     *                                          claimedCount:
     *                                              type: number
     *                                          name:
     *                                              type: string
     *                                          description:
     *                                              type: string
     *                                          usedCount:
     *                                              type: number
     *                                          imageUrls:
     *                                              type: array
     *                                              items:
     *                                                  type: string
     *
     */
    router.get('/top', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await RewardsQueryValidator.isValidDateRange(req.query);
            const result = await AnalyticsRewardsHandler.getTopRewards(organizationId, validatedObj,  ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/rewards/redeemedrewards/count:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Rewards
     *          security:
     *              - Token: []
     *          summary: Get redeemed rewards report as count
     *          parameters:
     *              - name: rewardId
     *                in: query
     *                type: string
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: groupBy
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [REWARD]
     *              - name: dateFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              oneOf:
     *                                  - $ref: '#/definitions/schemas/GroupBy-REWARD'
     *                                  - $ref: '#/definitions/schemas/GroupBy-NONE'
     *
     *
     * definitions:
     *   schemas:
     *       GroupBy-REWARD:
     *          type: object
     *          properties:
     *              data:
     *                type: array
     *                items:
     *                  type: object
     *                  properties:
     *                      rewardName:
     *                          type: number
     *                      totalCount:
     *                          type: number
     *                      redeemedCount:
     *                          type: number
     *                      claimedCount:
     *                          type: number
     *       GroupBy-NONE:
     *          type: object
     *          properties:
     *              data:
     *                type: array
     *                items:
     *                  type: object
     *                  properties:
     *                      count:
     *                          type: number
     */
    router.get('/redeemedrewards/count', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await RewardsQueryValidator.isValidRedemptionGet(req.query);
            const result = await AnalyticsRewardsHandler.getRedemptionReport(organizationId, validatedObj, false, false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/rewards/redeemedrewards/count/export:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Rewards
     *          security:
     *              - Token: []
     *          summary: Export redeemed rewards report as count
     *          parameters:
     *              - name: rewardId
     *                in: query
     *                type: string
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: groupBy
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [REWARD]
     *              - name: dateFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  url:
     *                                    type: string
     */
    router.get('/redeemedrewards/count/export', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await RewardsQueryValidator.isValidRedemptionGet(req.query);
            const result = await AnalyticsRewardsHandler.getRedemptionReport(organizationId, validatedObj, false, true, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/rewards/redeemedrewards/series:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Rewards
     *          security:
     *              - Token: []
     *          summary: Get redeemed rewards report as a series
     *          parameters:
     *              - name: rewardId
     *                in: query
     *                type: string
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: dateFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  data:
     *                                    type: array
     *                                    items:
     *                                      type: object
     *                                      properties:
     *                                          dateBucketKey:
     *                                              type: string
     *                                          count:
     *                                              type: number
     *
     */
    router.get('/redeemedrewards/series', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await RewardsQueryValidator.isValidRedemptionGet(req.query);
            const result = await AnalyticsRewardsHandler.getRedemptionReport(organizationId, validatedObj, true, false, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /analytics/rewards/redeemedrewards/series/export:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Rewards
     *          security:
     *              - Token: []
     *          summary: Export redeemed rewards report as a series
     *          parameters:
     *              - name: rewardId
     *                in: query
     *                type: string
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: dateFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: dateBucket
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [DAY, MONTH, WEEK, QUARTER, YEAR]
     *          responses:
     *              200:
     *                  description: Report get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                            type: object
     *                            properties:
     *                                url:
     *                                  type: string
     *
     */
    router.get('/redeemedrewards/series/export', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await RewardsQueryValidator.isValidRedemptionGet(req.query);
            const result = await AnalyticsRewardsHandler.getRedemptionReport(organizationId, validatedObj, true, true, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    return router;
}
