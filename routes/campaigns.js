'use strict';
const express = require('express');
const router = express.Router();
const CampaignsHandler = require('../lib/handlers/CampaignsHandler');
const obtainOrganization = require('../lib/middlewares/obtain.organization.middleware');
const CampaignsValidator = require('../lib/validators/CampaignsValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');

/**
 * @openapi
 *
 * definitions:
 *
 *      Campaign:
 *          type: object
 *          required:
 *             - regionId
 *             - name
 *          properties:
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              regionId:
 *                  type: string
 *              name:
 *                  type: string
 *              description:
 *                  type: string
 *              segmentIds:
 *                  type: array
 *                  items:
 *                    type: string
 *              segmentFilters:
 *                  type: array
 *                  items:
 *                    type: object
 *                    properties:
 *                      memberFilter:
 *                          type: string
 *                      transactionFilter:
 *                          type: string
 *              scheduleOn:
 *                  type: string
 *                  format: date-time
 *              channel:
 *                   type: string
 *                   enum: [SMS,EMAIL]
 *              senderId:
 *                   type: string
 *              message:
 *                  type: object
 *                  properties:
 *                    messageBody:
 *                      type: string
 *                    messageSubject:
 *                      type: string
 *              status:
 *                  type: string
 *                  readOnly: true
 *                  enum: [CREATING, RUNNING, FINISHED, LISTENING, FAILED, SCHEDULED, PAUSED, WAITING]
 *              type:
 *                  type: string
 *                  required: true
 *                  enum: [TRANSACTIONAL, PROMOTIONAL]
 *              visibility:
 *                  type: string
 *                  enum: [PUBLIC, PRIVATE]
 *              endOn:
 *                  type: string
 *                  format: date-time
 *              historyEvents:
 *                  type: object
 *                  readOnly: true
 *                  properties:
 *                    eventDate:
 *                      type: string
 *                      format: date-time
 *                    eventDetails:
 *                      type: string
 *                    eventBy:
 *                      type: string
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              createdBy:
 *                  type: string
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  readOnly: true
 *
 *      CampaignsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/Campaign'
 */
module.exports = (authorizer, authorizerPortal) => {
    /**
     * @openapi
     *
     * /campaigns:
     *      post:
     *          summary: Create a campaign
     *          produces:
     *              - application/json
     *          tags:
     *              - Campaigns
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Campaign'
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create campaign successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Campaign'
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await CampaignsHandler.createCampaign(
                payload,
                organizationId,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 201, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /campaigns:
     *      get:
     *          summary: Get a list of campaigns
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: channel
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [SMS,EMAIL]
     *              - name: searchKey
     *                in: query
     *                required: false
     *              - name: status
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [CREATING,RUNNING,FINISHED,LISTENING,FAILED,SCHEDULED,PAUSED,WAITING]
     *              - name: fromDate
     *                in: query
     *                schema:
     *                    type: string
     *                    format: date-time
     *              - name: toDate
     *                in: query
     *                schema:
     *                    type: string
     *                    format: date-time
     *          tags:
     *              - Campaigns
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a list of campaigns successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Campaign'
     */
    router.get('/', authorizer, async (req, res) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await CampaignsHandler.getCampaigns(organizationId, req.query, ability, boundary);
            res.status(200).send({
                limit: req.query.limit,
                skip: req.query.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });
    /**
     * @openapi
     *
     * /campaigns/report:
     *      get:
     *          summary: Get Campaign report by ID
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: categoryId
     *                in: query
     *                required: true
     *                type: string
     *          tags:
     *              - reports
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Report get success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                            type: object
     *                            properties:
     *                               data:
     *                                 type: object
     *                                 properties:
     *                                    report:
     *                                            type: array
     *                                    failedCount:
     *                                            type: string
     *                                    successCount:
     *                                            type: string
     *
     */
    router.get('/report', authorizer, async (req, res) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { regionId, categoryId } = await CampaignsValidator.isValidGetCampaignReportById(req.query);
            const result = await CampaignsHandler.getCampaignReport(
                organizationId,
                regionId,
                categoryId,
                boundary,
                ability
            );
            res.status(200).send(result);
        } catch (err) {
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });
    /**
     * @openapi
     *
     * /campaigns/{id}:
     *      get:
     *          summary: Get campaign by id
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                in: path
     *                required: true
     *                type: number
     *          tags:
     *              - Campaigns
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a list of campaigns successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Campaign'
     */
    router.get('/:id', authorizer, async (req, res) => {
        try {
            const { id } = await CampaignsValidator.isValidGetCampaignById(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await CampaignsHandler.getCampaignRequest(organizationId, id, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    /**
     * @openapi
     *
     * /campaigns/banners/public:
     *      get:
     *          summary: Get a list of public banner campaigns
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *          tags:
     *              - Campaigns
     *          responses:
     *              200:
     *                  description: Get a list of public banner campaigns successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Campaign'
     */
    router.get('/banners/public', obtainOrganization, async (req, res, next) => {
        try {
            const organizationId = req['organizationId'];
            const result = await CampaignsHandler.getPublicBannerCampaigns(organizationId, req.query);
            res.status(200).send({
                limit: req.query.limit,
                skip: req.query.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /campaigns/banners/private:
     *      get:
     *          summary: Get a list of private banner campaigns
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *          tags:
     *              - Campaigns
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a list of private banner campaigns successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Campaign'
     */
    router.get('/banners/private', obtainOrganization, authorizerPortal, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const memberId = req.user['id'];
            const result = await CampaignsHandler.getPrivateBannerCampaigns(organizationId, memberId, req.query);
            res.status(200).send({
                limit: req.query.limit,
                skip: req.query.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /campaigns/{id}:
     *      delete:
     *          summary: Archive a campaign
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          tags:
     *              - Campaigns
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Archive a campaign is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Campaign'
     */
    router.delete('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await CampaignsHandler.deleteCampaign(
                id,
                organizationId,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
};
