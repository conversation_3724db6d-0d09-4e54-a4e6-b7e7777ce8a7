'use strict';
const express = require('express');
const router = express.Router();
const CardPrintJobsHandler = require('../lib/handlers/CardPrintJobsHandler');
const CardPrintJobQueryValidator = require('../lib/validators/QueryValidators/CardPrintJobQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');

/**
 * @openapi
 *
 * definitions:
 *      History:
 *          type: object
 *          properties:
 *              eventDate:
 *                  type: string
 *                  format: date-time
 *              eventDetails:
 *                  type: string
 *              eventBy:
 *                  type: string
 *
 *      CardPrintJob:
 *          type: object
 *          properties:
 *              _id:
 *                  type: string
 *              regionId:
 *                  type: string
 *                  required: true
 *              organizationId:
 *                  type: string
 *                  required: true
 *              batchId:
 *                  type: string
 *                  required: true
 *              startingCardNo:
 *                  type: number
 *                  required: false
 *              endingCardNo:
 *                  type: number
 *                  required: false
 *              quantity:
 *                  type: number
 *                  required: false
 *              status:
 *                  type: string
 *                  enum: [PENDING,PRINTING,PRINTED,DISPATCHED,COMPLETED]
 *              jobType:
 *                  type: string
 *                  enum: [DIGITAL_CARD,EMBOSSED_CARD,KEY_TAG,REGULAR_CARD,REGULAR_CARD_AND_KEY_TAG]
 *              historyEvents:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/History'
 *              embossCardIds:
 *                  type: array
 *                  items:
 *                      type: string
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *              createdBy:
 *                  type: string
 *              updatedBy:
 *                  type: string
 *      GenericResponse:
 *          type: object
 *          properties:
 *              message:
 *                  type: string
 *
 */
module.exports = (authorizer) => {

    /**
     * @openapi
     *
     * /cardprintjobs:
     *      post:
     *          summary: Create a card print job
     *          description: DIGITAL - Provide jobType, regionId, quantity | EMBOSSED_CARD - Provide jobType, regionId, embossCardIds | INSTANT CARDS - Provide jobType, regionId, quantity
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              jobType:
     *                                  type: string
     *                                  enum: [DIGITAL_CARD,EMBOSSED_CARD,KEY_TAG,REGULAR_CARD,REGULAR_CARD_AND_KEY_TAG]
     *                              regionId:
     *                                  type: string
     *                                  required: true
     *                              quantity:
     *                                  type: number
     *                                  required: false
     *                              embossCardIds:
     *                                  type: array
     *                                  items:
     *                                      type: string
     *
     *          tags:
     *              - Card Print Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Card print job created.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/CardPrintJob'
     * 
     * 
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const payload = req.body;
            const { result, ...rest } = await CardPrintJobsHandler.createCardPrintJob(organizationId, userId, payload, ability, boundary);
            next({
                response: { status: 201, body: result },
                ...rest
              });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /cardprintjobs:
     *      get:
     *          summary: Get a list of card print jobs.
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: false
     *                type: number
     *              - name: skip
     *                in: query
     *                required: false
     *                type: number
     *              - name: createdOnFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: createdOnTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: status
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PENDING,PRINTING,PRINTED,DISPATCHED,COMPLETED,FAILED]
     *              - name: cardTypes
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *                      enum: [DIGITAL_CARD,KEY_TAG,REGULAR_CARD,REGULAR_CARD_AND_KEY_TAG,EMBOSSED_CARD]
     *              - name: batchId
     *                in: query
     *                required: false
     *                type: string
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: searchKey
     *                in: query
     *                type: string
     *              - name: fields
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *          tags:
     *              - Card Print Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a list of card print jobs success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: array
     *                              items:
     *                                  $ref: '#/definitions/CardPrintJob'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await CardPrintJobQueryValidator.isValidGet(req.query);
            const result = await CardPrintJobsHandler.getCardPrintJobs(organizationId, validatedObj, ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /cardprintjobs/{id}:
     *      put:
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              status:
     *                                  type: string
     *                                  enum: [PENDING,PRINTING,PRINTED,DISPATCHED,COMPLETED,FAILED]
     *                              note:
     *                                  type: string
     *          tags:
     *              - Card Print Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update card print job is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/CardPrintJob'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const payload = req.body;
            const { result, ...rest } = await CardPrintJobsHandler.updateCardBatchJob(payload, id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest
              });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /cardprintjobs/getcsv:
     *      get:
     *          summary: Get a CSV for Cards in a batch
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: printJobId
     *                in: query
     *                required: true
     *                type: string
     *              - name: memberFields
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *                      enum: ["postalAddress","residentialAddress","mobileNumber"]
     *          tags:
     *              - Card Print Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a csv of cards in a batch is success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  url:
     *                                      type: string
     */
    router.get('/getcsv', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { printJobId ,memberFields=[]} = await CardPrintJobQueryValidator.isValidCsvGet(req.query);
            const { result, ...rest } = await CardPrintJobsHandler.exportCardPrintBatchToCSV(organizationId, printJobId,memberFields, ability, boundary);
            next({
                response: { status: 200, body: result }, 
                ...rest
              });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /cardprintjobs/{id}:
     *      get:
     *          summary: Get a print job by id.
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          tags:
     *              - Card Print Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a list of card print jobs success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: array
     *                              items:
     *                                  $ref: '#/definitions/CardPrintJob'
     */
    router.get('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await CardPrintJobsHandler.getCardPrintJobById(organizationId, id, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    return router;
}
