'use strict';
const express = require('express');
const router = express.Router();
const { Type } = require('../lib/db/models/enums/member.enums');
const CharitiesHandler = require('../lib/handlers/CharitiesHandler');
const UsersQueryValidator = require('../lib/validators/QueryValidators/UsersQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');

/**
 * @openapi
 * definitions:
 *      CharityTier:
 *          type: object
 *          properties:
 *              tierId:
 *                  type: string
 *              lastUpdatedOn:
 *                  type: string
 *                  format: date
 *      CharityTierData:
 *          type: object
 *          properties:
 *              name:
 *                  type: string
 *              imageUrl:
 *                  type: string
 *              benefits:
 *                  type: array
 *                  items:
 *                      type: string
 *              points:
 *                  type: string
 *              status:
 *                  type: string
 *      CharityRewardMetadata:
 *          type: object
 *          properties:
 *              rewardId:
 *                  type: string
 *              refNumber:
 *                  type: string
 *              refName:
 *                  type: string
 *              notes:
 *                  type: notes
 *      CharityIdentification:
 *          type: object
 *          properties:
 *              identificationType:
 *                  type: string
 *                  enum: [NATIONAL_ID,DRIVER_LICENSE,PASSPORT]
 *              identificationNumber:
 *                  type: string
 *      AffinityGroup:
 *          type: object
 *          readOnly: true
 *          properties:
 *              affinityGroupId:
 *                  type: string
 *              joinedDate:
 *                  type: string
 *                  format: date
 *              expiryDate:
 *                  type: string
 *                  format: date
 *              lastJobOn:
 *                  type: string
 *                  format: date
 *              lastJobId:
 *                  type: string
 *              details:
 *                  type: object
 *                  properties:
 *                      name:
 *                          type: string
 *      Charity:
 *          type: object
 *          properties:
 *              _id:
 *                  type: string
 *                  readOnly: true
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              loyaltyId:
 *                  type: string
 *                  readOnly: true
 *              profilePicture:
 *                  type: string
 *              regionId:
 *                  type: string
 *              merchantLocationId:
 *                  type: string
 *              description:
 *                  type: string
 *              type:
 *                  readOnly: true
 *                  type: string
 *                  enum: [CHARITY]
 *                  default: PRIMARY
 *              status:
 *                  type: string
 *                  enum: [ACTIVE, DEACTIVE, SUSPENDED, ARCHIVED]
 *                  readOnly: true
 *              points:
 *                  type: integer
 *                  readOnly: true
 *                  format: int64
 *              identifications:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/CharityIdentification'
 *              tierPoints:
 *                  type: integer
 *                  readOnly: true
 *                  format: int64
 *              purchasesCount:
 *                  type: integer
 *                  readOnly: true
 *                  format: int64
 *              purchasesValue:
 *                  type: integer
 *                  readOnly: true
 *                  format: int64
 *              tags:
 *                  type: array
 *                  items:
 *                      type: string
 *              lastSeenOn:
 *                  type: string
 *                  format: date
 *                  readOnly: true
 *              registeredOn:
 *                  type: string
 *                  format: date
 *              firstName:
 *                  type: string
 *              lastName:
 *                  type: string
 *              preferredName:
 *                  type: string
 *              mobileNumber:
 *                  type: string
 *                  description: Should be in E.164 format excluding the '+' symbol
 *              additionalPhoneNumbers:
 *                  type: array
 *                  items:
 *                      type: string
 *              companyName:
 *                  type: string
 *              occupation:
 *                  type: string
 *              countryCode:
 *                  type: string
 *              country:
 *                  type: string
 *              email:
 *                  type: string
 *              isValidEmail:
 *                  readOnly: true
 *                  type: boolean
 *              isValidMobileNumber:
 *                  readOnly: true
 *                  type: boolean
 *              birthDate:
 *                  type: string
 *                  format: date
 *              gender:
 *                  type: string
 *                  enum: [MALE, FEMALE, OTHER]
 *              residentialAddress:
 *                  $ref: '#/definitions/Address'
 *              postalAddress:
 *                  $ref: '#/definitions/Address'
 *              createdBy:
 *                  type: string
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  readOnly: true
 *              registerMethod:
 *                  type: string
 *                  enum: [ADMIN_PORTAL,CUSTOMER_PORTAL]
 *                  readOnly: true
 *              rewardMetadata:
 *                  type: array
 *                  readOnly: true
 *                  items:
 *                      $ref: '#/definitions/CharityRewardMetadata'
 *              affinityGroupId:
 *                  type: string
 *              affinityGroup:
 *                  readOnly: true
 *                  $ref: '#/definitions/AffinityGroup'
 *              tier:
 *                  readOnly: true
 *                  $ref: '#/definitions/CharityTier'
 *              tierData:
 *                  readOnly: true
 *                  $ref: '#/definitions/CharityTierData'
 *              customAttributes:
 *                  description: any other attributes
 *                  type: object
 *              notificationPreference:
 *                  type: object
 *                  properties:
 *                      preferredChannel:
 *                          type: string
 *                          enum: [EMAIL, MOBILE, EMAIL_AND_MOBILE]
 *                      allowPromotionalNotifications:
 *                          type: boolean
 *          required:
 *              - regionId
 *      CharityStats:
 *          type: object
 *          properties:
 *              lifetimeValue:
 *                  type: integer
 *                  format: int64
 *                  readOnly: true
 *              churnRisk:
 *                  type: integer
 *                  format: int64
 *                  readOnly: true
 *              avgBucket:
 *                  type: integer
 *                  format: int64
 *                  readOnly: true
 *              totalBucket:
 *                  type: integer
 *                  format: int64
 *                  readOnly: true
 *              avgVisits:
 *                  type: integer
 *                  format: int64
 *                  readOnly: true
 *              lastUpdatedOn:
 *                  type: string
 *                  format: date
 *      CharityPortalMetadata:
 *          type: object
 *          properties:
 *              username:
 *                  type: string
 *                  readOnly: true
 *              userId:
 *                  type: string
 *                  readOnly: true
 *              lastAccessedOn:
 *                  type: string
 *                  format: date-time
 *              platforms:
 *                  type: array
 *                  items:
 *                      type: string
 *                      enum: [WEB,MOBILE,WATCH]
 *              mobileApp:
 *                  type: boolean
 *      CharityProfile:
 *          allOf:
 *              - $ref: '#/definitions/Charity'
 *              - type: object
 *                properties:
 *                  stats:
 *                      $ref: '#/definitions/CharityStats'
 *              - type: object
 *                properties:
 *                  portalMetadata:
 *                      $ref: '#/definitions/CharityPortalMetadata'
 *      CharityUpdate:
 *          type: object
 *          properties:
 *              identifications:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/CharityIdentification'
 *              profilePicture:
 *                  type: string
 *              tags:
 *                  type: array
 *                  items:
 *                      type: string
 *              firstName:
 *                  type: string
 *              lastName:
 *                  type: string
 *              preferredName:
 *                  type: string
 *              mobileNumber:
 *                  type: string
 *                  description: Should be in E.164 format excluding the '+' symbol
 *              additionalPhoneNumbers:
 *                  type: array
 *                  items:
 *                      type: string
 *              companyName:
 *                  type: string
 *              occupation:
 *                  type: string
 *              countryCode:
 *                  type: string
 *              country:
 *                  type: string
 *              email:
 *                  type: string
 *              birthDate:
 *                  type: string
 *                  format: date
 *                  readOnly: true
 *              gender:
 *                  type: string
 *                  enum: [MALE, FEMALE, OTHER]
 *              residentialAddress:
 *                  $ref: '#/definitions/Address'
 *              postalAddress:
 *                  $ref: '#/definitions/Address'
 *              affinityGroupId:
 *                  type: string
 *                  readOnly: true
 *              customAttributes:
 *                  description: any other attributes
 *                  type: object
 *              notificationPreference:
 *                  type: object
 *                  properties:
 *                      preferredChannel:
 *                          type: string
 *                          enum: [EMAIL, MOBILE, EMAIL_AND_MOBILE]
 *                      allowPromotionalNotifications:
 *                          type: boolean
 *      CharityResponse:
 *          type: object
 *          properties:
 *              limit: 
 *                type: number
 *              skip: 
 *                type: number
 *              total:
 *                type: number
 *              items:
 *                type: array
 *                items:
 *                  $ref: '#/definitions/Charity'
 *      StatusUpdatePrimaryCardData:
 *          type: object
 *          properties:
 *              cardId:
 *                  type: string
 *              cardNumber:
 *                  type: string
 *              waiveCardReplacementFee:
 *                  type: boolean
 *      StatusUpdateSecondaryCardData:
 *          type: object
 *          properties:
 *              secondaryMemberId:
 *                  type: string
 *              cardId:
 *                  type: string
 *              cardNumber:
 *                  type: string
 *              waiveCardReplacementFee:
 *                  type: boolean
 *      StatusUpdate:
 *          type: object
 *          properties:
 *              memberId:
 *                  type: string
 *              status:
 *                  type: string
 *                  enum: [ACTIVE, DEACTIVE, SUSPENDED]
 *              notes:
 *                  type: string
 *                  writeOnly: true
 *              cardId:
 *                  type: string
 *              cardNumber:
 *                  type: string
 *              waiveCardReplacementFee:
 *                  type: boolean
 *              reactivateOptionalSecondaryMembersData:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/StatusUpdateSecondaryCardData'
 *      TypeUpdate:
 *          type: object
 *          properties:
 *              loyaltyId:
 *                  type: string
 *              type:
 *                  type: string
 *                  enum: [PRIMARY,SECONDARY] 
 *              parentMemberId:
 *                  type: string
 *                  description: Required if type is Secondary
 *              notes: 
 *                  type: string 
 *                  writeOnly: true
 *      FilterObject:
 *          type: object
 *          properties:
 *              any:
 *                  type: string
 *      FilterArray:
 *          type: array
 *          items:
 *              type: object
 *              properties:
 *                  any:
 *                      type: string
 *      FilterString:
 *          type: string
 *      Filter:
 *          anyOf:
 *              - $ref: '#/definitions/FilterString'
 *              - $ref: '#/definitions/FilterObject'
 *              - $ref: '#/definitions/FilterArray'
 *              
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     * /charities:
     *      get:
     *          summary: Get a list of charities
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: affinityGroupId
     *                in: query
     *                required: false
     *                type: string
     *              - name: loyaltyId
     *                in: query
     *                required: false
     *                type: string
     *              - name: parentMemberId
     *                in: query
     *                required: false
     *                type: string
     *              - name: sortBy
     *                in: query
     *                required: false
     *                type: string
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *              - name: tierId
     *                in: query
     *                required: false
     *                type: string
     *              - name: type
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [PRIMARY,SECONDARY,CHARITY]
     *              - name: sortDirection
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [ASC,DESC]
     *              - name: fields
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get charities success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/CharityResponse'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedQuery = await UsersQueryValidator.isValid(req.query);
            const { limit, skip, regionId, type, ...otherQuery } = validatedQuery;
            const result = await CharitiesHandler.getCharities(organizationId, regionId, otherQuery, skip, limit, ability, {}, Type.CHARITY, false, boundary);
            res.status(200).send({
                limit: limit,
                skip: skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /charities/count:
     *      get:
     *          summary: Get charities count
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: affinityGroupId
     *                in: query
     *                required: false
     *                type: string
     *              - name: loyaltyId
     *                in: query
     *                required: false
     *                type: string
     *              - name: parentMemberId
     *                in: query
     *                required: false
     *                type: string
     *              - name: sortBy
     *                in: query
     *                required: false
     *                type: string
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *              - name: tierId
     *                in: query
     *                required: false
     *                type: string
     *              - name: type
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [PRIMARY,SECONDARY,CHARITY]
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get charities count success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                count:
     *                                  type: number
     */
    router.get('/count', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedQuery = await UsersQueryValidator.isValid(req.query);
            validatedQuery.countMembers = true;
            const { regionId, type, ...otherQuery } = validatedQuery;
            const result = await CharitiesHandler.getCharitiesCount(organizationId, regionId, otherQuery, ability, Type.CHARITY, boundary, {});
            res.status(200).send({
                count: result
            });
        } catch (err) {
            next(err);
        }
    });


    /**
     * @openapi
     * /charities/export:
     *      get:
     *          summary: Export a list of charities
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: affinityGroupId
     *                in: query
     *                required: false
     *                type: string
     *              - name: loyaltyId
     *                in: query
     *                required: false
     *                type: string
     *              - name: parentMemberId
     *                in: query
     *                required: false
     *                type: string
     *              - name: sortBy
     *                in: query
     *                required: false
     *                type: string
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *              - name: type
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [PRIMARY,SECONDARY,CHARITY]
     *              - name: sortDirection
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [ASC,DESC]
     *              - name: fields
     *                in: query
     *                required: true
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Export charities success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  url:
     *                                    type: string
     */
    router.get('/export', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedQuery = await UsersQueryValidator.isValidExport(req.query);
            const { limit, skip, regionId, type, ...otherQuery } = validatedQuery;
            const result = await CharitiesHandler.getCharities(organizationId, regionId, otherQuery, skip, limit, ability, {}, Type.CHARITY, true, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /charities/{id}:
     *      get:
     *          summary: Get a single charity by ID
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                in: path
     *                required: true
     *                type: string
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get charity success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/CharityProfile'
     */
    router.get('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await CharitiesHandler.getCharity(organizationId,id,ability,null, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /charities:
     *      post:
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: verifyEmail
     *                in: query
     *                required: false
     *                type: boolean
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Charity'
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create charity success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Charity'
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const { verifyEmail } = await UsersQueryValidator.isValidMemberCreateQuery(req.query);
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const payload = req.body;
            payload['type'] = Type.CHARITY;
            const { result, ...rest } = await CharitiesHandler.createCharity(payload, organizationId, verifyEmail, userId, ability, boundary);
            next({
                response: { status: 201, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /charities/status:
     *      post:
     *          summary: Update charity status
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/StatusUpdate'
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Charity status change success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/CharityProfile'
     */
    router.post('/status', authorizer, async (req, res, next) => {
        try {
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            let payload = req.body;
            const { result, ...rest } = await CharitiesHandler.updateCharityStatus(payload, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /charities/{id}:
     *      put:
     *          summary: Update charity profile
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                required: true
     *                type: string
     *                in: path
     *                description: Member Id
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/CharityUpdate'
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update charity success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Charity'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const boundary = req.user['boundary'];
            const ability = req.user['ability'];
            const payload = req.body;
            const { result, ...rest } = await CharitiesHandler.updateCharity(payload, organizationId, id, userId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /charities/erase:
     *      post:
     *          produces:
     *              - application/json
     *          summary: Erase charity account data
     *          tags:
     *              - Charities
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              memberId:
     *                                  type: string
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Erase charity request sent
     */
    router.post('/erase', authorizer, async (req, res, next) => {
        try {
            const { id: memberId } = await CommonPathValidator.isValid({ id: req.body.memberId });
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await CharitiesHandler.eraseCharity(organizationId, memberId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest 
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /charities/export:
     *      post:
     *          produces:
     *              - application/json
     *          summary: Export charity account data
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              memberId:
     *                                  type: string
     *          responses:
     *              200:
     *                  description: Export account data request sent
     */
    router.post('/export', authorizer, async (req, res, next) => {
        try {
            const { id: memberId } = await CommonPathValidator.isValid({ id: req.body.memberId });
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await CharitiesHandler.exportCharity(organizationId, memberId, ability, boundary);
            next({
                response: { status: 200, body: result }, 
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /charities/{id}:
     *      delete:
     *          summary: Archive a charity
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          tags:
     *              - Charities
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Archive charity is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Charity'
     */
    router.delete('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await CharitiesHandler.archiveCharity(id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
}

