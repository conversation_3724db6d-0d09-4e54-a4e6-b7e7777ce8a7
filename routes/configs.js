'use strict';
const express = require('express');
const router = express.Router();
const ConfigsHandler = require('./../lib/handlers/ConfigsHandler');
const { authorize, OPERATION } = require('@shoutout-labs/express-authorizer-middleware');
const MODULE_ID = require('@shoutout-labs/constants').AUTH_MODULE_IDS.LOYALTY.MODULE_ID_CONFIGS;

/**
 * @openapi
 *
 * definitions:
 *      Config:
 *          type: object
 *          properties:
 *              rewards:
 *                  type: object
 *                  properties:
 *                      portalName:
 *                          type: string
 *                      portalUrl:
 *                          type: string
 *                      portalImageUrl:
 *                          type: string
 *                      portalCoverImageUrl:
 *                          type: string
 *                      merchantName:
 *                          type: string
 *                      registrationBonusRewardId:
 *                          type: string
 *              userIdPatterns:
 *                  type: object
 *                  properties:
 *                      userType:
 *                          type: string
 *                      userId:
 *                          type: string
 *                      userTypeAttr:
 *                          type: string
 *                      userTypeMap:
 *                          type: object
 *                          properties:
 *              points:
 *                  type: object
 *                  properties:
 *                      merchantBonus:
 *                          type: number
 *                      rate:
 *                          type: number
 *                      alias:
 *                          type: string
 *                      registrationBonus:
 *                          type: number
 *                      portalRegistrationBonus:
 *                          type: number
 *                      referralBonus:
 *                          type: number
 *                      minRedeemBalance:
 *                          type: number
 *                      custom_purchasePoint:
 *                          type: number
 *                      custom_purchasePoints:
 *                          type: array
 *                          items:
 *                              type: object
 *                              properties:
 *                                  searchKey:
 *                                      type: string
 *                                  value:
 *                                      type: number
 *              tierPoints:
 *                  type: object
 *                  properties:
 *                      rate:
 *                          type: number
 *              pointRedeemForm:
 *                  type: object
 *                  properties:
 *                      metadata:
 *                          type: object
 *                          properties:
 *                      form:
 *                          type: object
 *                          properties:
 *              pointCollectForm:
 *                  type: object
 *                  properties:
 *                      metadata:
 *                          type: object
 *                          properties:
 *                      form:
 *                          type: object
 *                          properties:
 *              pointsConfigForm:
 *                  type: object
 *                  properties:
 *                      form:
 *                          type: object
 *                          properties:
 *              tiers:
 *                  type: object
 *                  properties:
 *                      data:
 *                          type: object
 *                          properties:
 *                              name:
 *                                  type: string
 *                              points:
 *                                  type: number
 *                              benefits:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              imageUrl:
 *                                  type: string
 *                      config:
 *                          type: object
 *                          properties:
 *                              immediateTierUpdate:
 *                                  type: boolean
 *                              immediateTierDowngrade:
 *                                  type: boolean
 *                              calculationWindow:
 *                                  type: number
 *              registrationForm:
 *                  type: object
 *                  properties:
 *                      metadata:
 *                          type: object
 *                          properties:
 *                      form:
 *                          type: object
 *                          properties:
 *                      lastUserId:
 *                          type: number
 *                      visitorId:
 *                          type: string
 *              klipConfig:
 *                  type: object
 *                  properties:
 *                      collectPoint:
 *                          type: boolean
 *                      redeemPoint:
 *                          type: boolean
 *                      userCreate:
 *                          type: boolean
 *              botEnabled:
 *                  type: boolean
 *              isSystemLoyaltyId:
 *                  type: boolean
 *              portalOtpSenderId:
 *                  type: string
 *              apiKey:
 *                  type: string
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /configs:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Configs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get the config is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Config'
     */
    router.get('/', authorizer, authorize(MODULE_ID, OPERATION.READ), async (req, res, next) => {
        try {
            let ownerId = req.user['id'];
            let { attributes } = req.query;
            const result = await ConfigsHandler.getConfig(ownerId, attributes);
            res.status(200).send(result);
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    /**
     * @openapi
     *
     * /configs:
     *      post:
     *          produces:
     *              - application/json
     *          tags:
     *              - Configs
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Config'
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create the config is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Config'
     */
    router.post('/', authorizer, authorize(MODULE_ID, OPERATION.WRITE), async (req, res, next) => {
        try {
            let ownerId = req.user['id'];
            let payload = req.body;
            const result = await ConfigsHandler.createConfig(payload, ownerId);
            res.status(201).send(result);
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    /**
     * @openapi
     *
     * /configs/{id}:
     *      put:
     *          produces:
     *              - application/json
     *          tags:
     *              - Configs
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Config'
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update the config is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Config'
     */
    router.put('/:id', authorizer, authorize(MODULE_ID, OPERATION.WRITE), async (req, res, next) => {
        try {
            if (req.params['id']) {
                let id = req.params['id'];
                let ownerId = req.user['id'];
                let payload = req.body;
                const result = await ConfigsHandler.updateConfig(payload, ownerId, id);
                res.status(200).send(result);
            } else {
                return res.status(400).send({
                    status: 400,
                    error: 'id is required'
                });
            }
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    /**
     * @openapi
     *
     * /configs/{id}:
     *      delete:
     *          produces:
     *              - application/json
     *          tags:
     *              - Configs
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Delete the config is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Config'
     */
    router.delete('/:id', authorizer, authorize(MODULE_ID, OPERATION.DELETE), async (req, res, next) => {
        try {
            if (req.params['id']) {
                let id = req.params['id'];
                let ownerId = req.user['id'];
                const result = await ConfigsHandler.deleteConfig(id, ownerId);
                res.status(result.status || 200).send(result);
            } else {
                return res.status(400).send({
                    status: 400,
                    error: 'id is required'
                });
            }
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });
    return router;
}
