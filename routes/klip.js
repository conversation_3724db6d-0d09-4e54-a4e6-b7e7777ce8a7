'use strict';
const express = require('express');
const router = express.Router();
const RewardQueryValidator = require('../lib/validators/QueryValidators/RewardQueryValidator');
const KlipHandler = require('../lib/handlers/KlipHandler');
const RedemptionLogsQueryValidator = require('../lib/validators/QueryValidators/RedemptionLogsQueryValidator');
const UsersQueryValidator = require('../lib/validators/QueryValidators/UsersQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');
const createValidator = require('../lib/middlewares/joi.validation.middleware');
const Joi = require('joi');

// Define validators for swagger generation
const validators = {
    klipMembers: {
        schemas: {
            query: UsersQueryValidator.membersSearchQueryParamsSchema
        },
        routeConfig: {
            path: '/klip/members',
            method: 'get',
            tags: ['KLIP'],
            summary: 'Get a list of members',
            description: 'Retrieves a paginated list of members with various filter options',
            responses: {
                200: {
                    description: 'Invalid request parameters',
                    schema: Joi.object({
                        limit: Joi.number().description('Number of records returned'),
                        skip: Joi.number().description('Number of records skipped'),
                        total: Joi.number().description('Total number of records'),
                        items: Joi.array().items(
                            Joi.object({
                                _id: Joi.string(),
                                firstName: Joi.string(),
                                lastName: Joi.string(),
                                email: Joi.string(),
                                mobileNumber: Joi.string(),
                                points: Joi.number(),
                                type: Joi.string(),
                                status: Joi.string(),
                                allowedRedeemablePoints: Joi.number().default(0)
                            })
                        )
                    })
                }
            }
        }
    }
};

module.exports = (authorizer) => {
    /**
     * @openapi
     * /klip/points/redeem:
     *      post:
     *          summary: Redeem points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsRedeem'
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsRedeemResponse'
     */
    router.post('/points/redeem', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.redeemPoints(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /klip/points/adjust:
     *      post:
     *          summary: adjust points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsAdjust'
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsAdjustResponse'
     */
    router.post('/points/adjust', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.adjustPointsRequest(
                organizationId,
                payload,
                userId,
                ability,
                null,
                false,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /klip/points/collect/bill:
     *      post:
     *          summary: Collect points from bill value
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsCollectBill'
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point collect success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsCollectResponse'
     */
    router.post('/points/collect/bill', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.collectPointsBill(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /klip/points/collect/points:
     *      post:
     *          summary: Collect points from points amount
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsCollect'
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point collect success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsCollectResponse'
     */
    router.post('/points/collect/points', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.collectPoints(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /klip/rewards:
     *      get:
     *          summary: Get a list of rewards
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: false
     *                type: string
     *              - name: status
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [DRAFT,ENABLED,DISABLED]
     *              - name: type
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [TANGIBLE,DIGITAL]
     *              - name: subType
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [VOUCHER,PARTNER,INSTANT]
     *              - name: portalVisibility
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [PARTNER_AND_CUSTOMER,PARTNER_ONLY,CUSTOMER_ONLY,NONE]
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *              - name: pointsLowerMargin
     *                in: query
     *                required: false
     *                type: number
     *              - name: pointsUpperMargin
     *                in: query
     *                required: false
     *                type: number
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get rewards is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardsResponse'
     */
    router.get('/rewards', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            let validatedObj = await RewardQueryValidator.isValidGet(req.query);
            const result = await KlipHandler.getRewards(organizationId, validatedObj, ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /klip/rewardredeem:
     *      post:
     *          summary: Redeem reward
     *          produces:
     *              - application/json
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          required:
     *                              - rewardId
     *                              - regionId
     *                              - merchantLocationId
     *                              - metadata
     *                          properties:
     *                              memberId:
     *                                  type: string
     *                              rewardId:
     *                                  type: string
     *                              regionId:
     *                                  type: string
     *                              pointsBundleId:
     *                                  type: string
     *                              merchantLocationId:
     *                                  type: string
     *                              metadata:
     *                                  oneOf:
     *                                      - $ref: '#/definitions/MetadataWhenSubTypePartner'
     *                                      - $ref: '#/definitions/Metadata'
     *          responses:
     *              200:
     *                  description: Reward redeem success. (voucher_code - VOUCHER) (topup_amount, topup_mobile_number - DIGITAL)
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardRedeemResponse'
     *
     */
    router.post('/rewardredeem', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.redeemReward(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: {
                    status: 200,
                    body: result
                },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /klip/redemptionlogs:
     *      get:
     *          summary: Get a list of redemption logs
     *          produces:
     *              - application/json
     *          tags:
     *              - KLIP
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: memberId
     *                in: query
     *                type: string
     *              - name: rewardId
     *                in: query
     *                type: string
     *              - name: distributionJobId
     *                in: query
     *                type: string
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: rewardType
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [TANGIBLE,DIGITAL]
     *              - name: rewardSubType
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [VOUCHER,PARTNER,INSTANT]
     *              - name: status
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [REQUESTED,READY,CLAIMED,CANCELLED,PROCESSING,COMPLETED,FAILED,REFUNDED]
     *              - name: statusArray
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *                      enum: [REQUESTED,READY,CLAIMED,CANCELLED,PROCESSING,COMPLETED,FAILED,REFUNDED]
     *              - name: processingStatus
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PENDING,PROCESSING,DISPATCHED,COMPLETED,FAILED]
     *              - name: fromDate
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: toDate
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: claimLocationId
     *                in: query
     *                required: false
     *                type: number
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get redemption logs success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RedemptionLogsResponse'
     */
    router.get('/redemptionlogs', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await RedemptionLogsQueryValidator.isValid(req.query);
            const result = await KlipHandler.getRedemptionLogs(organizationId, validatedObj, ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    router.get(
        '/members',
        authorizer,
        createValidator(validators.klipMembers.schemas, validators.klipMembers.routeConfig),
        async (req, res, next) => {
            try {
                const organizationId = req.user['organizationId'];
                const ability = req.user['ability'];
                const boundary = req.user['boundary'];
                const { limit, skip, regionId, type, ...otherQuery } = req.query;

                const result = await KlipHandler.getMembers(
                    organizationId,
                    regionId,
                    otherQuery,
                    skip,
                    limit,
                    ability,
                    {},
                    type,
                    false,
                    boundary
                );

                res.status(200).send({
                    limit: limit,
                    skip: skip,
                    total: result.total,
                    items: result.items
                });
            } catch (err) {
                next(err);
            }
        }
    );

    /**
     * @openapi
     * /klip/members/count:
     *      get:
     *          summary: Get members count
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: affinityGroupId
     *                in: query
     *                required: false
     *                type: string
     *              - name: loyaltyId
     *                in: query
     *                required: false
     *                type: string
     *              - name: parentMemberId
     *                in: query
     *                required: false
     *                type: string
     *              - name: sortBy
     *                in: query
     *                required: false
     *                type: string
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *              - name: searchField
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [FIRST_NAME,LAST_NAME,PREFERRED_NAME,EMAIL,MOBILE_NUMBER,CARD_NUMBER]
     *              - name: tierId
     *                in: query
     *                required: false
     *                type: string
     *              - name: type
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [PRIMARY,SECONDARY]
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get members count success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                count:
     *                                  type: number
     */
    router.get('/members/count', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedQuery = await UsersQueryValidator.isValid(req.query);
            validatedQuery.countMembers = true;
            const { regionId, type, ...otherQuery } = validatedQuery;
            const result = await KlipHandler.getMembersCount(
                organizationId,
                regionId,
                otherQuery,
                ability,
                {},
                type,
                boundary
            );
            res.status(200).send({
                count: result
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /klip/members/{id}:
     *      get:
     *          summary: Get a single member by ID
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                in: path
     *                required: true
     *                type: string
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get member success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/MemberProfile'
     */
    router.get('/members/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await KlipHandler.getMember(organizationId, id, ability, null, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /klip/claimreward:
     *      post:
     *          produces:
     *              - application/json
     *          summary: Claim reward
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          required:
     *                            - voucherCode
     *                          properties:
     *                              voucherCode:
     *                                  type: string
     *          responses:
     *              200:
     *                  description: Reward claim success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  rewardId:
     *                                      type: string
     *                                  voucherCode:
     *                                      type: string
     *                                  voucherStatus:
     *                                      type: string
     *                                  reward:
     *                                      type: object
     *                                      properties:
     *                                          name:
     *                                              type: string
     *                                          description:
     *                                              type: string
     *                                          type:
     *                                              type: string
     *                                          points:
     *                                              type: number
     *                                          imageUrls:
     *                                              type: array
     *                                              items:
     *                                                  type: string
     *
     *
     */
    router.post('/claimreward', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const userId = req.user['userId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const payload = req.body;
            const { result, ...rest } = await KlipHandler.claimReward(
                organizationId,
                userId,
                payload,
                ability,
                boundary
            );
            next({
                response: {
                    status: 200,
                    body: result
                },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /klip/points/redeemwithotprequest:
     *      post:
     *          summary: Request OTP token to redeem points with OTP
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsRedeem'
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  otpToken:
     *                                      type: string
     */
    router.post('/points/redeemwithotprequest', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.redeemPointsOtpRequest(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: {
                    status: 200,
                    body: {
                        otpToken: result
                    }
                },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /klip/points/redeemwithotp:
     *      post:
     *          summary: Redeem points with OTP
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              redemptionToken:
     *                                  type: string
     *                              otpCode:
     *                                  type: string
     *          tags:
     *              - KLIP
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsRedeemResponse'
     */
    router.post('/points/redeemwithotp', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await KlipHandler.redeemPointsWithOtp(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
};

// Export validators for swagger generation
module.exports.getValidators = () => validators;
