'use strict';
const express = require('express');
const router = express.Router();
const MerchantLocationsHandler = require('../lib/handlers/MerchantLocationsHandler');
const MerchantLocationQueryValidator = require('../lib/validators/QueryValidators/MerchantLocationQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');

/**
 * @openapi
 *
 * definitions:
 *      LoyaltyOptions:
 *          type: object
 *          properties:
 *              adjustPoints:
 *                  type: boolean
 *              enroll:
 *                  type: boolean
 *              earn:
 *                  type: boolean
 *              redeemPoints:
 *                  type: boolean
 *              redeemRewards:
 *                  type: boolean
 *              refund:
 *                  type: boolean
 *              void:
 *                  type: boolean
 *              claimReward:
 *                  type: boolean
 *      Contact:
 *          type: object
 *          properties:
 *              mobileNumber:
 *                  type: string
 *              email:
 *                  type: string
 *              address:
 *                  $ref: '#/definitions/Address'
 *      Location:
 *          type: object
 *          properties:
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              regionId:
 *                  type: string
 *              merchantId:
 *                  type: string
 *              locationName:
 *                  type: string
 *              contact:
 *                  $ref: '#/definitions/Contact'
 *              options:
 *                  $ref: '#/definitions/LoyaltyOptions'
 *              status:
 *                  type: string
 *                  enum: [DRAFT,ACTIVE,SUSPENDED]
 *              isPickupLocation:
 *                  type: boolean
 *              isShownInCreateTransactions:
 *                  type: boolean
 *              code:
 *                  type: string
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              createdBy:
 *                  type: string
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  readOnly: true
 *          required:
 *              - regionId
 *              - merchantId
 *              - locationName
 *              - contact
 *              - options
 *              - isPickupLocation
 *              - code
 *      LocationsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/Location'
 */
module.exports = (authorizer) => {
  /**
   * @openapi
   *
   * /locations:
   *      get:
   *          summary: Get a list of merchant locations
   *          produces:
   *              - application/json
   *          parameters:
   *              - name: limit
   *                in: query
   *                required: true
   *                type: number
   *              - name: skip
   *                in: query
   *                required: true
   *                type: number
   *              - name: regionId
   *                in: query
   *                required: true
   *                type: string
   *              - name: merchantId
   *                in: query
   *                required: false
   *                type: string
   *              - name: merchantIds
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *              - name: searchKey
   *                in: query
   *                required: false
   *                type: string
   *              - name: locationCode
   *                in: query
   *                required: false
   *                type: string
   *              - name: nearestLocations
   *                in: query
   *                required: false
   *                schema:
   *                  type: boolean
   *                  enum: [TRUE,FALSE]
   *              - name: isPickupLocation
   *                in: query
   *                required: false
   *                schema:
   *                  type: boolean
   *                  enum: [TRUE,FALSE]
   *              - name: locationIds
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *              - name: status
   *                in: query
   *                required: false
   *                schema:
   *                  type: string
   *                  enum: [DRAFT,ACTIVE,SUSPENDED]
   *          tags:
   *              - Locations
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Get locations is successful.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/LocationsResponse'
   */
  router.get('/', authorizer, async (req, res, next) => {
    try {
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const validatedObj = await MerchantLocationQueryValidator.isValidGet(req.query);
      const result = await MerchantLocationsHandler.getMerchantLocations(organizationId, validatedObj, ability, boundary);
      res.status(200).send({
        limit: validatedObj.limit,
        skip: validatedObj.skip,
        total: result.total,
        items: result.items
      });
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /locations/{id}:
   *      get:
   *          summary: Get a single location by Id
   *          produces:
   *              - application/json
   *          parameters:
   *              - name: id
   *                type: string
   *                in: path
   *                required: true
   *          tags:
   *              - Locations
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Get location is successful.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/Location'
   */
  router.get('/:id', authorizer, async (req, res, next) => {
    try {
      const { id } = await CommonPathValidator.isValid(req.params);
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const result = await MerchantLocationsHandler.getLocationById(
        id,
        organizationId,
        ability,
        boundary,
      );
      res.status(200).send(result);
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /locations:
   *      post:
   *          summary: Create a merchant location
   *          produces:
   *              - application/json
   *          tags:
   *              - Locations
   *          requestBody:
   *              content:
   *                  application/json:
   *                      schema:
   *                          $ref: '#/definitions/Location'
   *          security:
   *              - Token: []
   *          responses:
   *              201:
   *                  description: Create location is successful.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/Location'
   */
  router.post('/', authorizer, async (req, res, next) => {
    try {
      const payload = req.body;
      const userId = req.user['id'];
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const { result, ...rest } =
        await MerchantLocationsHandler.createMerchantLocation(
          payload,
          organizationId,
          userId,
          ability,
          boundary,
        );
      next({
        response: { status: 201, body: result },
        ...rest,
      });
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /locations/{id}:
   *      put:
   *          summary: Update a merchant location
   *          produces:
   *              - application/json
   *          tags:
   *              - Locations
   *          parameters:
   *              - name: id
   *                type: string
   *                in: path
   *          requestBody:
   *              content:
   *                  application/json:
   *                      schema:
   *                          type: object
   *                          properties:
   *                            locationName:
   *                                type: string
   *                            isPickupLocation:
   *                                    type: boolean
   *                            isShownInCreateTransactions:
   *                                    type: boolean
   *                            contact:
   *                                $ref: '#/definitions/Contact'
   *                            options:
   *                                $ref: '#/definitions/LoyaltyOptions'
   *                            status:
   *                                type: string
   *                                enum: [DRAFT,ACTIVE,SUSPENDED]
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Update location is successful.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/Location'
   */
  router.put('/:id', authorizer, async (req, res, next) => {
    try {
      const { id } = await CommonPathValidator.isValid(req.params);
      const payload = req.body;
      const userId = req.user['id'];
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const { result, ...rest } =
        await MerchantLocationsHandler.updateMerchantLocation(
          payload,
          id,
          organizationId,
          userId,
          ability,
          boundary,
        );
      next({
        response: { status: 200, body: result },
        ...rest,
      });
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /locations/{id}:
   *      delete:
   *          summary: Archive a merchant location
   *          produces:
   *              - application/json
   *          tags:
   *              - Locations
   *          parameters:
   *              - name: id
   *                type: string
   *                in: path
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Archive location is successful.
   */
  router.delete('/:id', authorizer, async (req, res, next) => {
    try {
      const { id } = await CommonPathValidator.isValid(req.params);
      const userId = req.user['id'];
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const { result, ...rest } =
        await MerchantLocationsHandler.deleteMerchantLocation(
          id,
          organizationId,
          userId,
          ability,
          boundary,
        );
      next({
        response: { status: 200, body: result },
        ...rest,
      });
    } catch (err) {
      next(err);
    }
  });

  return router;
};
