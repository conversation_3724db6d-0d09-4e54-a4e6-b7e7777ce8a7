'use strict';
const express = require('express');
const router = express.Router();
const PointsHandler = require('./../lib/handlers/PointsHandler');
const { POINT_TRANSFER_TYPES } = require('./../lib/db/models/enums/transaction.enums');
const createValidator = require('../lib/middlewares/joi.validation.middleware');
const AdjustPointsValidator = require('./../lib/validators/AdjustPointsValidator');
const Joi = require('joi');

// Define validators for swagger generation
const validators = {
    pointsReversalBill: {
        schemas: {
            body: AdjustPointsValidator.pointsReversalSchema
        },
        routeConfig: {
            path: '/points/reverse/bill',
            method: 'post',
            tags: ['Points'],
            summary: 'Reverse points awarded/redeemed from a bill transaction',
            description:
                'Reverses points from a previous bill transaction. Requires either memberId or cardNo for member identification.',
            responses: {
                201: {
                    description: 'Points reversal successful',
                    schema: AdjustPointsValidator.pointsReversalResponseSchema
                }
            }
        }
    },
    pointsReversalBillOtpRequest: {
        schemas: {
            body: AdjustPointsValidator.pointsReversalSchema
        },
        routeConfig: {
            path: '/points/reverse/billwithotprequest',
            method: 'post',
            tags: ['Points'],
            summary: 'Request OTP token to reverse points awarded/redeemed from a bill value',
            description: 'Generates OTP for secure points reversal operation',
            responses: {
                200: {
                    description: 'Success',
                    schema: Joi.object({
                        pointsReversalToken: Joi.string()
                            .required()
                            .description('Token to be used in the OTP verification step')
                    })
                }
            }
        }
    },
    pointsReversalBillWithOtp: {
        schemas: {
            body: AdjustPointsValidator.pointsReversalOtpRequestSchema
        },
        routeConfig: {
            path: '/points/reverse/billwithotp',
            method: 'post',
            tags: ['Points'],
            summary: 'Reverse points with OTP verification',
            description: 'Completes the points reversal operation using the provided OTP',
            responses: {
                201: { description: 'Success', schema: AdjustPointsValidator.pointsReversalResponseSchema }
            }
        }
    }
};

/**
 * @openapi
 * definitions:
 *      PointsRedeemResponseOne:
 *          type: object
 *          properties:
 *              balance_points:
 *                  type: number
 *              points:
 *                  type: number
 *      PointsRedeemResponseTwo:
 *          type: object
 *          properties:
 *              otpToken:
 *                  type: string
 *      PointsRedeemActivityData:
 *          type: object
 *          properties:
 *              points:
 *                  type: number
 *      PointsRedeemWithOtp:
 *          type: object
 *          required:
 *              - userId
 *              - activityData
 *          properties:
 *              userId:
 *                  type: string
 *              activityData:
 *                  $ref: '#/definitions/PointsRedeemActivityData'
 *              otpValidation:
 *                  type: object
 *                  properties:
 *                      otpToken:
 *                          type: string
 *                      otpCode:
 *                          type: string
 *      PointsRedeemValidation:
 *          type: object
 *          required:
 *              - userId
 *              - activityData
 *          properties:
 *              userId:
 *                  type: string
 *              activityData:
 *                  $ref: '#/definitions/PointsRedeemActivityData'
 *
 *      ProductItem:
 *          type: object
 *          properties:
 *                  productId:
 *                      type: string
 *                  productName:
 *                      type: string
 *                  productCategory:
 *                      type: array
 *                      items:
 *                          type: string
 *                  quantity:
 *                      type: number
 *                  amount:
 *                      type: number
 *
 *      InvoiceData:
 *          type: object
 *          properties:
 *                  invoiceId:
 *                      type: string
 *                  invoiceDate:
 *                      type: string
 *                      format: date-time
 *                  invoiceAmountWithTax:
 *                      type: number
 *                  invoiceAmountWithoutTax:
 *                      type: number
 *                  discountAmount:
 *                      type: number
 *                  billAmount:
 *                      type: number
 *
 *      PointRuleEvaluation:
 *          type: object
 *          properties:
 *                  pointRuleId:
 *                      type: string
 *                  points:
 *                      type: number
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     * definitions:
     *
     *      PointsCollectActivityData:
     *          type: object
     *          properties:
     *              bill:
     *                  type: number
     *              points:
     *                  type: number
     *              billReferenceId:
     *                  type: string
     *              source:
     *                  type: string
     *              branch:
     *                  type: string
     *              transactionDate:
     *                  type: string
     *                  format: date-time
     *
     *      PointsCollectBill:
     *          type: object
     *          required:
     *              - merchantId
     *              - billAmount
     *              - transactionDate
     *          properties:
     *              memberId:
     *                  type: string
     *              cardNo:
     *                  type: string
     *              merchantId:
     *                  type: string
     *              merchantLocationId:
     *                  type: string
     *              merchantLocationCode:
     *                  type: string
     *              billAmount:
     *                  type: number
     *              transactionDate:
     *                  type: string
     *                  format: date-time
     *              skipConfigValidation:
     *                  type: boolean
     *              notes:
     *                  type: string
     *              productItems:
     *                  type: array
     *                  items:
     *                      $ref: '#/definitions/ProductItem'
     *              invoiceData:
     *                  $ref: '#/definitions/InvoiceData'
     *              idempotentKey:
     *                  type: string
     *
     *
     *      PointsCollectResponse:
     *          type: object
     *          properties:
     *              transactionSubType:
     *                  type: string
     *              memberId:
     *                  type: string
     *              totalPoints:
     *                  type: number
     *              points:
     *                  type: number
     *              tierPoints:
     *                  type: number
     *              pointRuleEvaluations:
     *                      type: array
     *                      items:
     *                          $ref: '#/definitions/PointRuleEvaluation'
     *
     * /points/collect/bill:
     *      post:
     *          summary: Collect points from a bill value
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsCollectBill'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point collect success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsCollectResponse'
     */
    router.post('/collect/bill', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.collectPointsBill(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * definitions:
     *      PointsCollect:
     *          type: object
     *          required:
     *              - merchantId
     *              - pointsAmount
     *              - transactionDate
     *          properties:
     *              memberId:
     *                  type: string
     *              cardNo:
     *                  type: string
     *              merchantId:
     *                  type: string
     *              merchantLocationId:
     *                  type: string
     *              merchantLocationCode:
     *                  type: string
     *              pointsAmount:
     *                  type: number
     *              skipConfigValidation:
     *                  type: boolean
     *              transactionDate:
     *                  type: string
     *                  format: date-time
     *              productItems:
     *                  type: array
     *                  items:
     *                      $ref: '#/definitions/ProductItem'
     *              invoiceData:
     *                  $ref: '#/definitions/InvoiceData'
     *              notes:
     *                  type: string
     *              idempotentKey:
     *                  type: string
     *
     * /points/collect/points:
     *      post:
     *          summary: Collect points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsCollect'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point collect success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsCollectResponse'
     */
    router.post('/collect/points', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.collectPointsAmount(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * definitions:
     *
     *      PointsAdjust:
     *          type: object
     *          properties:
     *              memberId:
     *                  type: string
     *              cardNo:
     *                  type: string
     *              merchantId:
     *                  type: string
     *              merchantLocationId:
     *                  type: string
     *              merchantLocationCode:
     *                  type: string
     *              pointsAmount:
     *                  type: number
     *              transactionDate:
     *                  type: string
     *                  format: date-time
     *              transactionSubTypeId:
     *                  type: string
     *              transactionSubTypeReferenceId:
     *                  type: string
     *              notes:
     *                  type: string
     *              idempotentKey:
     *                  type: string
     *
     *
     *      PointsAdjustResponse:
     *          type: object
     *          properties:
     *              transactionSubType:
     *                  type: string
     *              memberId:
     *                  type: string
     *              totalPoints:
     *                  type: number
     *              points:
     *                  type: number
     *              otherTransactionDetails:
     *                  type: object
     *
     * /points/adjust:
     *      post:
     *          summary: Adjust points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsAdjust'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point adjust success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsAdjustResponse'
     */
    router.post('/adjust', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.adjustPointsRequest(
                organizationId,
                payload,
                userId,
                ability,
                null,
                false,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * definitions:
     *
     *      PointsDonate:
     *          type: object
     *          properties:
     *              memberId:
     *                  type: string
     *              charityMemberId:
     *                  type: string
     *              merchantId:
     *                  type: string
     *              merchantLocationId:
     *                  type: string
     *              merchantLocationCode:
     *                  type: string
     *              pointsAmount:
     *                  type: number
     *              notes:
     *                  type: string
     *
     *
     *      PointsDonateResponse:
     *          type: object
     *          properties:
     *              memberId:
     *                  type: string
     *              totalPoints:
     *                  type: number
     *              points:
     *                  type: number
     *
     * /points/donate:
     *      post:
     *          summary: Donate points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsDonate'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point donation success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsDonateResponse'
     */
    router.post('/donate', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.transferPoints(
                organizationId,
                payload,
                userId,
                POINT_TRANSFER_TYPES.DONATE,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /points/transfer:
     *      post:
     *          summary: Transfer points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsDonate'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point transfer success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsDonateResponse'
     */
    router.post('/transfer', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.transferPoints(
                organizationId,
                payload,
                userId,
                POINT_TRANSFER_TYPES.TRANSFER,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * definitions:
     *      PointsCollectSimulationResponse:
     *          type: object
     *          properties:
     *              points:
     *                  type: number
     *              tier_points:
     *                  type: number
     *      PointsCollectSimulationActivityData:
     *          type: object
     *          properties:
     *              bill:
     *                  type: number
     *              source:
     *                  type: string
     *              branch:
     *                  type: string
     *              billDate:
     *                  type: string
     *                  format: date-time
     *      PointsCollectSimulation:
     *          type: object
     *          required:
     *              - userId
     *              - activityData
     *          properties:
     *              userId:
     *                  type: string
     *              activityData:
     *                  $ref: '#/definitions/PointsCollectSimulationActivityData'
     * /points/collectsimulation:
     *      post:
     *          deprecated: true
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsCollectSimulation'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          summary: Get a simulated points amount
     *          description: Get a simulated points amount for the points collect payload without adding points to the user
     *          responses:
     *              200:
     *                  description: Point collect simulation success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsCollectSimulationResponse'
     */
    router.post('/collectsimulation', authorizer, async (req, res, next) => {
        try {
            const ownerId = req.user['id'];
            const payload = req.body;
            const result = await PointsHandler.collectPointsSimulation(ownerId, payload);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * definitions:
     *      PointsRedeem:
     *          type: object
     *          required:
     *              - merchantId
     *              - pointsAmount
     *          properties:
     *              memberId:
     *                  type: string
     *              cardNo:
     *                  type: string
     *              merchantId:
     *                  type: string
     *              merchantLocationId:
     *                  type: string
     *              merchantLocationCode:
     *                  type: string
     *              pointsAmount:
     *                  type: number
     *              skipConfigValidation:
     *                  type: boolean
     *              transactionSubTypeId:
     *                  type: string
     *              transactionSubTypeReferenceId:
     *                  type: string
     *              transactionDate:
     *                  type: string
     *                  format: date-time
     *              notes:
     *                  type: string
     *              invoiceData:
     *                  $ref: '#/definitions/InvoiceData'
     *              idempotentKey:
     *                  type: string
     *
     *      PointsRedeemResponse:
     *          type: object
     *          properties:
     *              transactionId:
     *                  type: string
     *              transactionSubType:
     *                  type: string
     *              memberId:
     *                  type: string
     *              balancePoints:
     *                  type: number
     *              points:
     *                  type: number
     *
     * /points/redeem:
     *      post:
     *          summary: Redeem points
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsRedeem'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsRedeemResponse'
     */
    router.post('/redeem', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.redeemPointsRequest(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /points/redeemwithotprequest:
     *      post:
     *          summary: Request OTP token to redeem with OTP
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsRedeem'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  otpToken:
     *                                      type: string
     */
    router.post('/redeemwithotprequest', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.redeemOtpRequest(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: {
                    status: 200,
                    body: {
                        otpToken: result
                    }
                },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /points/redeemwithotp:
     *      post:
     *          summary: Redeem with OTP
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              redemptionToken:
     *                                  type: string
     *                              otpCode:
     *                                  type: string
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsRedeemResponse'
     */
    router.post('/redeemwithotp', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.redeemPointsWithOtp(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /points/redeemvalidation:
     *      post:
     *          deprecated: true
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsRedeemValidation'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point redeem success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsRedeemResponseOne'
     */
    router.post('/redeemvalidation', authorizer, async (req, res, next) => {
        try {
            const ownerId = req.user['id'];
            const payload = req.body;
            const result = await PointsHandler.redeemPointsValidation(ownerId, payload);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /points/negative/redeem:
     *      post:
     *          summary: Redeem points (member negative allowed)
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsRedeem'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsRedeemResponse'
     */
    router.post('/negative/redeem', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.redeemPointsNegativeRequest(
                organizationId,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /points/negative/adjust:
     *      post:
     *          summary: Adjust points (member negative allowed)
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/PointsAdjust'
     *          tags:
     *              - Points
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Point adjust success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/PointsAdjustResponse'
     */
    router.post('/negative/adjust', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await PointsHandler.adjustPointsNegativeRequest(
                organizationId,
                payload,
                userId,
                ability,
                null,
                false,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    // Points reversal route
    router.post(
        '/reverse/bill',
        authorizer,
        createValidator(validators.pointsReversalBill.schemas, validators.pointsReversalBill.routeConfig),
        async (req, res, next) => {
            try {
                const payload = req.body;
                const userId = req.user['id'];
                const organizationId = req.user['organizationId'];
                const ability = req.user['ability'];
                const boundary = req.user['boundary'];

                const { result, ...rest } = await PointsHandler.pointsReversalBillRequest(
                    organizationId,
                    payload,
                    userId,
                    ability,
                    boundary
                );

                next({
                    response: { status: 201, body: result }, // Match the status code
                    ...rest
                });
            } catch (err) {
                next(err);
            }
        }
    );

    // Points reversal OTP request route
    router.post(
        '/reverse/billwithotprequest',
        authorizer,
        createValidator(
            validators.pointsReversalBillOtpRequest.schemas,
            validators.pointsReversalBillOtpRequest.routeConfig
        ),
        async (req, res, next) => {
            try {
                const payload = req.body;
                const userId = req.user['id'];
                const organizationId = req.user['organizationId'];
                const ability = req.user['ability'];
                const boundary = req.user['boundary'];
                const { result, ...rest } = await PointsHandler.pointsReversalBillWithOtpRequest(
                    organizationId,
                    payload,
                    userId,
                    ability,
                    boundary
                );
                next({
                    response: { status: 200, body: result },
                    ...rest
                });
            } catch (err) {
                next(err);
            }
        }
    );

    // Points reversal with OTP route
    router.post(
        '/reverse/billwithotp',
        authorizer,
        createValidator(validators.pointsReversalBillWithOtp.schemas, validators.pointsReversalBillWithOtp.routeConfig),
        async (req, res, next) => {
            try {
                const payload = req.body;
                const userId = req.user['id'];
                const organizationId = req.user['organizationId'];
                const ability = req.user['ability'];
                const boundary = req.user['boundary'];
                const { result, ...rest } = await PointsHandler.pointsReversalBillWithOtp(
                    organizationId,
                    payload,
                    userId,
                    ability,
                    boundary
                );
                next({
                    response: { status: 200, body: result },
                    ...rest
                });
            } catch (err) {
                next(err);
            }
        }
    );

    return router;
};

// Export validators for swagger generation
module.exports.getValidators = () => validators;
