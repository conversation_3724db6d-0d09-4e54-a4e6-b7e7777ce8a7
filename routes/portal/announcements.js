'use strict';
const express = require('express');
const router = express.Router();
const AnnouncementsHandler = require('./../../lib/handlers/portal/PortalAnnouncementsHandler');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      Announcement:
     *          type: object
     *          required:
     *              - title
     *              - description
     *              - imageUrl
     *              - status
     *          properties:
     *              title:
     *                  type: string
     *              description:
     *                  type: string
     *              imageUrl:
     *                  type: string
     *              status:
     *                  type: string
     *
     * /portal/announcements:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Announcements
     *          parameters:
     *              - name: limit
     *                schema:
     *                  type: integer
     *                in: query
     *              - name: skip
     *                schema:
     *                  type: integer
     *                in: query
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get announcements success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: array
     *                              items:
     *                                  $ref: '#/definitions/Announcement'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const ownerId = req.user['ownerId'];
            const contactId = req.user['contactId'];
            const result = await AnnouncementsHandler.getAnnouncements(ownerId, contactId);
            res.status(200).send(result);
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    return router;
}