'use strict';
const express = require('express');
const router = express.Router();
const ConfigsHandler = require('./../../lib/handlers/ConfigsHandler');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      Config:
     *          type: object
     *          properties:
     *              botEnabled:
     *                  type: boolean
     *              pointCollectForm:
     *                  type: object
     *              reports:
     *                  type: object
     *              pointRedeemForm:
     *                  type: object
     *              points:
     *                  type: object
     *              registrationForm:
     *                  type: object
     *              rewards:
     *                  type: object
     *              tiers:
     *                  type: object
     *              portalOtpSenderId:
     *                  type: string
     *              apiKey:
     *                  type: string
     *              isSystemLoyaltyId:
     *                  type: boolean
     *              klipConfig:
     *                  type: object
     *
     * /portal/configs:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Configs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get config success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Config'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            let ownerId = req.user['ownerId'];
            const result = await ConfigsHandler.getConfig(ownerId);
            res.status(200).send(result);
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    return router;
}
