'use strict';
const express = require('express');
const router = express.Router();
const PaginationValidator = require('./../../lib/validators/portal/PaginationValidator');
const MembersQueryValidator = require('./../../lib/validators/Analytics/MembersQueryValidator');
const AnalyticsMembersHandler = require('./../../lib/handlers/Analytics/AnalyticsMembersHandler');
const PortalMembersHandler = require('./../../lib/handlers/portal/PortalMembersHandler');
const obtainOrganization = require('../../lib/middlewares/obtain.organization.middleware');
const PortalMembersValidator = require('../../lib/validators/portal/PortalMembersValidator');

module.exports = (authorizer) => {
  /**
   * @openapi
   *
   * definitions:
   *      MemberTier:
   *          type: object
   *          properties:
   *              tierId:
   *                  type: string
   *              lastUpdatedOn:
   *                  type: string
   *                  format: date
   *      MemberCardNo:
   *          type: object
   *          properties:
   *              cardNo:
   *                type: string
   *                required: true
   *      MemberMobileNumber:
   *          type: object
   *          properties:
   *              mobileNumber:
   *                type: string
   *                required: true
   *      MemberTierData:
   *          type: object
   *          properties:
   *              name:
   *                  type: string
   *              imageUrl:
   *                  type: string
   *              benefits:
   *                  type: array
   *                  items:
   *                      type: string
   *              points:
   *                  type: string
   *              status:
   *                  type: string
   *      MemberRewardMetadata:
   *          type: object
   *          properties:
   *              rewardId:
   *                  type: string
   *              refNumber:
   *                  type: string
   *              refName:
   *                  type: string
   *              notes:
   *                  type: notes
   *      MemberIdentification:
   *          type: object
   *          properties:
   *              identificationType:
   *                  type: string
   *                  enum: [NATIONAL_ID,DRIVER_LICENSE,PASSPORT]
   *              identificationNumber:
   *                  type: string
   *      AffinityGroup:
   *          type: object
   *          readOnly: true
   *          properties:
   *              affinityGroupId:
   *                  type: string
   *              joinedDate:
   *                  type: string
   *                  format: date
   *              expiryDate:
   *                  type: string
   *                  format: date
   *              lastJobOn:
   *                  type: string
   *                  format: date
   *              lastJobId:
   *                  type: string
   *              details:
   *                  type: object
   *                  properties:
   *                      name:
   *                          type: string
   *      MemberDetails:
   *          type: object
   *          properties:
   *              _id:
   *                  type: string
   *                  readOnly: true
   *              organizationId:
   *                  type: string
   *                  readOnly: true
   *              loyaltyId:
   *                  type: string
   *                  readOnly: true
   *              profilePicture:
   *                  type: string
   *              parentMemberId:
   *                  type: string
   *              regionId:
   *                  type: string
   *              merchantLocationId:
   *                  type: string
   *              description:
   *                  type: string
   *              type:
   *                  type: string
   *                  enum: [PRIMARY,SECONDARY,CHARITY]
   *                  default: PRIMARY
   *              status:
   *                  type: string
   *                  enum: [ACTIVE, DEACTIVE, SUSPENDED, ARCHIVED]
   *                  readOnly: true
   *              points:
   *                  type: integer
   *                  readOnly: true
   *                  format: int64
   *              identifications:
   *                  type: array
   *                  items:
   *                      $ref: '#/definitions/MemberIdentification'
   *              tierPoints:
   *                  type: integer
   *                  readOnly: true
   *                  format: int64
   *              purchasesCount:
   *                  type: integer
   *                  readOnly: true
   *                  format: int64
   *              purchasesValue:
   *                  type: integer
   *                  readOnly: true
   *                  format: int64
   *              tags:
   *                  type: array
   *                  items:
   *                      type: string
   *              lastSeenOn:
   *                  type: string
   *                  format: date
   *                  readOnly: true
   *              registeredOn:
   *                  type: string
   *                  format: date
   *              firstName:
   *                  type: string
   *              lastName:
   *                  type: string
   *              preferredName:
   *                  type: string
   *              mobileNumber:
   *                  type: string
   *                  description: Should be in E.164 format excluding the '+' symbol
   *              additionalPhoneNumbers:
   *                  type: array
   *                  items:
   *                      type: string
   *              companyName:
   *                  type: string
   *              occupation:
   *                  type: string
   *              countryCode:
   *                  type: string
   *              country:
   *                  type: string
   *              email:
   *                  type: string
   *              isValidEmail:
   *                  readOnly: true
   *                  type: boolean
   *              isValidMobileNumber:
   *                  readOnly: true
   *                  type: boolean
   *              birthDate:
   *                  type: string
   *                  format: date
   *              gender:
   *                  type: string
   *                  enum: [MALE, FEMALE, OTHER]
   *              residentialAddress:
   *                  $ref: '#/definitions/Address'
   *              postalAddress:
   *                  $ref: '#/definitions/Address'
   *              createdBy:
   *                  type: string
   *                  readOnly: true
   *              updatedBy:
   *                  type: string
   *                  readOnly: true
   *              registerMethod:
   *                  type: string
   *                  enum: [ADMIN_PORTAL,CUSTOMER_PORTAL]
   *                  readOnly: true
   *              rewardMetadata:
   *                  type: array
   *                  readOnly: true
   *                  items:
   *                      $ref: '#/definitions/MemberRewardMetadata'
   *              affinityGroupId:
   *                  type: string
   *              affinityGroup:
   *                  readOnly: true
   *                  $ref: '#/definitions/AffinityGroup'
   *              tier:
   *                  readOnly: true
   *                  $ref: '#/definitions/MemberTier'
   *              tierData:
   *                  readOnly: true
   *                  $ref: '#/definitions/MemberTierData'
   *              customAttributes:
   *                  description: any other attributes
   *                  type: object
   *              notificationPreference:
   *                  type: object
   *                  properties:
   *                      preferredChannel:
   *                          type: string
   *                          enum: [EMAIL, MOBILE, EMAIL_AND_MOBILE]
   *                      allowPromotionalNotifications:
   *                          type: boolean
   *          required:
   *              - regionId
   *      MemberPortalMetadata:
   *          type: object
   *          properties:
   *              username:
   *                  type: string
   *                  readOnly: true
   *              userId:
   *                  type: string
   *                  readOnly: true
   *              lastAccessedOn:
   *                  type: string
   *                  format: date-time
   *              platforms:
   *                  type: array
   *                  items:
   *                      type: string
   *                      enum: [WEB,MOBILE,WATCH]
   *              mobileApp:
   *                  type: boolean
   *      MemberProfile:
   *          allOf:
   *              - $ref: '#/definitions/MemberDetails'
   *              - type: object
   *                properties:
   *                  portalMetadata:
   *                      $ref: '#/definitions/MemberPortalMetadata'
   *
   *      secondaryMember:
   *          type: object
   *          properties:
   *              merchantLocationId:
   *                  type: string
   *              identifications:
   *                  type: array
   *                  items:
   *                      $ref: '#/definitions/MemberIdentification'
   *              firstName:
   *                  type: string
   *              lastName:
   *                  type: string
   *              preferredName:
   *                  type: string
   *              mobileNumber:
   *                  type: string
   *                  description: Should be in E.164 format excluding the '+' symbol
   *              countryCode:
   *                  type: string
   *              country:
   *                  type: string
   *              email:
   *                  type: string
   *              birthDate:
   *                  type: string
   *                  format: date
   *              gender:
   *                  type: string
   *                  enum: [MALE, FEMALE, OTHER]
   *              residentialAddress:
   *                  $ref: '#/definitions/Address'
   *              status:
   *                  type: string
   *                  enum: [ACTIVE, DEACTIVE, SUSPENDED]
   *                  readOnly: true
   *              points:
   *                  type: number
   *              tierPoints:
   *                  type: number
   *              cards:
   *                  type: array
   *                  items:
   *                      type: object
   *                      properties:
   *                          cardNo:
   *                              type: number
   *                          status:
   *                              type: string
   *                              enum: [ASSIGNED]
   *
   *      Member:
   *          type: object
   *          properties:
   *              _id:
   *                  type: string
   *              firstName:
   *                  type: string
   *              lastName:
   *                  type: string
   *              preferredName:
   *                  type: string
   */

  /**
   * @openapi
   *
   * /portal/members/validate:
   *      post:
   *          produces:
   *              - application/json
   *          summary: Get member using card number
   *          tags:
   *              - Members
   *          requestBody:
   *              content:
   *                  application/json:
   *                      schema:
   *                       oneOf:
   *                         - $ref: '#/definitions/MemberCardNo'
   *                         - $ref: '#/definitions/MemberMobileNumber'
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Member get success
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/Member'
   */
  router.post('/validate', obtainOrganization, authorizer, async (req, res, next) => {
      try {
        const payload = req.body;
        const organizationId = req.organizationId;
        const result = await PortalMembersHandler.getPortalMemberByAttribute(
          payload,
          organizationId,
        );
        res.status(200).send(result);
      } catch (err) {
        next(err);
      }
    },
  );

  /**
   * @openapi
   *
   * /portal/members:
   *      get:
   *          produces:
   *              - application/json
   *          summary: Get secondary members
   *          tags:
   *              - Members
   *          security:
   *              - Token: []
   *          parameters:
   *              - name: limit
   *                in: query
   *                required: true
   *                type: number
   *              - name: skip
   *                in: query
   *                required: true
   *                type: number
   *          responses:
   *              200:
   *                  description: Members get success
   *                  content:
   *                      application/json:
   *                          schema:
   *                              type: object
   *                              properties:
   *                                  limit:
   *                                      type: number
   *                                  skip:
   *                                      type: number
   *                                  total:
   *                                      type: number
   *                                  items:
   *                                      $ref: '#/definitions/secondaryMember'
   *
   */
  router.get('/', obtainOrganization, authorizer, async (req, res, next) => {
    try {
      const organizationId = req['organizationId'];
      const memberId = req.user['id'];
      const validatedObj = await PaginationValidator.isValidGet(req.query);
      const result = await PortalMembersHandler.getSecondaryMembersPortal(
        organizationId,
        memberId,
        validatedObj,
      );
      res.status(200).send({
        limit: validatedObj.limit,
        skip: validatedObj.skip,
        total: result.total,
        items: result.items,
      });
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /portal/members/filter:
   *      get:
   *          produces:
   *              - application/json
   *          summary: Get member profile by filtering
   *          tags:
   *              - Members
   *          parameters:
   *              - name: filterBy
   *                in: query
   *                required: true
   *                schema:
   *                  type: string
   *                  enum: [MOBILE_NUMBER, EMAIL]
   *              - name: mobileNumber
   *                in: query
   *                required: false
   *                type: string
   *              - name: email
   *                in: query
   *                required: false
   *                type: string
   *          responses:
   *              200:
   *                  description: Member filter success
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/MemberProfile'
   *
   */
  router.get('/filter', obtainOrganization, async (req, res, next) => {
    try {
      const organizationId = req['organizationId'];
      const validatedObj = await PortalMembersValidator.portalFilterValidation(
        req.query,
      );

      const result = await PortalMembersHandler.getMemberProfileByFilterPortal(
        organizationId,
        validatedObj,
      );
      res.status(200).send(result);
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /portal/members/points-subtransactiontypes/counts:
   *      get:
   *          produces:
   *              - application/json
   *          summary: Get member points breakdown by sub transaction type
   *          tags:
   *              - Members
   *          parameters:
   *              - name: limit
   *                in: query
   *                type: number
   *                required: true
   *              - name: skip
   *                in: query
   *                type: number
   *                required: true
   *              - name: fromDate
   *                in: query
   *                required: false
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: toDate
   *                in: query
   *                required: false
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: transactionType
   *                in: query
   *                schema:
   *                   type: string
   *                   enum: [COLLECTION,ADJUSTMENT,REDEMPTION]
   *              - name: subtransactiontypes
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *          responses:
   *              200:
   *                  description: Report get success
   *                  content:
   *                      application/json:
   *                          schema:
   *                              type: object
   *                              properties:
   *                                  subTypeId:
   *                                      type: string
   *                                  name:
   *                                      type: string
   *                                  transactionType:
   *                                      type: string
   *                                  isVisibleToUser:
   *                                      type: string
   *                                  totalTransactions:
   *                                      type: number
   *                                  totalPoints:
   *                                      type: number
   *
   */
  router.get('/points-subtransactiontypes/counts', obtainOrganization, authorizer, async (req, res, next) => {
    try {
      const organizationId = req['organizationId'];
      const memberId = req.user['id'];
      const validatedObj = await MembersQueryValidator.isValidSubTransactionWisePointsGetPortal(req.query);
      validatedObj.memberId = memberId;
      const result = await AnalyticsMembersHandler.getSubTransactionWiseMemberPointsReport(organizationId, validatedObj);
      res.status(200).send(result);
    } catch (err) {
      next(err);
    }
  });

  return router;
};
