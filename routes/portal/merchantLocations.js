'use strict';
const express = require('express');
const router = express.Router();
const MerchantLocationsHandler = require('./../../lib/handlers/MerchantLocationsHandler');
const PaginationValidator = require('./../../lib/validators/portal/PaginationValidator');
const PortalRegionsValidator = require('./../../lib/validators/portal/PortalRegionsValidator');
const obtainOrganization = require('../../lib/middlewares/obtain.organization.middleware');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      Contact:
     *          type: object
     *          properties:
     *              mobileNumber:
     *                  type: string
     *              email:
     *                  type: string
     *              address:
     *                  $ref: '#/definitions/Address'
     *      Location:
     *          type: object
     *          properties:
     *              merchantId:
     *                  type: string
     *              locationName:
     *                  type: string
     *              contact:
     *                  $ref: '#/definitions/Contact'
     *              isPickupLocation:
     *                  type: boolean
     *              code:
     *                  type: string
     *              createdOn:
     *                  type: string
     *                  format: date-time
     *                  readOnly: true
     *
     *      LocationsResponse:
     *          type: object
     *          properties:
     *              limit:
     *                  type: number
     *              skip:
     *                  type: number
     *              total:
     *                  type: number
     *              items:
     *                  type: array
     *                  items:
     *                      $ref: '#/definitions/Location'
     */

    /**
     * @openapi
     * /portal/locations:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Locations
     *          security:
     *              - Token: []
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *          responses:
     *              200:
     *                  description: Locations get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/LocationsResponse'
     */
    router.get('/', obtainOrganization, authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const regionId = req.user['regionId'];
            const validatedObj = await PaginationValidator.isValidGet(req.query);
            const result = await MerchantLocationsHandler.getLocationsPortal(organizationId, regionId, validatedObj);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /portal/locations/public:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Locations
     *          security: []
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *          responses:
     *              200:
     *                  description: Locations get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  limit:
     *                                      type: number
     *                                  skip:
     *                                      type: number
     *                                  total:
     *                                      type: number
     *                                  items:
     *                                      type: array
     *                                      items:
     *                                          type: object
     *                                          properties:
     *                                              locationName:
     *                                                  type: string
     */
    router.get('/public', obtainOrganization, async (req, res, next) => {
        try {
            const organizationId = req['organizationId'];
            const validatedObj = await PortalRegionsValidator.isValidGet(req.query);
            const result = await MerchantLocationsHandler.getLocationsPortalPublic(organizationId, validatedObj);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
}
