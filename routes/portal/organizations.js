'use strict';
const express = require('express');
const router = express.Router();
const OrganizationHandler = require('./../../lib/handlers/OrganizationHandler');
const obtainOrganization = require('../../lib/middlewares/obtain.organization.middleware');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      Region:
     *          type: object
     *          properties:
     *              _id:
     *                  type: string
     *              regionName:
     *                  type: string
     *              defaultCountryISO2Code:
     *                  type: string
     *              defaultCurrencyCode:
     *                  type: string
     *              regionIconUrl:
     *                  type: string
     *
     *      Address:
     *          type: object
     *          properties:
     *              line1:
     *                  type: string
     *              line2:
     *                  type: string
     *              line3:
     *                  type: string
     *              city:
     *                  type: string
     *              stateOrProvince:
     *                  type: string
     *              zipOrPostcode:
     *                  type: string
     *
     *      Organization:
     *          type: object
     *          properties:
     *              organizationName:
     *                  type: string
     *              organizationLogoImageUrl:
     *                  type: string
     *              address:
     *                  $ref: '#/definitions/Address'
     *              regions:
     *                  type: array
     *                  items:
     *                      $ref: '#/definitions/Region'
     *              configuration:
     *                  type: object
     *                  properties:
     *                      baseCountryCurrencyCode:
     *                          type: string
     */

    /**
     * @openapi
     * /portal/organizations:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Organizations
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Organization get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Organization'
     */
    router.get('/', obtainOrganization, authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const result = await OrganizationHandler.getOrganizationPortal(organizationId);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    return router;
}
