'use strict';
const express = require('express');
const router = express.Router();
const obtainOrganization = require('../../lib/middlewares/obtain.organization.middleware');
const PublicDataHandler = require('../../lib/handlers/portal/PublicDataHandler');
const logger = require('../../lib/logger');
const config = require('../../lib/config');
const log = logger(config.logger);
const { portalPublicDataQueryParamValidationSchema } = require('../../lib/validators/portal/PortalPublicDataValidator');
const validators = {
    getPublicData: {
        schemas: {
            query: portalPublicDataQueryParamValidationSchema,
            response: {
                type: 'object',
                properties: {
                    organization: { $ref: '#/definitions/Organization' },
                    region: { $ref: '#/definitions/Region' },
                    merchants: {
                        type: 'array',
                        items: { $ref: '#/definitions/Merchant' }
                    },
                    locations: {
                        type: 'array',
                        items: { $ref: '#/definitions/Location' }
                    },
                    pointRules: {
                        type: 'array',
                        items: { $ref: '#/definitions/PointRule' }
                    },
                    tiers: {
                        type: 'array',
                        items: { $ref: '#/definitions/Tier' }
                    }
                }
            }
        },
        routeConfig: {
            path: '/portal/publicdata',
            method: 'get',
            tags: ['Public Data'],
            security: [{ Token: [] }],
            summary: 'Get public data',
            description: 'Fetches public data for the organization and region.',
            responses: {
                200: {
                    description: 'Get public data success',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    organization: { $ref: '#/definitions/Organization' },
                                    region: { $ref: '#/definitions/Region' },
                                    merchants: {
                                        type: 'array',
                                        items: { $ref: '#/definitions/Merchant' }
                                    },
                                    locations: {
                                        type: 'array',
                                        items: { $ref: '#/definitions/Location' }
                                    },
                                    pointRules: {
                                        type: 'array',
                                        items: { $ref: '#/definitions/PointRule' }
                                    },
                                    tiers: {
                                        type: 'array',
                                        items: { $ref: '#/definitions/Tier' }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = () => {
    router.get('/', obtainOrganization, async (req, res, next) => {
        try {
            log.info('Received request for public data');
            const organizationId = req['organizationId'];
            const regionId = req.query['regionId'];

            log.info(`Fetching public data for organizationId: ${organizationId}, regionId: ${regionId}`);

            const result = await PublicDataHandler.getPublicData(organizationId, regionId);
            res.status(200).send(result);
        } catch (err) {
            log.error('Error in public data endpoint:', err);
            next(err);
        }
    });

    return router;
};

module.exports.getValidators = () => validators;
