'use strict';
const express = require('express');
const router = express.Router();
const RegionsHandler = require('./../../lib/handlers/RegionsHandler');
const obtainOrganization = require('../../lib/middlewares/obtain.organization.middleware');
const PaginationValidator = require('./../../lib/validators/portal/PaginationValidator');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      RegionalPointConfiguration:
     *          type: object
     *          properties:
     *              minPointRedemptionAmount:
     *                  type: number
     *              maxPointRedemptionAmount:
     *                  type: number
     *              minPointsBalanceForRedemption:
     *                  type: number
     *              pointExpiryMethod:
     *                  type: string
     *                  enum: [FIXED,ROLLING]
     *              pointExpiryStartMonth:
     *                  type: number
     *              pointExpiryEndMonth:
     *                  type: number
     *              pointExpiryPeriod:
     *                  type: number
     *              pointExpiryGracePeriod:
     *                  type: number
     *              currencyAmountPerPoint:
     *                  type: number
     *              regionalPointConversionRates:
     *                  type: array
     *                  items:
     *                    type: object
     *                    properties:
     *                      destinationRegionId:
     *                        type: string
     *                      rate:
     *                        type: number
     *
     *      SupportInfo:
     *          type: object
     *          properties:
     *              phoneNumbers:
     *                  type: array
     *                  items:
     *                    type: string
     *              email:
     *                  type: string
     *              whatsappNumber:
     *                  type: string
     *
     *      Region:
     *          type: object
     *          properties:
     *              _id:
     *                  type: string
     *                  readOnly: true
     *              regionName:
     *                  type: string
     *              defaultCountryISO2Code:
     *                  type: string
     *              defaultCurrencyCode:
     *                  type: string
     *              regionIconUrl:
     *                  type: string
     *              defaultMerchantId:
     *                  type: string
     *              defaultMerchantLocationId:
     *                  type: string
     *              pointConfiguration:
     *                  $ref: '#/definitions/RegionalPointConfiguration'
     *              supportInfo:
     *                  $ref: '#/definitions/SupportInfo'
     *
     *      RegionPublic:
     *          type: object
     *          properties:
     *              _id:
     *                  type: string
     *              regionName:
     *                  type: string
     *              defaultCountryISO2Code:
     *                  type: string
     *              supportInfo:
     *                  $ref: '#/definitions/SupportInfo'
     */

    /**
     * @openapi
     * /portal/regions:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Regions
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Regional point configuration get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Region'
     */
    router.get('/', obtainOrganization, authorizer, async (req, res, next) => {
        try {
            const regionId = req.user['regionId'];
            const result = await RegionsHandler.getRegionPortal(regionId);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /portal/regions/public:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Regions
     *          security: []
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *          responses:
     *              200:
     *                  description: Regions get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  limit:
     *                                      type: number
     *                                  skip:
     *                                      type: number
     *                                  total:
     *                                      type: number
     *                                  items:
     *                                      type: array
     *                                      items:
     *                                          $ref: '#/definitions/RegionPublic'
     */
    router.get('/public', obtainOrganization, async (req, res, next) => {
        try {
            const organizationId = req['organizationId'];
            const { skip, limit } = await PaginationValidator.isValidGet(req.query);
            const result = await RegionsHandler.getRegionsPortal(organizationId, skip, limit);
            res.status(200).send({
                limit: limit,
                skip: skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
}
