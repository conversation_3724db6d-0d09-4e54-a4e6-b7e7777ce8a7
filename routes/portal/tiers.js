'use strict';
const express = require('express');
const router = express.Router();
const TiersHandler = require('./../../lib/handlers/TiersHandler');
const PaginationValidator = require('./../../lib/validators/portal/PaginationValidator');
const obtainOrganization = require('../../lib/middlewares/obtain.organization.middleware');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      Tier:
     *          type: object
     *          properties:
     *              name:
     *                  type: string
     *                  required: true
     *              imageUrl:
     *                  type: string
     *                  required: true
     *              benefits:
     *                  required: true
     *                  type: array
     *                  items:
     *                      type: string
     *              points:
     *                  required: true
     *                  type: number
     *
     * /portal/tiers:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - Tiers
     *          security:
     *              - Token: []
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *          responses:
     *              200:
     *                  description: Tiers get success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  limit:
     *                                      type: number
     *                                  skip:
     *                                      type: number
     *                                  total:
     *                                      type: number
     *                                  items:
     *                                      type: array
     *                                      items:
     *                                          $ref: '#/definitions/Tier'
     */
    router.get('/', obtainOrganization, authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const regionId = req.user['regionId'];
            const validatedObj = await PaginationValidator.isValidGet(req.query);
            validatedObj.regionId = regionId;
            const result = await TiersHandler.getTiersPortal(organizationId, validatedObj);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
}
