'use strict';

const express = require('express');
const router = express.Router();
const createValidator = require('../lib/middlewares/joi.validation.middleware');
const ProductCatalogueHandler = require('../lib/handlers/ProductCatalogueHandler');
const ProductCatalogueValidator = require('../lib/validators/ProductCatalogueValidator');

// * Define validators for Swagger generation.
const validators = {
    productCatalogueGet: {
        schemas: { query: ProductCatalogueValidator.productCatalogueGetSchema },
        routeConfig: {
            path: '/productcatalogues',
            method: 'get',
            tags: ['Product Catalogues'],
            summary: 'Get product catalogues',
            description: 'Get product catalogues. Requires "regionId", "limit" and "skip" values.',
            responses: {
                200: {
                    description: 'Get product catalogues successful',
                    schema: ProductCatalogueValidator.productCatalogueGetResponseSchema
                }
            }
        }
    },
    productCatalogueExport: {
        schemas: { body: ProductCatalogueValidator.productCatalogueExportSchema },
        routeConfig: {
            path: '/productcatalogues/export',
            method: 'post',
            tags: ['Product Catalogues'],
            summary: 'Export product catalogues',
            description: 'Export product catalogues. Requires "regionId" value.',
            responses: {
                200: {
                    description: 'Export product catalogues successful',
                    schema: ProductCatalogueValidator.productCatalogueExportResponseSchema
                }
            }
        }
    }
};

module.exports = (authorizer) => {
    // * Get product catalogues endpoint.
    router.get(
        '/',
        authorizer,
        createValidator(validators.productCatalogueGet.schemas, validators.productCatalogueGet.routeConfig),
        async (req, res, next) => {
            try {
                const organizationId = req.user['organizationId'];
                const ability = req.user['ability'];
                const boundary = req.user['boundary'];

                const validatedQuery = await ProductCatalogueValidator.isValidGet(req.query);
                const { limit, skip, ...otherQuery } = validatedQuery;

                const { total, items } = await ProductCatalogueHandler.getProductCatalogues(
                    organizationId,
                    limit,
                    skip,
                    otherQuery,
                    ability,
                    boundary
                );
                res.status(200).send({ limit, skip, total, items });
            } catch (err) {
                next(err);
            }
        }
    );
    // * Export product catalogues endpoint.
    router.post(
        '/export',
        authorizer,
        createValidator(validators.productCatalogueExport.schemas, validators.productCatalogueExport.routeConfig),
        async (req, _res, next) => {
            try {
                const organizationId = req.user['organizationId'];
                const ability = req.user['ability'];
                const boundary = req.user['boundary'];
                const userId = req.user['id'];

                const validatedFilters = await ProductCatalogueValidator.isValidExport(req.body);

                const { result, ...rest } = await ProductCatalogueHandler.exportProductCatalogues(
                    organizationId,
                    validatedFilters,
                    userId,
                    ability,
                    boundary
                );
                next({
                    response: { status: 200, body: result },
                    ...rest
                });
            } catch (err) {
                next(err);
            }
        }
    );

    return router;
};

// * Export validators for Swagger generation.
module.exports.getValidators = () => validators;
