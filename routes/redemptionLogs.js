'use strict';
const express = require('express');
const router = express.Router();
const RedemptionLogsHandler = require('./../lib/handlers/RedemptionLogsHandler');
const QueryValidator = require('../lib/validators/QueryValidators/RedemptionLogsQueryValidator');
const {authorize, OPERATION} = require('@shoutout-labs/express-authorizer-middleware');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');
const MODULE_ID = require('@shoutout-labs/constants').AUTH_MODULE_IDS.LOYALTY.MODULE_ID_REDEMPTION_LOGS;

/**
 * @openapi
 *
 * definitions:
 *      History:
 *          type: object
 *          properties:
 *              eventDate:
 *                  type: string
 *                  format: date-time
 *              eventDetails:
 *                  type: string
 *              eventBy:
 *                  type: string
 *
 *      RewardMetadata:
 *          type: object
 *          properties:
 *              partnerRefNumber:
 *                  type: string
 *              partnerRefName:
 *                  type: string
 *              partnerNotes:
 *                  type: string
 *              claimLocation:
 *                      type: string
 *
 *      RedemptionLog:
 *          type: object
 *          properties:
 *                  organizationId:
 *                      type: string
 *                  regionId:
 *                      type: string
 *                  memberId:
 *                      type: string
 *                  rewardId:
 *                      type: string
 *                  distributionJobId:
 *                      type: string
 *                  transactionId:
 *                      type: string
 *                  pointsRedeemed:
 *                      type: number
 *                  status:
 *                      type: string
 *                      enum: [REQUESTED,READY,CLAIMED,CANCELLED,PROCESSING,COMPLETED,FAILED,REFUNDED]
 *                  processingStatus:
 *                      type: string
 *                      enum: [PENDING,PROCESSING,DISPATCHED,COMPLETED,FAILED]
 *                  rewardType:
 *                      type: string
 *                      enum: [DIGITAL,TANGIBLE]
 *                  rewardSubType:
 *                      type: string
 *                      enum: [VOUCHER,PARTNER,INSTANT]
 *                  refundStatus:
 *                      type: string
 *                      enum: [NONE,REFUNDED]
 *                  notes:
 *                      type: string
 *                  metadata:
 *                      $ref: '#/definitions/RewardMetadata'
 *                  reward:
 *                      readOnly: true
 *                      type: object
 *                      properties:
 *                          name:
 *                              type: string
 *                          imageUrls:
 *                              type: array
 *                              items:
 *                                  type: string
 *                  pickupLocation:
 *                      readOnly: true
 *                      type: object
 *                      properties:
 *                          locationName:
 *                              type: string
 *                  historyEvents:
 *                      type: array
 *                      items:
 *                        $ref: '#/definitions/History'
 *                  createdOn:
 *                      type: string
 *                      format: date-time
 *                      readOnly: true
 *                  updatedOn:
 *                      type: string
 *                      format: date-time
 *                      readOnly: true
 *                  createdBy:
 *                      type: string
 *                      readOnly: true
 *                  updatedBy:
 *                      type: string
 *                      readOnly: true
 *
 *      RedemptionLogsSummaryResponse:
 *          type: object
 *          properties:
 *                  rewardId:
 *                      type: string
 *                  date:
 *                      type: string
 *                      format: date
 *                  totalCount:
 *                      type: number
 *
 *
 *      RedemptionLogsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/RedemptionLog'
 *
 *
 *
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /redemptionlogs:
     *      get:
     *          summary: Get a list of redemption logs
     *          produces:
     *              - application/json
     *          tags:
     *              - Redemption Logs
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: memberId
     *                in: query
     *                type: string
     *              - name: rewardId
     *                in: query
     *                type: string
     *              - name: distributionJobId
     *                in: query
     *                type: string
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: rewardType
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [TANGIBLE,DIGITAL]
     *              - name: rewardSubType
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [VOUCHER,PARTNER,INSTANT]
     *              - name: status
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [REQUESTED,READY,CLAIMED,CANCELLED,PROCESSING,COMPLETED,FAILED,REFUNDED]
     *              - name: statusArray
     *                in: query
     *                required: false
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *                      enum: [REQUESTED,READY,CLAIMED,CANCELLED,PROCESSING,COMPLETED,FAILED,REFUNDED]
     *              - name: processingStatus
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PENDING,PROCESSING,DISPATCHED,COMPLETED,FAILED]
     *              - name: fromDate
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: toDate
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: claimLocationId
     *                in: query
     *                required: false
     *                type: number
     *              - name: partnerRewardSearchField
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  enum: [PARTNER_REF_NUMBER, PARTNER_REF_NAME]
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get redemption logs success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RedemptionLogsResponse'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await QueryValidator.isValid(req.query);
            const result = await RedemptionLogsHandler.getRedemptionLogs(organizationId, validatedObj, ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/{id}:
     *      get:
     *          summary: Get a reward redemption log
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a reward redemption log is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RedemptionLog'
     */
    router.get('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await RedemptionLogsHandler.getRedemptionLog(id, organizationId, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/summary:
     *      get:
     *          deprecated: true
     *          produces:
     *              - application/json
     *          tags:
     *              - Redemption Logs
     *          parameters:
     *              - name: rewardId
     *                type: string
     *                in: query
     *              - name: contactId
     *                type: string
     *                in: query
     *              - name: fromDate
     *                type: string
     *                in: query
     *                format: date
     *              - name: toDate
     *                type: string
     *                in: query
     *                format: date
     *              - name: rewardType
     *                type: string
     *                enum: [VOUCHER,DIGITAL]
     *                in: query
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get redemption logs summary success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: array
     *                              items:
     *                                  $ref: '#/definitions/RedemptionLogsSummaryResponse'
     */
    router.get('/summary', authorizer, authorize(MODULE_ID, OPERATION.READ), async (req, res, next) => {
        try {
            const ownerId = req.user['id'];
            const validatedObj = await QueryValidator.isValidSummary(req.query);
            const result = await RedemptionLogsHandler.getLogsSummary(ownerId, validatedObj.rewardId, validatedObj.contactId, validatedObj.fromDate, validatedObj.toDate, validatedObj.rewardType);
            res.status(200).send(result);
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/{id}:
     *      put:
     *          summary: Update a reward redemption log
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            status:
     *                                type: string
     *                                enum: [REQUESTED,READY,CLAIMED,CANCELLED,PROCESSING,COMPLETED,FAILED,REFUNDED]
     *                            processingStatus:
     *                                type: string
     *                                enum: [PENDING,PROCESSING,DISPATCHED,COMPLETED,FAILED]
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update a reward redemption log is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RedemptionLog'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await RedemptionLogsHandler.updateRewardRedemptionLogWithAuditLogs(payload, id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/cancel/{id}:
     *      put:
     *          summary: Cancel a redemption log item
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            notes:
     *                                type: string
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Cancel successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  transactionSubType:
     *                                      type: string
     *                                  memberId:
     *                                      type: string
     *                                  totalPoints:
     *                                      type: number
     *                                  points:
     *                                      type: number
     */
    router.put('/cancel/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { result, ...rest } = await RedemptionLogsHandler.cancelLogItem(payload, id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/refund/{id}:
     *      put:
     *          summary: Refund a redemption log item
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            notes:
     *                                type: string
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Refund successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  transactionSubType:
     *                                      type: string
     *                                  memberId:
     *                                      type: string
     *                                  totalPoints:
     *                                      type: number
     *                                  points:
     *                                      type: number
     */
    router.put('/refund/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const  { result, ...rest } = await RedemptionLogsHandler.refundLogItem(payload, id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },  
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/partnerrewards/{id}:
     *      put:
     *          summary: Update a partner reward redemption log item's reference number and reference name
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            rewardName:
     *                                type: string
     *                            partnerRefNumber:
     *                                type: string
     *                            partnerRefName:
     *                                type: string
     *                            partnerNotes:
     *                                type: string
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Successfully updated a partner reward redemption log.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RedemptionLog'
     */
    router.put('/partnerrewards/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const  { result, ...rest } = await RedemptionLogsHandler.updatePartnerRewardLogItem(payload, id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },  
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/partnerrewards/bulkrefund:
     *      put:
     *          summary: Refund all failed and processing (if any) log items of a partner reward distribution job
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            distributionJobId:
     *                                type: string
     *                            notes:
     *                                type: string
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Bulk refund successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  eligibleRefundCount:
     *                                      type: number
     */
    router.put('/partnerrewards/bulkrefund', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const  { result, ...rest } = await RedemptionLogsHandler.bulkRefundPartnerRewardLogItems(payload, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },  
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /redemptionlogs/reprocess/{id}:
     *      put:
     *          summary: Reprocess a failed partner reward redemption log item
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            partnerRefNumber:
     *                                type: string
     *                            partnerRefName:
     *                                type: string
     *                            partnerNotes:
     *                                type: string
     *                            bundleValue:
     *                                type: number
     *          tags:
     *              - Redemption Logs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Reprocessing successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RedemptionLog'
     */
    router.put('/reprocess/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const  { result, ...rest } = await RedemptionLogsHandler.reprocessFailedLogItem(payload, id, organizationId, userId, ability, boundary);
            next({
                response: { status: 200, body: result },  
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
}
