'use strict';
const express = require('express');
const router = express.Router();
const CommonQueryValidator = require('../lib/validators/QueryValidators/CommonQueryValidator');
const RegionsHandler = require('../lib/handlers/RegionsHandler');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');

/**
 * @openapi
 *
 * definitions:
 *      MemberStatusUpdateWebhookConfig:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *              description:
 *                  type: string
 *      RegionalPointConfiguration:
 *          type: object
 *          required:
 *             - minPointRedemptionAmount
 *             - maxPointRedemptionAmount
 *             - minPointsBalanceForRedemption
 *             - pointExpiryMethod
 *             - pointExpiryStartMonth
 *             - pointExpiryEndMonth
 *             - pointExpiryPeriod
 *             - pointExpiryGracePeriod
 *             - currencyAmountPerPoint
 *             - regionalPointConversionRates
 *          properties:
 *              minPointRedemptionAmount:
 *                  type: number
 *              maxPointRedemptionAmount:
 *                  type: number
 *              minPointsBalanceForRedemption:
 *                  type: number
 *              pointExpiryMethod:
 *                  type: string
 *                  enum: [FIXED,ROLLING]
 *              pointExpiryStartMonth:
 *                  type: number
 *              pointExpiryEndMonth:
 *                  type: number
 *              pointExpiryPeriod:
 *                  type: number
 *              pointExpiryGracePeriod:
 *                  type: number
 *              currencyAmountPerPoint:
 *                  type: number
 *              jobEnabled:
 *                  type: boolean
 *              jobFrequency:
 *                  type: string
 *              pointExpiryCalculationJobFrequency:
 *                  type: string
 *              pointExpiryCalculationSubTransactionTypeIds:
 *                  type: array
 *                  items:
 *                    type: string
 *              regionalPointConversionRates:
 *                  type: array
 *                  items:
 *                    type: object
 *                    properties:
 *                      destinationRegionId:
 *                        type: string
 *                      rate:
 *                        type: number
 *      RegionalMemberConfiguration:
 *          type: object
 *          properties:
 *              maxSecondaryAccounts:
 *                  type: number
 *              enrolmentForm:
 *                  type: object
 *              defaultAffinityGroupId:
 *                  type: string
 *              affinityGroupExpiryJobEnabled:
 *                  type: boolean
 *              affinityGroupExpiryJobFrequency:
 *                  type: string
 *              segmentsMemberStatsGeneratorJobEnabled:
 *                  type: boolean
 *      InsightCalculations:
 *          type: object
 *          properties:
 *              avgCustomerLifeSpan:
 *                  type: number
 *                  readOnly: true
 *              avgFrequencyRate:
 *                  type: number
 *                  readOnly: true
 *              jobEnabled:
 *                  type: boolean
 *              insightCalculationJobFrequency:
 *                  type: string
 *      TransactionConfigurations:
 *          type: object
 *          properties:
 *              analyticsTransactionSyncJobEnabled:
 *                  type: boolean
 *              transactionSyncRangeInDays:
 *                  type: number
 *      EmailConfiguration:
 *          type: object
 *          properties:
 *              fromAddress:
 *                  type: string
 *      SmsConfiguration:
 *          type: object
 *          properties:
 *              phoneNumber:
 *                  type: string
 *      NotificationConfiguration:
 *          type: object
 *          properties:
 *              emailConfiguration:
 *                  $ref: '#/definitions/EmailConfiguration'
 *              smsConfiguration:
 *                  $ref: '#/definitions/SmsConfiguration'
 *              preBirthdayNotifications:
 *                  type: object
 *                  properties:
 *                      enabled:
 *                          type: boolean
 *                      notifyDaysBefore:
 *                          type: number
 *              birthdayNotifications:
 *                  type: object
 *                  properties:
 *                      enabled:
 *                          type: boolean
 *      ProviderConfiguration:
 *          type: object
 *          properties:
 *              emailProvidersList:
 *                  type: array
 *                  items:
 *                    type: [string]
 *              smsProvidersList:
 *                  type: array
 *                  items:
 *                    type: [string, integer]
 *      WebhookConfiguration:
 *          type: object
 *          properties:
 *              memberStatusUpdate:
 *                  $ref: '#/definitions/MemberStatusUpdateWebhookConfig'
 *      SupportInfo:
 *          type: object
 *          properties:
 *              phoneNumbers:
 *                  type: array
 *                  items:
 *                    type: string
 *              email:
 *                  type: string
 *              whatsappNumber:
 *                  type: string
 *      Region:
 *          type: object
 *          required:
 *             - timeZone
 *          properties:
 *              _id:
 *                  type: string
 *                  readOnly: true
 *              regionName:
 *                  type: string
 *              defaultCountryISO2Code:
 *                  type: string
 *              defaultCurrencyCode:
 *                  type: string
 *              regionIconUrl:
 *                  type: string
 *              defaultMerchantId:
 *                  type: string
 *              defaultMerchantLocationId:
 *                  type: string
 *              defaultMerchant:
 *                  type: object
 *                  properties:
 *                    name:
 *                        type: string
 *              defaultMerchantLocation:
 *                  type: object
 *                  properties:
 *                    name:
 *                        type: string
 *              pointConfiguration:
 *                  $ref: '#/definitions/RegionalPointConfiguration'
 *              memberConfiguration:
 *                  $ref: '#/definitions/RegionalMemberConfiguration'
 *              insightCalculations:
 *                  $ref: '#/definitions/InsightCalculations'
 *              notificationConfiguration:
 *                  $ref: '#/definitions/NotificationConfiguration'
 *              providerConfiguration:
 *                  $ref: '#/definitions/ProviderConfiguration'
 *              webhookConfiguration:
 *                  $ref: '#/definitions/WebhookConfiguration'
 *              transactionConfigurations:
 *                  $ref: '#/definitions/TransactionConfigurations'
 *              supportInfo:
 *                  $ref: '#/definitions/SupportInfo'
 *              timeZone:
 *                  type: string
 *      RegionsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/Region'
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /regions:
     *      get:
     *          summary: Get a list of regions
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                type: number
     *              - name: skip
     *                in: query
     *                type: number
     *          tags:
     *              - Regions
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get regions list successful
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RegionsResponse'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { skip, limit } = await CommonQueryValidator.isValidSkipLimitFifty(req.query);
            const result = await RegionsHandler.getRegions(organizationId, skip, limit, ability, boundary);
            res.status(200).send({
                limit: limit,
                skip: skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /regions:
     *      post:
     *          summary: Create region
     *          produces:
     *              - application/json
     *          tags:
     *              - Regions
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Region'
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create region is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Region'
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const { result, ...rest } = await RegionsHandler.createRegion(payload, organizationId, userId, ability);
            next({
                response: { status: 201, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /regions/{id}:
     *      put:
     *          summary: Update a region
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/Region'
     *          tags:
     *              - Regions
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update a region is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Region'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const { result, ...rest } = await RegionsHandler.updateRegion(payload, id, organizationId, userId, ability);
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
};
