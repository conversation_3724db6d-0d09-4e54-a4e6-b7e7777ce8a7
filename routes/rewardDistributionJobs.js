'use strict';
const express = require('express');
const router = express.Router();
const multer = require('multer');
const { v1: uuidV1 } = require('uuid');
const path = require('path');
const secretConfig = require('./../config');
const USER_ASSETS_BUCKET = secretConfig.USER_ASSETS_BUCKET;
const config = require('../lib/config');
const logger = require('../lib/logger');
const log = logger(config.logger);
const StorageWrapper = require('../lib/wrappers/StorageWrapper');
const RewardDistributionJobsHandler = require('../lib/handlers/RewardDistributionJobsHandler');
const RewardDistributionJobsQueryValidator = require('../lib/validators/QueryValidators/RewardDistributionJobsQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');
const RewardDistributionsImportJobsValidator = require('../lib/validators/RewardDistributionsImportJobsValidator');

const fileFilter = (req, file, cb) => {
  if (~['text/plain', 'text/csv'].indexOf(file.mimetype)) {
    cb(null, true);
  } else {
    cb(null, false);
  }
};

const keyHandlerFunc = (req, file, cb) => {
  const organizationId = req.user['organizationId'];
  // TODO: Add permissions
  // const ability = req.user['ability'];
  //ForbiddenError.from(ability).throwUnlessCan(TRANSACTION.ACTIONS.CREATE_TRANSACTION_IMPORT_JOB, TRANSACTION.MODULE_ID);

  log.debug(`Partner reward import file payload`, req.body);

  RewardDistributionsImportJobsValidator.isValidUpload(req.query)
    .then(({ regionId, distributionJobId }) => {
      // TODO: validate regionId
      cb(null, `${organizationId}/partnerRewardDistributionImports/${regionId}/${distributionJobId}/${uuidV1()}${path.extname(file.originalname)}`);
    })
    .catch(err => {
      cb(err);
    });
}

const upload = multer({
  storage: StorageWrapper.getStorage(USER_ASSETS_BUCKET, keyHandlerFunc),
  fileFilter,
  limits: {
    fileSize: Number(secretConfig.MAX_UPLOAD_FILE_SIZE)
  }
});

/**
 * @openapi
 *
 * definitions:
 *
 *      History:
 *          type: object
 *          properties:
 *              eventDate:
 *                  type: string
 *                  format: date-time
 *              eventDetails:
 *                  type: string
 *              eventBy:
 *                  type: string
 *
 *      RewardDistributionJob:
 *          type: object
 *          required:
 *             - regionId
 *             - rewardId
 *          properties:
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              regionId:
 *                  type: string
 *              merchantId:
 *                  type: string
 *              rewardId:
 *                  type: string
 *              batchId:
 *                  readOnly: true
 *                  type: string
 *              itemCount:
 *                  readOnly: true
 *                  type: number
 *              successCount:
 *                  readOnly: true
 *                  type: number
 *              failedCount:
 *                  readOnly: true
 *                  type: number
 *              status:
 *                   readOnly: true
 *                   type: string
 *                   enum: [PENDING,PROCESSING,DISPATCHED,COMPLETED,FAILED]
 *              historyEvents:
 *                  readOnly: true
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/History'
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              createdBy:
 *                  type: string
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  readOnly: true
 *
 *      FieldMapping:
 *        type: object
 *        properties:
 *            fileColumnName:
 *              type: string
 *            systemAttributeName:
 *              type: string
 *              enum: [REDEMPTION_ID, MEMBER_ID, POINTS_AMOUNT, PARTNER_REF_NUMBER, PARTNER_REF_NAME, PARTNER_NOTES, STATUS]
 *
 *      RewardDistributionsImportJob:
 *        type: object
 *        required:
 *           - regionId
 *           - distributionJobId
 *        properties:
 *            organizationId:
 *                type: string
 *                readOnly: true
 *            regionId:
 *                type: string
 *            merchantId:
 *                type: string
 *            distributionJobId:
 *                type: string
 *            startedOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            completedOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            totalRecordsCount:
 *                type: number
 *                readOnly: true
 *            processedRecordsCount:
 *                type: number
 *                readOnly: true
 *            successRecordsCount:
 *                type: number
 *                readOnly: true
 *            failedRecordsCount:
 *                type: number
 *                readOnly: true
 *            fileId:
 *                type: string
 *                readOnly: true
 *            fieldMappings:
 *                type: array
 *                items:
 *                   $ref: '#/definitions/FieldMapping'
 *            status:
 *                type: string
 *                enum: [PENDING,PROCESSING,COMPLETED,FAILED]
 *                readOnly: true
 *            createdOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            updatedOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            createdBy:
 *                type: string
 *                readOnly: true
 *            updatedBy:
 *                type: string
 *                readOnly: true
 *
 *      RewardDistributionJobsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/RewardDistributionJob'
 */
module.exports = (authorizer) => {

    /**
     * @openapi
     *
     * /rewarddistributionjobs:
     *      get:
     *          summary: Get a list reward distribution jobs
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: createdOnFrom
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: createdOnTo
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: status
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PENDING,PROCESSING,DISPATCHED,COMPLETED,FAILED]
     *              - name: batchId
     *                in: query
     *                required: false
     *                type: number
     *              - name: rewardId
     *                in: query
     *                required: false
     *                type: number
     *              - name: searchKey
     *                in: query
     *                required: false
     *                type: string
     *          tags:
     *              - Reward Distribution Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get reward distribution jobs is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardDistributionJobsResponse'
     */
    router.get('/', authorizer, async (req, res, next) => {
      try {
        const organizationId = req.user['organizationId'];
        const ability = req.user['ability'];
        const boundary = req.user['boundary'];
        const validatedObj = await RewardDistributionJobsQueryValidator.isValidGet(req.query);
        const result = await RewardDistributionJobsHandler.getRewardDistributionJobs(organizationId, validatedObj, ability, boundary);
        res.status(200).send({
          limit: validatedObj.limit,
          skip: validatedObj.skip,
          total: result.total,
          items: result.items
        });
      } catch (err) {
        next(err);
      }
    });

    /**
     * @openapi
     *
     * /rewarddistributionjobs:
     *      post:
     *          summary: Create a reward distribution job
     *          produces:
     *              - application/json
     *          tags:
     *              - Reward Distribution Jobs
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          required:
     *                            regionId
     *                            redemptionLogIds
     *                          properties:
     *                            regionId:
     *                              type: string
     *                            merchantId:
     *                              type: string
     *                            redemptionLogIds:
     *                              type: array
     *                              items:
     *                                type: string
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create reward distribution job is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardDistributionJob'
     */
    router.post('/', authorizer, async (req, res, next) => {
      try {
        const payload = req.body;
        const userId = req.user['id'];
        const organizationId = req.user['organizationId'];
        const ability = req.user['ability'];
        const boundary = req.user['boundary'];
        const { result, ...rest } = await RewardDistributionJobsHandler.createRewardDistributionJob(payload, organizationId, userId, ability, boundary);
        next({
          response: { status: 201, body: result },
          ...rest
        });
      } catch (err) {
        next(err);
      }
    });

    /**
     * @openapi
     *
     * /rewarddistributionjobs/{id}:
     *      put:
     *          summary: Update a reward distribution job
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            status:
     *                                type: string
     *                                enum: [PROCESSING,DISPATCHED,COMPLETED,FAILED]
     *          tags:
     *              - Reward Distribution Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update a reward distribution job is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardDistributionJob'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
      try {
        const { id } = await CommonPathValidator.isValid(req.params);
        const payload = req.body;
        const userId = req.user['id'];
        const organizationId = req.user['organizationId'];
        const ability = req.user['ability'];
        const boundary = req.user['boundary'];
        const { result, ...rest } = await RewardDistributionJobsHandler.updateRewardDistributionJob(payload, id, organizationId, userId, ability, boundary);
        next({
          response: { status: 200, body: result },
          ...rest
        });
      } catch (err) {
        next(err);
      }
    });

    /**
     * @openapi
     *
     * /rewarddistributionjobs/getcsv:
     *      get:
     *          summary: Get a CSV for Rewards in a distribution job
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: distributionJobId
     *                in: query
     *                required: true
     *                type: string
     *              - name: customExporter
     *                in: query
     *                type: string
     *              - name: redemptionStatus
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PROCESSING,COMPLETED,FAILED,REFUNDED]
     *          tags:
     *              - Reward Distribution Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a csv of rewards in a distribution job is success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  url:
     *                                      type: string
     */
    router.get('/getcsv', authorizer, async (req, res, next) => {
      try {
        const organizationId = req.user['organizationId'];
        const ability = req.user['ability'];
        const boundary = req.user['boundary'];
        const validatedObj = await RewardDistributionJobsQueryValidator.isValidCsvGet(req.query);
        const { result, ...rest } = await RewardDistributionJobsHandler.exportDistributionJobToCSV(organizationId, validatedObj, ability, boundary);
        next({ 
          response: { status: 200, body: result }, 
          ...rest
        });
      } catch (err) {
        next(err);
      }
    });

    /**
     * @openapi
     *
     * /rewarddistributionjobs/uploadfile:
     *      post:
     *          summary: Upload CSV file to import verified partner reward redemptions of a distribution batch
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: regionId
     *                type: string
     *                in: query
     *                required: true
     *              - name: distributionJobId
     *                type: string
     *                in: query
     *                required: true
     *          requestBody:
     *              content:
     *                  multipart/form-data:
     *                      schema:
     *                          type: object
     *                          required:
     *                            - file
     *                          properties:
     *                              file:
     *                                  type: string
     *                                  format: binary
     *          tags:
     *              - Reward Distribution Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: File upload successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                fileToken:
     *                                  type: string
     *                                headers:
     *                                  type: array
     *                                  items:
     *                                    type: string
     */
    router.post('/uploadfile', authorizer, upload.single('file') , authorizer, async (req, res, next) => {
      try {
        const { file: { mimetype, size, key, bucket, originalname } } = req;
        const originalFileName = path.parse(originalname).name;
        const userId = req.user['id'];
        const organizationId = req.user['organizationId'];
        const ability = req.user['ability'];
        const boundary = req.user['boundary'];
        const payload = req.query;
        const { result, ...rest } = await RewardDistributionJobsHandler.generateFileUploadTokenForImportRewardDistributions(
          organizationId,
          userId, 
          payload, 
          { mimetype, size, key, bucket, originalFileName }, 
          ability, 
          boundary
        );
        next({
          response: { status: 200, body: result },
          ...rest
        });
      } catch (err) {
        next(err);
      }
    });

    /**
     * @openapi
     *
     * /rewarddistributionjobs/importjobs:
     *      post:
     *          summary: Create a reward distributions import job
     *          produces:
     *              - application/json
     *          tags:
     *              - Reward Distribution Jobs
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            fieldMappings:
     *                                type: array
     *                                items:
     *                                   $ref: '#/definitions/FieldMapping'
     *                            fileToken:
     *                                type: string
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create reward distributions import job is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardDistributionsImportJob'
     */
    router.post('/importjobs', authorizer, async (req, res, next) => {
      try {
        const payload = req.body;
        const userId = req.user['id'];
        const organizationId = req.user['organizationId'];
        const ability = req.user['ability'];
        const boundary = req.user['boundary'];
        const { result, ...rest } = await RewardDistributionJobsHandler.createRewardDistributionsImportJob(payload, organizationId, userId, ability, boundary);
        next({
          response: { status: 201, body: result },
          ...rest
        });
      } catch (err) {
        next(err);
      }
    });

    return router;
}
