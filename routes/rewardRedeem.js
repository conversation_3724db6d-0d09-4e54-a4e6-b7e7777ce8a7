'use strict';
const express = require('express');
const router = express.Router();
const RewardRedeemHandler = require('./../lib/handlers/RewardRedeemHandler');

/**
 * @openapi
 *
 * definitions:
 *      RewardRedeemResponse:
 *          type: object
 *          properties:
 *                  rewardName:
 *                      type: string
 *                  transactionSubType:
 *                      type: number
 *                  memberId:
 *                      type: string
 *                  balancePoints:
 *                      type: string
 *                  points:
 *                      type: string
 *
 *      Metadata:
 *          type: object
 *          properties:
 *              claimLocationId:
 *                  type: string
 *
 *      MetadataWhenSubTypePartner:
 *          type: object
 *          properties:
 *              partnerRefNumber:
 *                  type: string
 *              partnerRefName:
 *                  type: string
 *              partnerNotes:
 *                  type: string
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /rewardredeem:
     *      post:
     *          summary: Redeem reward
     *          produces:
     *              - application/json
     *          tags:
     *              - Rewards Redemptions
     *          security:
     *              - Token: []
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          required:
     *                              - rewardId
     *                              - regionId
     *                              - merchantLocationId
     *                              - metadata
     *                          properties:
     *                              memberId:
     *                                  type: string
     *                              rewardId:
     *                                  type: string
     *                              regionId:
     *                                  type: string
     *                              pointsBundleId:
     *                                  type: string
     *                              merchantLocationId:
     *                                  type: string
     *                              metadata:
     *                                  oneOf:
     *                                      - $ref: '#/definitions/MetadataWhenSubTypePartner'
     *                                      - $ref: '#/definitions/Metadata'
     *          responses:
     *              200:
     *                  description: Reward redeem success. (voucher_code - VOUCHER) (topup_amount, topup_mobile_number - DIGITAL)
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardRedeemResponse'
     *
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await RewardRedeemHandler.redeemReward(organizationId, payload, userId, ability, boundary);
            res.status(201).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * definitions:
     *      RewardsRedeem:
     *          type: object
     *          required:
     *              - rewardId
     *              - regionId
     *              - merchantLocationId
     *              - metadata
     *          properties:
     *              memberId:
     *                  type: string
     *              rewardId:
     *                  type: string
     *              regionId:
     *                  type: string
     *              pointsBundleId:
     *                  type: string
     *              merchantLocationId:
     *                  type: string
     *              metadata:
     *                  oneOf:
     *                      - $ref: '#/definitions/MetadataWhenSubTypePartner'
     *                      - $ref: '#/definitions/Metadata'
     *
     * /rewardredeem/redeemwithotprequest:
     *      post:
     *          summary: Request OTP token to redeem with OTP
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/RewardsRedeem'
     *          tags:
     *              - Rewards Redemptions
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  otpToken:
     *                                      type: string
     */
    router.post('/redeemwithotprequest', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await RewardRedeemHandler.redeemOtpRequest(organizationId, payload, userId, ability, boundary);
            res.status(200).send({
                "otpToken": result
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /rewardredeem/redeemwithotp:
     *      post:
     *          summary: Redeem with OTP
     *          produces:
     *              - application/json
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              redemptionToken:
     *                                  type: string
     *                              otpCode:
     *                                  type: string
     *          tags:
     *              - Rewards Redemptions
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/RewardRedeemResponse'
     */
    router.post('/redeemwithotp', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const result = await RewardRedeemHandler.redeemWithOtp(organizationId, payload, userId, ability, boundary);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    return router;
}
