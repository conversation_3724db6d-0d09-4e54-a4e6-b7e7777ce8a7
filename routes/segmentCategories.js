'use strict';
const express = require('express');
const router = express.Router();
const SegmentCategoryHandler = require('./../lib/handlers/SegmentCategoryHandler');
const SegmentCategoriesQueryValidator = require('./../lib/validators/QueryValidators/SegmentCategoriesQueryValidator');
const CommonPathValidator = require('./../lib/validators/PathValidators/CommonPathValidator');

/**
 * @openapi
 * definitions:
 *
 *      SegmentCategoryResponse:
 *           type: object
 *           properties:
 *               limit:
 *                   type: number
 *               skip:
 *                   type: number
 *               total:
 *                   type: number
 *               items:
 *                   type: array
 *                   items:
 *                       $ref: '#/definitions/SegmentCategory'
 *
 *      SegmentCategory:
 *          type: object
 *          properties:
 *              _id:
 *                  type: string
 *                  readOnly: true
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              regionId:
 *                  type: string
 *              name:
 *                  type: string
 *              iconUrl:
 *                  type: string
 *              isEditable:
 *                  type: boolean
 *                  readOnly: true
 *              createdBy:
 *                  type: string
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  readOnly: true
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *          required:
 *              - name
 *              - regionId
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     * /segmentcategories:
     *      post:
     *          summary: Create a segment category
     *          produces:
     *              - application/json
     *          tags:
     *              - Segment Categories
     *          security:
     *              - Token: []
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          $ref: '#/definitions/SegmentCategory'
     *          responses:
     *              201:
     *                  description: Create segment success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/SegmentCategory'
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const callerId = req.user['id'];
            const payload = req.body;
            const { result: body, ...rest } = await SegmentCategoryHandler.createCategory(
                organizationId,
                payload,
                callerId,
                ability
            );
            next({
                response: { status: 201, body },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /segmentcategories:
     *      get:
     *          summary: Get a list of segment categories
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *          tags:
     *              - Segment Categories
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get segment categories success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: array
     *                              items:
     *                                  $ref: '#/definitions/SegmentCategory'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const validatedObj = await SegmentCategoriesQueryValidator.isValid(req.query);
            const result = await SegmentCategoryHandler.getCategoryList(organizationId, validatedObj, ability);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /segmentcategories/{id}:
     *      put:
     *          summary: Update a segment category
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *                required: true
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                            name:
     *                                type: string
     *                            iconUrl:
     *                                type: string
     *          tags:
     *              - Segment Categories
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Update segment category is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/SegmentCategory'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const callerId = req.user['id'];
            const payload = req.body;
            const { result: body, ...rest } = await SegmentCategoryHandler.updateCategory(
                payload,
                id,
                organizationId,
                callerId,
                ability
            );
            next({
                response: { status: 200, body },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     * /segmentcategories/{id}:
     *      delete:
     *          summary: Delete a segment category
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                in: path
     *                required: true
     *                type: string
     *          tags:
     *              - Segment Categories
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Delete segment category success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/SegmentCategory'
     */
    router.delete('/:id', authorizer, async (req, res, next) => {
        try {
            const { id } = await CommonPathValidator.isValid(req.params);
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const { result: body, ...rest } = await SegmentCategoryHandler.deleteCategory(id, organizationId, ability);
            next({
                response: { status: 200, body },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
};
