'use strict';
const express = require('express');
const router = express.Router();
const StagedTransactionsQueryValidator = require('../lib/validators/QueryValidators/StagedTransactionsQueryValidator');
const CommonPathValidator = require('../lib/validators/PathValidators/CommonPathValidator');
const StagedTransactionsHandler = require('../lib/handlers/StagedTransactionsHandler');
/**
 * @openapi
 *
 * definitions:
 *
 *      ProductItem:
 *          type: object
 *          properties:
 *                  productId:
 *                      type: string
 *                  productName:
 *                      type: string
 *                  productCategory:
 *                      type: array
 *                      items:
 *                          type: string
 *                  quantity:
 *                      type: number
 *                  amount:
 *                      type: number
 *
 *      InvoiceData:
 *          type: object
 *          properties:
 *                  invoiceId:
 *                      type: string
 *                  invoiceDate:
 *                      type: string
 *                      format: date-time
 *                  invoiceAmountWithTax:
 *                      type: number
 *                  invoiceAmountWithoutTax:
 *                      type: number
 *                  discountAmount:
 *                      type: number
 *                  billAmount:
 *                      type: number
 *
 *      PointRuleEvaluation:
 *          type: object
 *          properties:
 *                  pointRuleId:
 *                      type: string
 *                  points:
 *                      type: number
 *
 *      StagedTransaction:
 *          type: object
 *          properties:
 *                  organizationId:
 *                      type: string
 *                      readOnly: true
 *                  regionId:
 *                      type: string
 *                      readOnly: true
 *                  merchantId:
 *                      type: string
 *                      readOnly: true
 *                  merchantLocationId:
 *                      type: string
 *                  memberId:
 *                      type: string
 *                  loyaltyId:
 *                      type: string
 *                  transactionOn:
 *                      type: string
 *                      format: date-time
 *                  redeemablePoints:
 *                      type: number
 *                      readOnly: true
 *                  tierPoints:
 *                      type: number
 *                      readOnly: true
 *                  transactionAmount:
 *                      type: number
 *                      readOnly: true
 *                  transactionType:
 *                      type: string
 *                      enum: [COLLECTION,ADJUSTMENT,REDEMPTION]
 *                      readOnly: true
 *                  transactionSubTypeId:
 *                      type: string
 *                  importJobId:
 *                      type: string
 *                      readOnly: true
 *                  merchant:
 *                      readOnly: true
 *                      type: object
 *                      properties:
 *                          merchantName:
 *                              type: string
 *                  merchantLocation:
 *                      readOnly: true
 *                      type: object
 *                      properties:
 *                          locationName:
 *                              type: string
 *                  productItems:
 *                      type: array
 *                      items:
 *                          $ref: '#/definitions/ProductItem'
 *                  invoiceData:
 *                      $ref: '#/definitions/InvoiceData'
 *                  pointRuleEvaluations:
 *                      type: array
 *                      items:
 *                          $ref: '#/definitions/PointRuleEvaluation'
 *                      readOnly: true
 *                  createdBy:
 *                      type: string
 *                      readOnly: true
 *                  updatedBy:
 *                      type: string
 *                      readOnly: true
 *                  status:
 *                      type: string
 *                      enum: [PENDING_ACTION,RESOLVED,DISCARDED]
 *                      readOnly: true
 *                  rawData:
 *                      readOnly: true
 *                      anyOf:
 *                        - type: string
 *                        - type: number
 *                        - type: integer
 *                        - type: boolean
 *                        - type: array
 *                          items: {}
 *                        - type: object
 *                  failedReason:
 *                      type: string
 *                      readOnly: true
 *
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /stagedtransactions:
     *      get:
     *          summary: Get a list of staged(invalid) transactions
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: merchantId
     *                in: query
     *                type: string
     *              - name: cardNo
     *                in: query
     *                type: string
     *              - name: merchantLocationId
     *                in: query
     *                type: string
     *              - name: failureReason
     *                in: query
     *                type: string
     *              - name: subType
     *                in: query
     *                type: string
     *              - name: importJobId
     *                in: query
     *                type: string
     *              - name: searchKey
     *                in: query
     *                type: string
     *              - name: createdOn
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionOn
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: status
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PENDING_ACTION,RESOLVED,DISCARDED]
     *          tags:
     *              - Staged Transactions(Invalid Transactions)
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get a list of transactions success
     *                  content:
     *                      application/json:
     *                          schema:
     *                              type: object
     *                              properties:
     *                                  limit:
     *                                      type: number
     *                                  skip:
     *                                      type: number
     *                                  total:
     *                                      type: number
     *                                  items:
     *                                      type: array
     *                                      items:
     *                                          $ref: '#/definitions/StagedTransaction'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const userId = req.user['id'];
            const boundary = req.user['boundary'];
            const { limit, skip, regionId, ...validateRest } = await StagedTransactionsQueryValidator.isValidGet(
                req.query
            );
            const result = await StagedTransactionsHandler.getStagedTransactions(
                organizationId,
                { limit, skip, regionId, ...validateRest },
                ability,
                boundary,
                userId
            );
            res.status(200).send({
                limit: limit,
                skip: skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /stagedtransactions/export:
     *      get:
     *          summary: Export a list of staged(invalid) transactions
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: regionId
     *                in: query
     *                type: string
     *                required: true
     *              - name: merchantId
     *                in: query
     *                type: string
     *              - name: cardNo
     *                in: query
     *                type: string
     *              - name: merchantLocationId
     *                in: query
     *                type: string
     *              - name: failureReason
     *                in: query
     *                type: string
     *              - name: subType
     *                in: query
     *                type: string
     *              - name: importJobId
     *                in: query
     *                type: string
     *              - name: searchKey
     *                in: query
     *                type: string
     *              - name: createdOn
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionOn
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionOnFromDate
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: transactionOnToDate
     *                in: query
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: status
     *                in: query
     *                schema:
     *                  type: string
     *                  enum: [PENDING_ACTION,RESOLVED,DISCARDED]
     *              - name: notificationEmails
     *                in: query
     *                required: true
     *                schema:
     *                  type: array
     *                  items:
     *                      type: string
     *          tags:
     *              - Staged Transactions(Invalid Transactions)
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Export staged transactions success. A job will be generated that sends the URL of the export file to the specified email address.
     */
    router.get('/export', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const userId = req.user['id'];
            const boundary = req.user['boundary'];
            const validatedObj = await StagedTransactionsQueryValidator.isValidExport(req.query);
            const result = await StagedTransactionsHandler.exportStagedTransactions(
                organizationId,
                validatedObj,
                ability,
                boundary,
                userId
            );
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /stagedtransactions/{id}:
     *      put:
     *          summary: Update a staged(invalid) transaction
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *          requestBody:
     *              description: Upon successful update, transaction will be processed automatically if the required attributes are available
     *              content:
     *                  application/json:
     *                      schema:
     *                          allOf:
     *                              - $ref: '#/definitions/StagedTransaction'
     *                              - type: object
     *                                properties:
     *                                  cardNo:
     *                                      type: string
     *                                  notes:
     *                                      type: string
     *          tags:
     *              - Staged Transactions(Invalid Transactions)
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Staged transaction updated
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/StagedTransaction'
     */
    router.put('/:id', authorizer, async (req, res, next) => {
        try {
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const { id } = await CommonPathValidator.isValid(req.params);
            const payload = req.body;
            const boundary = req.user['boundary'];
            const { result, ...rest } = await StagedTransactionsHandler.updateStagedTransaction(
                organizationId,
                id,
                payload,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /stagedtransactions/{id}:
     *      delete:
     *          summary: Archive a staged(invalid) transaction
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: id
     *                type: string
     *                in: path
     *          tags:
     *              - Staged Transactions(Invalid Transactions)
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Staged transaction archived
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/StagedTransaction'
     */
    router.delete('/:id', authorizer, async (req, res, next) => {
        try {
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const { id } = await CommonPathValidator.isValid(req.params);
            const { result, ...rest } = await StagedTransactionsHandler.deleteStagedTransaction(
                organizationId,
                id,
                userId,
                ability,
                boundary
            );
            next({
                response: { status: 200, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });
    return router;
};
