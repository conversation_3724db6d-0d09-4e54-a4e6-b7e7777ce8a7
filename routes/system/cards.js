'use strict';
const CardsHandler = require('../../lib/handlers/CardsHandler');
const CardQueryValidator = require('../../lib/validators/QueryValidators/CardQueryValidator');
const { authorize, OPERATION } = require('@shoutout-labs/express-authorizer-middleware');
const MODULE_ID = require('@shoutout-labs/authz-utils').AUTH_CONSTANTS.AUTH_MODULE_IDS.LOYALTY.MODULE_ID_CARDS;
const express = require('express');
const router = express.Router();

/**
 * @openapi
 * definitions:
 *      History:
 *          type: object
 *          properties:
 *              eventDate:
 *                  type: string
 *                  format: date-time
 *              eventDetails:
 *                  type: string
 *              eventBy:
 *                  type: string
 *
 *      EmbossCard:
 *          type: object
 *          properties:
 *              embossRequestedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              embossIssuedOn:
 *                  type: string
 *                  format: date-time
 *              embossStatus:
 *                  type: string
 *                  enum: [ACTIVE,NONE,REQUESTED]
 *                  readOnly: true
 *              previousCardType:
 *                  type: string
 *                  enum: [DIGITAL_CARD,KEY_TAG,REGULAR_CARD,REGULAR_CARD_AND_KEY_TAG]
 *                  readOnly: true
 *              previousBatchJobId:
 *                  type: string
 *                  readOnly: true
 *              previousBatchJobNumber:
 *                  type: string
 *                  readOnly: true
 *              printedName:
 *                  type: string
 *                  readOnly: true
 *              merchantId:
 *                  type: string
 *                  readOnly: true
 *              merchantLocationId:
 *                  type: string
 *                  readOnly: true
 *              merchantLocationName:
 *                  type: string
 *                  readOnly: true
 *              requestedBy:
 *                  type: string
 *                  readOnly: true
 *
 *      Card:
 *          type: object
 *          properties:
 *              _id:
 *                  type: string
 *              memberId:
 *                  type: string
 *              member:
 *                  readOnly: true
 *                  type: object
 *                  properties:
 *                      preferredName:
 *                          type: string
 *              regionId:
 *                  type: string
 *                  required: true
 *              cardNo:
 *                  type: number
 *                  required: true
 *              cardNoStr:
 *                  type: string
 *                  required: true
 *              organizationId:
 *                  type: string
 *                  required: true
 *              printJobId:
 *                  type: string
 *                  required: true
 *              printJobNumber:
 *                  type: string
 *                  required: true
 *              distributionJobId:
 *                  type: string
 *                  required: true
 *              distributionJobNumber:
 *                  type: string
 *                  required: true
 *              status:
 *                  type: string
 *                  enum: [PENDING,ACTIVE,ASSIGNED,DEACTIVATED,SUSPENDED]
 *              type:
 *                  type: string
 *                  enum: [DIGITAL_CARD,KEY_TAG,REGULAR_CARD,REGULAR_CARD_AND_KEY_TAG,EMBOSSED_CARD]
 *              historyEvents:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/History'
 *              embossCard:
 *                  $ref: '#/definitions/EmbossCard'
 *              assignedDate:
 *                  type: string
 *                  format: date-time
 *              note:
 *                  type: string
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *              createdBy:
 *                  type: string
 *              updatedBy:
 *                  type: string
 *      GenericResponse:
 *          type: object
 *          properties:
 *              message:
 *                  type: string
 *
 */
module.exports = (authorizer) => {

  /**
   * @openapi
   *
   * /system/cards:
   *      get:
   *          summary: Get a list of cards
   *          produces:
   *              - application/json
   *          parameters:
   *              - name: limit
   *                in: query
   *                required: false
   *                type: number
   *              - name: skip
   *                in: query
   *                required: false
   *                type: number
   *              - name: organizationId
   *                in: query
   *                required: true
   *                type: string
   *              - name: regionId
   *                in: query
   *                required: false
   *                type: string
   *              - name: assignedDateFrom
   *                in: query
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: assignedDateTo
   *                in: query
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: createdOnFrom
   *                in: query
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: createdOnTo
   *                in: query
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: status
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *                      enum: [PENDING,READY,ACTIVE,ASSIGNED,SUSPENDED,DEACTIVATED]
   *              - name: processingStatus
   *                in: query
   *                schema:
   *                  type: string
   *                  enum: [REQUESTED,PENDING,PRINTING,PRINTED,DISPATCHED,COMPLETED,FAILED]
   *              - name: printJobId
   *                in: query
   *                required: false
   *                type: string
   *              - name: distributionJobId
   *                in: query
   *                required: false
   *                type: string
   *              - name: memberId
   *                in: query
   *                required: false
   *                type: string
   *              - name: cardNo
   *                in: query
   *                type: string
   *              - name: searchKey
   *                in: query
   *                type: string
   *              - name: cardTypes
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *                      enum: [DIGITAL_CARD,KEY_TAG,REGULAR_CARD,REGULAR_CARD_AND_KEY_TAG,EMBOSSED_CARD]
   *              - name: cardIds
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *              - name: fields
   *                in: query
   *                required: false
   *                schema:
   *                  type: array
   *                  items:
   *                      type: string
   *          tags:
   *              - Cards
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Get a list of cards success
   *                  content:
   *                      application/json:
   *                          schema:
   *                              type: object
   *                              properties:
   *                                  limit:
   *                                      type: number
   *                                  skip:
   *                                      type: number
   *                                  total:
   *                                      type: number
   *                                  items:
   *                                      type: array
   *                                      items:
   *                                          $ref: '#/definitions/Card'
   */
  router.get('/', authorizer, authorize(MODULE_ID, OPERATION.READ), async (req, res, next) => {
    try {
      const validatedQuery = await CardQueryValidator.isValidSystemGetCards(req.query);
      const result = await CardsHandler.getCards(validatedQuery.organizationId, validatedQuery, false);
      res.status(200).send({
        limit: validatedQuery.limit,
        skip: validatedQuery.skip,
        total: result.total,
        items: result.items
      });
    } catch (err) {
      next(err);
    }
  });

  return router;
}
