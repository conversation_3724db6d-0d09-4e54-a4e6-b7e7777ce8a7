'use strict';
const express = require('express');
const EventsValidator = require("../../lib/validators/eventsValidator");
const EventHandler = require("../../lib/handlers/eventHandler");
const router = express.Router();


module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /system/event/memberloginevent:
     *      put:
     *          produces:
     *              - application/json
     *          tags:
     *              - Event
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                        type: object
     *                        properties:
     *                          identityProviderId:
     *                            type: string
     *                            required: true
     *                          loginDate:
     *                            type: string
     *                            format: date-time
     *                            required: true
     */
    router.put('/memberloginevent', authorizer, async (req, res, next) => {
        try {
            const validatedObj = await EventsValidator.isValidLoginEventPayload(req.body);
            const { result, ...rest }=await EventHandler.updateMemberLastAccessedDate(validatedObj);
            next({
                response: { status: 201, body: result },
                ...rest
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
}
