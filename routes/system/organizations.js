'use strict';
const express = require('express');
const router = express.Router();
const { authorize, OPERATION } = require('@shoutout-labs/express-authorizer-middleware');
const MODULE_ID = require('@shoutout-labs/authz-utils').AUTH_CONSTANTS.AUTH_MODULE_IDS.LOYALTY.MODULE_ID_ORGANIZATIONS;
const OrganizationHandler = require('../../lib/handlers/OrganizationHandler');
const CommonQueryValidator = require('../../lib/validators/QueryValidators/CommonQueryValidator');

/**
 * @openapi
 *
 * definitions:
 *      Address:
 *          type: object
 *          properties:
 *              line1:
 *                  type: string
 *              line2:
 *                  type: string
 *              line3:
 *                  type: string
 *              city:
 *                  type: string
 *              stateOrProvince:
 *                  type: string
 *              zipOrPostcode:
 *                  type: string
 *      MemberStatusUpdateWebhookConfig:
 *          type: object
 *          properties:
 *              status:
 *                  type: boolean
 *              description:
 *                  type: string
 *      WebhookConfiguration:
 *          type: object
 *          properties:
 *              memberStatusUpdate:
 *                  $ref: '#/definitions/MemberStatusUpdateWebhookConfig'
 *      Organization:
 *          type: object
 *          properties:
 *              organizationName:
 *                  type: string
 *              organizationLogoImageUrl:
 *                  type: string
 *              organizationFavicon:
 *                  type: string
 *              organizationAppTitle:
 *                  type: string
 *              address:
 *                  $ref: '#/definitions/Address'
 *              regions:
 *                  readOnly: true
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/Region'
 *              subtransactionTypeIdMap:
 *                  type: object
 *                  properties:
 *                      collectPointsBill:
 *                          type: string
 *                      collectPointsManual:
 *                          type: string
 *                      collectPointsDonation:
 *                          type: string
 *                      redeemPointsDonation:
 *                          type: string
 *                      redeemPointsReward:
 *                          type: string
 *                      adjustPointsTransferSubstract:
 *                          type: string
 *                      adjustPointsTransferAdd:
 *                          type: string
 *                      adjustPointsRegionalTransferSubtract:
 *                          type: string
 *                      adjustPointsRegionalTransferAdd:
 *                          type: string
 *                      refundPoints:
 *                          type: string
 *                      primaryAccountAdd:
 *                          type: string
 *                      secondaryAccountSubtract:
 *                          type: string
 *                      expirePoints:
 *                          type: string
 *                      cardReplacement:
 *                          type: string
 *              configuration:
 *                  type: object
 *                  properties:
 *                      baseRegionId:
 *                          type: string
 *                      baseCountryISO2Code:
 *                          type: string
 *                          readOnly: true
 *                      baseCountryCurrencyCode:
 *                          type: string
 *                          readOnly: true
 *                      cardConfiguration:
 *                          type: object
 *                          readOnly: true
 *                          properties:
 *                              loyaltyCardNumberLength:
 *                                  type: number
 *                              allowManualCardGeneration:
 *                                  type: boolean
 *                      notificationConfiguration:
 *                          type: object
 *                          properties:
 *                              emailConfiguration:
 *                                  type: object
 *                                  properties:
 *                                      fromAddress:
 *                                          type: string
 *                              smsConfiguration:
 *                                  type: object
 *                                  properties:
 *                                      phoneNumber:
 *                                          type: string
 *                      tierConfiguration:
 *                          type: object
 *                          properties:
 *                              tierCalculationWindow:
 *                                  type: number
 *                                  minimum: 1
 *                              tierCalculationSubTransactionTypeIds:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              jobEnabled:
 *                                  type: boolean
 *                              jobFrequency:
 *                                  type: string
 *                      reportConfiguration:
 *                          type: object
 *                          properties:
 *                              financialYearStartMonth:
 *                                  type: number
 *                                  minimum: 1
 *                                  maximum: 12
 *                      portalConfiguration:
 *                          type: object
 *                          properties:
 *                              allowSelfSignup:
 *                                  type: boolean
 *                              allowedOrigins:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              idpMetadata:
 *                                  type: object
 *                                  properties:
 *                                      realm:
 *                                          type: string
 *                                      clientId:
 *                                          type: string
 *                                      clientSecret:
 *                                          type: string
 *                                      certUrl:
 *                                          type: string
 *                                      issuer:
 *                                          type: string
 *                                      audience:
 *                                          type: string
 *                      memberPrimaryAttribute:
 *                          type: string
 *                      webhookConfiguration:
 *                          $ref: '#/definitions/WebhookConfiguration'
 *
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /system/organizations:
     *      get:
     *          summary: Get organization details
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: organizationId
     *                in: query
     *                required: true
     *                type: string
     *              - name: origin
     *                in: query
     *                required: false
     *                type: string
     *          tags:
     *              - Organizations
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get organization is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/Organization'
     */
    router.get('/', authorizer, authorize(MODULE_ID, OPERATION.READ), async (req, res, next) => {
        try {
            const validatedObj = await CommonQueryValidator.isValidSystemOrganizationGet(req.query);
            const result = await OrganizationHandler.getOrganization(validatedObj, false, false);
            res.status(200).send(result);
        } catch (err) {
            next(err);
        }
    });

    return router;
};
