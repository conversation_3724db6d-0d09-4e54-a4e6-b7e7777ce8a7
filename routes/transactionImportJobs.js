'use strict';
const express = require('express');
const router = express.Router();
const multer = require('multer');
const config = require('../lib/config');
const logger = require('../lib/logger');
const log = logger(config.logger);
const TransactionImportJobsQueryValidator = require('../lib/validators/QueryValidators/TransactionImportJobsQueryValidator');
const TransactionImportJobHandler = require('../lib/handlers/TransactionImportJobHandler');
const StorageWrapper = require('../lib/wrappers/StorageWrapper');
const secretConfig = require('./../config');
const USER_ASSETS_BUCKET = secretConfig.USER_ASSETS_BUCKET;
const { v1: uuidV1 } = require('uuid');
const path = require('path');
const { ForbiddenError } = require('@casl/ability');
const TransactionImportJobsValidator = require('./../lib/validators/TransactionImportJobsValidator');

const fileFilter = (req, file, cb) => {
  if (file.mimetype === 'text/csv') {
    cb(null, true);
  } else {
    cb(null, false);
  }
};

const keyHandlerFunc = (req, file, cb) => {
  const organizationId = req.user['organizationId'];
  const ability = req.user['ability'];
  //ForbiddenError.from(ability).throwUnlessCan(TRANSACTION.ACTIONS.CREATE_TRANSACTION_IMPORT_JOB, TRANSACTION.MODULE_ID); TODO: Add permissions
  log.info(`transactions import file payload`, req.body);
  TransactionImportJobsValidator.isValidUpload(req.query).then(({ regionId, merchantId }) => {
    //todo: validate regionId and merchantId
    cb(null, `${organizationId}/transactions/${regionId}/${merchantId}/${uuidV1()}${path.extname(file.originalname)}`);
  }).catch(err => {
    cb(err);
  });
}

const upload = multer({
  storage: StorageWrapper.getStorage(USER_ASSETS_BUCKET, keyHandlerFunc),
  fileFilter,
  limits: {
    fileSize: Number(secretConfig.MAX_UPLOAD_FILE_SIZE)
  }
});

/**
 * @openapi
 *
 * definitions:
 *
 *      FilePart:
 *        type: object
 *        properties:
 *          fileId:
 *            type: string
 *          recordsCount:
 *            type: number
 *          failedCount:
 *            type: number
 *          status:
 *            type: string
 *            enum: [PENDING,PROCESSING,COMPLETED,FAILED]
 *          startedOn:
 *            type: string
 *            format: date-time
 *          completedOn:
 *            type: string
 *            format: date-time
 *
 *      FieldMapping:
 *        type: object
 *        properties:
 *            fileColumnName:
 *              type: string
 *            systemAttributeName:
 *              type: string
 *              enum: [LOYALTY_CARD,BILL_AMOUNT,POINTS_AMOUNT,TRANSACTION_DATE,BILL_DETAILS,BILL_REFERENCE]
 *
 *      TransactionImportJob:
 *        type: object
 *        required:
 *           - regionId
 *           - merchantId
 *           - merchantLocationSelection
 *           - merchantLocationId
 *           - merchantLocationColumnName
 *           - transactionType
 *        properties:
 *            organizationId:
 *                type: string
 *                readOnly: true
 *            regionId:
 *                type: string
 *            merchantId:
 *                type: string
 *            merchantLocationSelection:
 *                type: string
 *                enum: [STATIC,DYNAMIC]
 *            merchantLocationId:
 *                type: string
 *            merchantLocationColumnName:
 *                type: string
 *            startedOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            completedOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            totalRecordsCount:
 *                type: number
 *                readOnly: true
 *            failedRecordsCount:
 *                type: number
 *                readOnly: true
 *            transactionType:
 *                type: string
 *                enum: [COLLECTION,ADJUSTMENT]
 *            fileId:
 *                type: string
 *                readOnly: true
 *            fileParts:
 *                type: array
 *                items:
 *                   $ref: '#/definitions/FilePart'
 *                readOnly: true
 *            fieldMappings:
 *                type: array
 *                items:
 *                   $ref: '#/definitions/FieldMapping'
 *            status:
 *                type: string
 *                enum: [PENDING,PROCESSING,COMPLETED,FAILED]
 *                readOnly: true
 *            merchant:
 *                type: object
 *                readOnly: true
 *                properties:
 *                  merchantName:
 *                    type: string
 *            merchantLocation:
 *                type: object
 *                readOnly: true
 *                properties:
 *                  locationName:
 *                    type: string
 *            createdOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            updatedOn:
 *                type: string
 *                format: date-time
 *                readOnly: true
 *            createdBy:
 *                type: string
 *                readOnly: true
 *            updatedBy:
 *                type: string
 *                readOnly: true
 *
 *      TransactionImportJobsResponse:
 *        type: object
 *        properties:
 *            limit:
 *                type: number
 *            skip:
 *                type: number
 *            total:
 *                type: number
 *            items:
 *                type: array
 *                items:
 *                    $ref: '#/definitions/TransactionImportJob'
 */
module.exports = (authorizer) => {
  /**
   * @openapi
   *
   * /transactionimportjobs:
   *      get:
   *          summary: Get a list of transaction import jobs
   *          produces:
   *              - application/json
   *          parameters:
   *              - name: limit
   *                in: query
   *                required: true
   *                type: number
   *              - name: skip
   *                in: query
   *                required: true
   *                type: number
   *              - name: regionId
   *                in: query
   *                required: true
   *                type: string
   *              - name: merchantId
   *                in: query
   *                required: false
   *                type: string
   *              - name: merchantLocationId
   *                in: query
   *                required: false
   *                type: string
   *              - name: createdDateFrom
   *                in: query
   *                required: false
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: createdDateTo
   *                in: query
   *                required: false
   *                schema:
   *                  type: string
   *                  format: date-time
   *              - name: importJobId
   *                in: query
   *                required: false
   *                type: string
   *          tags:
   *              - Transaction import jobs
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: Get transaction import jobs is successful.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/TransactionImportJobsResponse'
   */
  router.get('/', authorizer, async (req, res, next) => {
    try {
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const validatedObj = await TransactionImportJobsQueryValidator.isValidGet(req.query);
      const result = await TransactionImportJobHandler.getTransactionImportJobs(organizationId, validatedObj, ability, boundary);
      res.status(200).send({
        limit: validatedObj.limit,
        skip: validatedObj.skip,
        total: result.total,
        items: result.items
      });
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /transactionimportjobs/uploadfile:
   *      post:
   *          summary: Upload CSV file to import transactions
   *          produces:
   *              - application/json
   *          parameters:
   *              - name: regionId
   *                type: string
   *                in: query
   *                required: true
   *              - name: merchantId
   *                type: string
   *                in: query
   *                required: true
   *          requestBody:
   *              content:
   *                  multipart/form-data:
   *                      schema:
   *                          type: object
   *                          required:
   *                            - file
   *                          properties:
   *                              file:
   *                                  type: string
   *                                  format: binary
   *          tags:
   *              - Transaction import jobs
   *          security:
   *              - Token: []
   *          responses:
   *              200:
   *                  description: File upload success.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              type: object
   *                              properties:
   *                                fileToken:
   *                                  type: string
   *                                headers:
   *                                  type: array
   *                                  items:
   *                                    type: string
   */
  router.post('/uploadfile', authorizer, upload.single('file') , authorizer, async (req, res, next) => {
    try {
      const { file: { mimetype, size, key, bucket } } = req;
      const userId = req.user['id'];
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const payload = req.query;
      const { result, ...rest } = await TransactionImportJobHandler.generateFileUploadToken(organizationId, userId, payload, {mimetype, size, key, bucket}, ability, boundary);
      next({
        response: { status: 200, body: result },
        ...rest
      });
    } catch (err) {
      next(err);
    }
  });

  /**
   * @openapi
   *
   * /transactionimportjobs:
   *      post:
   *          summary: Create a transactions import job
   *          produces:
   *              - application/json
   *          tags:
   *              - Transaction import jobs
   *          requestBody:
   *              content:
   *                  application/json:
   *                      schema:
   *                          type: object
   *                          properties:
   *                            merchantLocationSelection:
   *                                type: string
   *                                enum: [STATIC,DYNAMIC]
   *                            merchantLocationId:
   *                                type: string
   *                            merchantLocationCode:
   *                                type: string
   *                            merchantLocationColumnName:
   *                                type: string
   *                            transactionType:
   *                                type: string
   *                                enum: [COLLECTION,ADJUSTMENT]
   *                            transactionSubTypeId:
   *                                type: string
   *                            fieldMappings:
   *                                type: array
   *                                items:
   *                                   $ref: '#/definitions/FieldMapping'
   *                            fileToken:
   *                                type: string
   *          security:
   *              - Token: []
   *          responses:
   *              201:
   *                  description: Create a transactions import job is successful.
   *                  content:
   *                      application/json:
   *                          schema:
   *                              $ref: '#/definitions/TransactionImportJob'
   */
  router.post('/', authorizer, async (req, res, next) => {
    try {
      const payload = req.body;
      const userId = req.user['id'];
      const organizationId = req.user['organizationId'];
      const ability = req.user['ability'];
      const boundary = req.user['boundary'];
      const { result, ...rest } = await TransactionImportJobHandler.createTransactionImportJob(payload, organizationId, userId, ability, boundary);
      next({
        response: { status: 201, body: result },
        ...rest
      });
    } catch (err) {
      next(err);
    }
  });

  return router;
}
