'use strict';
const express = require('express');
const router = express.Router();
const TransactionImportRetryJobsQueryValidator = require('../lib/validators/QueryValidators/TransactionImportRetryJobsQueryValidator');
const SubTransactionTypesHandler = require('../lib/handlers/SubTransactionTypesHandler');
const TransactionImportRetryJobsHandler = require('../lib/handlers/TransactionImportRetryJobsHandler');

/**
 * @openapi
 *
 * definitions:
 *
 *      TransactionImportRetryJob:
 *          type: object
 *          properties:
 *              organizationId:
 *                  type: string
 *                  readOnly: true
 *              regionId:
 *                  type: string
 *                  readOnly: true
 *              merchantId:
 *                  type: string
 *                  readOnly: true
 *              importJobId:
 *                  type: string
 *                  readOnly: true
 *              startedOn:
 *                  type: string
 *                  format: date-time
 *              completedOn:
 *                  type: string
 *                  format: date-time
 *              totalInvalidTransactionsCount:
 *                  type: number
 *              processedRecordsCount:
 *                  type: number
 *              successRecordsCount:
 *                  type: number
 *              failedRecordsCount:
 *                  type: number
 *              status:
 *                  type: string
 *                  readOnly: true
 *                  enum: [PENDING,PROCESSING,COMPLETED,FAILED]
 *              createdOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              updatedOn:
 *                  type: string
 *                  format: date-time
 *                  readOnly: true
 *              createdBy:
 *                  type: string
 *                  readOnly: true
 *              updatedBy:
 *                  type: string
 *                  readOnly: true
 *
 *      TransactionImportRetryJobsResponse:
 *          type: object
 *          properties:
 *              limit:
 *                  type: number
 *              skip:
 *                  type: number
 *              total:
 *                  type: number
 *              items:
 *                  type: array
 *                  items:
 *                      $ref: '#/definitions/TransactionImportRetryJob'
 */
module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * /transactionimportretryjobs:
     *      get:
     *          summary: Get a list of transaction import retry jobs
     *          produces:
     *              - application/json
     *          parameters:
     *              - name: limit
     *                in: query
     *                required: true
     *                type: number
     *              - name: skip
     *                in: query
     *                required: true
     *                type: number
     *              - name: regionId
     *                in: query
     *                required: true
     *                type: string
     *              - name: merchantId
     *                in: query
     *                required: false
     *                type: string
     *              - name: createdDateFrom
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: createdDateTo
     *                in: query
     *                required: false
     *                schema:
     *                  type: string
     *                  format: date-time
     *              - name: importJobId
     *                in: query
     *                required: false
     *                type: string
     *          tags:
     *              - Transaction Import Retry Jobs
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get transaction import retry jobs successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/TransactionImportRetryJobsResponse'
     */
    router.get('/', authorizer, async (req, res, next) => {
        try {
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const validatedObj = await TransactionImportRetryJobsQueryValidator.isValidGet(req.query);
            const result = await TransactionImportRetryJobsHandler.getTransactionImportRetryJobs(organizationId, validatedObj, ability, boundary);
            res.status(200).send({
                limit: validatedObj.limit,
                skip: validatedObj.skip,
                total: result.total,
                items: result.items
            });
        } catch (err) {
            next(err);
        }
    });

    /**
     * @openapi
     *
     * /transactionimportretryjobs:
     *      post:
     *          summary: Create a transaction import retry job
     *          produces:
     *              - application/json
     *          tags:
     *              - Transaction Import Retry Jobs
     *          requestBody:
     *              content:
     *                  application/json:
     *                      schema:
     *                          type: object
     *                          properties:
     *                              importJobId:
     *                                  type: string
     *          security:
     *              - Token: []
     *          responses:
     *              201:
     *                  description: Create transaction import retry job is successful.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/TransactionImportRetryJob'
     */
    router.post('/', authorizer, async (req, res, next) => {
        try {
            const payload = req.body;
            const userId = req.user['id'];
            const organizationId = req.user['organizationId'];
            const ability = req.user['ability'];
            const boundary = req.user['boundary'];
            const {
                result,
                ...rest
            } = await TransactionImportRetryJobsHandler.createTransactionImportRetryJob(payload, organizationId, userId, ability, boundary);
            next({
                response: { status: 201, body: result }, ...rest,
            });
        } catch (err) {
            next(err);
        }
    });

    return router;
};
