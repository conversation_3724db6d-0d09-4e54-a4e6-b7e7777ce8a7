'use strict';
const express = require('express');
const router = express.Router();
const RedemptionLogsHandler = require('./../lib/handlers/RedemptionLogsHandler');
const {authorize, OPERATION} = require('@shoutout-labs/express-authorizer-middleware');
const MODULE_ID = require('@shoutout-labs/constants').AUTH_MODULE_IDS.LOYALTY.MODULE_ID_USER_REWARDS;
const Utils = require('./../lib/Utils');
const RedemptionLogsQueryValidator = require('./../lib/validators/QueryValidators/RedemptionLogsQueryValidator');

module.exports = (authorizer) => {
    /**
     * @openapi
     *
     * definitions:
     *      UserRewardsResponse:
     *          type: array
     *          items:
     *              type: object
     *              properties:
     *                  ownerId:
     *                      type: string
     *                  createdBy:
     *                      type: string
     *                  contactId:
     *                      type: string
     *                  rewardId:
     *                      type: string
     *                  rewardName:
     *                      type: string
     *                  rewardImageUrls:
     *                      type: array
     *                      items:
     *                          type: string
     *                  rewardType:
     *                      type: string
     *                      enum: [VOUCHER,DIGITAL]
     *                  pointsRedeemed:
     *                      type: number
     *                  status:
     *                      type: string
     *                      enum: [PENDING_CLAIM,CLAIMED]
     *                  rewardData:
     *                      type: object
     *                      properties:
     *                          claimLocation:
     *                              type: string
     *                          voucher:
     *                              type: string
     *                  rewardValue:
     *                      type: number
     *
     * /userrewards:
     *      get:
     *          produces:
     *              - application/json
     *          tags:
     *              - User Rewards
     *          parameters:
     *              - name: rewardId
     *                type: string
     *                in: query
     *              - name: loyaltyId
     *                type: string
     *                in: query
     *              - name: fromDate
     *                type: string
     *                format: date
     *                in: query
     *              - name: toDate
     *                type: string
     *                format: date
     *                in: query
     *              - name: rewardType
     *                type: string
     *                in: query
     *              - name: limit
     *                type: number
     *                in: query
     *              - name: skip
     *                type: number
     *                in: query
     *          security:
     *              - Token: []
     *          responses:
     *              200:
     *                  description: Get user rewards success.
     *                  content:
     *                      application/json:
     *                          schema:
     *                              $ref: '#/definitions/UserRewardsResponse'
     */
    router.get('/', authorizer, authorize(MODULE_ID, OPERATION.READ), async (req, res, next) => {
        try {
            const ownerId = req.user['id'];
            const validatedQuery = await RedemptionLogsQueryValidator.isValidAdmin(req.query);
            let contactId;
            if (validatedQuery.loyaltyId) {
                contactId = Utils.generateId(validatedQuery.loyaltyId, ownerId);
            }
            const result = await RedemptionLogsHandler.getLogs(ownerId, validatedQuery.rewardId, contactId, validatedQuery.fromDate, validatedQuery.toDate, validatedQuery.rewardtype, validatedQuery.limit, validatedQuery.skip);
            res.status(200).send(result);
        } catch (err) {
            if (err.statusCode) {
                return res.status(err.statusCode).send({
                    status: err.statusCode,
                    error: err.message
                });
            }
            return res.status(500).send({
                status: 500,
                error: 'Server Error'
            });
        }
    });

    return router;
}