# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "a859e0256567d983461f9466a18efbc9"
application_url = "https://shpify.public.loyalty.cxforge.com"
embedded = true
name = "C<PERSON><PERSON><PERSON><PERSON> by ShoutOUT "
handle = "cxforge-loyalty-by-shoutout"

[webhooks]
api_version = "2025-01"
  [[webhooks.subscriptions]]
  uri = "https://shpify.public.loyalty.cxforge.com/webhooks/customers/data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://shpify.public.loyalty.cxforge.com/webhooks/customers/redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://shpify.public.loyalty.cxforge.com/webhooks/shop/redact"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "https://shpify.public.loyalty.cxforge.com/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "customers/create", "orders/create" ]
  uri = "arn:aws:events:us-west-2::event-source/aws.partner/shopify.com/205157859329/shopify_cxforge_public_webhook"

[auth]
redirect_urls = [   
  "https://shpify.public.loyalty.cxforge.com/auth/callback",
  "https://shpify.public.loyalty.cxforge.com/auth/shopify/callback",
  "https://shpify.public.loyalty.cxforge.com/api/auth/callback" 
]

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_discounts,write_products,read_discounts"


[pos]
embedded = true

[build]
include_config_on_deploy = true
dev_store_url = "so-test-shop.myshopify.com"

[app_proxy]
url = "https://shpify.public.loyalty.cxforge.com/proxy"
prefix = "apps"
subpath = "members"