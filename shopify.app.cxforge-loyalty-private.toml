# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "2b1151ec35363f3df1b839a63bbd7a18"
application_url = "https://shopify.loyalty.cxforge.com"
embedded = true
name = "cxforge-loyalty-private"
handle = "cxforge-loyalty-private"

[build]
include_config_on_deploy = true
dev_store_url = "so-test-shop.myshopify.com"

[webhooks]
api_version = "2025-01"
  [[webhooks.subscriptions]]
  uri = "https://shopify.loyalty.cxforge.com/webhooks/customers/data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://shopify.loyalty.cxforge.com/webhooks/customers/redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://shopify.loyalty.cxforge.com/webhooks/shop/redact"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "https://shopify.loyalty.cxforge.com/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "customers/create", "orders/create" ]
  uri = "arn:aws:events:us-west-2::event-source/aws.partner/shopify.com/207561654273/shopify_cxforge_webhook"


[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_discounts,write_products,read_discounts"

[auth]
redirect_urls =[
  "https://shopify.loyalty.cxforge.com/auth/callback",
  "https://shopify.loyalty.cxforge.com/auth/shopify/callback",
  "https://shopify.loyalty.cxforge.com/api/auth/callback"
]
     

[pos]
embedded = true

[app_proxy]
url = "https://shopify.loyalty.cxforge.com/proxy"
prefix = "apps"
subpath = "members"
