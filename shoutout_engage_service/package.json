{"name": "shoutout-engage-service", "version": "0.1.0", "private": true, "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "joi": "^17.12.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.2.3", "morgan": "^1.10.0", "redis": "^4.6.13", "winston": "^3.12.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/bull": "^4.10.0", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/node": "^20.11.30", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "eslint": "^8.57.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.6", "nodemon": "^3.1.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.3"}}