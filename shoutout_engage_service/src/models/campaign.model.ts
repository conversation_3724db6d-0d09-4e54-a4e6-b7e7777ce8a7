import mongoose, { Document, Schema } from 'mongoose';

// Campaign status enum
export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending',
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  FAILED = 'failed'
}

// Campaign type enum
export enum CampaignType {
  SMS = 'sms',
  EMAIL = 'email',
  WHATSAPP = 'whatsapp'
}

// Campaign interface
export interface ICampaign extends Document {
  name: string;
  organizationId: string;
  userId: string;
  type: CampaignType;
  status: CampaignStatus;
  content: {
    body: string;
    subject?: string;
    template?: string;
    variables?: Record<string, any>;
  };
  schedule?: {
    sendAt: Date;
    timezone: string;
  };
  recipients: {
    segmentId?: string;
    contactIds?: string[];
    count: number;
  };
  sender: {
    id: string;
    name?: string;
    email?: string;
    phone?: string;
  };
  tracking: {
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    shortLinks?: boolean;
  };
  stats: {
    sent: number;
    delivered: number;
    failed: number;
    opened?: number;
    clicked?: number;
    replied?: number;
  };
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
}

// Campaign schema
const campaignSchema = new Schema<ICampaign>(
  {
    name: {
      type: String,
      required: true,
      trim: true
    },
    organizationId: {
      type: String,
      required: true,
      index: true
    },
    userId: {
      type: String,
      required: true,
      index: true
    },
    type: {
      type: String,
      enum: Object.values(CampaignType),
      required: true
    },
    status: {
      type: String,
      enum: Object.values(CampaignStatus),
      default: CampaignStatus.DRAFT
    },
    content: {
      body: {
        type: String,
        required: true
      },
      subject: {
        type: String
      },
      template: {
        type: String
      },
      variables: {
        type: Schema.Types.Mixed
      }
    },
    schedule: {
      sendAt: {
        type: Date
      },
      timezone: {
        type: String,
        default: 'UTC'
      }
    },
    recipients: {
      segmentId: {
        type: String
      },
      contactIds: {
        type: [String]
      },
      count: {
        type: Number,
        default: 0
      }
    },
    sender: {
      id: {
        type: String,
        required: true
      },
      name: {
        type: String
      },
      email: {
        type: String
      },
      phone: {
        type: String
      }
    },
    tracking: {
      utmSource: {
        type: String
      },
      utmMedium: {
        type: String
      },
      utmCampaign: {
        type: String
      },
      shortLinks: {
        type: Boolean,
        default: false
      }
    },
    stats: {
      sent: {
        type: Number,
        default: 0
      },
      delivered: {
        type: Number,
        default: 0
      },
      failed: {
        type: Number,
        default: 0
      },
      opened: {
        type: Number,
        default: 0
      },
      clicked: {
        type: Number,
        default: 0
      },
      replied: {
        type: Number,
        default: 0
      }
    },
    sentAt: {
      type: Date
    }
  },
  {
    timestamps: true
  }
);

// Indexes
campaignSchema.index({ organizationId: 1, status: 1 });
campaignSchema.index({ organizationId: 1, type: 1 });
campaignSchema.index({ 'schedule.sendAt': 1 }, { sparse: true });

// Create and export the model
const Campaign = mongoose.model<ICampaign>('Campaign', campaignSchema);

export default Campaign;