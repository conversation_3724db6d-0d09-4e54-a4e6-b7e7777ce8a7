import React, { useState, useCallback, useEffect, useContext } from 'react';
import { Heading, Row, Col, Card,Container } from "@shoutout-labs/shoutout-themes-enterprise";
import BaseLayout from "Layout/BaseLayout";
import { getCardsActivationsummary, getCardsAnalytics, getCardsProcessingStatus } from "Services";
import {
    AccessPermissionModuleNames, AccessPermissionModules,
    AnalyticsActiveExport, AnalyticsActiveView,
    BarChartDataProperties,
    CardTypes,
    ChartColorCodes,
} from "Data";
import { toast } from "react-toastify";
import FlexBox from "../common/flexBox/FlexBox";
import {UserContext} from "Contexts";
import {CardStatus} from "Data";
import VerticalBarChartCard from "../common/charts/barcharts/verticalBarChart/VerticalBarChartCard";
import moment from "moment";
import TreeMapDiskChart from "../common/charts/treeMapDisk/TreeMapDiskChart";
import { convertDataToTreeMapDiskChart, sortAscendingOrder } from "../utils/AnalyticsUtility";
import { convertToLocaleString } from "../../../Utils";
import AnalyticsAccessControl from "../AnalyticsAccessControl";
import "./Cards.scss";

const cardDataFormatConverter = (cardData) => {

    const dataObj = {
        TOTAL : 0,
        [CardStatus?.ACTIVE] : 0,
        [CardStatus?.ASSIGNED] : 0,
        [CardStatus?.DEACTIVATED] : 0,
        [CardStatus?.SUSPENDED] : 0
    };

    if(cardData.data.length > 0){
        cardData?.data.forEach(data => {
             if(dataObj.hasOwnProperty(data.status)){
                 dataObj[data.status] = data.count;
                 dataObj.TOTAL= dataObj.TOTAL + Number(data.count);
             }
        })
    }
    return dataObj;
}


const CardAnalytics = () => {
    const { selectedRegion,isAuthorizedForAction } = useContext(UserContext);
    const [keyTagCardData, setKeyTagCardData] = useState({});
    //const [regularCardData, setRegularCardData] = useState({});
    //const [keyTagRegularCardData, setKeyTagRegularCardData] = useState({});
    const [digitalCardData, setDigitalCardData] = useState({});
    const [cardActivation, setCardActivation] = useState({
        [`${BarChartDataProperties["CARD_ACTIVATION"][1]}`]:[],
        [`${BarChartDataProperties["CARD_ACTIVATION"][2]}`]:[]
        ,[`${BarChartDataProperties["CARD_ACTIVATION"][0]}`]:[]});
    const [digitalCardCount , setDigitalCardCount] = useState(0);
    const [keyTagCardCount , setKeyTagCardCount] = useState(0);
    const [isLoadingCardActivation, setIsLoadingCardActivation] = useState(false);
    const [isLoadingEmbossedCardsStatus, setIsLoadingEmbossedCardsStatus] = useState(false);
    const [embossedCardsStatus, setEmbossedCardsStatus] = useState({data:[],elements:[]});
    const [toDate, setToDate] = useState(moment().format("YYYY-MM-DD"));
    const [fromDate, setFromDate] = useState(moment().subtract(1, 'M').format("YYYY-MM-DD"));
    const [dateBucket,setDateBucket]=useState("DAY")
    const [dateBuckets,setDateBuckets]=useState([
        {  label: "Day", value: "DAY"},
        {  label: "Week", value: "WEEK"},
        {  label: "Month", value: "MONTH"},
    ])

    const loadCardsAnalyticsData = useCallback(async()=> {
        try{
            const keyTagData = await getCardsAnalytics({regionId :  selectedRegion._id , type : [CardTypes.KEY_TAG]});
            const digitalData = await getCardsAnalytics({regionId :  selectedRegion._id , type : [CardTypes.DIGITAL_CARD]});

            setKeyTagCardData(cardDataFormatConverter(keyTagData));
           /// setRegularCardData(cardDataFormatConverter(regularData));
          //  setKeyTagRegularCardData(cardDataFormatConverter(keyTagAndRegularData));
            setDigitalCardData(cardDataFormatConverter(digitalData));

        }catch (e){
            console.error(e);
            toast.error(
                e.message || "Cards analytics data loading failed"
              );
        }

    },[
        selectedRegion,
        setKeyTagCardData,
        setDigitalCardData,
    ]);

    const loadCardActivationData  = useCallback(async ()=> {
        try{
            setIsLoadingCardActivation(true);
            const activationSummary = await getCardsActivationsummary({ regionId:selectedRegion._id, type:[ CardTypes.DIGITAL_CARD, CardTypes.KEY_TAG], dateFrom:fromDate, dateTo:toDate, dateBucket:dateBucket });
            setCardActivation(activationSummary.data.length!==0?sortAscendingOrder({
                dataset:activationSummary.data,
                fromDate:fromDate,
                toDate:toDate,
                properties:BarChartDataProperties["CARD_ACTIVATION"],
                dateBucket
            }):{ [`${BarChartDataProperties["CARD_ACTIVATION"][1]}`]:[],
                [`${BarChartDataProperties["CARD_ACTIVATION"][2]}`]:[],
                [`${BarChartDataProperties["CARD_ACTIVATION"][0]}`]:[]})
            setDigitalCardCount(activationSummary.data.length!==0?activationSummary.data.reduce((sum, a) => sum + a["digitalCards"], 0):"0")
            setKeyTagCardCount(activationSummary.data.length!==0?activationSummary.data.reduce((sum, a) => sum + a["keyTagCards"], 0):"0")
            setIsLoadingCardActivation(false)

        }catch (e){
            console.error(e);
            toast.error(
                e.message || "Card activation data loading failed"
            );
            setIsLoadingCardActivation(false);
        }

    },[setIsLoadingCardActivation,selectedRegion,fromDate,toDate,dateBucket,setCardActivation,setDigitalCardCount,setKeyTagCardCount]);

    const loadEmbossedCardsStatus  = useCallback(async ()=> {
        try{
            setIsLoadingEmbossedCardsStatus(true);
            const cardsProcessingStatus = await getCardsProcessingStatus({
                regionId:selectedRegion._id,
                type:[CardTypes.EMBOSSED_CARD]
            })
            setEmbossedCardsStatus(cardsProcessingStatus.data.length!==0?
                convertDataToTreeMapDiskChart(cardsProcessingStatus.data):
                {data:[],elements:[]}
            )
            setIsLoadingEmbossedCardsStatus(false)

        }catch (e){
            console.error(e);
            toast.error(
                e.message || "Embossed Cards status loading failed"
            );
            setIsLoadingEmbossedCardsStatus(false);
        }

    },[setIsLoadingEmbossedCardsStatus,selectedRegion,setEmbossedCardsStatus]);


    useEffect(() => {
        if(isAuthorizedForAction(AccessPermissionModuleNames.CARD_ANALYTICS,AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardActivationReport)){
            loadCardActivationData()
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateBucket,toDate,fromDate]);

    useEffect(() => {
        if(isAuthorizedForAction(AccessPermissionModuleNames.CARD_ANALYTICS,AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardsSummary)){
            loadCardsAnalyticsData();
        }
        if(isAuthorizedForAction(AccessPermissionModuleNames.CARD_ANALYTICS,AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardProcessingStatusReport)){
            loadEmbossedCardsStatus();
        }
    //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (
        <BaseLayout
            topLeft={<Heading text="Analytics - Cards" />}
            bottom={
                <div className="member-analytics">
                    <Card className="mr-2 w-lg-100 w-md-50 mt-3">
                        <Row className="my-3 pb-2">
                            <Col lg={12} md={12} sm={12} xs={12}>
                                <AnalyticsAccessControl
                                    moduleName={AccessPermissionModuleNames.CARD_ANALYTICS}
                                    actionNames={[
                                        AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardActivationReport,
                                        AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ExportCardActivationReport]}
                                    Logic={"OR"}
                                >
                                    <VerticalBarChartCard
                                        title="Card Assignments"
                                        isLoading={isLoadingCardActivation}
                                        yAxisData={cardActivation[`${BarChartDataProperties["CARD_ACTIVATION"][1]}`]|| []}
                                        yAxisData2={cardActivation[`${BarChartDataProperties["CARD_ACTIVATION"][2]}`]|| []}
                                        xAxisData={cardActivation[`${BarChartDataProperties["CARD_ACTIVATION"][0]}`]|| []}
                                        colors={[ChartColorCodes.DARK_BLUE,ChartColorCodes.ORANGE]}
                                        setDateBucket={setDateBucket}
                                        fromDate ={fromDate}
                                        setFromDate={setFromDate}
                                        toDate={toDate}
                                        setToDate={setToDate}
                                        dateBuckets ={dateBuckets}
                                        setDateBuckets={setDateBuckets}
                                        isVisibleDateFilter={true}
                                        reloadButton
                                        legend={
                                            [`Key Tag Card ${convertToLocaleString(keyTagCardCount)}`,
                                                `Digital Card ${convertToLocaleString(digitalCardCount)}`]
                                        }
                                        activeExport={AnalyticsActiveExport.CARD_ACTIVATION}
                                        selectedRegion={selectedRegion}
                                        dateBucket={dateBucket}
                                        activeView={AnalyticsActiveView.CARD_ANALYTICS}
                                    />

                                </AnalyticsAccessControl>
                            </Col>
                        </Row>
                    </Card>
                    <br/>
                    <Row>
                        <Col lg={6} md={6} sm={6} xs={6}>
                            <Card>
                                <Card.Body>
                                    <Row className='mb-3'>
                                        <Col lg={12} md={12} sm={12} xs={12}>
                                            <div className="mr-2 w-lg-100 w-md-50 mt-3">
                                                <AnalyticsAccessControl
                                                    moduleName={AccessPermissionModuleNames.CARD_ANALYTICS}
                                                    actionNames={[AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardsSummary]}
                                                    Logic={"AND"}
                                                >
                                                    <>
                                                        <h3>Key Tag Card Status Distribution</h3>
                                                        <FlexBox
                                                            data={keyTagCardData}
                                                            title="Key Tag"
                                                        />
                                                    </>
                                                </AnalyticsAccessControl>
                                            </div>
                                        </Col>
                                      {/*  <Col lg={6} md={6} sm={6} xs={6}>
                                            <div className="mr-2 w-lg-100 w-md-50 mt-3">
                                                <FlexBox
                                                    data={regularCardData}
                                                    title="Regular"
                                                />
                                            </div>
                                        </Col>*/}
                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col lg={6} md={6} sm={6} xs={6}>
                                <Card>
                                    <Card.Body>
                                        <Row className='mb-3'>
                                            {/* <Col lg={6} md={6} sm={6} xs={6}>
                                            <div className="mr-2 w-lg-100 w-md-50 mt-3">
                                                <FlexBox
                                                    data={keyTagRegularCardData}
                                                    title="Key Tag and Regular"
                                                />
                                            </div>
                                        </Col>*/}
                                        <Col lg={12} md={12} sm={12} xs={12}>
                                            <div className="mr-2 w-lg-100 w-md-50 mt-3">
                                                <AnalyticsAccessControl
                                                    moduleName={AccessPermissionModuleNames.CARD_ANALYTICS}
                                                    actionNames={[AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardsSummary]}
                                                    Logic={"AND"}
                                                >
                                                    <>
                                                        <h3>Digital Card Status Distribution</h3>
                                                        <FlexBox
                                                            data={digitalCardData}
                                                            title="Digital"
                                                        />
                                                    </>
                                                </AnalyticsAccessControl>
                                            </div>
                                        </Col>
                                    </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                    <br/>
                    <Row>
                        <Col lg={12} md={12} sm={12} xs={12}>
                            <Card>
                                <Card.Body>
                                    <div className="mr-2 w-lg-100 w-md-50 mt-3">
                                        <AnalyticsAccessControl
                                            moduleName={AccessPermissionModuleNames.CARD_ANALYTICS}
                                            actionNames={[AccessPermissionModules[AccessPermissionModuleNames.CARD_ANALYTICS].actions.ViewCardProcessingStatusReport]}
                                            Logic={"AND"}
                                        >
                                            <>
                                                <Container>
                                                    <h3>Card Embossing Flow</h3>
                                                    <TreeMapDiskChart
                                                        isLoading={isLoadingEmbossedCardsStatus}
                                                        data={embossedCardsStatus.data}
                                                        elements={embossedCardsStatus.elements}
                                                    />
                                                </Container>
                                            </>
                                        </AnalyticsAccessControl>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </div>

            }
        />
    )
}

export default CardAnalytics
