import React, { useCallback, useState } from "react";
import PropTypes from "prop-types";
import DefaultGraphImage from "assets/images/defaultImages/Graph 01.svg";
import { ChartHeights, DefaultChartHeight } from "Data";
import DateBucket from "Components/analyticsView/shared/dateBucketFilter/DateBucket";
import { LoadingComponent } from "Components/utils";
import ChartHeightPicker from "../shared/chartHeightPicker/ChartHeightPicker";
import LineChartV2 from "./LineChartV2";

const LineChartCard = ({
    xAxisData,
    yAxisData,
    colors,
    legendData,
    isDataAvailable,
    hideChartHeight,
    hideDateBuckets,
    disabled,
    isLoadingChart,
    lineChartEvents,
    dateBuckets,
    setDateBucket,
}) => {
    const [lineChartLineType, setLineChartType] = useState("false");
    const [lineChartHeight, setLineChartHeight] = useState(DefaultChartHeight);

    const onChangeLineChartHeight = useCallback(
        (e) =>
            setLineChartHeight(
                ChartHeights[e.currentTarget.dataset.value] || {}
            ),
        [setLineChartHeight]
    );

    const onChangeLineChartLineType = useCallback(
        (e) => setLineChartType(String(e.currentTarget.value)),
        [setLineChartType]
    );

    return (
        <div>
            {(!hideChartHeight || !hideDateBuckets) && (
                <div
                    className={`mx-3 mb-4 d-flex justify-content-${
                        !hideChartHeight ? "between" : "end"
                    } align-items-center`}
                >
                    {!hideChartHeight && (
                        <div className="ml-3">
                            <ChartHeightPicker
                                selectedChartHeight={lineChartHeight}
                                smooth={lineChartLineType}
                                disabled={disabled}
                                onChangeChartHeight={onChangeLineChartHeight}
                                onChangeChartLineType={
                                    onChangeLineChartLineType
                                }
                            />
                        </div>
                    )}
                    {!hideDateBuckets && (
                        <div className="d-flex justify-content-end align-items-center">
                            <DateBucket
                                isLoading={disabled}
                                dateBuckets={dateBuckets}
                                isDataAvailable={isDataAvailable}
                                setDateBucket={setDateBucket}
                            />
                        </div>
                    )}
                </div>
            )}
            {isLoadingChart ? (
                <div className="text-center grey-bg">
                    <LoadingComponent />
                    <h4 className="mb-0 pb-3">
                        Loading points activity overview chart...
                    </h4>
                </div>
            ) : (
                <>
                    {isDataAvailable ? (
                        <LineChartV2
                            xAxisData={xAxisData}
                            yAxisData={yAxisData}
                            colors={colors}
                            legendData={legendData}
                            selectedHeight={lineChartHeight}
                            smooth={lineChartLineType === "true"}
                            lineChartEvents={lineChartEvents}
                        />
                    ) : (
                        <div className="text-center">
                            <img
                                className="img-fluid"
                                src={DefaultGraphImage}
                                alt="default graph"
                            />
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

LineChartCard.defaultProps = {
    xAxisData: [],
    yAxisData: [],
    colors: [],
    legendData: [],
    isDataAvailable: false,
    hideChartHeight: false,
    hideDateBuckets: false,
    disabled: false,
    isLoadingChart: false,
    lineChartEvents: {},
    dateBuckets: [],
    setDateBucket: () => {},
};

LineChartCard.propTypes = {
    xAxisData: PropTypes.array,
    yAxisData: PropTypes.array,
    colors: PropTypes.array,
    legendData: PropTypes.array,
    isDataAvailable: PropTypes.bool,
    hideChartHeight: false,
    hideDateBuckets: PropTypes.bool,
    disabled: PropTypes.bool,
    isLoadingChart: PropTypes.bool,
    lineChartEvents: PropTypes.object,
    dateBuckets: PropTypes.array,
    setDateBucket: PropTypes.func,
};

export default LineChartCard;
