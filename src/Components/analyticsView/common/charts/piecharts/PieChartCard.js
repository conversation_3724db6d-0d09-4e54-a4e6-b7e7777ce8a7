import React from 'react';
import DefaultGraphImage from 'assets/images/defaultImages/Graph 01.svg';
import { LoadingComponent } from "Components/utils/UtilComponents";
import ReactEcharts from 'echarts-for-react';
import { Col, Row } from "@shoutout-labs/shoutout-themes-enterprise";

const PieChartCard = ({ data, title, isLoading,fromDate,toDate}) => {

    return (
        <div className="mt-3 px-4 w-100">
            <Col lg={10} md={10} sm={10} xs={10}>
                <Row>
                    <Col lg={12} md={12} sm={12} xs={12}>
                        {title !== "" && <h3 className="mt-1 mx-3">{title}</h3>}
                    </Col>
                    {(fromDate&&toDate)&&<Col lg={12} md={12} sm={12} xs={12}>
                        <h5 className="mt-1 mx-3">Processed Between {fromDate} to {toDate}</h5>
                    </Col>}
                </Row>
            </Col>
            {isLoading ?
                <LoadingComponent/>
                :
                <>
                    {data && data.length > 0 ?
                        <ReactEcharts
                            style={{ width:"100%"}}
                            option={{
                                tooltip : {
                                    trigger: 'item',
                                    formatter: "{a} <br/>{b} : {c} ({d}%)"
                                },
                                series : [
                                    {
                                        name: '',
                                        type: 'pie',
                                        radius: ['30%', '70%'],
                                        center: ['50%', '60%'],
                                        height: '80%',
                                        left: 'center',
                                        width: '90%',
                                        data:data,
                                        itemStyle: {
                                            emphasis: {
                                                shadowBlur: 10,
                                                shadowOffsetX: 0,
                                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                                            }
                                        },
                                        label: {
                                            alignTo: 'edge',
                                            formatter: '{name|{b}}\n{time|{c}}',
                                            minMargin: 5,
                                            edgeDistance: 10,
                                            lineHeight: 15,
                                            rich: {
                                                time: {
                                                    fontSize: 10,
                                                    color: '#999'
                                                }
                                            }
                                        },
                                        labelLine: {
                                            smooth: 0.2,
                                            length: 10,
                                            length2: 20,
                                            maxSurfaceAngle: 10
                                        },
                                        labelLayout: function (params) {
                                            const isLeft = params.labelRect.x <200;
                                            const points = params.labelLinePoints;
                                            // Update the end point.
                                            points[2][0] = isLeft
                                                ? params.labelRect.x
                                                : params.labelRect.x + params.labelRect.width;
                                            return {
                                                labelLinePoints: points
                                            };
                                        },
                                    }
                                ]

                            }}
            />  :
                        <img src={DefaultGraphImage} className="img-fluid p-4" alt='default graph'/>
                    }
                </>
            }
        </div>
    )
}

export default PieChartCard
