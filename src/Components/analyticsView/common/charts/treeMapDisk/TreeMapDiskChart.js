import React from 'react';
import DefaultGraphImage from 'assets/images/defaultImages/Graph 01.svg';
import { LoadingComponent } from "Components/utils/UtilComponents";
import ReactEcharts from 'echarts-for-react';
import {ChartColorCodes} from "Data";


const TreeMapDiskChart = ({
    data,
    title,
    isLoading,
    leftMargin,
    elements
}) => {

    return (
        <div className="py-3 px-4">
            {title !== "" && <h3>{title}</h3>}
            {isLoading ?
                <LoadingComponent/>
                :
                <>
                    {data.length!==0 ||elements.length!==0? <ReactEcharts
                        style={{ marginLeft:  leftMargin || '-11rem', marginRight: '-11rem'}}
                        option={{
                                color : [ChartColorCodes.GREEN],
                              /*  tooltip: {
                                    trigger: 'item',
                                    axisPointer: {
                                        animation: true
                                    }
                                },*/
                                scaleLimit:{max:"0.9"},
                                graphic: {
                                    elements:elements,
                                },
                                series: [
                                    {
                                        layout: 'none',
                                        roam: false,
                                        label: {
                                            show: true
                                        },
                                        edgeSymbol: ['roundRect', 'arrow'],
                                        edgeSymbolSize: [10, 10],
                                        edgeLabel: {
                                            fontSize: 10
                                        },
                                        type: "graph",
                                        symbol: "roundRect",
                                        symbolSize: 100,
                                        data:data,
                                        links: [
                                            {
                                                source: 0,
                                                target: 1,
                                            },
                                            {
                                                source: 1,
                                                target: 2,
                                            },
                                            {
                                                source: 2,
                                                target: 3,
                                            },
                                            {
                                                source: 4,
                                            }
                                        ],
                                        lineStyle: {
                                            opacity: 0.9,
                                            width: 2,
                                            curveness: 0
                                        }
                                    }
                                ]
                            }
                        }
                    />:
                        <img src={DefaultGraphImage} className="img-fluid p-4" alt='default graph'/>
                    }

                </>
            }
        </div>
    )
}

export default TreeMapDiskChart
