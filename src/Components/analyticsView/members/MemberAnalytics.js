import React, { useCallback, useContext, useEffect, useState } from "react";
import { Card, Col, Heading, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import BaseLayout from "Layout/BaseLayout";
import {
    getMemberRegistrationAnalyticsCount,
    getMemberRegistrationAnalyticsSeries,
    getNewReturnMemberCount,
    getNewReturnMemberSeries,
} from "Services";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    AnalyticsActiveExport,
    AnalyticsActiveView,
    BarChartDataProperties,
    ChartColorCodes,
    MemberRegisterFilter,
    MemberRegistrationGroupByTypes,
    PieChartsColorCodes,
    RegistrationMethod,
} from "Data";

import { toast } from "react-toastify";
import PieChartCard from "../common/charts/piecharts/PieChartCard";
import HorizontalBarChartCard from "../common/charts/barcharts/horizontalBarChart/HorizontalBarChartCard";
import Filter from "../common/filterComponent/Filter";
import { UserContext } from "Contexts";
import { convertToLocaleString, toTitleCase } from "Utils";
import VerticalBarChartCard from "../common/charts/barcharts/verticalBarChart/VerticalBarChartCard";
import moment from "moment";

import { convertRegistrationMemberSeriesToVerticalBarChartFormat, sortAscendingOrder } from "../utils/AnalyticsUtility";
import AnalyticsAccessControl from "../AnalyticsAccessControl";
import "./Members.scss";

const AGE = "AGE";
const isVisibleDateFilter = true;
const convertToPieChartDataFormat = (dataset, type) => {
    let dataSeries = [];

    if (dataset && dataset.length > 0) {
        dataSeries = dataset.map(
            dataItem => ({
                name: type && type === AGE ? dataItem?.label : toTitleCase(`${
                    dataItem?.label && dataItem?.locationCode ?
                        dataItem?.label + "/" + dataItem?.locationCode
                        : dataItem?.label || dataItem?.tier || "Other"
                }`),
                value: dataItem?.count,
            }),
        );

    }
    return dataSeries;
};

const MemberAnalytics = () => {

    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const [regMethodData, setRegMethodData] = useState([]);
    const [accountTypesData, setAccountTypesData] = useState([]);
    const [genderData, setGenderData] = useState([]);
    const [ageGroupData, setAgeGroupData] = useState([]);
    const [locationData, setLocationData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingDataRegistration, setIsLoadingDataRegistration] = useState(false);
    const [selectedOption, setSelectedOption] = useState([]);
    const [selectedAge, setSelectedAge] = useState([]);
    const [selectedGender, setSelectedGender] = useState([]);
    const [locationBasedOption, setLocationBasedOption] = useState([]);
    const [locationBasedAge, setLocationBasedAge] = useState([]);
    const [locationBasedGender, setLocationBasedGender] = useState([]);
    const [isLoadingReturnMembers, setIsLoadingReturnMembers] = useState(false);
    const [isLoadingRegistrationMembers, setIsLoadingRegistrationMembers] = useState(false);
    const [toDate, setToDate] = useState(moment().format("YYYY-MM-DD"));
    const [fromDate, setFromDate] = useState(moment().subtract(1, "M").format("YYYY-MM-DD"));
    const [dateBucket, setDateBucket] = useState("DAY");
    const [returnMembers, setReturnMembers] = useState({
        [`${BarChartDataProperties["NEW_MEMBERS"][1]}`]: [],
        [`${BarChartDataProperties["NEW_MEMBERS"][2]}`]: []
        , [`${BarChartDataProperties["NEW_MEMBERS"][0]}`]: [],
    });
    const [returnMembersCount, setReturnMembersCount] = useState({
        newMembers: 0,
        returnMembers: 0,
    });
    const [registrationMembers, setRegistrationMembers] = useState({
        [`${BarChartDataProperties["REGISTRATION_MEMBERS"][1]}`]: [],
        [`${BarChartDataProperties["REGISTRATION_MEMBERS"][2]}`]: []
        , [`${BarChartDataProperties["REGISTRATION_MEMBERS"][0]}`]: [],
    });
    const [registrationMembersCount, setRegistrationMembersCount] = useState({
        adminPortal: 0,
        customerPortal: 0,
    });
    const [dateBuckets, setDateBuckets] = useState([
        { label: "Day", value: "DAY" },
        { label: "Week", value: "WEEK" },
        { label: "Month", value: "MONTH" },
    ]);


    const loadMemberAnalyticsDataRegistration = useCallback(async () => {

        try {
            setIsLoadingDataRegistration(true);
            const queryObjectRegistration = {
                regionId: selectedRegion._id,
                ageRange: selectedAge[0]?.value,
                registrationMethod: "",
                gender: selectedGender[0]?.value,
            };
            const memberRegMethodData = await getMemberRegistrationAnalyticsCount({
                ...queryObjectRegistration,
                groupBy: MemberRegistrationGroupByTypes.REGISTRATION_METHOD,
            });
            const memberAccountTypeData = await getMemberRegistrationAnalyticsCount({
                ...queryObjectRegistration,
                groupBy: MemberRegistrationGroupByTypes.TYPE,
            });
            const memberAgeData = await getMemberRegistrationAnalyticsCount({
                ...queryObjectRegistration,
                groupBy: MemberRegistrationGroupByTypes.AGE,
            });
            const memberGenderData = await getMemberRegistrationAnalyticsCount({
                ...queryObjectRegistration,
                groupBy: MemberRegistrationGroupByTypes.GENDER,
            });

            setRegMethodData(convertToPieChartDataFormat(memberRegMethodData?.data));
            setAccountTypesData(convertToPieChartDataFormat(memberAccountTypeData?.data));
            setAgeGroupData(convertToPieChartDataFormat(memberAgeData?.data, AGE));
            setGenderData(convertToPieChartDataFormat(memberGenderData?.data));
            setIsLoadingDataRegistration(false);

        } catch (e) {
            console.error(e);
            toast.error(
                e.message || "Member analytics data loading failed",
            );
            setIsLoadingDataRegistration(false);
        }

    }, [
        setRegMethodData,
        setAccountTypesData,
        setIsLoadingDataRegistration,
        selectedRegion,
        selectedAge,
        selectedGender,
        setAgeGroupData,
        setGenderData,
    ]);

    const loadNewMembersRegistration = useCallback(async () => {
        try {
            setIsLoadingRegistrationMembers(true);

            const registrationMemberSeries = await getMemberRegistrationAnalyticsSeries({
                regionId: selectedRegion._id,
                registrationsFrom: fromDate,
                registrationsTo: toDate, dateBucket,
                groupBy: MemberRegistrationGroupByTypes.REGISTRATION_METHOD,
            });

            const registrationMemberCount = await getMemberRegistrationAnalyticsCount({
                regionId: selectedRegion._id,
                registrationsFrom: fromDate,
                registrationsTo: toDate,
                groupBy: MemberRegistrationGroupByTypes.REGISTRATION_METHOD,
            });

            setRegistrationMembers(registrationMemberSeries.data.length !== 0 && sortAscendingOrder({
                dataset: convertRegistrationMemberSeriesToVerticalBarChartFormat(registrationMemberSeries.data),
                fromDate: fromDate,
                toDate: toDate,
                properties: BarChartDataProperties["REGISTRATION_MEMBERS"],
                dateBucket,
            }));
            const registrationMemberCountObj = {
                adminPortal: 0,
                customerPortal: 0,
            };
            registrationMemberCount?.data?.forEach((registrationMember) => {
                if (registrationMember?.label === RegistrationMethod.ADMIN_PORTAL) {
                    registrationMemberCountObj["adminPortal"] = registrationMember?.count || 0;
                } else {
                    registrationMemberCountObj["customerPortal"] = registrationMember?.count || 0;
                }
            });
            setRegistrationMembersCount(registrationMemberCountObj);
            setIsLoadingRegistrationMembers(false);

        } catch (e) {
            console.error(e);
            toast.error(
                e.message || "Member analytics data loading failed",
            );
            setIsLoadingRegistrationMembers(false);
        }

    }, [dateBucket, fromDate, selectedRegion, toDate, setRegistrationMembers, setRegistrationMembersCount]);

    const loadNewMembersAndReturnMembers = useCallback(async () => {
        try {
            setIsLoadingReturnMembers(true);
            const returnMemberSeries = await getNewReturnMemberSeries({
                regionId: selectedRegion._id,
                transactionsFrom: fromDate,
                transactionsTo: toDate,
                dateBucket,
            });
            const returnMemberCount = await getNewReturnMemberCount({
                regionId: selectedRegion._id,
                transactionsFrom: fromDate,
                transactionsTo: toDate,
            });
            setReturnMembers(returnMemberSeries.data.length !== 0 && sortAscendingOrder({
                dataset: returnMemberSeries.data,
                fromDate: fromDate,
                toDate: toDate,
                properties: BarChartDataProperties["NEW_MEMBERS"],
                dateBucket,
            }));
            setReturnMembersCount(returnMemberCount);
            setIsLoadingReturnMembers(false);

        } catch (e) {
            console.error(e);
            toast.error(
                e.message || "Member analytics data loading failed",
            );
            setIsLoadingReturnMembers(false);
        }

    }, [dateBucket, fromDate, selectedRegion, toDate, setReturnMembers, setReturnMembersCount]);

    const loadMemberAnalyticsDataLocation = useCallback(async () => {

        const queryObjectRegistration = {
            regionId: selectedRegion._id,
            ageRange: locationBasedAge[0]?.value,
            registrationMethod: "",
            gender: locationBasedGender[0]?.value,
            groupBy: MemberRegistrationGroupByTypes.BRANCH,
            registrationsFrom: fromDate,
            registrationsTo: toDate,
        };
        try {
            setIsLoading(true);
            const locationData = await getMemberRegistrationAnalyticsCount(queryObjectRegistration);
            setLocationData(convertToPieChartDataFormat(locationData?.data));
            setIsLoading(false);

        } catch (e) {
            console.error(e);
            toast.error(
                e.message || "Location wise member analytics data loading failed",
            );
            setIsLoading(false);
        }

    }, [
        setIsLoading,
        selectedRegion,
        locationBasedAge,
        locationBasedGender,
        setLocationData,
        fromDate,
        toDate,
    ]);

    /*

        const loadMemberAnalyticsDataAge = useCallback(async()=> {

            const queryObjectRegistration = {
                regionId : selectedRegion._id,
                groupBy : MemberRegistrationGroupByTypes.AGE,
            }
            try{

                setIsLoading(true);
                const [
                    memberAgeData,
                    memberGenderData
                ] = await Promise.all([
                    getMemberRegistrationAnalyticsCount({...queryObjectRegistration , groupBy : MemberRegistrationGroupByTypes.AGE}),
                    getMemberRegistrationAnalyticsCount({...queryObjectRegistration , groupBy : MemberRegistrationGroupByTypes.GENDER}),
                ]);
                setAgeGroupData(convertToPieChartDataFormat(memberAgeData?.data, AGE));
                setGenderData(convertToPieChartDataFormat(memberGenderData?.data));
                setIsLoading(false);

            }catch (e){
                console.error(e);
                toast.error(
                    e.message || "Member analytics data loading failed"
                  );
                setIsLoading(false);
            }

        },[
            setAgeGroupData,
            setIsLoading,
            selectedRegion,
            setGenderData
        ]);
    */

    useEffect(() => {
        if (isAuthorizedForAction(AccessPermissionModuleNames.MEMBER_ANALYTICS, AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationCounts)) {
            loadMemberAnalyticsDataRegistration();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);


    useEffect(() => {
        if (isAuthorizedForAction(AccessPermissionModuleNames.MEMBER_ANALYTICS, AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewNewReturnMemberSeries)) {
            loadNewMembersAndReturnMembers();
        }
        if (isAuthorizedForAction(AccessPermissionModuleNames.MEMBER_ANALYTICS, AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationSeries) && isAuthorizedForAction(AccessPermissionModuleNames.MEMBER_ANALYTICS, AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationCounts)) {
            loadNewMembersRegistration();
        }
        if (isAuthorizedForAction(AccessPermissionModuleNames.MEMBER_ANALYTICS, AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationCounts)) {
            loadMemberAnalyticsDataLocation();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [toDate, fromDate, dateBucket]);
    return (
        <BaseLayout
            topLeft={<Heading text="Analytics - Members" />}
            bottom={
                <div className="member-analytics">
                    <Card className="mr-2 w-lg-100 w-md-50 mt-3">
                        <AnalyticsAccessControl
                            moduleName={AccessPermissionModuleNames.MEMBER_ANALYTICS}
                            actionNames={[
                                AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewNewReturnMemberSeries,
                                AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ExportNewReturnMemberSeries,
                            ]}
                            Logic={"OR"}
                        >
                            <Row className="my-3 pb-2">
                                <Col lg={12} md={12} sm={12} xs={12}>
                                    <VerticalBarChartCard
                                        title="New Members Transactions vs Return Member Transactions"
                                        isLoading={isLoadingReturnMembers}
                                        yAxisData={returnMembers[`${BarChartDataProperties["NEW_MEMBERS"][1]}`] || []}
                                        yAxisData2={returnMembers[`${BarChartDataProperties["NEW_MEMBERS"][2]}`] || []}
                                        xAxisData={returnMembers[`${BarChartDataProperties["NEW_MEMBERS"][0]}`] || []}
                                        colors={[ChartColorCodes.DARK_BLUE, ChartColorCodes.ORANGE]}
                                        setDateBucket={setDateBucket}
                                        fromDate={fromDate}
                                        setFromDate={setFromDate}
                                        toDate={toDate}
                                        setToDate={setToDate}
                                        dateBuckets={dateBuckets}
                                        setDateBuckets={setDateBuckets}
                                        isVisibleDateFilter={isVisibleDateFilter}
                                        reloadButton
                                        legend={
                                            [`Return Members Transactions ${returnMembersCount.returnMembers}`,
                                                `New Members Transactions ${returnMembersCount.newMembers}`]
                                        }
                                        selectedRegion={selectedRegion}
                                        activeExport={AnalyticsActiveExport.NEW_MEMBERS_AND_RETURN_MEMBERS}
                                        dateBucket={dateBucket}
                                        activeView={AnalyticsActiveView.MEMBER_ANALYTICS}
                                    />
                                </Col>
                            </Row>
                        </AnalyticsAccessControl>
                        <AnalyticsAccessControl
                            moduleName={AccessPermissionModuleNames.MEMBER_ANALYTICS}
                            actionNames={[
                                AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationCounts,
                                AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationSeries,
                            ]}
                            Logic={"OR"}
                        >
                            <Row className="my-3 pb-2">
                                <Col lg={12} md={12} sm={12} xs={12}>
                                    <VerticalBarChartCard
                                        title="Registration Members"
                                        isLoading={isLoadingRegistrationMembers}
                                        yAxisData={registrationMembers[`${BarChartDataProperties["REGISTRATION_MEMBERS"][1]}`] || []}
                                        yAxisData2={registrationMembers[`${BarChartDataProperties["REGISTRATION_MEMBERS"][2]}`] || []}
                                        xAxisData={registrationMembers[`${BarChartDataProperties["REGISTRATION_MEMBERS"][0]}`] || []}
                                        colors={[ChartColorCodes.DARK_BLUE, ChartColorCodes.ORANGE]}
                                        setDateBucket={setDateBucket}
                                        fromDate={fromDate}
                                        setFromDate={setFromDate}
                                        toDate={toDate}
                                        setToDate={setToDate}
                                        dateBuckets={dateBuckets}
                                        setDateBuckets={setDateBuckets}
                                        isVisibleDateFilter={isVisibleDateFilter}
                                        reloadButton
                                        legend={
                                            [`Admin Portal ${convertToLocaleString(registrationMembersCount.adminPortal)}`,
                                                `Customer Portal ${convertToLocaleString(registrationMembersCount.customerPortal)}`]
                                        }
                                        selectedRegion={selectedRegion}
                                        activeExport={AnalyticsActiveExport.NEW_MEMBERS_AND_RETURN_MEMBERS}
                                        dateBucket={dateBucket}
                                        activeView={AnalyticsActiveView.MEMBER_ANALYTICS}
                                        isVisibleDateBucketFilter={false}
                                    />
                                </Col>
                            </Row>
                        </AnalyticsAccessControl>
                        <div className="px-4">
                            <hr />
                        </div>
                        <AnalyticsAccessControl
                            moduleName={AccessPermissionModuleNames.MEMBER_ANALYTICS}
                            actionNames={[
                                AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationCounts,
                                AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ExportMemberRegistrationCounts,
                            ]}
                            Logic={"OR"}
                        >
                            <Row className="my-3">
                                <Col lg={12} md={12} sm={12} xs={12}>
                                    <HorizontalBarChartCard
                                        isLoading={isLoading}
                                        analyticsDataType="registration by locations analytics"
                                        barchartTitle="Registration by Locations"
                                        data={locationData}
                                        xKey1="value"
                                        yKey="name"
                                        barColors={[ChartColorCodes.DARK_BLUE]}
                                        setSelectedFilterByOption={setLocationBasedOption}
                                        selectedFilterByOption={locationBasedOption}
                                        applyFilter={loadMemberAnalyticsDataLocation}
                                        filterByOptions={MemberRegisterFilter}
                                        setSelectedAge={setLocationBasedAge}
                                        setSelectedGender={setLocationBasedGender}
                                        selectedAge={locationBasedAge}
                                        selectedGender={locationBasedGender}
                                        activeExport={AnalyticsActiveExport.LOCATION_BASED_REGISTRATION}
                                        isFilterBy={true}
                                        hideExport={true}
                                        selectedRegion={selectedRegion}
                                        fromDate={fromDate}
                                        toDate={toDate}
                                        groupBy={MemberRegistrationGroupByTypes.BRANCH}
                                        activeView={AnalyticsActiveView.MEMBER_ANALYTICS}
                                    />
                                </Col>
                            </Row>
                        </AnalyticsAccessControl>
                    </Card>
                    <Card className="mr-2 w-lg-100 w-md-50 mt-3">
                        <AnalyticsAccessControl
                            moduleName={AccessPermissionModuleNames.MEMBER_ANALYTICS}
                            actionNames={[AccessPermissionModules[AccessPermissionModuleNames.MEMBER_ANALYTICS].actions.ViewMemberRegistrationCounts]}
                            Logic={"AND"}
                        >
                            <Row className="mb-3 mt-1 mx-3">
                                <Col lg={12} md={12} sm={12} xs={12} className="mx-4">
                                    <Filter
                                        setSelectedOption={setSelectedOption}
                                        selectedOption={selectedOption}
                                        applyFilter={loadMemberAnalyticsDataRegistration}
                                        options={MemberRegisterFilter}
                                        setSelectedAge={setSelectedAge}
                                        setSelectedGender={setSelectedGender}
                                        selectedAge={selectedAge}
                                        selectedGender={selectedGender}
                                    />
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col lg={6} md={6} sm={12} xs={12}>
                                    <div className="d-flex justify-content-center">
                                        <PieChartCard
                                            title="Registration by Registration Methods"
                                            colors={PieChartsColorCodes.REG_BY_REG_METHODS}
                                            data={regMethodData}
                                            isLoading={isLoadingDataRegistration}
                                        />
                                    </div>
                                </Col>
                                <Col lg={6} md={6} sm={12} xs={12}>
                                    <div className="d-flex justify-content-center">
                                        <PieChartCard
                                            title="Registration by Account Types"
                                            colors={PieChartsColorCodes.REG_BY_ACC_TYPES}
                                            data={accountTypesData}
                                            isLoading={isLoadingDataRegistration}
                                        />
                                    </div>
                                </Col>
                                <Col lg={6} md={6} sm={12} xs={12}>
                                    <div className="d-flex justify-content-center">
                                        <PieChartCard
                                            title="Age Distribution"
                                            colors={PieChartsColorCodes.AGE}
                                            data={ageGroupData}
                                            isLoading={isLoadingDataRegistration}
                                        />
                                    </div>
                                </Col>
                                <Col lg={6} md={6} sm={12} xs={12}>
                                    <div className="d-flex justify-content-center">
                                        <PieChartCard
                                            title="Gender Distribution"
                                            colors={PieChartsColorCodes.REG_BY_GENDER}
                                            data={genderData}
                                            isLoading={isLoadingDataRegistration}
                                        />
                                    </div>
                                </Col>
                            </Row>
                        </AnalyticsAccessControl>
                    </Card>
                </div>
            }
        />
    );
};

export default MemberAnalytics;
