import React from "react";
import { Card, OverlayTrigger, Tooltip, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faInfoCircle } from "FaICIconMap";
import PointsActivitySumCard from "../pointsActivityOverview/pointsActivitySumCard/PointsActivitySumCard"; 
import "../pointsActivityOverview/pointsActivitySumCard/PointsActivitySumCard.scss";

const PointsCardView = ({
    statName,
    statData,
    isLoading,
    customClassName = "",
    cardKey = "",
    description = "",
}) => {
    
    
    const formattedStatData = typeof statData === "number" ? statData.toString() : statData;


    return (
        <div
            data-id={cardKey}
            className={`points-sum-card ${customClassName || "w-25 d-flex justify-content-around align-items-center"}`}
        >
            <Card.Body className="d-flex flex-column align-items-start p-1"> 
                <div className="d-flex justify-content-between align-items-center w-100">
                    <div className="font-weight-bold stat-name-large p-3 m-0">
                        {statName}
                        {description && (
                            <OverlayTrigger
                                placement="bottom"
                                overlay={<Tooltip id={`tooltip-${cardKey}`}>{description}</Tooltip>}
                            >
                                <span className="d-inline-block ml-2">
                                    <IcIcon className="ml-1" icon={faInfoCircle} />
                                </span>
                            </OverlayTrigger>
                        )}
                    </div>
                </div>
                
                <PointsActivitySumCard
                    statName=" " 
                    statData={formattedStatData} 
                    isLoading={isLoading}
                    customClassName={customClassName}
                    cardKey={cardKey}
                />
            </Card.Body>
        </div>
    );
};

export default PointsCardView;
