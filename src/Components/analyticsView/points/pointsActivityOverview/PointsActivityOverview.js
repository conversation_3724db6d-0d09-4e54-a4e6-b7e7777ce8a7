import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import moment from "moment";
import PropTypes from "prop-types";
import { DataContext, UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    LineChartColorCodes,
    TransactionTypes,
} from "Data";
import {
    exportPointsOverviewSeries,
    getPointsOverviewCounts,
    getPointsOverviewSeries,
    getPointsSummary,
} from "Services";
import {
    formatToCommonFormat,
    formatToMonthAndYearOnly,
    getDefaultValuesOfFilters,
    getQueryFilters,
    removeDuplicatedKeysFromArrayOfObjects,
    roundOffToTwoDecimals,
    toTitleCase,
} from "Utils";
import {
    DateBucketData,
    DefaultDateBuckets,
    PointsSummaryDataKeys,
    PointsSummaryTotals,
    SortOrderForRatioForChildren,
    SortOrderForUnknown,
} from "Components/analyticsView/data";
import LineChartCard from "Components/analyticsView/common/charts/linechart/LineChartCard";
import AnalyticsAccessControl from "Components/analyticsView/AnalyticsAccessControl";
import { AnalyticsFunctionalAccessControl } from "Components/analyticsView/AnalyticsFunctionalAccessControl";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";
import ReportsViaEmail from "Components/common/reportsViaEmail/ReportsViaEmail";
import PointsAnalyticsV2Header from "../shared/PointsAnalyticsV2Header";
import PointsActivitySumCard from "./pointsActivitySumCard/PointsActivitySumCard";
import PointsSummary from "./pointsSummary/PointsSummary";

const activityOverviewFilters = [
    { label: "Merchant", value: "merchantId" },
    { label: "Merchant Location", value: "merchantLocationId" },
];

const inputKeys = [
    {
        key: "select",
        values: ["merchantId", "merchantLocationId"],
    },
];

const analyticsCountDefaultData = {
    [TransactionTypes.COLLECTION]: {
        sumDataKey: "pointscollected",
        pointscollected: 0,
    },
    [TransactionTypes.REDEMPTION]: {
        sumDataKey: "pointsredeemed",
        pointsredeemed: 0,
    },
    [`${toTitleCase(TransactionTypes.ADJUSTMENT)} (+)`]: {
        sumDataKey: "pointsadjustmentsplus",
        pointsadjustmentsplus: 0,
    },
    [`${toTitleCase(TransactionTypes.ADJUSTMENT)} (-)`]: {
        sumDataKey: "pointsadjustmentsminus",
        pointsadjustmentsminus: 0,
    },
};

const PointsActivityOverview = ({
    fromDate,
    toDate,
    dateBucketForDateRange,
    dateBucketsForDateRange,
    disableParentComponents,
    setDisableParentComponents,
    setDateBucketForDateRange,
}) => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const {
        allMerchantsForDropdown = [],
        allMerchantLocationsForDropdown = [],
    } = useContext(DataContext);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingSumData, setIsLoadingSumData] = useState(false);
    const [isLoadingTableData, setIsLoadingTableData] = useState(false);
    const [lineChartData, setLineChartData] = useState({
        xAxisData: [],
        yAxisData: [],
    });
    const [analyticsSumsData, setAnalyticsSumsData] = useState(
        analyticsCountDefaultData
    );
    const [gridData, setGridData] = useState([[]]);
    const [dateBucket, setDateBucket] = useState(DateBucketData.DAY);
    const [isDataAvailable, setIsDataAvailable] = useState(false);
    const [isTableDataAvailable, setIsTableDataAvailable] = useState(false);
    const [appliedFilters, setAppliedFilters] = useState([]);
    const [appliedFilterRows, setAppliedFilterRows] = useState([]);
    const [isReloading, setIsReloading] = useState(false);
    const [showExportPointsOverviewReport, setShowExportPointsOverviewReport] =
        useState(false);
    const [scrollToTableColumnValue, setScrollToTableColumnValue] =
        useState(null);

    const getQueryParamData = useCallback(
        (arrayToReduce) =>
            arrayToReduce.reduce((result, item) => {
                const filterInput =
                    inputKeys.find((iK) => {
                        if (iK.key === "date-range") {
                            return Object.keys(iK.values).includes(item.value);
                        } else {
                            return iK.values.includes(item.value);
                        }
                    })?.key || "";
                let options = [];
                let labelKey = "label";
                let valueKey = "value";
                let groupBy = "";

                const defaultValues = getDefaultValuesOfFilters(
                    { filterInput, filterKey: item.value },
                    inputKeys
                );

                if (filterInput === "select") {
                    switch (item.value) {
                        case "merchantId":
                            options = allMerchantsForDropdown;
                            labelKey = "merchantName";
                            valueKey = "_id";
                            break;
                        case "merchantLocationId":
                            options = allMerchantLocationsForDropdown;
                            labelKey = "locationName";
                            valueKey = "_id";
                            groupBy = "merchantName";
                            break;
                        default:
                            break;
                    }
                }

                result[item.value] = {
                    filterInput,
                    key: item.value,
                    id: item.value,
                    name: item.value,
                    valueKey,
                    placeholder: item?.label?.toLowerCase() || "",
                    ...defaultValues,
                    ...(filterInput === "select"
                        ? { options, labelKey, groupBy }
                        : {}),
                };

                return result;
            }, {}),
        [allMerchantsForDropdown, allMerchantLocationsForDropdown]
    );

    const tabQueryFilterData = useMemo(
        () => ({
            queryParamFilterOptions: activityOverviewFilters,
            queryParamFilterMetadata: getQueryParamData(
                activityOverviewFilters
            ),
        }),
        [getQueryParamData]
    );

    const loadPointsActivityOverviewData = useCallback(
        async ({ fromDate, toDate, bucket }, filters = []) => {
            try {
                let queryObj = {
                    regionId: selectedRegion?._id,
                    dateFrom: fromDate,
                    dateTo: toDate,
                    dateBucket: bucket,
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                setDisableParentComponents(true);
                setIsLoading(true);
                const response = await getPointsOverviewSeries(queryObj);

                const mappedResponse =
                    response?.data?.map((item) => ({
                        ...item,
                        dateBucketKey: item?.datebucketkey
                            ? formatToCommonFormat(item.datebucketkey)
                            : "",
                    })) || [];
                mappedResponse.sort(
                    (a, b) =>
                        new Date(a?.dateBucketKey?.toString()) -
                        new Date(b?.dateBucketKey?.toString())
                );

                const xData = mappedResponse.map((mR) => mR.dateBucketKey);

                const mappedResToObj = mappedResponse.reduce((result, item) => {
                    Object.keys(item).forEach((key) => {
                        const values = [];
                        mappedResponse.forEach((value) => {
                            values.push(value[key]);
                        });
                        result = { ...result, [key]: values };
                    });
                    return result;
                }, {});
                const yData = Object.entries(mappedResToObj)
                    .map(([key, values]) => {
                        let keyValue = null;

                        switch (key) {
                            case "pointscollected": {
                                keyValue = toTitleCase(
                                    TransactionTypes.COLLECTION
                                );
                                break;
                            }
                            case "pointsredeemed": {
                                keyValue = toTitleCase(
                                    TransactionTypes.REDEMPTION
                                );
                                break;
                            }
                            case "pointsadjustmentsplus": {
                                keyValue = `${toTitleCase(
                                    TransactionTypes.ADJUSTMENT
                                )} (+)`;
                                break;
                            }
                            case "pointsadjustmentsminus": {
                                keyValue = `${toTitleCase(
                                    TransactionTypes.ADJUSTMENT
                                )} (-)`;
                                break;
                            }
                            default:
                                break;
                        }

                        return {
                            key: keyValue,
                            values: values?.every((v) => !isNaN(v))
                                ? values.map((val) =>
                                      roundOffToTwoDecimals(val || 0)
                                  )
                                : values,
                        };
                    })
                    .filter((item) => item.key);

                setLineChartData({
                    xAxisData: xData,
                    yAxisData: yData,
                });
                setIsDataAvailable(
                    xData.length !== 0 &&
                        yData.every((item) => item.values.length !== 0) &&
                        !yData
                            .map((item) => item.values.map((v) => v))
                            .flat()
                            .every((val) => val === 0)
                );
                setIsLoading(false);
                setDisableParentComponents(false);
            } catch (e) {
                setIsLoading(false);
                setDisableParentComponents(false);
                setLineChartData({
                    xAxisData: [],
                    yAxisData: [],
                });
                setIsDataAvailable(false);
                console.error(e);
                toast.error(
                    <div>
                        Failed to load points activity overview analytics!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            selectedRegion?._id,
            setDisableParentComponents,
            setIsLoading,
            setLineChartData,
            setIsDataAvailable,
        ]
    );

    const loadPointsActivityOverviewSumData = useCallback(
        async (filters = []) => {
            try {
                let queryObj = {
                    regionId: selectedRegion?._id,
                    dateFrom: fromDate,
                    dateTo: toDate,
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                setDisableParentComponents(true);
                setIsLoadingSumData(true);
                const analyticsSumResponse = await getPointsOverviewCounts(
                    queryObj
                );

                const countData =
                    analyticsSumResponse?.data &&
                    Array.isArray(analyticsSumResponse.data) &&
                    analyticsSumResponse.data.length !== 0
                        ? analyticsSumResponse.data[0]
                        : analyticsCountDefaultData;

                setAnalyticsSumsData((prevValue) => ({
                    [TransactionTypes.COLLECTION]: {
                        ...prevValue[TransactionTypes.COLLECTION],
                        pointscollected: countData?.pointscollected || 0,
                    },
                    [TransactionTypes.REDEMPTION]: {
                        ...prevValue[TransactionTypes.REDEMPTION],
                        pointsredeemed: countData?.pointsredeemed || 0,
                    },
                    [`${toTitleCase(TransactionTypes.ADJUSTMENT)} (+)`]: {
                        ...prevValue[
                            `${toTitleCase(TransactionTypes.ADJUSTMENT)} (+)`
                        ],
                        pointsadjustmentsplus:
                            countData?.pointsadjustmentsplus || 0,
                    },
                    [`${toTitleCase(TransactionTypes.ADJUSTMENT)} (-)`]: {
                        ...prevValue[
                            `${toTitleCase(TransactionTypes.ADJUSTMENT)} (-)`
                        ],
                        pointsadjustmentsminus:
                            countData?.pointsadjustmentsminus
                                ? `-${countData.pointsadjustmentsminus}`
                                : 0,
                    },
                }));
                setIsLoadingSumData(false);
                setDisableParentComponents(false);
            } catch (e) {
                setIsLoadingSumData(false);
                setDisableParentComponents(false);
                setAnalyticsSumsData(analyticsCountDefaultData);
                console.error(e);
                toast.error(
                    <div>
                        Failed to load points activity summary!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            selectedRegion?._id,
            fromDate,
            toDate,
            setDisableParentComponents,
            setIsLoadingSumData,
            setAnalyticsSumsData,
        ]
    );

    const loadPointsSummaryData = useCallback(
        async ({ fromDate, toDate, bucket }, filters = []) => {
            try {
                let queryObj = {
                    regionId: selectedRegion?._id,
                    dateFrom: fromDate,
                    dateTo: toDate,
                    dateBucket: bucket,
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                setScrollToTableColumnValue(null);
                setDisableParentComponents(true);
                setIsLoadingTableData(true);
                const response = await getPointsSummary(queryObj);
                const data =
                    response?.data && Object.keys(response.data).length !== 0
                        ? response.data
                        : {};

                const rowTitleValues = [];
                const allSortedDatesAndValues = [];

                Object.entries(data).forEach(([date, value]) => {
                    let dateText = date || "~unknown";

                    switch (bucket) {
                        case DateBucketData.MONTH:
                        case DateBucketData.QUARTER: {
                            dateText = moment(date).isValid()
                                ? formatToMonthAndYearOnly(date)
                                : date;
                            break;
                        }
                        case DateBucketData.YEAR: {
                            dateText = moment(date).isValid()
                                ? new Date(date).getFullYear()
                                : date;
                            break;
                        }
                        default:
                            break;
                    }
                    const datesAndValues = [
                        { value: dateText, date, order: 0, className: "" },
                    ];

                    // * Below 'if' condition is used to prevent pushing duplicate values into the array.
                    if (
                        !rowTitleValues.some(
                            (rTV) =>
                                rTV?.id ===
                                PointsSummaryTotals.TOTAL_BALANCE.type
                        )
                    )
                        rowTitleValues.push({
                            id: PointsSummaryTotals.TOTAL_BALANCE.type,
                            value: PointsSummaryTotals.TOTAL_BALANCE.label,
                            order: PointsSummaryTotals.TOTAL_BALANCE.sortOrder,
                            description:
                                PointsSummaryTotals.TOTAL_BALANCE.description,
                            className: "grid-cell-totals",
                        });

                    datesAndValues.push({
                        subTypeId: PointsSummaryTotals.TOTAL_BALANCE.type,
                        value: value?.total || 0,
                        date,
                        label: PointsSummaryTotals.TOTAL_BALANCE.label,
                        order: PointsSummaryTotals.TOTAL_BALANCE.sortOrder,
                        description:
                            PointsSummaryTotals.TOTAL_BALANCE.description,
                        className: "grid-cell-totals",
                    });

                    if (filters.length === 0) {
                        // * Below 'if' condition is used to prevent pushing duplicate values into the array.
                        if (
                            !rowTitleValues.some(
                                (rTV) =>
                                    rTV?.id ===
                                    PointsSummaryTotals.TOTAL_OUTSTANDING.type
                            )
                        )
                            rowTitleValues.push({
                                id: PointsSummaryTotals.TOTAL_OUTSTANDING.type,
                                value: PointsSummaryTotals.TOTAL_OUTSTANDING
                                    .label,
                                order: PointsSummaryTotals.TOTAL_OUTSTANDING
                                    .sortOrder,
                                description:
                                    PointsSummaryTotals.TOTAL_OUTSTANDING
                                        .description,
                                className: "grid-cell-totals",
                            });

                        datesAndValues.push({
                            subTypeId:
                                PointsSummaryTotals.TOTAL_OUTSTANDING.type,
                            value: value?.outstanding || 0,
                            date,
                            label: PointsSummaryTotals.TOTAL_OUTSTANDING.label,
                            order: PointsSummaryTotals.TOTAL_OUTSTANDING
                                .sortOrder,
                            description:
                                PointsSummaryTotals.TOTAL_OUTSTANDING
                                    .description,
                            className: "grid-cell-totals",
                        });
                    }

                    if (value?.types && Object.keys(value.types).length !== 0) {
                        Object.entries(value.types).forEach(
                            ([vKey, vValue]) => {
                                const className = `grid-cell-${PointsSummaryDataKeys[vKey]?.color}`;

                                // * Below 'if' condition is used to prevent pushing duplicate values into the array.
                                if (
                                    !rowTitleValues.some(
                                        (rTV) =>
                                            rTV?.id ===
                                            PointsSummaryDataKeys[vKey]?.label
                                    )
                                )
                                    rowTitleValues.push({
                                        id:
                                            PointsSummaryDataKeys[vKey]
                                                ?.label || "~unknown",
                                        value:
                                            PointsSummaryDataKeys[vKey]
                                                ?.label || "~unknown",
                                        order:
                                            PointsSummaryDataKeys[vKey]
                                                ?.sortOrder ||
                                            SortOrderForUnknown,
                                        className,
                                    });
                                datesAndValues.push({
                                    subTypeId:
                                        PointsSummaryDataKeys[vKey]?.label ||
                                        "~unknown",
                                    value: vValue?.total || 0,
                                    date,
                                    label:
                                        PointsSummaryDataKeys[vKey]?.label ||
                                        "~unknown",
                                    order:
                                        PointsSummaryDataKeys[vKey]
                                            ?.sortOrder || SortOrderForUnknown,
                                    className,
                                });

                                if (vValue?.items?.length !== 0) {
                                    vValue.items?.forEach((vItem) => {
                                        let sortOrderForChild =
                                            SortOrderForUnknown;

                                        if (
                                            PointsSummaryDataKeys[vKey]
                                                ?.sortOrder
                                        ) {
                                            const existingChildElem =
                                                PointsSummaryDataKeys[
                                                    vKey
                                                ]?.childElements.find(
                                                    (cE) =>
                                                        cE?.label ===
                                                            vItem?.subTypeName ||
                                                        cE?.label ===
                                                            vItem?.subTypeId ||
                                                        cE?.label === "~unknown"
                                                );

                                            if (existingChildElem) {
                                                sortOrderForChild =
                                                    existingChildElem?.sortOrder;
                                            } else {
                                                const sOChild =
                                                    PointsSummaryDataKeys[vKey]
                                                        .sortOrder +
                                                    (PointsSummaryDataKeys[vKey]
                                                        ?.childElements.length +
                                                        1) *
                                                        SortOrderForRatioForChildren;

                                                sortOrderForChild = sOChild;
                                                PointsSummaryDataKeys[
                                                    vKey
                                                ]?.childElements?.push({
                                                    label:
                                                        vItem?.subTypeName ||
                                                        vItem?.subTypeId ||
                                                        "~unknown",
                                                    sortOrder: sOChild,
                                                });
                                            }
                                        }

                                        // * Below 'if' condition is used to prevent pushing duplicate values into the array.
                                        if (
                                            !rowTitleValues.some(
                                                (rTV) =>
                                                    rTV?.id === vItem?.subTypeId
                                            )
                                        )
                                            rowTitleValues.push({
                                                id:
                                                    vItem?.subTypeId ||
                                                    "~unknown",
                                                value:
                                                    vItem?.subTypeName ||
                                                    vItem?.subTypeId ||
                                                    "~unknown",
                                                order: sortOrderForChild,
                                                subOrder:
                                                    vItem?.subTypeName ||
                                                    vItem?.subTypeId ||
                                                    "~unknown",
                                                className: "",
                                            });
                                        datesAndValues.push({
                                            subTypeId:
                                                vItem?.subTypeId || "~unknown",
                                            value: vItem?.totalPoints || 0,
                                            date,
                                            label:
                                                vItem?.subTypeName ||
                                                vItem?.subTypeId ||
                                                "~unknown",
                                            order: sortOrderForChild,
                                            className: "",
                                        });
                                    });
                                }
                            }
                        );
                    }

                    datesAndValues.sort((a, b) => a?.order - b?.order);
                    allSortedDatesAndValues.push(datesAndValues);
                });

                rowTitleValues.sort((a, b) => a?.order - b?.order);

                const allUniqueRowTitles =
                    removeDuplicatedKeysFromArrayOfObjects(
                        rowTitleValues,
                        "id"
                    );

                const allGridData = allSortedDatesAndValues.map((child) => {
                    const allAvailableRowTitlesOfChild = child?.map(
                        (ch) =>
                            `${ch?.subTypeId || "~unknown"}:${
                                ch?.label || "~unknown"
                            }`
                    );

                    const dates = [
                        ...child,
                        ...allUniqueRowTitles
                            .filter(
                                (aURT) =>
                                    !allAvailableRowTitlesOfChild?.includes(
                                        `${aURT?.id || "~unknown"}:${
                                            aURT?.value || "~unknown"
                                        }`
                                    )
                            )
                            .map((fAURT) => ({
                                subTypeId: fAURT?.id || "~unknown",
                                value: 0,
                                date: null,
                                label: fAURT?.value || "~unknown",
                                order: fAURT?.order || SortOrderForUnknown,
                                className: fAURT?.className || "",
                            })),
                    ];

                    return dates.sort((a, b) => a?.order - b?.order);
                });

                allGridData.unshift([
                    { id: null, value: <>&nbsp;</>, order: 0, className: "" }, // * The 0,0 cell data.
                    ...allUniqueRowTitles.sort((a, b) => a?.order - b?.order),
                ]);

                setGridData(allGridData);
                setIsTableDataAvailable(Object.keys(data).length !== 0);
                setIsLoadingTableData(false);
                setDisableParentComponents(false);
            } catch (e) {
                console.error(e);
                setScrollToTableColumnValue(null);
                setIsLoadingTableData(false);
                setDisableParentComponents(false);
                setGridData([[]]);
                setIsTableDataAvailable(false);
                toast.error(
                    <div>
                        Failed to load points summary analytics!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            selectedRegion?._id,
            setDisableParentComponents,
            setScrollToTableColumnValue,
            setIsLoadingTableData,
            setGridData,
            setIsTableDataAvailable,
        ]
    );

    const onSetDateBucket = useCallback(
        (dB) => {
            setDateBucket(dB);
            setDateBucketForDateRange("");
            loadPointsActivityOverviewData(
                { fromDate, toDate, bucket: dB },
                appliedFilters
            );
            loadPointsSummaryData(
                { fromDate, toDate, bucket: dB },
                appliedFilters
            );
        },
        [
            fromDate,
            toDate,
            appliedFilters,
            setDateBucketForDateRange,
            loadPointsActivityOverviewData,
            loadPointsSummaryData,
            setDateBucket,
        ]
    );

    const onReloadPointActivityOverviewData = useCallback(async () => {
        setIsReloading(true);
        await Promise.all([
            loadPointsActivityOverviewData(
                { fromDate, toDate, bucket: dateBucket },
                appliedFilters
            ),
            loadPointsActivityOverviewSumData(appliedFilters),
            loadPointsSummaryData(
                { fromDate, toDate, bucket: dateBucket },
                appliedFilters
            ),
        ]);
        setIsReloading(false);
    }, [
        fromDate,
        toDate,
        appliedFilters,
        dateBucket,
        loadPointsActivityOverviewData,
        loadPointsActivityOverviewSumData,
        loadPointsSummaryData,
        setIsReloading,
    ]);

    const onShowExportPointsOverviewReport = useCallback(
        () => setShowExportPointsOverviewReport(true),
        [setShowExportPointsOverviewReport]
    );

    const onCloseExportPointsOverviewReport = useCallback(
        () => setShowExportPointsOverviewReport(false),
        [setShowExportPointsOverviewReport]
    );

    const onExportPointActivityOverviewData = useCallback(
        async (notificationEmails = []) => {
            try {
                let queryObj = {
                    regionId: selectedRegion?._id,
                    dateFrom: fromDate,
                    dateTo: toDate,
                    dateBucket,
                    notificationEmails,
                };

                if (appliedFilters.length !== 0) {
                    queryObj = {
                        ...queryObj,
                        ...getQueryFilters(appliedFilters),
                    };
                }
                setDisableParentComponents(true);
                await exportPointsOverviewSeries(queryObj);
                setDisableParentComponents(false);
            } catch (e) {
                setDisableParentComponents(false);
                throw e;
            }
        },
        [
            fromDate,
            toDate,
            selectedRegion?._id,
            dateBucket,
            appliedFilters,
            setDisableParentComponents,
        ]
    );

    // * This is used for setting the column to scroll to in the Points Summary table, based on the data point value of the line chart.
    const onMouseOverLineChart = useCallback(
        (e) => {
            const colIndexforHoveredPoint =
                gridData.findIndex((gD) =>
                    gD.find((i) => i?.date === e?.name)
                ) || null;
            setScrollToTableColumnValue(colIndexforHoveredPoint);
        },
        [gridData, setScrollToTableColumnValue]
    );

    // * Line chart events
    // ? References: "https://www.npmjs.com/package/echarts-for-react" and "https://echarts.apache.org/en/api.html#events.Mouse%20events"
    const onEvents = useMemo(
        () => ({ mouseover: onMouseOverLineChart }),
        [onMouseOverLineChart]
    );

    useEffect(() => {
        if (dateBucketForDateRange) {
            setDateBucket(dateBucketForDateRange);
            loadPointsActivityOverviewData(
                { fromDate, toDate, bucket: dateBucketForDateRange },
                appliedFilters
            );
            loadPointsSummaryData(
                { fromDate, toDate, bucket: dateBucketForDateRange },
                appliedFilters
            );
            setDateBucketForDateRange("");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate, toDate, dateBucketForDateRange, appliedFilters]);

    useEffect(() => {
        if (!dateBucketForDateRange) {
            loadPointsActivityOverviewData(
                { fromDate, toDate, bucket: dateBucket },
                appliedFilters
            );
            loadPointsSummaryData(
                { fromDate, toDate, bucket: dateBucket },
                appliedFilters
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fromDate, toDate, appliedFilters]);

    useEffect(() => {
        loadPointsActivityOverviewSumData(appliedFilters);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedRegion?._id, fromDate, toDate, appliedFilters]);

    return (
        <div className="my-3">
            <AnalyticsAccessControl
                moduleName={AccessPermissionModuleNames.POINT_ANALYTICS}
                actionNames={[
                    AccessPermissionModules[
                        AccessPermissionModuleNames.POINT_ANALYTICS
                    ].actions.ViewPointsOverviewSeries,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.POINT_ANALYTICS
                    ].actions.ViewPointsOverviewCount,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.POINT_ANALYTICS
                    ].actions.ExportPointsOverviewSeries,
                ]}
                Logic={"OR"}
            >
                <PointsAnalyticsV2Header
                    analyticsHeaderName="Points Activity Overview"
                    fromDate={
                        fromDate ||
                        moment().subtract(1, "M").format("YYYY-MM-DD")
                    }
                    toDate={toDate || moment().format("YYYY-MM-DD")}
                    tabQueryFilterData={tabQueryFilterData}
                    appliedFilters={appliedFilters}
                    appliedFilterRows={appliedFilterRows}
                    disabled={
                        disableParentComponents ||
                        isLoading ||
                        isLoadingSumData ||
                        isReloading
                    }
                    isDataAvailable={isDataAvailable}
                    isReloading={isReloading}
                    reloadBtnName="Reload Points Activity Overview and Points Summary"
                    setAppliedFilters={setAppliedFilters}
                    setAppliedFilterRows={setAppliedFilterRows}
                    onReload={onReloadPointActivityOverviewData}
                    onExport={onShowExportPointsOverviewReport}
                />
                <div className="mt-3">
                    {AnalyticsFunctionalAccessControl({
                        isAuthorizedForAction,
                        moduleName: AccessPermissionModuleNames.POINT_ANALYTICS,
                        actionNames: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.POINT_ANALYTICS
                            ].actions.ViewPointsOverviewCount,
                        ],
                        Logic: "AND",
                    }) ? (
                        <div className="d-flex justify-content-around align-items-center mt-3">
                            {Object.entries(analyticsSumsData).map(
                                ([key, value]) => (
                                    <PointsActivitySumCard
                                        key={key}
                                        statName={`Points ${
                                            ~[
                                                TransactionTypes.COLLECTION,
                                                TransactionTypes.REDEMPTION,
                                            ].indexOf(key)
                                                ? toTitleCase(key)
                                                : key
                                        }`}
                                        customClassName="mx-2 w-100 d-flex justify-content-around align-items-center"
                                        statData={value[value.sumDataKey]}
                                        isLoading={isLoadingSumData}
                                        cardKey={value.sumDataKey}
                                    />
                                )
                            )}
                        </div>
                    ) : (
                        <h4 className="text-danger text-center rounded grey-bg p-3">
                            You are not authorized to view this content
                        </h4>
                    )}
                    {AnalyticsFunctionalAccessControl({
                        isAuthorizedForAction,
                        moduleName: AccessPermissionModuleNames.POINT_ANALYTICS,
                        actionNames: [
                            AccessPermissionModules[
                                AccessPermissionModuleNames.POINT_ANALYTICS
                            ].actions.ViewPointsOverviewSeries,
                        ],
                        Logic: "AND",
                    }) ? (
                        <>
                            <hr />
                            <LineChartCard
                                xAxisData={lineChartData.xAxisData}
                                yAxisData={lineChartData.yAxisData}
                                colors={[
                                    LineChartColorCodes.COLLECTION,
                                    LineChartColorCodes.REDEMPTION,
                                    LineChartColorCodes.ADJUSTMENT_POSITIVE,
                                    LineChartColorCodes.ADJUSTMENT_NEGATIVE,
                                ]}
                                legendData={lineChartData.yAxisData.map(
                                    (yD) => yD?.key
                                )}
                                isDataAvailable={isDataAvailable}
                                disabled={
                                    disableParentComponents ||
                                    isLoading ||
                                    isLoadingSumData ||
                                    !isDataAvailable
                                }
                                isLoadingChart={isLoading}
                                lineChartEvents={onEvents}
                                dateBuckets={
                                    dateBucketsForDateRange?.length !== 0
                                        ? dateBucketsForDateRange
                                        : DefaultDateBuckets
                                }
                                setDateBucket={onSetDateBucket}
                            />
                            <div className="mt-3">
                                <PointsSummary
                                    regionId={selectedRegion?._id}
                                    dateFrom={fromDate}
                                    dateTo={toDate}
                                    dateBucket={dateBucket}
                                    tabQueryFilterData={tabQueryFilterData}
                                    appliedFilters={appliedFilters}
                                    appliedFilterRows={appliedFilterRows}
                                    disableParentComponents={
                                        disableParentComponents
                                    }
                                    gridData={gridData}
                                    isTableDataAvailable={isTableDataAvailable}
                                    isLoadingTableData={isLoadingTableData}
                                    scrollToTableColumnValue={
                                        scrollToTableColumnValue
                                    }
                                    setDisableParentComponents={
                                        setDisableParentComponents
                                    }
                                />
                            </div>
                        </>
                    ) : (
                        <h4 className="text-danger text-center rounded grey-bg p-3">
                            You are not authorized to view this content
                        </h4>
                    )}
                    {showExportPointsOverviewReport && (
                        <ReportsViaEmail
                            show={showExportPointsOverviewReport}
                            exportHeader="Export Points Activity Overview Report"
                            customInfoPanel={
                                appliedFilters.length !== 0 &&
                                appliedFilterRows.length !== 0 ? (
                                    <div className="w-100 mb-3">
                                        <QueryParamFilters
                                            queryParamFilterMetadata={
                                                tabQueryFilterData?.queryParamFilterMetadata
                                            }
                                            queryParamFilterOptions={
                                                tabQueryFilterData?.queryParamFilterOptions
                                            }
                                            appliedFilters={appliedFilters}
                                            appliedFilterRows={
                                                appliedFilterRows
                                            }
                                            viewOnly
                                            viewOnlyTitle={
                                                <div className="font-weight-bold mb-2">
                                                    {`Filter${
                                                        appliedFilters.length ===
                                                        1
                                                            ? ""
                                                            : "s"
                                                    } Applied for Points Activity Overview Report`}
                                                </div>
                                            }
                                        />
                                    </div>
                                ) : null
                            }
                            successText="Successfully created a job to export the Points Activity Overview analytics. The report will be sent to the given email address/addresses when ready."
                            errorText="Failed to export Points Activity Overview analytics!"
                            onExport={onExportPointActivityOverviewData}
                            onHide={onCloseExportPointsOverviewReport}
                        />
                    )}
                </div>
            </AnalyticsAccessControl>
        </div>
    );
};

PointsActivityOverview.defaultProps = {
    fromDate: "",
    toDate: "",
    dateBucketForDateRange: "",
    dateBucketsForDateRange: [],
    disableParentComponents: false,
    setDisableParentComponents: () => {},
    setDateBucketForDateRange: () => {},
};

PointsActivityOverview.propTypes = {
    fromDate: PropTypes.string,
    toDate: PropTypes.string,
    dateBucketForDateRange: PropTypes.string,
    dateBucketsForDateRange: PropTypes.array,
    disableParentComponents: PropTypes.bool,
    setDisableParentComponents: PropTypes.func,
    setDateBucketForDateRange: PropTypes.func,
};

export default PointsActivityOverview;
