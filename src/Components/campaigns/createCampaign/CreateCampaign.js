import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import {
    But<PERSON>,
    Heading,
    IcIcon,
    Wizard,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faAngleLeftB } from "FaICIconMap";
import BaseLayout from "Layout/BaseLayout";
import { CreateCampaignContext, DataContext } from "Contexts";
import { CampaignChannels, RedirectViews } from "Data";
import { toTitleCase } from "Utils";
import CreateCampaignFirstPage from "./createCampaignWizard/CreateCampaignFirstPage";
import CreateCampaignSecondPage from "./createCampaignWizard/CreateCampaignSecondPage";
import CreateCampaignThirdPage from "./createCampaignWizard/CreateCampaignThirdPage";

import "./CreateCampaign.scss";

const CreateCampaignWizardView = ({ campaignTypeToReadable, isCreating }) => {
    return useMemo(
        () => (
            <div className="w-75">
                <Wizard
                    validate
                    finishButtonClick={() => {
                    }}
                    color="primary"
                    disabled={isCreating}
                    finishButtonText={
                        isCreating
                            ? "Creating..."
                            : `Create ${campaignTypeToReadable} Campaign`
                    }
                    steps={[
                        {
                            stepName: "Setup",
                            component: CreateCampaignFirstPage,
                        },
                        {
                            stepName: "Design",
                            component: CreateCampaignSecondPage,
                            stepProps: { eventAttributes: [] },
                        },
                        {
                            stepName: "Confirm",
                            component: CreateCampaignThirdPage,
                        },
                    ]}
                />
            </div>
        ),
        [campaignTypeToReadable, isCreating],
    );
};

const CreateCampaign = () => {
    const { segments } = useContext(DataContext);
    const [redirectView, setRedirectView] = useState(RedirectViews.MEMBERS_VIEW);

    const {
        campaignChannel,
        campaignType,
        isCreating,
        appliedFilters,
        isLoadingMemberCount,
        setCampaignChannel,
        setToSegments,
        setIsNavigatedFromOutside,
        setAppliedFilters,
        setMembersCountForSegmentsOrFilters,
        loadMembersCountDataForFiltersOrSegments,
        reset,
    } = useContext(CreateCampaignContext);
    const history = useHistory();
    const location = useLocation();


    const campaignTypeToReadable = useMemo(
        () =>
            campaignChannel === CampaignChannels.EMAIL
                ? toTitleCase(campaignChannel)
                : campaignChannel,
        [campaignChannel],
    );

    const onNavigatingBack = useCallback(() => {
            setCampaignChannel("");
            history.goBack();
        },
        [ setCampaignChannel, history],
    );

    const onLoadMembersCountForFiltersOrSegmentsFromOutsideCampaignViews =
        useCallback(
            async (filters = []) => {
                const membersCountDataResponse =
                    await loadMembersCountDataForFiltersOrSegments(filters);
                setMembersCountForSegmentsOrFilters(membersCountDataResponse);
            },
            [
                loadMembersCountDataForFiltersOrSegments,
                setMembersCountForSegmentsOrFilters,
            ],
        );

    useEffect(() => {
        if (!campaignChannel) {
            history.push("/campaigns/sms"); // * Using sms campaigns page as a redirect fallback.
        }

        return () => {
            setCampaignChannel("");
            reset();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [campaignChannel, history]);

    useEffect(() => {
        const campaignData = location.state?.campaignData;
        if (campaignData) {
            let selectedSegment = {};
            if (campaignData?.redirectView) {
                setRedirectView(campaignData?.redirectView);
            }

            if (campaignData?.selectedSegmentId) {
                selectedSegment = segments.find(
                    (segment) => segment._id === campaignData.selectedSegmentId,
                );

                setToSegments(
                    [
                        {
                            label: selectedSegment?.name || "~ unknown",
                            value: selectedSegment._id || "~ unknown",
                            memberFilter: selectedSegment?.memberFilter || selectedSegment?.filter || {},
                            transactionFilter: selectedSegment?.transactionFilter || {},
                        },
                    ],
                );
            }
            if (
                (campaignData?.currentFilters &&
                Object.keys(campaignData.currentFilters).length > 0)||
                (campaignData?.currentTransactionFilters &&
                Object.keys(campaignData.currentTransactionFilters).length >0)
            ) {
                let filtersToApply = [{
                    ...(campaignData?.currentFilters && Object.keys(campaignData?.currentFilters)?.length > 0
                        ? { memberFilter: campaignData?.currentFilters }
                        : {}),
                    ...(campaignData?.currentTransactionFilters && Object.keys(campaignData?.currentTransactionFilters)?.length > 0
                        ? { transactionFilter: campaignData?.currentTransactionFilters }
                        : {}),
                    segment: {
                        id: selectedSegment?._id
                            ? selectedSegment._id
                            : "custom_filter",
                        name: selectedSegment?.name
                            ? selectedSegment.name
                            : "Filtered Members",
                        isCustomFilter:
                            Object.keys(selectedSegment).length === 0,
                    },
                }];
                setAppliedFilters(filtersToApply);
                if (filtersToApply?.length !== 0) {
                    onLoadMembersCountForFiltersOrSegmentsFromOutsideCampaignViews(filtersToApply);
                }
            }
            setIsNavigatedFromOutside(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        location.state?.campaignData,
        segments,
        setToSegments,
        setIsNavigatedFromOutside,
        setAppliedFilters,
    ]);

    useEffect(() => {
        if (campaignType[0]?.value && appliedFilters?.length !== 0) {
            onLoadMembersCountForFiltersOrSegmentsFromOutsideCampaignViews(
                appliedFilters,
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [campaignType, appliedFilters]);

    return (
        <div className="mt-4 create-campaign-view">
            <BaseLayout
                topLeft={
                    <Heading
                        text={`Create ${campaignTypeToReadable} Campaign`}
                    />
                }
                topRight={
                    <div className="d-flex align-items-center">
                        {location.state?.campaignData && (
                            <Button
                                name={redirectView}
                                variant="dark"
                                size="sm"
                                disabled={isCreating || isLoadingMemberCount}
                                onClick={onNavigatingBack}
                            >
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        icon={faAngleLeftB}
                                        size="lg"
                                        className="mr-2"
                                    />
                                    {redirectView === RedirectViews.MEMBERS_VIEW ? "Back to Members" : "Back to Segments"}
                                </div>
                            </Button>
                        )}
                        <Button
                            className="btn shadow-none"
                            name={campaignChannel}
                            variant="link"
                            size="sm"
                            disabled={isCreating || isLoadingMemberCount}
                            onClick={onNavigatingBack}
                        >
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    icon={faAngleLeftB}
                                    size="lg"
                                    className="mr-2"
                                />
                                {`Back to ${campaignTypeToReadable} Campaigns`}
                            </div>
                        </Button>
                    </div>
                }
                bottom={
                    <div className="mt-4 d-flex justify-content-center w-100">
                        <CreateCampaignWizardView
                            campaignChannel={campaignChannel}
                            campaignTypeToReadable={campaignTypeToReadable}
                            isCreating={isCreating}
                        />
                    </div>
                }
            />
        </div>
    );
};
export default CreateCampaign;
