import React, {
    useState,
    useRef,
    useContext,
    useImperativeHandle,
    useCallback,
    useMemo,
    useEffect,
} from "react";
import { toast } from "react-toastify";
import {
    Badge,
    Button,
    Col,
    Form,
    IcIcon,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faPen, faPlus } from "FaICIconMap";
import { CreateCampaignContext, DataContext } from "Contexts";
import {
    CampaignChannels,
    CampaignTypes,
    LaunchDates, PromotionalCampaignFilter,
    PromotionalCampaignMsg,
} from "Data";
import {
    convertTimeFrom12HTo24H,
    getDiffBetween2DatesInMinutes,
    toTitleCase,
} from "Utils";
import PopUpWindow from "Components/campaigns/shared/popUpWindow/PopUpWindow";
import TimeOfDayPicker from "Components/common/timeOfDayPicker/TimeOfDayPicker";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import CampaignSegmentSelector from "./shared/campaignSegmentSelector/CampaignSegmentSelector";
import SegmentsOrFiltersSummaryView from "./shared/segmentsOrFiltersSummaryView/SegmentsOrFiltersSummaryView";

const CampaignTypeOptions = Object.values(CampaignTypes).map((cT) => ({
    label: toTitleCase(cT),
    value: cT,
}));

const SegmentOptions = [
    { label: "All Members", value: "ALL" },
    { label: "Select Member Segments", value: "SELECT_MEMBER_SEGMENTS" },
];

const LauchDateOptions = Object.values(LaunchDates).map((item) => ({
    label: toTitleCase(item),
    value: item,
}));

const CreateCampaignFirstPage = (props, ref) => {
    const { segments } = useContext(DataContext);
    const {
        configuredProviders,
        campaignChannel,
        campaignName,
        campaignDescription,
        campaignType,
        selectedProvider,
        to,
        selectSegments,
        isLoadingMemberCount,
        membersCountForSegmentsOrFilters,
        selectLaunchDate,
        launchDate,
        hours,
        minutes,
        timeOfDay,
        appliedFilters,
        isNavigatedFromOutsideOfCampaignViews,
        setMembersCountForSegmentsOrFilters,
        loadMembersCountDataForFiltersOrSegments,
        setCampaignType,
        setSelectedProvider,
        setAttribute,
        setLaunchDate,
        setLaunchTime,
        setAppliedFilters,
    } = useContext(CreateCampaignContext);
    const formRef = useRef();
    const [validated, setValidated] = useState(false);
    const [showAppliedFilters, setShowAppliedFilters] = useState(false);
    const [popUpState, setPopUpState] = useState({
        modalHeaderName: "",
        renderingComponentName: "",
    });
    const [showCampaignSegmentSelector, setShowCampaignSegmentSelector] = useState(false);
    const [launchDateToNowDiff, setLaunchDateToNowDiff] = useState(0);

    const selectedCampaignTypeConfiguredProviders = useMemo(
        () =>
            configuredProviders.filter(
                (configuredProvider) =>
                    configuredProvider?.providerType === campaignChannel,
            ),
        [campaignChannel, configuredProviders],
    );

    const isSegmentsNotSelected = useMemo(() => {
        return selectSegments === "SELECT_MEMBER_SEGMENTS" && to.length === 0;
    }, [selectSegments, to.length]);

    const isLaunchDateEmptyClasses = useMemo(() => {
        if (validated) {
            return selectLaunchDate === LaunchDates.LATER && launchDate
                ? "date-valid"
                : "date-invalid";
        }
        return "";
    }, [launchDate, selectLaunchDate, validated]);

    const isNotValidLaterLaunchDate = useMemo(() => {
        if (selectLaunchDate === LaunchDates.LATER) {
            return !launchDate || (launchDate && launchDateToNowDiff < 60);
        } else return false;
    }, [launchDate, launchDateToNowDiff, selectLaunchDate]);

    const dateValidityClasses = useMemo(() => {
        if (validated) {
            return !isNotValidLaterLaunchDate
                ? "border-success"
                : "border-danger";
        }
        return "";
    }, [isNotValidLaterLaunchDate, validated]);

    const onChangeFromAddress = useCallback(
        (e) => setSelectedProvider(e),
        [setSelectedProvider],
    );

    const onChangeCampaignType = useCallback((selected) => {
            try {
                switch (selected[0]?.value) {
                    case CampaignTypes.PROMOTIONAL: {
                        const memberFiltersHaveMarketingConsentId = appliedFilters?.some((item) => item?.id === PromotionalCampaignFilter.id);
                        if (!memberFiltersHaveMarketingConsentId) {
                            const memberFiltersWithMarketingConsent = appliedFilters;
                            memberFiltersWithMarketingConsent.push(
                                PromotionalCampaignFilter,
                            );
                            setAppliedFilters(memberFiltersWithMarketingConsent);
                        }
                        break;
                    }
                    case CampaignTypes.TRANSACTIONAL: {
                        const memberFiltersWithoutMarketingConsent = appliedFilters.filter(
                            (item) =>
                                item?.id !== PromotionalCampaignFilter?.id,
                        );
                        setMembersCountForSegmentsOrFilters(0);
                        setAppliedFilters(memberFiltersWithoutMarketingConsent);
                        break;
                    }
                    default:
                        break;
                }
                setCampaignType(selected);
            } catch (err) {
                console.error(err);
                toast.error(
                    <div>
                        Failed to set campaign type!
                        <br />
                        {err.message
                            ? `Error: ${err.message}`
                            : "Please try again later."}
                    </div>,
                );
            }
        },
        [setCampaignType, appliedFilters, setAppliedFilters, setMembersCountForSegmentsOrFilters],
    );

    const isFromAddressNotFound = useCallback(() => {
        if (selectedProvider.length === 0) {
            throw new Error(
                `From ${
                    campaignChannel === CampaignChannels.EMAIL
                        ? "email address"
                        : "sender id / mobile number"
                } cannot be empty!`,
            );
        }
    }, [selectedProvider, campaignChannel]);

    const onShowViewAppliedFilters = useCallback(
        (event) => {
            try {
                event.stopPropagation();
                const segmentIds = to?.map((item) => item?.value) || [];
                const segmentFilters =
                    to?.length !== 0 ? to : appliedFilters || [];

                setPopUpState({
                    modalHeaderName: "Campaign Segments and Filters",
                    renderingComponentName: event.currentTarget.name,
                    modalBodyContent: {
                        segmentIds,
                        segmentFilters,
                        isPromotionalCampaign:
                            campaignType[0]?.value ===
                            CampaignTypes.PROMOTIONAL,
                    },
                });
                setShowAppliedFilters(true);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to view applied filters!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>,
                );
            }
        },
        [
            campaignType,
            to,
            appliedFilters,
            setPopUpState,
            setShowAppliedFilters,
        ],
    );

    const onCloseViewAppliedFilters = useCallback(() => {
        setPopUpState({
            modalHeaderName: "",
            renderingComponentName: "",
        });
        setShowAppliedFilters(false);
    }, [setPopUpState, setShowAppliedFilters]);

    const onShowCampaignSegmentSelector = useCallback(
        () => setShowCampaignSegmentSelector(true),
        [setShowCampaignSegmentSelector],
    );

    const onCloseCampaignSegmentSelector = useCallback(
        () => setShowCampaignSegmentSelector(false),
        [setShowCampaignSegmentSelector],
    );

    const onSetTime = useCallback(
        (timeProperty, timeValue) =>
            setLaunchTime({ key: timeProperty, value: timeValue }),
        [setLaunchTime],
    );

    const onLoadMembersCountForFiltersOrSegments = useCallback(async () => {
        const membersCountDataResponse =
            await loadMembersCountDataForFiltersOrSegments(
                appliedFilters,
            );
        setMembersCountForSegmentsOrFilters(membersCountDataResponse);
    }, [
        loadMembersCountDataForFiltersOrSegments,
        appliedFilters,
        setMembersCountForSegmentsOrFilters,
    ]);

    useImperativeHandle(ref, () => ({
        isValidated() {
            let formValid = formRef.current.checkValidity();

            if (isNotValidLaterLaunchDate) formValid = false;
            if (
                campaignName &&
                campaignDescription &&
                !isNotValidLaterLaunchDate &&
                selectSegments === "SELECT_MEMBER_SEGMENTS"
            )
                formValid = !isSegmentsNotSelected;

            if (!formValid) setValidated(true);

            return formValid;
        },
        async onClickNext() {
            return new Promise((resolve) => {
                try {
                    isFromAddressNotFound();
                    resolve();
                } catch (e) {
                    console.error(e);
                    toast.error(
                        e.message
                            ? `Error: ${e.message}`
                            : "Please try again later.",
                    );
                }
            });
        },
    }));

    useEffect(() => {
        if (selectedCampaignTypeConfiguredProviders.length === 1) {
            setSelectedProvider(selectedCampaignTypeConfiguredProviders);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCampaignTypeConfiguredProviders]);

    useEffect(() => {
        if (selectLaunchDate === LaunchDates.LATER && launchDate) {
            const launchDateToDate = new Date(launchDate);
            const timeOfDayIn24H = convertTimeFrom12HTo24H(
                `${hours}:${minutes} ${timeOfDay}`,
            );
            const [hh, mm] = timeOfDayIn24H.split(":");
            const launchDateAndTime = new Date(
                launchDateToDate.getFullYear(),
                launchDateToDate.getMonth(),
                launchDateToDate.getDate(),
                hh,
                mm,
            );

            setLaunchDateToNowDiff(
                getDiffBetween2DatesInMinutes(launchDateAndTime, new Date()),
            );
        }
    }, [hours, launchDate, minutes, selectLaunchDate, timeOfDay]);

    return (
        <div ref={ref} className="mt-5 w-100">
            <Form
                ref={formRef}
                validated={validated}
                className="d-flex flex-column align-items-center w-100"
            >
                {isLoadingMemberCount ? (
                    <div className="w-75 mb-4 text-center font-weight-bold rounded p-2 grey-bg">
                        <h2 className="mb-0">
                            Loading form data, please wait...
                        </h2>
                    </div>
                ) : null}
                <Form.Group className="w-75">
                    <Form.Label>
                        Campaign Type
                        <span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Select
                        id="selectedProvider"
                        labelKey="label"
                        options={CampaignTypeOptions}
                        placeholder="Select a campaign type..."
                        selected={campaignType}
                        disabled={isLoadingMemberCount}
                        onChange={onChangeCampaignType}
                        required
                    />
                    {validated && campaignType.length === 0 && (
                        <Form.Text className="text-danger">
                            * Campaign type cannot be empty!
                        </Form.Text>
                    )}
                    {campaignType[0]?.value === CampaignTypes.PROMOTIONAL ? (
                        <div className="mt-3 px-3 py-2 info-div-type-secondary rounded">
                            {PromotionalCampaignMsg}
                        </div>
                    ) : null}
                </Form.Group>
                <Form.Group className="w-75">
                    <Form.Label className="d-flex align-items-center">
                        Campaign Name
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <Form.Control
                        type="text"
                        name="campaignName"
                        placeholder="Enter campaign name..."
                        value={campaignName}
                        disabled={isLoadingMemberCount}
                        onChange={setAttribute}
                        required
                    />
                    {validated && !campaignName && (
                        <Form.Text className="text-danger">
                            * Campaign name cannot be empty!
                        </Form.Text>
                    )}
                </Form.Group>
                <Form.Group className="w-75">
                    <Form.Label className="d-flex align-items-center">
                        Campaign Description
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <Form.Control
                        as="textarea"
                        name="campaignDescription"
                        rows={3}
                        value={campaignDescription}
                        onChange={setAttribute}
                        disabled={isLoadingMemberCount}
                        placeholder="Enter campaign description..."
                        required
                    />
                    {validated && !campaignDescription && (
                        <Form.Text className="text-danger">
                            * Campaign description cannot be empty!
                        </Form.Text>
                    )}
                </Form.Group>
                <Form.Group className="w-75">
                    <Form.Label>
                        {"From "}(
                        <span className="font-weight-bold">{`${
                            campaignChannel === CampaignChannels.EMAIL
                                ? "Email Address"
                                : "Sender Id/Mobile Number"
                        }`}</span>
                        )<span className="text-danger">*</span>
                    </Form.Label>
                    <Form.Select
                        id="selectedProvider"
                        labelKey="label"
                        options={selectedCampaignTypeConfiguredProviders}
                        placeholder={
                            selectedCampaignTypeConfiguredProviders.length === 0
                                ? `No ${
                                    campaignChannel
                                        ? campaignChannel.toLowerCase() + " "
                                        : ""
                                }provider configurations found!`
                                : `Select ${
                                    campaignChannel === CampaignChannels.EMAIL
                                        ? "an email address"
                                        : "a sender id/mobile number"
                                }...`
                        }
                        selected={selectedProvider}
                        disabled={
                            selectedCampaignTypeConfiguredProviders.length ===
                            0 || isLoadingMemberCount
                        }
                        onChange={onChangeFromAddress}
                        required
                    />
                    {validated &&
                        selectedCampaignTypeConfiguredProviders.length !== 0 &&
                        selectedProvider.length === 0 && (
                            <Form.Text className="text-danger">
                                {`* From ${
                                    campaignChannel === CampaignChannels.EMAIL
                                        ? "email address"
                                        : "sender id/mobile number"
                                } cannot be empty!`}
                            </Form.Text>
                        )}
                    {selectedCampaignTypeConfiguredProviders.length === 0 && (
                        <Form.Text className="text-danger font-weight-bold">
                            {`* ${
                                campaignChannel
                                    ? toTitleCase(campaignChannel) + " provider"
                                    : "Provider"
                            } configurations are missing! Please check
                            your region and/or organization configurations.`}
                        </Form.Text>
                    )}
                </Form.Group>
                {isNavigatedFromOutsideOfCampaignViews ? (
                    <div className="w-75">
                        <div className="mb-4">
                            <DetailsAsLabelValue
                                label={
                                    <div className="d-flex justify-content-between align-items-center">
                                        To
                                        {appliedFilters?.length > 0 ? (
                                            <Button
                                                className="p-0 shadow-none"
                                                variant="link"
                                                size="sm"
                                                name="SEGMENT_DATA"
                                                disabled={isLoadingMemberCount}
                                                onClick={
                                                    onShowViewAppliedFilters
                                                }
                                            >
                                                <small>
                                                    Show Applied Filters
                                                </small>
                                            </Button>
                                        ) : null}
                                    </div>
                                }
                                value={
                                    to.length !== 0 ? (
                                        to.map((t) => (
                                            <Badge
                                                key={t?.label}
                                                className="mx-1 px-2 py-1"
                                                variant="secondary"
                                            >
                                                {t?.label || "~ unknown"}
                                            </Badge>
                                        ))
                                    ) : (
                                        <>
                                            {campaignType[0]?.value ===
                                            CampaignTypes.PROMOTIONAL &&
                                            appliedFilters?.length === 0 &&
                                            isLoadingMemberCount ? (
                                                "Loading..."
                                            ) : (
                                                <Badge
                                                    className="mx-1 px-2 py-1"
                                                    variant={
                                                        appliedFilters?.length >
                                                        0
                                                            ? "warning"
                                                            : "success"
                                                    }
                                                >
                                                    {(() => {
                                                        const mTxt =
                                                            campaignType[0]
                                                                ?.value ===
                                                            CampaignTypes.PROMOTIONAL &&
                                                            membersCountForSegmentsOrFilters >
                                                            0
                                                                ? " with Marketing Consent"
                                                                : "";
                                                        return appliedFilters?.length >
                                                        0
                                                            ? `Filtered Members${mTxt}`
                                                            : `All Members${mTxt}`;
                                                    })()}
                                                </Badge>
                                            )}
                                        </>
                                    )
                                }
                            />
                        </div>
                        {appliedFilters?.length > 0 ||
                        membersCountForSegmentsOrFilters > 0 ? (
                            <Form.Group>
                                <SegmentsOrFiltersSummaryView
                                    totalMembersCount={
                                        membersCountForSegmentsOrFilters
                                    }
                                    onLoadMembersCountForFiltersOrSegments={
                                        onLoadMembersCountForFiltersOrSegments
                                    }
                                />
                            </Form.Group>
                        ) : null}
                    </div>
                ) : (
                    <Form.Group className="w-75 mb-0">
                        <Form.Label className="d-flex align-items-center">
                            To
                            <div className="ml-1 text-danger">*</div>
                            {segments.length === 0 ? (
                                <Form.Text className="ml-2 text-orange">
                                    (No segments found to select)
                                </Form.Text>
                            ) : null}
                        </Form.Label>
                        <Form.Group className="input-group mt-3">
                            {SegmentOptions.filter((sO) => {
                                if (segments.length === 0)
                                    return (
                                        sO.value !== "SELECT_MEMBER_SEGMENTS"
                                    );
                                return sO;
                            }).map((option) => (
                                <Form.Check
                                    key={option.value}
                                    className="rounded-0 input-check mr-3"
                                    custom
                                    checked={selectSegments === option.value}
                                    value={option.value}
                                    id={option.value}
                                    label={option.label}
                                    name="selectSegments"
                                    type="radio"
                                    disabled={isLoadingMemberCount}
                                    onChange={setAttribute}
                                    required
                                />
                            ))}
                            {selectSegments === "ALL" &&
                                campaignType[0]?.value ===
                                CampaignTypes.PROMOTIONAL && (
                                    <Form.Group className="w-100 mb-3">
                                        <SegmentsOrFiltersSummaryView
                                            totalMembersCount={
                                                membersCountForSegmentsOrFilters
                                            }
                                            selectedSegments={to}
                                            onLoadMembersCountForFiltersOrSegments={
                                                onLoadMembersCountForFiltersOrSegments
                                            }
                                            customInfoLine1={
                                                <>
                                                    <span className="font-weight-bold">
                                                        All members
                                                    </span>
                                                    {
                                                        " who have consented to receiving "
                                                    }
                                                    <span className="font-weight-bold">
                                                        marketing and/or
                                                        promotional material
                                                    </span>
                                                    {", "}
                                                </>
                                            }
                                            noShowLabel
                                        />
                                    </Form.Group>
                                )}
                        </Form.Group>
                        {selectSegments === "SELECT_MEMBER_SEGMENTS" && (
                            <Form.Group>
                                <div
                                    className={`border${
                                        validated
                                            ? !isSegmentsNotSelected
                                                ? " border-success success-bg"
                                                : " border-danger danger-bg"
                                            : ""
                                    } rounded p-3 text-center`}
                                >
                                    <div className="d-flex justify-content-center align-items-center">
                                        <Button
                                            name="selectMemberSegments"
                                            variant="primary"
                                            size="sm"
                                            disabled={
                                                isLoadingMemberCount ||
                                                segments.length === 0
                                            }
                                            onClick={
                                                onShowCampaignSegmentSelector
                                            }
                                        >
                                            <>
                                                {segments?.length === 0 ? (
                                                    "No segments found to select."
                                                ) : (
                                                    <>
                                                        <IcIcon
                                                            size="lg"
                                                            className="mr-2"
                                                            icon={
                                                                to.length !== 0
                                                                    ? faPen
                                                                    : faPlus
                                                            }
                                                        />
                                                        {`${
                                                            to.length !== 0
                                                                ? "Update"
                                                                : "Select"
                                                        } Target Member Segment/s`}
                                                    </>
                                                )}
                                            </>
                                        </Button>
                                    </div>
                                    {to?.length !== 0 ? (
                                        <div className="w-100 mb-3 text-left">
                                            <DetailsAsLabelValue
                                                label={
                                                    <div className="d-flex justify-content-between align-items-center">
                                                        Selected Segments
                                                        {appliedFilters?.length >
                                                        0 ? (
                                                            <Button
                                                                className="p-0 shadow-none"
                                                                variant="link"
                                                                size="sm"
                                                                name="SEGMENT_DATA"
                                                                disabled={
                                                                    isLoadingMemberCount
                                                                }
                                                                onClick={
                                                                    onShowViewAppliedFilters
                                                                }
                                                            >
                                                                <small>
                                                                    Show Applied
                                                                    Filters
                                                                </small>
                                                            </Button>
                                                        ) : null}
                                                    </div>
                                                }
                                                value={to.map((t) => (
                                                    <Badge
                                                        key={t?.label}
                                                        className="mx-1 px-2 py-1"
                                                        variant={
                                                            t?.label
                                                                ? "secondary"
                                                                : "default"
                                                        }
                                                    >
                                                        {t?.label ||
                                                            "~ unknown"}
                                                    </Badge>
                                                ))}
                                            />
                                        </div>
                                    ) : null}
                                    {membersCountForSegmentsOrFilters > 0 ? (
                                        <Form.Group className="w-100 mb-3">
                                            <SegmentsOrFiltersSummaryView
                                                totalMembersCount={
                                                    membersCountForSegmentsOrFilters
                                                }
                                                selectedSegments={to}
                                                onLoadMembersCountForFiltersOrSegments={
                                                    onLoadMembersCountForFiltersOrSegments
                                                }
                                            />
                                        </Form.Group>
                                    ) : null}
                                </div>
                                {validated && isSegmentsNotSelected && (
                                    <Form.Text className="text-danger">
                                        * Target member segments cannot be
                                        empty!
                                    </Form.Text>
                                )}
                            </Form.Group>
                        )}
                    </Form.Group>
                )}
                <Form.Group className="w-75 mb-0">
                    <Form.Label className="d-flex align-items-center">
                        Launch Date
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <Form.Group className="input-group mt-3">
                        {LauchDateOptions.map((option) => (
                            <Form.Check
                                key={option.value}
                                className="rounded-0 input-check mr-3"
                                custom
                                checked={selectLaunchDate === option.value}
                                value={option.value}
                                id={option.value}
                                label={option.label}
                                name="selectLaunchDate"
                                type="radio"
                                disabled={isLoadingMemberCount}
                                onChange={setAttribute}
                                required
                            />
                        ))}
                    </Form.Group>
                    {selectLaunchDate === LaunchDates.LATER && (
                        <Row
                            className={`w-100 ml-2 border rounded p-3 ${dateValidityClasses}`}
                        >
                            <Col
                                xs={12}
                                sm={12}
                                md={4}
                                lg={4}
                                xl={4}
                                className="w-100 px-3"
                            >
                                <Form.Group className="mb-0">
                                    <Form.Label className="d-flex align-items-center">
                                        Date
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <div className={isLaunchDateEmptyClasses}>
                                        <Form.Date
                                            id="start-date-id"
                                            date={launchDate}
                                            selectText="Select launch date..."
                                            disabled={isLoadingMemberCount}
                                            onChange={setLaunchDate}
                                            minDate={new Date()}
                                            required
                                        />
                                    </div>
                                </Form.Group>
                            </Col>
                            <Col
                                xs={12}
                                sm={12}
                                md={8}
                                lg={8}
                                xl={8}
                                className="w-100 px-3"
                            >
                                <Form.Group className="mb-0">
                                    <Form.Label className="d-flex align-items-center">
                                        Time
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <TimeOfDayPicker
                                        hours={hours}
                                        minutes={minutes}
                                        timeOfDay={timeOfDay}
                                        disabled={isLoadingMemberCount}
                                        setTime={onSetTime}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    )}
                    {validated &&
                        selectLaunchDate === LaunchDates.LATER &&
                        launchDate &&
                        launchDateToNowDiff < 60 && (
                            <Form.Text className="mt-3 text-danger">
                                {`* Launch date and time must be at least an hour ahead of the current date and time! Difference: ${launchDateToNowDiff} ${
                                    ~[-1, 1].indexOf(launchDateToNowDiff)
                                        ? "minute"
                                        : "minutes"
                                }.`}
                            </Form.Text>
                        )}
                    {validated &&
                        selectLaunchDate === LaunchDates.LATER &&
                        !launchDate && (
                            <Form.Text className="mt-3 text-danger">
                                * Launch date cannot be empty!
                            </Form.Text>
                        )}
                </Form.Group>
            </Form>
            {showAppliedFilters && (
                <PopUpWindow
                    show={showAppliedFilters}
                    handleClose={onCloseViewAppliedFilters}
                    popUpState={popUpState}
                />
            )}
            {showCampaignSegmentSelector && (
                <CampaignSegmentSelector
                    show={showCampaignSegmentSelector}
                    onHide={onCloseCampaignSegmentSelector}
                />
            )}
        </div>
    );
};

export default CreateCampaignFirstPage;
