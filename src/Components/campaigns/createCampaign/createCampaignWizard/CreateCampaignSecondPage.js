import React, {
    useState,
    useRef,
    useContext,
    useImperativeHandle,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import { exportText } from "@shoutout-labs/shoutout-message-editor-enterprise";
import { Form, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faInfoCircle, faTimesCircle } from "FaICIconMap";
import { CreateCampaignContext, DataContext } from "Contexts";
import { CampaignChannels } from "Data";
import {
    curlyBracesRegex,
    getSmsLength,
    isGSMAlphabet,
    removeDuplicatesFromArrayOfObjects,
} from "Utils";
import CampaignEmailEditor from "./shared/campaignEmailEditor/CampaignEmailEditor";
import CampaignSmsEditor from "./shared/campaignSmsEditor/CampaignSmsEditor";
import PreviewSms from "Components/common/previewSms/PreviewSms";

const CreateCampaignSecondPage = (props, ref) => {
    const { contactAttributes } = useContext(DataContext);
    const {
        campaignChannel,
        selectedProvider,
        subject,
        emailEditorState,
        smsEditorState,
        setAttribute,
        onChangeEmailEditor,
        onChangeSmsEditor,
    } = useContext(CreateCampaignContext);
    const formRef = useRef();
    const [validated, setValidated] = useState(false);

    const customAttributes = useMemo(() => {
        const mappedCustomeAttrs = Object.entries(contactAttributes).reduce(
            (result, [key, value]) => {
                if (value.label) {
                    result.push({
                        name: value.label,
                        attr: key,
                        type: "person",
                    });
                }
                return result;
            },
            props?.eventAttributes || []
        );

        return removeDuplicatesFromArrayOfObjects(mappedCustomeAttrs);
    }, [contactAttributes, props?.eventAttributes]);

    const isEditorTextEmpty = useMemo(() => {
        if (campaignChannel === CampaignChannels.SMS && smsEditorState) {
            return !smsEditorState.getCurrentContent().hasText();
        } else if (campaignChannel === CampaignChannels.EMAIL && emailEditorState) {
            return !emailEditorState.getCurrentContent().hasText();
        } else return false;
    }, [campaignChannel, emailEditorState, smsEditorState]);

    const smsEditorData = useMemo(() => {
        if (campaignChannel === CampaignChannels.SMS && smsEditorState) {
            const editorText = exportText(smsEditorState);
            const smsLength = getSmsLength(editorText);
            const isCustomAttribute = editorText.match(curlyBracesRegex);
            let maxCharacterCount = 459;

            if (editorText && !isGSMAlphabet(editorText)) {
                maxCharacterCount = 201;
                return {
                    isUnicode: true,
                    smsLength,
                    maxCharacterCount,
                    characterOverLimit: smsLength > maxCharacterCount,
                    isCustomAttribute,
                    pageCount:
                        smsLength !== 0
                            ? 1 + Math.max(0, Math.ceil((smsLength - 70) / 67))
                            : 0,
                };
            } else {
                return {
                    isUnicode: false,
                    smsLength,
                    maxCharacterCount,
                    characterOverLimit: smsLength > maxCharacterCount,
                    isCustomAttribute,
                    pageCount:
                        smsLength !== 0
                            ? 1 +  Math.max(0, Math.ceil((smsLength - 160) / 153))
                            : 0,
                };
            }
        }
        return {};
    }, [campaignChannel, smsEditorState]);

    useImperativeHandle(ref, () => ({
        isValidated() {
            let formValid =
                campaignChannel === CampaignChannels.EMAIL
                    ? formRef.current.checkValidity()
                    : true;

            if (isEditorTextEmpty) formValid = false;

            if (!formValid) {
                setValidated(true);
            }
            return formValid;
        },
        async onClickNext() {
            return new Promise((resolve) => {
                try {
                    if (
                        campaignChannel === CampaignChannels.SMS &&
                        smsEditorData.characterOverLimit
                    )
                        throw new Error(
                            `Maximum character limit exceeded. Please limit to ${smsEditorData.maxCharacterCount} characters.`
                        );

                    resolve();
                } catch (e) {
                    console.error(e);
                    toast.error(`Error: ${e.message}`);
                }
            });
        },
    }));

    return (
        <div ref={ref} className="mt-5 w-100">
            {campaignChannel === CampaignChannels.EMAIL ? (
                <Form
                    ref={formRef}
                    validated={validated}
                    className="d-flex flex-column align-items-center w-100"
                >
                    <Form.Group controlId="email-subject" className="w-100">
                        <Form.Label className="d-flex align-items-center">
                            Subject
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Control
                            type="text"
                            name="subject"
                            placeholder="Enter the email campaign subject..."
                            value={subject}
                            onChange={setAttribute}
                            required
                        />
                    </Form.Group>
                </Form>
            ) : null}
            <Form.Group controlId="email-subject" className="w-100">
                <Form.Label className="d-flex align-items-center">
                    {campaignChannel === CampaignChannels.SMS
                        ? "SMS Content"
                        : "Email Body"}
                    <div className="ml-1 text-danger">*</div>
                </Form.Label>
                {validated && isEditorTextEmpty && (
                    <Form.Text className="text-danger">
                        {`* ${
                            campaignChannel === CampaignChannels.SMS
                                ? "SMS content"
                                : "Email body"
                        } cannot be empty!`}
                    </Form.Text>
                )}
                {/* // TODO: Do we need to add sms pages validation?
                 */}
                <div className="mt-3">
                    {campaignChannel === CampaignChannels.SMS ? (
                        <div className="d-flex justify-content-between">
                            <div className="w-100 mr-3">
                                <CampaignSmsEditor
                                    editorState={smsEditorState}
                                    customAttributes={customAttributes}
                                    onChangeSmsEditor={onChangeSmsEditor}
                                />
                                <div className="d-flex flex-row justify-content-center">
                                    <div className="text-center my-3 font-weight-bold mr-3">
                                        {"Characters: "}
                                        <span
                                            className={`${
                                                smsEditorData.characterOverLimit
                                                    ? "text-danger"
                                                    : ""
                                            }`}
                                        >
                                            {smsEditorData.smsLength}
                                        </span>
                                        /{smsEditorData.maxCharacterCount}
                                    </div>
                                    <div className="text-center my-3 font-weight-bold">
                                        {`Page Count: ${smsEditorData?.pageCount}`}
                                    </div>
                                </div>
                                {smsEditorData.isUnicode && (
                                    <div className="mb-3 p-2 warning-message rounded d-fle align-items-">
                                        <IcIcon
                                            className="mr-2"
                                            icon={faInfoCircle}
                                            size="lg"
                                        />
                                        <small>
                                            Your message contains Unicode
                                            characters.
                                        </small>
                                    </div>
                                )}
                                {smsEditorData.isCustomAttribute && (
                                    <div className="mb-3 p-2 warning-message rounded d-fle align-items-">
                                        <IcIcon
                                            className="mr-2"
                                            icon={faInfoCircle}
                                            size="lg"
                                        />
                                        <small>
                                            Custom attributes may affect the no.
                                            of sms pages depending on the length
                                            of the actual attributes.
                                        </small>
                                    </div>
                                )}
                                {smsEditorData.characterOverLimit && (
                                    <div className="mb-3 p-2 error-message rounded d-fle align-items-">
                                        <IcIcon
                                            className="mr-2"
                                            icon={faTimesCircle}
                                            size="lg"
                                        />
                                        <small>
                                            {`Maximum character limit exceeded. Please limit to ${smsEditorData.maxCharacterCount} characters.`}
                                        </small>
                                    </div>
                                )}
                            </div>
                            <div className="w-50">
                                <PreviewSms
                                    senderId={selectedProvider[0]?.value || ""}
                                    smsContent={
                                        smsEditorState
                                            ? exportText(smsEditorState)
                                            : null
                                    }
                                />
                            </div>
                        </div>
                    ) : (
                        <CampaignEmailEditor
                            editorState={emailEditorState}
                            customAttributes={customAttributes}
                            onChangeEmailEditor={onChangeEmailEditor}
                        />
                    )}
                </div>
            </Form.Group>
        </div>
    );
};

export default CreateCampaignSecondPage;
