import React, { useState, useCallback, useContext, useEffect } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Row,
    Col,
    SubHeading,
    Form,
    Button,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { CardStatus, CardTypes } from "Data";
import { useToggle, useOnChangeInput } from "Hooks";
import { createCardPrintJobs, getCardSummary } from "Services";
import CardStatusView from "../../cardsPool/shared/cardStats/CardStatsView";

const DigitalGenerate = ({ regionId }) => {
    const [cardAmount, setCardAmount, resetCardAmount] = useOnChangeInput("");
    const [isRequesting, toggleIsRequesting] = useToggle(false);
    const [validated, setValidated] = useState(false);
    const [resetCardSummary, setResetCardSummary] = useToggle(true);
    const [cardSummary, setCardSummary] = useState({
        assignedCards: 0,
        activatedCards: 0,
    });

    const generateCards = useCallback(
        async (e) => {
            e.preventDefault();

            if (e.target.checkValidity()) {
                try {
                    toggleIsRequesting();
                    setValidated(false);
                    await createCardPrintJobs({
                        jobType: CardTypes.DIGITAL_CARD,
                        regionId: regionId,
                        quantity: Number(cardAmount),
                    });
                    toggleIsRequesting();
                    setResetCardSummary();
                    toast.success(
                        "Successfully created a card generation request."
                    );
                    resetCardAmount();
                } catch (e) {
                    console.error(e);
                    toggleIsRequesting();
                    toast.error(
                        <div>
                            Failed to create a card generation request!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }

            setCardAmount("");
        },
        [
            regionId,
            setResetCardSummary,
            setCardAmount,
            toggleIsRequesting,
            cardAmount,
            resetCardAmount,
        ]
    );

    useEffect(() => {
        if (resetCardSummary) {
            (async () => {
                try {
                    const digitalCardStockResponse = await getCardSummary({
                        cardTypes: [CardTypes.DIGITAL_CARD],
                        regionId,
                    });
                    setCardSummary({
                        assignedCards:
                            digitalCardStockResponse.assignedCards || 0,
                        activatedCards:
                            digitalCardStockResponse.activatedCards || 0,
                    });
                } catch (e) {
                    console.error(e);
                    toast.error(
                        <div>
                            Failed to load card generation summary!
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                } finally {
                    setResetCardSummary();
                }
            })();
        }
        // eslint-disable-next-line
    }, [regionId, resetCardSummary]);

    return (
        <>
            <Row>
                <Col xs={4} md={4} lg={4}>
                    <CardStatusView
                        cardStatus="Active cards"
                        cardQuantity={cardSummary.activatedCards}
                        cardColor={CardStatus.ACTIVE}
                    />
                </Col>
                <Col xs={4} md={4} lg={4}>
                    <CardStatusView
                        cardStatus="Assigned cards"
                        cardQuantity={cardSummary.assignedCards}
                        cardColor={CardStatus.ASSIGNED}
                    />
                </Col>
            </Row>
            <Row className="mt-5">
                <Col xl="6" lg="6" md="6" sm="12" className="pl-0">
                    <SubHeading text="Generate Digital Cards" />
                    <Form
                        onSubmit={generateCards}
                        validated={validated}
                        noValidate
                    >
                        <Form.Group>
                            <Form.Label>Card Amount</Form.Label>
                            <Form.Control
                                type="number"
                                placeholder="Enter card amount"
                                value={cardAmount}
                                required
                                onChange={setCardAmount}
                            />
                        </Form.Group>

                        <div className="float-right mt-3">
                            <Button
                                variant="primary"
                                size="sm"
                                type="submit"
                                disabled={isRequesting}
                            >
                                {isRequesting
                                    ? "Generating Cards..."
                                    : "Generate Cards"}
                            </Button>
                        </div>
                    </Form>
                </Col>
            </Row>
        </>
    );
};

const DigitalGenerateContainer = () => {
    const { regionId } = useContext(UserContext);

    return <DigitalGenerate regionId={regionId} />;
};

DigitalGenerate.propTypes = {
    /**
     * Selected region
     */
    regionId: PropTypes.string.isRequired,
};

export default DigitalGenerate;
export { DigitalGenerateContainer };
