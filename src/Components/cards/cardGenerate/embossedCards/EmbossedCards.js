import React, { useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { CardGenerateJobStatus, CardTypes } from "Data";
import { toTitleCase } from "Utils";
import NewRequestView from "./newRequests/NewRequestView";
import CardGenerateViewHoc from "../shared/CardGenerateViewHoc";

const EmbossedCards = () => {
    const [tab, setTab] = useState(CardGenerateJobStatus.NEW_REQUEST);
    const [isLoadingTab, setIsLoadingTab] = useState(false);
    const location = useLocation();

    const renderTable = useMemo(() => {
        switch (tab) {
            case CardGenerateJobStatus.NEW_REQUEST:
                return (
                    <NewRequestView
                        jobStatus={tab}
                        cardType={CardTypes.EMBOSSED_CARD}
                        setIsLoadingTab={setIsLoadingTab}
                    />
                );
            case CardGenerateJobStatus.PENDING:
            case CardGenerateJobStatus.PRINTING:
            case CardGenerateJobStatus.PRINTED:
            case CardGenerateJobStatus.DISPATCHED:
            case CardGenerateJobStatus.COMPLETED:
            case CardGenerateJobStatus.FAILED:
                return (
                    <CardGenerateViewHoc
                        jobStatus={tab}
                        cardType={CardTypes.EMBOSSED_CARD}
                        setIsLoadingTab={setIsLoadingTab}
                    />
                );
            default:
                return null;
        }
    }, [tab]);

    useEffect(() => {
        if (location.state) {
            setTab(location.state);
        }
    }, [location.state]);

    return (
        <div>
            <Tabs
                defaultActiveKey={tab}
                activeKey={tab}
                transition={false}
                id="noanim-tab-example"
                className="ml-n4 mb-3 border-solid-bottom"
                onSelect={setTab}
            >
                {Object.values(CardGenerateJobStatus)
                    .sort((a, b) => {
                        if (a === CardGenerateJobStatus.NEW_REQUEST) {
                            return -1;
                        } else if (b === CardGenerateJobStatus.NEW_REQUEST) {
                            return 1;
                        } else {
                            return 0;
                        }
                    })
                    .map((cGJS) => (
                        <Tab
                            key={cGJS}
                            eventKey={cGJS}
                            title={
                                cGJS === CardGenerateJobStatus.NEW_REQUEST
                                    ? "New Requests"
                                    : toTitleCase(cGJS)
                            }
                            disabled={isLoadingTab}
                        />
                    ))}
            </Tabs>
            {renderTable}
        </div>
    );
};

export default EmbossedCards;
