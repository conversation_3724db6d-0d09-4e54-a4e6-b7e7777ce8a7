// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InstantCardGenerate component snapshot Matches the snapshot 1`] = `
<div
  className="mt-5 row"
>
  <div
    className="pl-0 col-xl-6 col-lg-6 col-md-6 col-sm-12"
  >
    <h2
      className="mb-0"
    >
      Generate Instant Cards
    </h2>
    <form
      className=""
      noValidate={true}
      onSubmit={[Function]}
    >
      <div
        className="form-group"
      >
        <label
          className="d-flex align-items-center form-label"
          htmlFor="card-type"
        >
          Card Type
          <div
            className="ml-1 text-danger"
          >
            *
          </div>
        </label>
        <div
          className="rbt select-typeahead "
          style={
            Object {
              "outline": "none",
              "position": "relative",
            }
          }
          tabIndex={-1}
        >
          <div
            style={
              Object {
                "display": "flex",
                "flex": 1,
                "height": "100%",
                "position": "relative",
              }
            }
          >
            <input
              aria-autocomplete="both"
              aria-expanded={false}
              aria-haspopup="listbox"
              autoComplete="off"
              className="custom-select  form-control"
              id="card-type"
              onBlur={[Function]}
              onChange={[Function]}
              onClick={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              placeholder="Select a merchant"
              required={true}
              role="combobox"
              type="text"
              value=""
            />
            <button
              className="close rbt-close clear-button"
              onClick={[Function]}
              type="button"
            >
              <span
                aria-hidden="true"
              >
                ×
              </span>
              <span
                className="sr-only visually-hidden"
              />
            </button>
            <input
              aria-hidden={true}
              className="rbt-input-hint"
              readOnly={true}
              style={
                Object {
                  "backgroundColor": "transparent",
                  "borderColor": "transparent",
                  "boxShadow": "none",
                  "color": "rgba(0, 0, 0, 0.54)",
                  "left": 0,
                  "pointerEvents": "none",
                  "position": "absolute",
                  "top": 0,
                  "width": "100%",
                }
              }
              tabIndex={-1}
              value=""
            />
          </div>
        </div>
      </div>
      <div
        className="form-group"
      >
        <label
          className="form-label"
          htmlFor="merchant"
        >
          Merchant
        </label>
        <div
          className="rbt select-typeahead "
          style={
            Object {
              "outline": "none",
              "position": "relative",
            }
          }
          tabIndex={-1}
        >
          <div
            style={
              Object {
                "display": "flex",
                "flex": 1,
                "height": "100%",
                "position": "relative",
              }
            }
          >
            <input
              aria-autocomplete="both"
              aria-expanded={false}
              aria-haspopup="listbox"
              autoComplete="off"
              className="custom-select  form-control"
              id="merchant"
              onBlur={[Function]}
              onChange={[Function]}
              onClick={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              placeholder="Select a merchant"
              required={true}
              role="combobox"
              type="text"
              value=""
            />
            <button
              className="close rbt-close clear-button"
              onClick={[Function]}
              type="button"
            >
              <span
                aria-hidden="true"
              >
                ×
              </span>
              <span
                className="sr-only visually-hidden"
              />
            </button>
            <input
              aria-hidden={true}
              className="rbt-input-hint"
              readOnly={true}
              style={
                Object {
                  "backgroundColor": "transparent",
                  "borderColor": "transparent",
                  "boxShadow": "none",
                  "color": "rgba(0, 0, 0, 0.54)",
                  "left": 0,
                  "pointerEvents": "none",
                  "position": "absolute",
                  "top": 0,
                  "width": "100%",
                }
              }
              tabIndex={-1}
              value=""
            />
          </div>
        </div>
      </div>
      <div
        className="form-group"
      >
        <label
          className="form-label"
          htmlFor="merchant-location"
        >
          Merchant Location
        </label>
        <div
          className="rbt select-typeahead "
          style={
            Object {
              "outline": "none",
              "position": "relative",
            }
          }
          tabIndex={-1}
        >
          <div
            style={
              Object {
                "display": "flex",
                "flex": 1,
                "height": "100%",
                "position": "relative",
              }
            }
          >
            <input
              aria-autocomplete="both"
              aria-expanded={false}
              aria-haspopup="listbox"
              autoComplete="off"
              className="custom-select  form-control"
              id="merchant-location"
              onBlur={[Function]}
              onChange={[Function]}
              onClick={[Function]}
              onFocus={[Function]}
              onKeyDown={[Function]}
              placeholder="Select a merchant"
              required={true}
              role="combobox"
              type="text"
              value=""
            />
            <button
              className="close rbt-close clear-button"
              onClick={[Function]}
              type="button"
            >
              <span
                aria-hidden="true"
              >
                ×
              </span>
              <span
                className="sr-only visually-hidden"
              />
            </button>
            <input
              aria-hidden={true}
              className="rbt-input-hint"
              readOnly={true}
              style={
                Object {
                  "backgroundColor": "transparent",
                  "borderColor": "transparent",
                  "boxShadow": "none",
                  "color": "rgba(0, 0, 0, 0.54)",
                  "left": 0,
                  "pointerEvents": "none",
                  "position": "absolute",
                  "top": 0,
                  "width": "100%",
                }
              }
              tabIndex={-1}
              value=""
            />
          </div>
        </div>
      </div>
      <div
        className="form-group"
      >
        <label
          className="d-flex align-items-center form-label"
          htmlFor="card-amount"
        >
          Card Amount
          <div
            className="ml-1 text-danger"
          >
            *
          </div>
        </label>
        <input
          className="form-control"
          id="card-amount"
          onChange={[Function]}
          placeholder="Enter card amount"
          required={true}
          type="number"
          value=""
        />
      </div>
      <div
        className="text-right mt-3"
      >
        <button
          className="btn btn-primary"
          disabled={false}
          type="submit"
        >
          Generate Cards
        </button>
      </div>
    </form>
  </div>
</div>
`;
