import React, {
    useMemo,
    useState,
    useCallback,
    useEffect,
    useContext,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faCreditCard, faMap, faPower, faProcess, faUser } from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import {
    CardGenerateJobStatus,
    CardStatus,
    AccessPermissionModuleNames,
    AccessPermissionModules,
    CardTypes,
    batchJobIdType,
    CardStatusVariant,
    CardGenerateJobStatusVariant,
} from "Data";
import { useToggle } from "Hooks";
import { getCardList, updateCardStatus } from "Services";
import {
    getTruncatedStringWithTooltip,
    toTitleCaseFromCamelCase,
} from "Utils";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import TopPanel from "./topPanel/TopPanel";
import ConfirmationModal from "./ConfirmationModal";
import CardTableView from "./cardTable/CardTableView";
import HistoryView from "./batchDetails/HistoryView";

const defaultCols = [
    { name: "id", hidden: true },
    { name: "cardNumber", headerStyle: { width: "15%" }, icon: faCreditCard },
    { name: "printedName", headerStyle: { width: "20%" }, icon: faUser },
    { name: "pickupLocation", headerStyle: { width: "15%" }, icon: faMap },
    { name: "cardStatus", icon: faPower },
    { name: "processingStatus", icon: faProcess },
    { name: "", headerStyle: { width: "20%" }, icon: null },
];

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: (
        <div className="d-flex align-items-center">
            {icon && (
                <IcIcon icon={icon} size="lg" className="mr-2 text-black" />
            )}
            {toTitleCaseFromCamelCase(name)}
        </div>
    ),
    sort: false,
    ...rest,
});

const defaultSkip = 1,
    defaultLimit = 25;
let searchStateUpdateTimeout;

const CardsViewHoc = ({
    jobStatus,
    cardType,
    batchID,
    cardsStatus,
    selectedBatch,
}) => {
    const { regionId, isAuthorizedForAction } = useContext(UserContext);
    const { merchantLocations } = useContext(DataContext);
    const [isLoading, setIsLoading] = useState(false);
    const [isReloading, setIsReloading] = useState(false);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [searchText, setSearchText] = useState("");
    const [data, setData] = useState([]);
    const [totalCards, setTotalCards] = useState(0);
    const [selectedItems, setSelectedItems] = useState([]);
    const [showModal, setShowModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState("");
    const [isSaving, toggleIsSaving] = useToggle(false);
    const [showHistoryView, setShowHistoryView] = useState(false);
    const [historyDetails, setHistoryDetails] = useState([]);
    const [isHistoryAvailable, setIsHistoryAvailable] = useState(false);

    const pickupLocationsObj = useMemo(
        () =>
            Object.values(merchantLocations)
                .reduce((result, location) => {
                    result.push(
                        Object.values(location || {}).filter(
                            (locObj) => locObj?.isPickupLocation
                        )
                    );
                    return result;
                }, [])
                .flat()
                .reduce((result, pickupLocation) => {
                    result[pickupLocation?._id] = pickupLocation;
                    return result;
                }, {}),
        [merchantLocations]
    );

    const loadCardData = useCallback(
        async ({ limit, skip, searchKey = "" }) => {
            const queryObj = {
                limit,
                skip: (skip - 1) * limit,
                ...(cardType !== CardTypes.EMBOSSED_CARD &&
                jobStatus !== CardGenerateJobStatus.FAILED
                    ? { [`${batchJobIdType[cardsStatus]}`]: batchID }
                    : { cardIds: selectedBatch?.embossCardIds }),
                searchKey,
                regionId,
            };

            try {
                setIsLoading(true);

                const cards = await getCardList(queryObj);
                setData(cards.items);
                setTotalCards(cards.total);

                setIsLoading(false);
            } catch (e) {
                console.error(e);
                setIsLoading(false);
                toast.error(
                    <div>
                        Failed to load cards list!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            regionId,
            batchID,
            cardsStatus,
            selectedBatch,
            jobStatus,
            cardType,
            setData,
            setIsLoading,
            setTotalCards,
        ]
    );

    const onReloadCardData = useCallback(async () => {
        setIsReloading(true);
        await loadCardData({ limit, skip: defaultSkip, searchKey: searchText });
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [limit, searchText, setSkip, loadCardData, setIsReloading]);

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadCardData({
                limit,
                skip: newSkip,
                searchKey: searchText,
            });
        },
        [setSkip, loadCardData, limit, searchText]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadCardData({
                limit: newLimit,
                skip,
                searchKey: searchText,
            });
        },
        [setLimit, setSkip, loadCardData, skip, searchText]
    );

    const onSearch = useCallback(
        (searchText) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(searchText);

            searchStateUpdateTimeout = setTimeout(async () => {
                await loadCardData({
                    limit,
                    skip: defaultSkip,
                    searchKey: searchText,
                });
                setSkip(defaultSkip);
            }, 2000);
        },
        [limit, setSearchText, loadCardData, setSkip]
    );

    const onShowModal = useCallback(
        (e) => {
            setShowModal(true);
            setSelectedCard(e.currentTarget.dataset.id);
        },
        [setShowModal, setSelectedCard]
    );

    const onHideModal = useCallback(() => {
        setShowModal(false);
        setSelectedCard("");
    }, [setShowModal, setSelectedCard]);

    const onSingleCardFail = useCallback(async () => {
        try {
            const payload = {
                processingStatus: CardGenerateJobStatus.FAILED,
            };
            toggleIsSaving();
            await updateCardStatus(selectedCard, payload);
            onHideModal();
            toggleIsSaving();
            loadCardData({ limit, skip });
        } catch (e) {
            toggleIsSaving();
            console.error(e);
            toast.error(
                <div>
                    Failed to update the selected card's processing status!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [toggleIsSaving, selectedCard, skip, limit, loadCardData, onHideModal]);

    const columns = useMemo(() => {
        const columns = [];

        defaultCols.forEach((item) => {
            columns.push(defaultColumnTemplate(item));
        });

        return columns.sort((a, b) => a.order - b.order);
    }, []);

    const onShowHistoryView = useCallback(
        (e) => {
            setIsHistoryAvailable(false);
            const card = data.filter(
                (item) => item._id === e.currentTarget.dataset.id
            );
            setShowHistoryView(true);
            setHistoryDetails(card[0]?.historyEvents);
            setIsHistoryAvailable(true);
        },
        [setShowHistoryView, setHistoryDetails, data, setIsHistoryAvailable]
    );

    const onHideHistoryView = useCallback(() => {
        setShowHistoryView(false);
        setHistoryDetails([]);
    }, [setShowHistoryView, setHistoryDetails]);

    const Data = useMemo(
        () =>
            data.map((card) => {
                return {
                    id: card._id,
                    cardNumber:
                        card?.cardNo ||
                        applyBadgeStyling({
                            customValue: "Card number not found.",
                        }),
                    printedName: getTruncatedStringWithTooltip({
                        value: card.embossCard?.printedName,
                        customUnknownValue: applyBadgeStyling({
                            customValue: "Printed name not found.",
                        }),
                    }),
                    cardStatus: applyBadgeStyling({
                        text: card?.status,
                        variant: CardStatusVariant[card?.status],
                    }),
                    pickupLocation: getTruncatedStringWithTooltip({
                        value: pickupLocationsObj[
                            card?.embossCard?.merchantLocationId
                        ]?.locationName,
                        valueMaxLength: 20,
                        customUnknownValue: applyBadgeStyling({
                            customValue: "Pickup location not found.",
                        }),
                    }),
                    cardMarkedAs: applyBadgeStyling({
                        text: card?.processingStatus,
                        variant:
                            CardGenerateJobStatusVariant[
                                card?.processingStatus
                            ],
                    }),
                    processingStatus: applyBadgeStyling({
                        text: card?.processingStatus,
                        variant:
                            CardGenerateJobStatusVariant[
                                card?.processingStatus
                            ],
                    }),
                    "": (
                        <>
                            {isAuthorizedForAction(
                                AccessPermissionModuleNames.CARD,
                                AccessPermissionModules[
                                    AccessPermissionModuleNames.CARD
                                ].actions.UpdateCard
                            ) &&
                                (card?.processingStatus ===
                                    CardGenerateJobStatus.PENDING ||
                                    card?.processingStatus ===
                                        CardGenerateJobStatus.PRINTING ||
                                    card?.processingStatus ===
                                        CardGenerateJobStatus.PRINTED ||
                                    card?.processingStatus ===
                                        CardGenerateJobStatus.DISPATCHED) && (
                                    <Button
                                        className="mr-2"
                                        variant="outline-danger"
                                        size="sm"
                                        data-id={card._id}
                                        onClick={onShowModal}
                                        disabled={
                                            card.status ===
                                                CardStatus.DEACTIVATED ||
                                            card.status === CardStatus.SUSPENDED
                                        }
                                    >
                                        Mark as Failed
                                    </Button>
                                )}
                            <Button
                                variant="outline-primary"
                                size="sm"
                                data-id={card._id}
                                onClick={onShowHistoryView}
                            >
                                View History
                            </Button>
                        </>
                    ),
                };
            }),
        [
            pickupLocationsObj,
            data,
            isAuthorizedForAction,
            onShowModal,
            onShowHistoryView,
        ]
    );

    useEffect(() => {
        setSkip(defaultSkip);
        loadCardData({ limit, skip: defaultSkip });

        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <div>
            <TopPanel
                type={cardType}
                isLoading={isLoading}
                searchText={searchText}
                onSearch={onSearch}
                setSearchText={setSearchText}
                statusOfProcess={jobStatus}
                isCardView={true}
                isReloading={isReloading}
                reloadDataText="Card List"
                onReload={onReloadCardData}
            />
            <CardTableView
                columns={columns}
                data={Data}
                sizePerPage={limit}
                page={skip}
                onChangePagination={onChangePagination}
                onChangePageSize={onChangePageSize}
                totalCount={totalCards}
                isLoading={isLoading}
                selectedItems={selectedItems}
                setSelectedItems={setSelectedItems}
            />
            <ConfirmationModal
                showModel={showModal}
                hideModal={onHideModal}
                selectedStatus={CardGenerateJobStatus.FAILED}
                onChangeBatchJobStatus={onSingleCardFail}
                isSaving={isSaving}
            />
            {isHistoryAvailable && (
                <HistoryView
                    show={showHistoryView}
                    handleClose={onHideHistoryView}
                    historyEvents={historyDetails || []}
                />
            )}
        </div>
    );
};

CardsViewHoc.defaultProps = {
    batchID: "",
    cardsStatus: "",
    selectedBatch: {},
};

CardsViewHoc.propTypes = {
    jobStatus: PropTypes.string.isRequired,
    cardType: PropTypes.string.isRequired,
    batchID: PropTypes.string,
    cardsStatus: PropTypes.string,
    selectedBatch: PropTypes.object,
};

export default CardsViewHoc;
