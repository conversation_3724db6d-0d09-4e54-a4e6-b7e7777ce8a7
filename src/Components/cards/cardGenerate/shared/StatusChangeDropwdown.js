import React, { useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
    IcIcon,
    DropdownButton,
    DropdownItem,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faPrint, faDispatch, faCheck, faTimes } from "FaICIconMap";
import { cardDistributionStatus, CardGenerateJobStatus, CardTypes } from "Data";

const StatusChangeDropwdown = ({
    jobStatus,
    setShowModel,
    setSeletedStatus,
    id,
    setSelectedBatchId,
    cardType,
}) => {
    const showConfirmModal = useCallback(
        (e) => {
            setShowModel(true);
            setSeletedStatus(e);
            setSelectedBatchId(id);
        },
        [setShowModel, setSeletedStatus, setSelectedBatchId, id]
    );
    const nextStatus = useMemo(() => {
        return {
            PENDING: {
                nextStep: CardGenerateJobStatus.PRINTING,
                icon: faPrint,
                action: "Printing",
            },
            PRINTING:
                cardType === CardTypes.INSTANT_CARD
                    ? {
                        nextStep: CardGenerateJobStatus.COMPLETED,
                        icon: faCheck,
                        action: "Completed",
                    }
                    : {
                        nextStep: CardGenerateJobStatus.PRINTED,
                        icon: faPrint,
                        action: "Printed",
                    },
            PRINTED: {
                nextStep: CardGenerateJobStatus.DISPATCHED,
                icon: faDispatch,
                action: "Dispatched",
            },
            DISPATCHED: {
                nextStep: CardGenerateJobStatus.COMPLETED,
                icon: faCheck,
                action: "Completed",
            },
            PROCESSING: {
                nextStep: cardDistributionStatus.DISPATCHED,
                icon: faDispatch,
                action: "Dispatched",
            },
        };
    }, [cardType]);

    return (
        <div>
            <DropdownButton
                variant="outline-secondary"
                size="sm"
                title={<span className="font-weight-bold">Mark As</span>}
                className="mr-2"
                onSelect={showConfirmModal}
                onClick={(e) => e.stopPropagation()}
                style={{ position: "static" }}
            >
                {jobStatus && (
                    <DropdownItem
                        eventKey={nextStatus[jobStatus].nextStep}
                        key={nextStatus[jobStatus].nextStep}
                        className="text-secondary"
                    >
                        <IcIcon
                            className="mr-2"
                            size="lg"
                            icon={nextStatus[jobStatus].icon}
                        />
                        {nextStatus[jobStatus].action}
                    </DropdownItem>
                )}
                <DropdownItem
                    eventKey={CardGenerateJobStatus.FAILED}
                    key={CardGenerateJobStatus.FAILED}
                    className="text-danger"
                >
                    <IcIcon className="mr-2" size="lg" icon={faTimes} />
                    Failed
                </DropdownItem>
            </DropdownButton>
        </div>
    );
};

StatusChangeDropwdown.propTypes = {
    /**
     * Job status : NEW_REQUEST,PENDING,PRINTING,PRINTED,DELIVERD,COMPLETED,FAILED
     */
    jobStatus: PropTypes.string.isRequired,

    /**
     * Show job status change confirmation modal
     */
    setShowModel: PropTypes.func.isRequired,

    /**
     * Set selected status
     */
    setSeletedStatus: PropTypes.func.isRequired,
};

export default StatusChangeDropwdown;
