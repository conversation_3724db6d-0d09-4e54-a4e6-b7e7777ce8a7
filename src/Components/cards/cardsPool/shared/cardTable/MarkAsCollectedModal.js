import React, { useCallback, useState } from "react";
import { toast } from "react-toastify";
import { Button, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { updateCardStatus } from "Services";

const MarkAsCollectedModal = ({ show, onHide, embossCardData }) => {
    const [isUpdating, setIsUpdating] = useState(false);

    const onClickMarkAsCollected = useCallback(
        async (event) => {
            event.preventDefault();
            try {
                setIsUpdating(true);
                await updateCardStatus(embossCardData.id, {
                    embossCard: { embossIssuedOn: new Date() },
                });
                setIsUpdating(false);
                toast.success("Successfully updated card as collected");
                onHide(null, "Successful");
            } catch (e) {
                console.error(e);
                setIsUpdating(false);
                toast.error(
                    <div>
                        Failed to mark as collected!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [embossCardData, onHide, setIsUpdating]
    );

    return (
        <Modal show={show} onHide={onHide} centered backdrop="static">
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>Mark as Collected</Modal.Title>
            </Modal.Header>
            <Modal.Body className="pt-3 mt-2 px-2">
                <p>
                    {`Do you want to mark the card `}
                    <span className="font-weight-bold">
                        {embossCardData.embossCardNo || "~ unknown"}
                    </span>
                    {` as collected?`}
                </p>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="outline-primary"
                    size="sm"
                    disabled={isUpdating}
                    onClick={onHide}
                >
                    Cancel
                </Button>
                <Button
                    className="ml-2"
                    type="submit"
                    variant="secondary"
                    size="sm"
                    disabled={isUpdating}
                    onClick={onClickMarkAsCollected}
                >
                    {isUpdating ? "Updating..." : "Mark as Collected"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default MarkAsCollectedModal;
