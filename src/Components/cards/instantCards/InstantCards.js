import React, {useCallback, useState} from 'react'
import BaseLayout from "../../../Layout/BaseLayout";
import {Heading, IcIcon, Tab, Tabs} from "@shoutout-labs/shoutout-themes-enterprise";
import {UilPostcard} from "@iconscout/react-unicons";
import {InstantCardsStatus} from "../../../Data";
import InstantCardView from './instantCardView/InstantCardView'
const InstantCards=()=>{
    const [tab, setTab] = useState(InstantCardsStatus.CARD_STOCK);
    const [merchantStatus, setMerchantStatus] = useState({merchant: {_id:  '', name:''}, location: {_id: '', name:''},type:""});
    const generateNewCardDistribution=useCallback((event) => {
        event.stopPropagation()
        event.preventDefault()
        setTab(event.target.dataset.event_key);
        setMerchantStatus({
            merchant: {
                _id: event.target.dataset.merchant_id,
                name:event.target.dataset.merchant_name
            },
            location: {
                _id: event.target.dataset.location_id,
                name:event.target.dataset.location_name
            },
            type:event.target.dataset.type
        })

    }, [setMerchantStatus,setTab]);
    return (
        <BaseLayout
            topLeft={<Heading text="Instant Cards" />}
            bottom={
                <div className="container-fluid">
                    <Tabs
                        onSelect={setTab}
                        activeKey={tab}
                        transition={false}
                        className="ml-n3 mb-3 border-solid-bottom"
                    >
                        <Tab
                            eventKey={InstantCardsStatus.CARD_STOCK}
                            title={
                                <>
                                    <IcIcon className="mr-2" size="md" icon={UilPostcard} />
                                    <span className="mr-2">Card Stock</span>
                                </>
                            }
                        >
                        </Tab>
                        <Tab
                            eventKey={InstantCardsStatus.CARD_DISTRIBUTION}
                            title={
                                <>
                                    <IcIcon className="mr-2" size="md" icon={UilPostcard} />
                                    <span className="mr-2">Card Distribution</span>
                                </>
                            }
                        >
                        </Tab>
                        <Tab
                            eventKey={InstantCardsStatus.CARD_PRINTING}
                            title={
                                <>
                                    <IcIcon className="mr-2" size="md" icon={UilPostcard} />
                                    <span className="mr-2">Card Printing</span>
                                </>
                            }
                        >
                        </Tab>
                    </Tabs>
                    <InstantCardView
                        instantCardsStatus={tab}
                        merchantStatus={merchantStatus}
                        generateNewCardDistribution={generateNewCardDistribution}
                    />
                </div>
            }
        />
    );
}
export default InstantCards
