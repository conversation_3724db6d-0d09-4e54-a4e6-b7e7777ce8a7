import React, { useMemo } from "react";
import { State, City } from "country-state-city";
import PropTypes from "prop-types";
import { Col, Form, FormSelect, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import { toTitleCase } from "Utils";

const usedInCreateScenario = ["memberEnroll", "createMerchant", "addMerchantLocation"];
const usedInEditScenario = ["editMember", "editMerchant", "editMerchantLocation"];
const usedInDisableFields = ["editMerchant", "addMerchantLocation", "editMerchantLocation"];

const AddressInputs = ({
  usedIn,
  countryISOCode,
  country,
  stateOrProvince,
  line1,
  line2,
  line3,
  city,
  zipOrPostcode,
  isChanging,
  onChangeHandler,
  onChangeCountry,
  onChangeState,
  onChangeCity,
  showCountryRequired,   
  showAddressRequired,   
  showCityRequired       
}) => {
  const statesOfCountry = useMemo(() =>
    State.getStatesOfCountry(countryISOCode).map(state => ({
      key: state.isoCode,
      value: toTitleCase(state.name)
    })),
    [countryISOCode]
  );

  const selectedStateOrProvince = useMemo(() => {
    if (~ usedInEditScenario.indexOf(usedIn) && stateOrProvince) {
      if (Array.isArray(stateOrProvince)) {
        return stateOrProvince
      } else {
        const currentStateorProvince = statesOfCountry.filter(
          state => state.value === stateOrProvince
        )
        return currentStateorProvince.length === 0
          ? [stateOrProvince]
          : currentStateorProvince
      }
    } else return []
  }, [usedIn, stateOrProvince, statesOfCountry]);

  const citiesOfCountry = useMemo(() => {
    if (~ usedInCreateScenario.indexOf(usedIn)) {
      if (stateOrProvince.length > 0) {
        return City.getCitiesOfState(
          countryISOCode,
          stateOrProvince[0]?.key
        ).map(city => ({ key: city.stateCode, value: toTitleCase(city.name) }))
      } else {
        return City.getCitiesOfCountry(countryISOCode).map(city => ({
          key: city.stateCode,
          value: toTitleCase(city.name)
        }))
      }
    } else if (~ usedInEditScenario.indexOf(usedIn)) {
      if (stateOrProvince) {
        if (Array.isArray(stateOrProvince)) {
          if (stateOrProvince.length === 0) {
            return City.getCitiesOfCountry(countryISOCode).map(city => ({
              key: city.stateCode,
              value: toTitleCase(city.name)
            }))
          } else {
            return City.getCitiesOfState(
              countryISOCode,
              stateOrProvince[0]?.key
            ).map(city => ({
              key: city.stateCode,
              value: toTitleCase(city.name)
            }))
          }
        } else {
          return City.getCitiesOfState(
            countryISOCode,
            statesOfCountry.find(state => state.value === stateOrProvince)?.key
          ).map(city => ({
            key: city.stateCode,
            value: toTitleCase(city.name)
          }))
        }
      } else {
        return City.getCitiesOfCountry(countryISOCode).map(city => ({
          key: city.stateCode,
          value: toTitleCase(city.name)
        }))
      }
    }
  }, [usedIn, countryISOCode, stateOrProvince, statesOfCountry]);

  const selectedCity = useMemo(() => {
    if (~ usedInEditScenario.indexOf(usedIn) && city) {
      if (Array.isArray(city)) {
        return city
      } else {
        const currentCity = citiesOfCountry.filter(
          state => state.value === city
        )
        return currentCity.length === 0 ? [city] : currentCity
      }
    } else return []
  }, [usedIn, city, citiesOfCountry]);

  return (
    <div>
      <Form.Group>
        <Form.Label className="d-flex align-items-center">
          Country
          {showCountryRequired && <div className="ml-1 text-danger">*</div>}
        </Form.Label>
        <Form.CountrySelect
          selected={country}
          onChange={~ usedInDisableFields.indexOf(usedIn) ? () => {} : onChangeCountry}
          placeholder="Enter Country"
          inputProps={~ usedInDisableFields.indexOf(usedIn)}
          disabled={isChanging || ~ usedInDisableFields.indexOf(usedIn)}
          required={showCountryRequired}
          
        />
      </Form.Group>
      <Form.Group controlId="state-province">
        <Form.Label>
          State/Province/Parish/District
        </Form.Label>
        <FormSelect
          allowNew
          clearButton
          id="state-province"
          multiple={false}
          disabled={isChanging}
          onChange={onChangeState}
          labelKey="value"
          options={statesOfCountry}
          placeholder="Enter State/Province/Parity/District"
          selected={~ usedInCreateScenario.indexOf(usedIn) ? 
            stateOrProvince : selectedStateOrProvince
          }
        />
      </Form.Group>
      <Form.Group>
        <Form.Label className="d-flex align-items-center">
          Address
          {showAddressRequired && <div className="ml-1 text-danger">*</div>}
        </Form.Label>
        <Row>
          <Col>
            <Form.Control
              id="line_1"
              type="text"
              name="line1"
              placeholder="Address Line One *"
              value={line1}
              disabled={isChanging}
              onChange={onChangeHandler}
              required={showAddressRequired}            />
          </Col>
          <Col>
            <Form.Control
              id="line_2"
              type="text"
              name="line2"
              placeholder="Address Line Two"
              value={line2}
              disabled={isChanging}
              onChange={onChangeHandler}
            />
          </Col>
        </Row>
        <br />
        <Row>
          <Col>
            <Form.Control
              id="line_3"
              type="text"
              name="line3"
              placeholder="Address Line Three"
              value={line3}
              disabled={isChanging}
              onChange={onChangeHandler}
            />
          </Col>
        </Row>
      </Form.Group>
      <Row>
        <Col>
          <Form.Group>
            <Form.Label className="d-flex align-items-center">
              City
              {showCityRequired && <div className="ml-1 text-danger">*</div>}
            </Form.Label>
            <FormSelect
              allowNew
              clearButton
              id="city"              
              multiple={false}
              disabled={isChanging}
              onChange={onChangeCity}
              labelKey="value"
              options={citiesOfCountry}
              placeholder="Enter City"
              required={showCityRequired}
              selected={~ usedInCreateScenario.indexOf(usedIn) ? city : selectedCity}
            />
          </Form.Group>
        </Col>
        <Col>
          <Form.Group>
            <Form.Label className="d-flex align-items-center">
              Postal/Zip Code
            </Form.Label>
            <Form.Control
              id="zip_post_code"
              type="text"
              name="zipOrPostcode"
              placeholder="Zip or Postcode"
              value={zipOrPostcode}
              disabled={isChanging}
              onChange={onChangeHandler}
            />
          </Form.Group>
        </Col>
      </Row>
    </div>
  );
};

AddressInputs.defaultProps = {
  line1: "Address Line One",
  onChangeHandler: () => {},
  onChangeCountry: () => {},
  onChangeState: () => {},
  onChangeCity: () => {},
  showCountryRequired: false,
  showAddressRequired: false,
  showCityRequired: false
};

AddressInputs.propTypes = {
  /**
   * Location component is used in
   */
  usedIn: PropTypes.string.isRequired,

  /**
   * Country's ISO code value
   */
  countryISOCode: PropTypes.string.isRequired,

  /**
   * Country value
   */
  country: PropTypes.array.isRequired,

  /**
   * State/Province/Parish/District value
   */
  stateOrProvince: PropTypes.any,

  /**
   * AddresslLine 1 value
   */
  line1: PropTypes.string.isRequired,

  /**
   * Address line 2 value
   */
  line2: PropTypes.string,

  /**
   * Address line 3 value
   */
  line3: PropTypes.string,

  /**
   * City value
   */
  city: PropTypes.any,

  /**
   * Zip or Postal code value
   */
  zipOrPostcode: PropTypes.string,

  /**
   * Function to set/change values for line1, line2, line3, zipOrPostcode
   */
  onChangeHandler: PropTypes.func.isRequired,

  /**
   * Function to set/change values for country
   */
  onChangeCountry: PropTypes.func.isRequired,

  /**
   * Function to set/change values for stateOrProvince
   */
  onChangeState: PropTypes.func.isRequired,

  /**
   * Function to set/change values for city
   */
  onChangeCity: PropTypes.func.isRequired,

  /**
   * Boolean to indicate if Country fields are required
   */

  showCountryRequired: PropTypes.bool,
  /**
   * Boolean to indicate if Address field is required
   */
  showAddressRequired: PropTypes.bool,
  /**
   * Boolean to indicate if City field is required
   */

  showCityRequired: PropTypes.bool

};

export default AddressInputs;
