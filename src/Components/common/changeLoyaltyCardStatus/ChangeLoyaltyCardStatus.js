import React, { useCallback } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, Modal, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { CardSuspendReasons, CardStatus, CardTypes } from "Data";
import { toTitleCase } from "Utils";

import "./ChangeLoyaltyCardStatus.scss";

const getTextColor = (status) => {
    switch (status) {
        case CardStatus.ASSIGNED:
            return "text-secondary";
        case CardStatus.DEACTIVATED:
            return "text-danger";
        case CardStatus.SUSPENDED:
            return "text-orange";
        default:
            return "";
    }
};

const ChangeLoyaltyCardStatus = ({
    show,
    cardType,
    updatedStatus,
    suspendedReason,
    note,
    isUpdating,
    validated,
    waiveCardReplacementFee,
    customBtn = null,
    onHide,
    updateCard,
    setSuspendedReason,
    setNote,
    setWaiveCardReplacementFee,
}) => {
    const onChangeSuspendedReason = useCallback(
        (e) => {
            setSuspendedReason(e.target.value);
            if (e.target.value === CardSuspendReasons.OTHER) {
                setNote("");
            } else {
                setNote(e.target.value);
            }
        },
        [setSuspendedReason, setNote]
    );

    const onChangeNote = useCallback(
        (e) => {
            if (cardType === CardTypes.DIGITAL_CARD) {
                setSuspendedReason(CardSuspendReasons.OTHER);
            }
            setNote(e.target.value);
        },
        [cardType, setNote, setSuspendedReason]
    );

    const onChangeWaiveCardReplacementFeeStatus = useCallback(
        (e) => setWaiveCardReplacementFee(e.target.checked),
        [setWaiveCardReplacementFee]
    );

    return (
        <Modal
            className="change-loyalty-card-status-modal-view"
            show={show}
            onHide={onHide}
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>Update Card Status</Modal.Title>
            </Modal.Header>
            <Form
                className="form-body"
                onSubmit={updateCard}
                validated={validated}
                noValidate
            >
                <Modal.Body>
                    <p className="mb-4">
                        {`Are you sure you want update loyalty card status to `}
                        <span
                            className={`font-weight-bold ${getTextColor(
                                updatedStatus
                            )}`}
                        >
                            {updatedStatus}
                        </span>
                        ?
                    </p>

                    {updatedStatus === CardStatus.SUSPENDED && (
                        <>
                            <Form.Label>
                                Reason to Suspend the Card
                                <span className="text-danger">*</span>
                            </Form.Label>
                            {cardType !== CardTypes.DIGITAL_CARD && (
                                <Form.Group
                                    controlId="reason"
                                    className="input-group"
                                >
                                    {Object.keys(CardSuspendReasons).map(
                                        (reason) => (
                                            <Form.Check
                                                key={reason}
                                                className="rounded-0 input-check mr-4"
                                                custom
                                                id={reason}
                                                value={reason}
                                                type="radio"
                                                name="suspendedReason"
                                                label={toTitleCase(reason)}
                                                checked={
                                                    suspendedReason === reason
                                                }
                                                disabled={isUpdating}
                                                onChange={
                                                    onChangeSuspendedReason
                                                }
                                                required
                                            />
                                        )
                                    )}
                                </Form.Group>
                            )}
                            {(suspendedReason === CardSuspendReasons.OTHER ||
                                cardType === CardTypes.DIGITAL_CARD) && (
                                <Form.Group controlId="note">
                                    <Form.Control
                                        as="textarea"
                                        rows={5}
                                        placeholder="Enter reason to suspend..."
                                        value={note}
                                        disabled={isUpdating}
                                        onChange={onChangeNote}
                                        required
                                    />
                                </Form.Group>
                            )}
                        </>
                    )}
                    {updatedStatus === CardStatus.DEACTIVATED && (
                        <>
                            <div className="mb-3 border p-2 border-danger rounded text-center text-danger deactivate-card-warning">
                                <h2 className="p-0 m-0">WARNING!</h2>
                                <div className="font-weight-bold">
                                    Deactivating a card will invalidate the card
                                    forever.
                                </div>
                                <div className="pb-3 font-weight-bold">
                                    This action cannot be undone.
                                </div>
                            </div>
                            {updatedStatus === CardStatus.DEACTIVATED && (
                                <Form.Group className="mt-2">
                                    <Form.Check
                                        className="member-checkbox-margin"
                                        name="waiveCardReplacementFee"
                                        id="checkbox"
                                        type="checkbox"
                                        label="Waive card replacement fee"
                                        checked={waiveCardReplacementFee}
                                        disabled={isUpdating}
                                        onChange={
                                            onChangeWaiveCardReplacementFeeStatus
                                        }
                                    />
                                </Form.Group>
                            )}
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer
                    className={`d-flex justify-content-${
                        customBtn ? "between" : "end"
                    } align-items-center`}
                >
                    {customBtn && <div>{customBtn}</div>}
                    <div>
                        <Button
                            className="mr-1"
                            variant="outline-primary"
                            size="sm"
                            onClick={onHide}
                            disabled={isUpdating}
                        >
                            Cancel
                        </Button>
                        <Button
                            className="ml-1"
                            type="submit"
                            variant={
                                updatedStatus === CardStatus.DEACTIVATED
                                    ? "danger"
                                    : "primary"
                            }
                            size="sm"
                            disabled={isUpdating}
                        >
                            {isUpdating ? "Updating..." : "Update Status"}
                        </Button>
                    </div>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

ChangeLoyaltyCardStatus.propTypes = {
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func.isRequired,
    updateCard: PropTypes.func,
    updatedStatus: PropTypes.string,
    setSuspendedReason: PropTypes.func,
    suspendedReason: PropTypes.string,
    note: PropTypes.string,
    setNote: PropTypes.func,
    isUpdating: PropTypes.bool,
    customBtn: PropTypes.any,
};

export default ChangeLoyaltyCardStatus;
