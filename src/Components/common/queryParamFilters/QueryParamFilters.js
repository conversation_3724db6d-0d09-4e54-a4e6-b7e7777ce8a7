import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { v4 as uuidv4 } from "uuid";
import {
    <PERSON><PERSON>,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCheckCircle,
    faExclamationCircle,
    faPlus,
    faTimes,
} from "FaICIconMap";
import { OperationsAsOptions } from "Data";
import {
    areArraysEquals,
    checkIfFirstLetterOfStringIsAVowel,
    formatToCommonFormat,
    getTruncatedStringWithTooltip,
} from "Utils";
import FilterBooleanWidget from "./widgets/FilterBooleanWidget";
import FilterDateRangeWidget from "./widgets/filterDateRangeWidget/FilterDateRangeWidget";
import FilterDropdownWidget from "./widgets/FilterDropdownWidget";
import FilterSingleDateWidget from "./widgets/FilterSingleDateWidget";
import FilterTextInputWidget from "./widgets/FilterTextInputWidget";

import "./QueryParamFilters.scss";

const QueryParamFilterViewOnly = ({
    filterMetadata = {},
    selectedFilterOption = [],
    selectedFilterOperator = [],
    value,
}) => {
    QueryParamFilterViewOnly.defaultProps = {
        value: null,
    };

    QueryParamFilterViewOnly.propTypes = {
        filterMetadata: PropTypes.object.isRequired,
        selectedFilterOption: PropTypes.PropTypes.arrayOf(PropTypes.object),
        selectedFilterOperator: PropTypes.PropTypes.arrayOf(PropTypes.object),
        value: PropTypes.any,
    };
    const optionMetadata = useMemo(
        () => filterMetadata[selectedFilterOption[0]?.value] || {},
        [filterMetadata, selectedFilterOption]
    );

    const filterOptionValueWidget = useMemo(() => {
        switch (optionMetadata?.filterInput) {
            case "text":
            case "number":
            case "date":
                return value || "~unknown";
            case "select":
                return (
                    value[0][optionMetadata?.labelKey || "label"] || "~unknown"
                );
            case "date-range":
                return `From: ${value?.fromDate || "~unknown"} - To ${
                    value?.toDate || "~unknown"
                }`;
            case "boolean":
                return value ? "True" : "False";
            default:
                return "~unknown";
        }
    }, [value, optionMetadata]);

    return (
        <div className="w-100 d-flex align-items-center">
            <div>
                {getTruncatedStringWithTooltip({
                    value: selectedFilterOption[0]?.label || "~unknown",
                    valueMaxLength: 20,
                })}
            </div>
            <div className="mx-2">-</div>
            {selectedFilterOperator?.length !== 0 ? (
                <>
                    <div className="mr- font-weight-bold">
                        {selectedFilterOperator[0]?.label}
                    </div>
                    <div className="mx-2">-</div>
                </>
            ) : null}
            <div className="font-weight-bold">
                {getTruncatedStringWithTooltip({
                    value: filterOptionValueWidget,
                    valueMaxLength: 45,
                })}
            </div>
        </div>
    );
};

const QueryParamFilter = ({
    rowId = "",
    filterMetadata = {},
    filterSelectOptions = [],
    selectedFilterOption = [],
    selectedFilterOperator = [],
    value,
    isLoading,
    onSelectFilterOption,
    onSelectFilterOperation,
    onChangeFilterValue,
}) => {
    QueryParamFilter.defaultProps = {
        isLoading: false,
        value: null,
        onSelectFilterOption: () => {},
        onSelectFilterOperation: () => {},
        onChangeFilterValue: () => {},
    };

    QueryParamFilter.propTypes = {
        rowId: PropTypes.string.isRequired,
        filterMetadata: PropTypes.object.isRequired,
        filterSelectOptions: PropTypes.PropTypes.arrayOf(PropTypes.object),
        selectedFilterOption: PropTypes.PropTypes.arrayOf(PropTypes.object),
        selectedFilterOperator: PropTypes.PropTypes.arrayOf(PropTypes.object),
        value: PropTypes.any,
        isLoading: PropTypes.bool,
        onSelectFilterOption: PropTypes.func,
        onSelectFilterOperation: PropTypes.func,
        onChangeFilterValue: PropTypes.func,
    };
    const optionMetadata = useMemo(
        () => filterMetadata[selectedFilterOption[0]?.value] || {},
        [filterMetadata, selectedFilterOption]
    );

    const onChangeSelect = useCallback(
        (e) => onSelectFilterOption(rowId, e),
        [rowId, onSelectFilterOption]
    );

    const onChangeOperatorSelect = useCallback(
        (e) => {
            const customOperatorKey =
                optionMetadata?.customOperatorKey || "operation";
            const changingFilter = selectedFilterOption[0]?.label || "";
            onSelectFilterOperation(
                rowId,
                customOperatorKey,
                changingFilter,
                e
            );
        },
        [
            rowId,
            optionMetadata?.customOperatorKey,
            selectedFilterOption,
            onSelectFilterOperation,
        ]
    );

    const onChangeFilterValueWidget = useCallback(
        (filterValue) => {
            const filterInput = optionMetadata?.filterInput || "";
            const changingFilter = selectedFilterOption[0]?.label || "";
            onChangeFilterValue(
                rowId,
                filterInput,
                changingFilter,
                filterValue
            );
        },
        [
            rowId,
            selectedFilterOption,
            onChangeFilterValue,
            optionMetadata?.filterInput,
        ]
    );

    const customOperatorWidget = useMemo(() => {
        if (optionMetadata?.useCustomOperators) {
            return (
                <div
                    className={`${
                        selectedFilterOperator.length !== 0 ? "mr-3" : ""
                    }`}
                >
                    <FilterDropdownWidget
                        id={
                            selectedFilterOperator[0]?.value ||
                            "query-operator-selector"
                        }
                        placeholder="Select an operator..."
                        selectOptions={OperationsAsOptions}
                        selectedValue={selectedFilterOperator}
                        disabled={isLoading}
                        onChangeSelect={onChangeOperatorSelect}
                    />
                </div>
            );
        }

        return null;
    }, [
        selectedFilterOperator,
        isLoading,
        optionMetadata?.useCustomOperators,
        onChangeOperatorSelect,
    ]);

    const filterOptionValueWidget = useMemo(() => {
        switch (optionMetadata?.filterInput) {
            case "text":
            case "number": {
                let optionalProps = {};

                if (optionMetadata?.filterInput === "number") {
                    optionalProps = { min: optionMetadata?.minimumValue || 0 };
                }

                return (
                    <FilterTextInputWidget
                        name={optionMetadata?.name || "filter-input"}
                        placeholder={`Enter ${
                            checkIfFirstLetterOfStringIsAVowel(
                                optionMetadata?.placeholder || ""
                            )
                                ? "an"
                                : "a"
                        } ${optionMetadata?.placeholder || "value"}...`}
                        type={optionMetadata.filterInput}
                        value={value || optionMetadata?.defaultValue || ""}
                        disabled={isLoading}
                        onChange={onChangeFilterValueWidget}
                        {...optionalProps}
                    />
                );
            }
            case "select":
                return (
                    <FilterDropdownWidget
                        id={optionMetadata?.id || "query-filter-value-selector"}
                        placeholder={`Select ${
                            checkIfFirstLetterOfStringIsAVowel(
                                optionMetadata?.placeholder || ""
                            )
                                ? "an"
                                : "a"
                        } ${optionMetadata?.placeholder || "value"}...`}
                        selectOptions={optionMetadata?.options || []}
                        selectedValue={
                            value || optionMetadata?.defaultValue || []
                        }
                        labelKey={optionMetadata?.labelKey || "label"}
                        groupBy={optionMetadata?.groupBy || ""}
                        disabled={isLoading}
                        onChangeSelect={onChangeFilterValueWidget}
                    />
                );
            case "date":
                return (
                    <FilterSingleDateWidget
                        name={optionMetadata?.name || "date-select"}
                        placeholder={`Select ${
                            checkIfFirstLetterOfStringIsAVowel(
                                optionMetadata?.placeholder || ""
                            )
                                ? "an"
                                : "a"
                        } ${
                            optionMetadata?.placeholder
                                ? optionMetadata.placeholder + " date"
                                : "date value"
                        }...`}
                        value={value || optionMetadata?.defaultValue || ""}
                        disabled={isLoading}
                        onChange={onChangeFilterValueWidget}
                    />
                );
            case "date-range":
                return (
                    <FilterDateRangeWidget
                        fromDate={
                            value?.fromDate ||
                            optionMetadata?.defaultFromDate ||
                            ""
                        }
                        toDate={
                            value?.toDate || optionMetadata?.defaultToDate || ""
                        }
                        disabled={isLoading}
                        onSelectDateRange={onChangeFilterValueWidget}
                    />
                );
            case "boolean":
                return (
                    <FilterBooleanWidget
                        name={optionMetadata?.name || "filter-boolean"}
                        checkValues={optionMetadata?.options || []}
                        disabled={isLoading}
                        onChange={onChangeFilterValueWidget}
                    />
                );
            default:
                return null;
        }
    }, [isLoading, value, optionMetadata, onChangeFilterValueWidget]);

    return (
        <div className="w-100 d-flex justify-content-start align-items-center">
            <div className="mr-3">
                <FilterDropdownWidget
                    id={
                        selectedFilterOption[0]?.value ||
                        "query-filter-selector"
                    }
                    placeholder="Select a filter option..."
                    selectOptions={filterSelectOptions}
                    selectedValue={selectedFilterOption}
                    disabled={isLoading || selectedFilterOption.length !== 0}
                    onChangeSelect={onChangeSelect}
                />
            </div>
            {customOperatorWidget}
            {optionMetadata?.useCustomOperators &&
                selectedFilterOption.length !== 0 &&
                selectedFilterOperator.length !== 0 &&
                filterOptionValueWidget}
            {!optionMetadata?.useCustomOperators &&
                selectedFilterOption.length !== 0 &&
                filterOptionValueWidget}
        </div>
    );
};

const setFilterRowErrorMessage = (dynamicMsg = "") =>
    `Please ${dynamicMsg || "select a value"} or remove this filter row.`;

const getUpdatedFilterData = ({
    fR = {},
    filterInput = "",
    filterValue = null,
    queryParamFilterMetadata = {},
}) => {
    switch (filterInput) {
        case "text":
        case "number": {
            const textValue = filterValue.currentTarget.value || "";

            const filterMetadata =
                queryParamFilterMetadata[fR.selectedFilterOption[0]?.value] ||
                {};

            return {
                selectedFilterData: {
                    queryFilterToBeApplied: {
                        ...(filterMetadata?.useCustomOperators
                            ? {
                                  ...fR?.selectedFilterData
                                      ?.queryFilterToBeApplied,
                              }
                            : {}),
                        [fR.selectedFilterOption[0]?.value]: textValue,
                    },
                    value: textValue,
                },
            };
        }
        case "select": {
            return {
                selectedFilterData: {
                    queryFilterToBeApplied: {
                        [fR.selectedFilterOption[0]?.value]:
                            filterValue[0]?.[
                                fR?.selectedFilterValueKey || "value"
                            ],
                    },
                    value: filterValue,
                },
            };
        }
        case "date": {
            const dateValue = filterValue
                ? formatToCommonFormat(filterValue)
                : "";

            return {
                selectedFilterData: {
                    queryFilterToBeApplied: {
                        [fR.selectedFilterOption[0]?.value]: dateValue,
                    },
                    value: dateValue,
                },
            };
        }
        case "date-range": {
            const fromDate = filterValue?.startDate
                ? formatToCommonFormat(filterValue?.startDate)
                : "";
            const toDate = filterValue?.endDate
                ? formatToCommonFormat(filterValue?.endDate)
                : "";

            const dateRangeMetadata =
                queryParamFilterMetadata[fR.selectedFilterOption[0]?.value] ||
                {};

            return {
                selectedFilterData: {
                    queryFilterToBeApplied: {
                        [dateRangeMetadata?.fromDateKey]: fromDate,
                        [dateRangeMetadata?.toDateKey]: toDate,
                    },
                    value: { fromDate, toDate },
                },
            };
        }
        case "boolean": {
            const booleanValue = filterValue.currentTarget.value;

            return {
                selectedFilterData: {
                    queryFilterToBeApplied: {
                        [fR.selectedFilterOption[0]?.value]: booleanValue,
                    },
                    value: booleanValue,
                },
            };
        }
        default:
            throw new Error("Invalid filter input");
    }
};

const validateDateRangeFilterRows = ({
    row = {},
    rowFilterMetadata = {},
    errorMsgObj = {},
    validatedResults = [],
    isValidResults = [],
}) => {
    errorMsgObj.inputTypeMsg = "select a date";

    const queryFilterFromDateValue =
        row.selectedFilterData.queryFilterToBeApplied[
            rowFilterMetadata?.fromDateKey
        ] || "";
    const queryFilterToDateValue =
        row.selectedFilterData.queryFilterToBeApplied[
            rowFilterMetadata?.toDateKey
        ] || "";

    if (queryFilterFromDateValue === "" || queryFilterToDateValue === "") {
        const emptyDateError = {
            filterRowId: row.rowId,
            invalidFilterError: setFilterRowErrorMessage(
                `${errorMsgObj.inputTypeMsg} for "${errorMsgObj.filterName}"`
            ),
        };

        return validatedResults.map((vR) =>
            vR?.filterRowId === row.rowId ? { ...vR, ...emptyDateError } : vR
        );
    } else {
        return isValidResults;
    }
};

const validateFilterRows = ({
    row = {},
    queryParamFilterMetadata = {},
    validatedResults = [],
}) => {
    const errorMsgObj = {
        inputTypeMsg: "",
        inputTypeIncorrectValueMsg: "",
        filterName: row.selectedFilterOption[0]?.label || "filter",
    };
    const rowSelectedFilterOptionValue = row.selectedFilterOption[0]?.value;
    const rowFilterMetadata =
        queryParamFilterMetadata[rowSelectedFilterOptionValue] || {};
    const isValidResults = validatedResults.map((vR) =>
        vR?.filterRowId === row.rowId ? { ...vR, invalidFilterError: "" } : vR
    );
    const queryFilterValue =
        row.selectedFilterData.queryFilterToBeApplied[
            rowSelectedFilterOptionValue
        ] || "";

    switch (rowFilterMetadata.filterInput) {
        case "date-range": {
            return validateDateRangeFilterRows({
                row,
                rowFilterMetadata,
                errorMsgObj,
                validatedResults,
                isValidResults,
            });
        }
        case "boolean":
        case "date":
        case "select":
        case "text":
        case "number": {
            switch (rowFilterMetadata.filterInput) {
                case "date":
                    errorMsgObj.inputTypeMsg = "select a date value";
                    break;
                case "select":
                    errorMsgObj.inputTypeMsg = "select a value";
                    break;
                case "text":
                    errorMsgObj.inputTypeMsg = "enter a value";
                    break;
                case "number": {
                    if (
                        rowFilterMetadata?.useCustomOperators &&
                        row?.selectedFilterOperator?.length === 0
                    ) {
                        errorMsgObj.inputTypeMsg = "select an operator";
                    } else if (
                        !isNaN(Number(rowFilterMetadata?.minimumValue)) &&
                        !isNaN(Number(queryFilterValue)) &&
                        Number(queryFilterValue) <
                            Number(rowFilterMetadata?.minimumValue)
                    ) {
                        errorMsgObj.inputTypeIncorrectValueMsg = `Value for "${errorMsgObj.filterName}" cannot be less than "${rowFilterMetadata.minimumValue}"!`;
                    } else {
                        errorMsgObj.inputTypeMsg = "enter a value";
                    }
                    break;
                }
                case "boolean":
                    errorMsgObj.inputTypeMsg = "check a value";
                    break;
                default:
                    break;
            }

            if (
                errorMsgObj.inputTypeIncorrectValueMsg ||
                queryFilterValue === ""
            ) {
                const dynamicMsgForRow = `${errorMsgObj.inputTypeMsg} for "${errorMsgObj.filterName}"`;

                const emptyValueError = {
                    filterRowId: row.rowId,
                    invalidFilterError:
                        errorMsgObj.inputTypeIncorrectValueMsg ||
                        setFilterRowErrorMessage(dynamicMsgForRow),
                };

                return validatedResults.map((vR) =>
                    vR?.filterRowId === row.rowId
                        ? { ...vR, ...emptyValueError }
                        : vR
                );
            } else {
                return isValidResults;
            }
        }
        default:
            return validatedResults;
    }
};

const filterRowData = {
    rowId: uuidv4(),
    selectedFilterOption: [],
    selectedFilterOperator: [],
    selectedFilterData: {
        queryFilterToBeApplied: {},
        value: null,
    },
};
const maxFiltersPerFilteration = 6;

const QueryParamFilters = ({
    multipleFilters,
    queryParamFilterMetadata = {},
    queryParamFilterOptions = [],
    isLoading,
    viewOnly,
    viewOnlyTitle,
    appliedFilters = [],
    appliedFilterRows = [],
    location,
    setAppliedFilters,
    setAppliedFilterRows,
}) => {
    const [filterRows, setFilterRows] = useState([{ ...filterRowData }]);
    const [isFiltersValidated, setIsFiltersValidated] = useState(false);
    const [isValidFilters, setIsValidFilters] = useState([]);
    const [isApplied, setIsApplied] = useState(false);

    const filterSelectOptions = useMemo(
        () =>
            queryParamFilterOptions.filter((qF) => {
                const selectedFilters = filterRows.map(
                    (item) => item?.selectedFilterOption[0]?.value
                );

                return !selectedFilters.includes(qF?.value);
            }),
        [filterRows, queryParamFilterOptions]
    );

    const addFilterRow = useCallback(() => {
        setFilterRows((prevRows) => {
            const newRows = [...prevRows];
            newRows.push({ ...filterRowData, rowId: uuidv4() });

            return newRows;
        });
    }, [setFilterRows]);

    const removeFilterRow = useCallback(
        (e) => {
            setFilterRows(
                filterRows.filter((row) => row?.rowId !== e.currentTarget.id)
            );
        },
        [filterRows, setFilterRows]
    );

    const onSelectFilterOption = useCallback(
        (rowId, selectOption) => {
            const selectedFilterKeyAndOption = {
                selectedFilterOption: selectOption,
                selectedFilterData: {
                    queryFilterToBeApplied: {},
                    value: null,
                },
                selectedFilterValueKey: "label",
            };
            const selectedFilterMetadata =
                queryParamFilterMetadata[selectOption[0]?.value] || {};

            switch (selectedFilterMetadata?.filterInput) {
                case "select":
                    selectedFilterKeyAndOption.selectedFilterValueKey =
                        selectedFilterMetadata?.valueKey;
                    selectedFilterKeyAndOption.selectedFilterData = {
                        queryFilterToBeApplied: {
                            [selectOption[0]?.value]: "",
                        },
                        value: [],
                    };
                    break;
                case "date-range":
                    selectedFilterKeyAndOption.selectedFilterData = {
                        queryFilterToBeApplied: {
                            [selectedFilterMetadata?.fromDateKey]: "",
                            [selectedFilterMetadata?.toDateKey]: "",
                        },
                        value: {
                            fromDate: "",
                            toDate: "",
                        },
                    };
                    break;
                case "date":
                case "text":
                case "number":
                case "boolean":
                    selectedFilterKeyAndOption.selectedFilterData = {
                        queryFilterToBeApplied: {
                            [selectOption[0]?.value]: "",
                        },
                        value: "",
                    };
                    break;
                default:
                    break;
            }

            const updatedFilterRows = filterRows.map((fR) =>
                fR?.rowId === rowId
                    ? { ...fR, ...selectedFilterKeyAndOption }
                    : fR
            );
            setFilterRows(updatedFilterRows);
        },
        [filterRows, queryParamFilterMetadata, setFilterRows]
    );

    const onSelectFilterOperation = useCallback(
        (rowId, operationKey, changingFilter, selectOperation) => {
            try {
                const updatedFilterRows = filterRows.map((fR) => {
                    const updatedFilterData = {
                        selectedFilterOperator: selectOperation,
                        selectedFilterData: {
                            ...fR.selectedFilterData,
                            queryFilterToBeApplied: {
                                ...fR.selectedFilterData.queryFilterToBeApplied,
                                [operationKey]: selectOperation[0]?.value,
                            },
                        },
                    };
                    return fR?.rowId === rowId
                        ? { ...fR, ...updatedFilterData }
                        : fR;
                });
                setFilterRows(updatedFilterRows);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        {`Failed to change operator value of `}
                        {changingFilter ? `"` + changingFilter + `"` : "row"}!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [filterRows, setFilterRows]
    );

    const onChangeFilterValue = useCallback(
        (rowId, filterInput, changingFilter, filterValue) => {
            try {
                const updatedFilterRows = filterRows.map((fR) => {
                    const updatedFilterData = getUpdatedFilterData({
                        fR,
                        filterInput,
                        filterValue,
                        queryParamFilterMetadata,
                    });

                    return fR?.rowId === rowId
                        ? { ...fR, ...updatedFilterData }
                        : fR;
                });
                setFilterRows(updatedFilterRows);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        {`Failed to change filter value of `}
                        {changingFilter ? `"` + changingFilter + `"` : "row"}!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [queryParamFilterMetadata, filterRows, setFilterRows]
    );

    const applyFilters = useCallback(() => {
        setIsFiltersValidated(true);
        let validated = false;

        validated =
            isValidFilters.filter((iVF) => iVF?.invalidFilterError !== "")
                .length === 0;

        if (validated) {
            setAppliedFilters(
                filterRows.map(
                    (fR) => fR.selectedFilterData.queryFilterToBeApplied
                )
            );
            setAppliedFilterRows(filterRows);
            setIsApplied(true);
            setIsFiltersValidated(false);
        }
    }, [
        setAppliedFilters,
        setAppliedFilterRows,
        isValidFilters,
        filterRows,
        setIsFiltersValidated,
        setIsApplied,
    ]);

    const clearFilters = useCallback(() => {
        if (location?.state) {
            location.state = undefined;
        }
        setFilterRows([{ ...filterRowData, rowId: uuidv4() }]);
        setIsFiltersValidated(false);
        setAppliedFilters([]);
        setAppliedFilterRows([]);
        setIsApplied(false);
    }, [
        location,
        setAppliedFilters,
        setAppliedFilterRows,
        setFilterRows,
        setIsFiltersValidated,
        setIsApplied,
    ]);

    useEffect(() => {
        if (appliedFilterRows.length !== 0) {
            setFilterRows(appliedFilterRows);
        }
    }, [appliedFilterRows]);

    useEffect(() => {
        if (filterRows.length === 0) {
            setIsFiltersValidated(false);
        }
    }, [filterRows.length]);

    useEffect(() => {
        try {
            const defaultResults = {
                invalidFilterError: "",
            };
            let validatedResults = [];

            validatedResults = filterRows.map((item) => ({
                filterRowId: item.rowId,
                ...defaultResults,
            }));

            filterRows.forEach((row) => {
                if (row.selectedFilterOption.length === 0) {
                    const filterOptionEmptyError = {
                        filterRowId: row.rowId,
                        invalidFilterError: setFilterRowErrorMessage(
                            "select a filter option"
                        ),
                    };

                    validatedResults = validatedResults.map((vR) =>
                        vR?.filterRowId === row.rowId
                            ? { ...vR, ...filterOptionEmptyError }
                            : vR
                    );
                } else {
                    validatedResults = validateFilterRows({
                        row,
                        queryParamFilterMetadata,
                        validatedResults,
                    });
                }
            });
            setIsValidFilters(validatedResults);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to validate filters!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [filterRows, queryParamFilterMetadata]);

    useEffect(() => {
        if (filterRows.length !== 0 && appliedFilters.length !== 0) {
            const filterRowsQueryFilters = filterRows.map(
                (fR) => fR.selectedFilterData.queryFilterToBeApplied
            );

            if (areArraysEquals(appliedFilters, filterRowsQueryFilters, true)) {
                setIsApplied(true);
            } else {
                setIsApplied(false);
            }
        }
    }, [appliedFilters, filterRows]);

    return (
        <Card className="query-param-filters">
            <Card.Body className={!viewOnly ? "my-2" : ""}>
                {multipleFilters && !viewOnly && (
                    <div className="text-right mb-3">
                        {queryParamFilterOptions.length !== filterRows.length &&
                            filterRows.length < maxFiltersPerFilteration && (
                                <Button
                                    size="sm"
                                    variant="primary"
                                    disabled={isLoading}
                                    onClick={addFilterRow}
                                >
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faPlus}
                                        />
                                        Add Filter
                                    </div>
                                </Button>
                            )}
                        {queryParamFilterOptions.length !==
                            maxFiltersPerFilteration &&
                            filterRows.length === maxFiltersPerFilteration && (
                                <h3 className="mb-0 text-orange">{`Only a maximum of ${maxFiltersPerFilteration} fitlers can be applied at a time.`}</h3>
                            )}
                    </div>
                )}
                {viewOnly ? viewOnlyTitle || "Applied Filters" : null}
                <div
                    className={`filter-rows ${
                        !viewOnly ? "mb-3" : "grey-bg rounded"
                    } text-right`}
                >
                    {filterRows.length > 0 ? (
                        <div className={`${viewOnly ? "view-only px-3" : ""}`}>
                            {filterRows.map((fR) => {
                                const isInvalidFilterRowMsg =
                                    (isFiltersValidated &&
                                        isValidFilters.find(
                                            (iVF) =>
                                                iVF?.filterRowId === fR.rowId
                                        )?.invalidFilterError) ||
                                    "";
                                const optionMetadata =
                                    queryParamFilterMetadata[
                                        (fR?.selectedFilterOption || [])[0]
                                            ?.value
                                    ] || {};
                                let faIconValue = "";
                                let validatedClasses = "";

                                if (isFiltersValidated) {
                                    faIconValue = isInvalidFilterRowMsg
                                        ? faExclamationCircle
                                        : faCheckCircle;
                                    validatedClasses = isInvalidFilterRowMsg
                                        ? "is-invalid-filter-row"
                                        : "is-valid-filter-row";
                                }

                                return (
                                    <div
                                        key={fR.rowId}
                                        className={`border rounded ${
                                            validatedClasses || "text-muted"
                                        } p-3 my-3 w-100 ${
                                            viewOnly
                                                ? "bg-white"
                                                : "filter-row-bg"
                                        }`}
                                    >
                                        {viewOnly ? (
                                            <QueryParamFilterViewOnly
                                                key={fR.rowId}
                                                filterMetadata={
                                                    queryParamFilterMetadata
                                                }
                                                selectedFilterOption={
                                                    fR?.selectedFilterOption ||
                                                    []
                                                }
                                                selectedFilterOperator={
                                                    fR?.selectedFilterOperator ||
                                                    []
                                                }
                                                value={
                                                    fR?.selectedFilterData
                                                        ?.value
                                                }
                                            />
                                        ) : (
                                            <div className="w-100 d-flex justify-content-between align-items-center">
                                                <div className="w-100 justify-content-between align-items-center">
                                                    <div
                                                        className={`w-100 d-flex ${
                                                            optionMetadata?.useCustomOperators &&
                                                            fR
                                                                ?.selectedFilterOperator
                                                                ?.length !== 0
                                                                ? "flex-column  align-items-start"
                                                                : "align-items-center"
                                                        } justify-content-between`}
                                                    >
                                                        <div className="w-100 d-flex align-items-center">
                                                            {faIconValue && (
                                                                <IcIcon
                                                                    className="mr-2"
                                                                    icon={
                                                                        faIconValue
                                                                    }
                                                                    size="lg"
                                                                />
                                                            )}
                                                            <QueryParamFilter
                                                                key={fR.rowId}
                                                                rowId={fR.rowId}
                                                                filterMetadata={
                                                                    queryParamFilterMetadata
                                                                }
                                                                selectedFilterOption={
                                                                    fR?.selectedFilterOption ||
                                                                    []
                                                                }
                                                                selectedFilterOperator={
                                                                    fR?.selectedFilterOperator ||
                                                                    []
                                                                }
                                                                value={
                                                                    fR
                                                                        ?.selectedFilterData
                                                                        ?.value
                                                                }
                                                                filterSelectOptions={
                                                                    filterSelectOptions
                                                                }
                                                                isLoading={
                                                                    isLoading
                                                                }
                                                                onSelectFilterOption={
                                                                    onSelectFilterOption
                                                                }
                                                                onSelectFilterOperation={
                                                                    onSelectFilterOperation
                                                                }
                                                                onChangeFilterValue={
                                                                    onChangeFilterValue
                                                                }
                                                            />
                                                        </div>
                                                        {isInvalidFilterRowMsg && (
                                                            <div
                                                                className={`w-50 text-left text-danger ${
                                                                    optionMetadata?.useCustomOperators &&
                                                                    fR
                                                                        ?.selectedFilterOperator
                                                                        ?.length !==
                                                                        0
                                                                        ? "pt-3"
                                                                        : ""
                                                                }`}
                                                            >
                                                                <small>
                                                                    <div className="d-flex align-items-start">
                                                                        <div className="mr-1">
                                                                            *
                                                                        </div>
                                                                        <div>
                                                                            {
                                                                                isInvalidFilterRowMsg
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                </small>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                                {multipleFilters && (
                                                    <Button
                                                        className="p-0 shadow-none text-danger"
                                                        id={fR.rowId}
                                                        variant="link"
                                                        size="sm"
                                                        disabled={isLoading}
                                                        onClick={
                                                            removeFilterRow
                                                        }
                                                    >
                                                        <IcIcon
                                                            icon={faTimes}
                                                            size="2x"
                                                        />
                                                    </Button>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    ) : (
                        <div className="text-center filter-row-bg border rounded p-3">
                            <h3 className="mb-0">Please add a new filter</h3>
                        </div>
                    )}
                </div>
                {!viewOnly ? (
                    <div className="d-flex justify-content-center align-items-center">
                        <Button
                            variant="outline-primary"
                            className="mx-2"
                            size="sm"
                            disabled={
                                isLoading ||
                                isApplied ||
                                filterRows.length === 0
                            }
                            onClick={applyFilters}
                        >
                            {isLoading && isApplied
                                ? "Applying..."
                                : "Apply Filter"}
                        </Button>
                        <Button
                            variant="outline-danger"
                            className="mx-2"
                            size="sm"
                            disabled={
                                isLoading ||
                                !isApplied ||
                                filterRows.length === 0
                            }
                            onClick={clearFilters}
                        >
                            {isLoading && !isApplied
                                ? "Clearing..."
                                : "Clear Filter"}
                        </Button>
                    </div>
                ) : null}
            </Card.Body>
        </Card>
    );
};

QueryParamFilters.defaultProps = {
    multipleFilters: false,
    isLoading: false,
    viewOnly: false,
    viewOnlyTitle: "",
    appliedFilters: [],
    appliedFilterRows: [],
    location: {},
};

QueryParamFilters.propTypes = {
    queryParamFilterMetadata: PropTypes.object.isRequired,
    queryParamFilterOptions: PropTypes.arrayOf(PropTypes.object).isRequired,
    multipleFilters: PropTypes.bool,
    isLoading: PropTypes.bool,
    viewOnly: PropTypes.bool,
    viewOnlyTitle: PropTypes.string,
    appliedFilters: PropTypes.arrayOf(PropTypes.object),
    appliedFilterRows: PropTypes.arrayOf(PropTypes.object),
    location: PropTypes.object,
    setAppliedFilters: PropTypes.func.isRequired,
    setAppliedFilterRows: PropTypes.func.isRequired,
};

export default QueryParamFilters;
