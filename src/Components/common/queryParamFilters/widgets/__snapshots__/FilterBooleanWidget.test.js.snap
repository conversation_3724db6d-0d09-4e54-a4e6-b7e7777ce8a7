// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FilterBooleanWidget component snapshot Matches the snapshot 1`] = `
Array [
  <div
    className="rounded-0 input-check mr-3 custom-control custom-radio"
  >
    <input
      className="custom-control-input"
      disabled={false}
      id="true"
      onChange={[Function]}
      type="radio"
      value={true}
    />
    <label
      className="custom-control-label"
      htmlFor="true"
      title=""
    >
      True
    </label>
  </div>,
  <div
    className="rounded-0 input-check mr-3 custom-control custom-radio"
  >
    <input
      className="custom-control-input"
      disabled={false}
      id="false"
      onChange={[Function]}
      type="radio"
      value={false}
    />
    <label
      className="custom-control-label"
      htmlFor="false"
      title=""
    >
      False
    </label>
  </div>,
]
`;
