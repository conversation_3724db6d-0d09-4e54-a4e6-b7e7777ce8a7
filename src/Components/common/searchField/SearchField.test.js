import React from 'react'
import {render, screen, cleanup} from '@testing-library/react';
import { create } from "react-test-renderer";
import SearchField from './SearchField';

afterEach(() => {
    cleanup();
});

test('Renders SearchFiled component correctly', ()=> {

    const onInputChange = jest.fn();

    const props =  {onInputChange : onInputChange , textValue: 'Test', children: '', placeholderValue: 'Search'};
        
    const {queryByTestId} = render(<SearchField props={props}/>);

    expect(queryByTestId("search-field")).toBeTruthy();
    expect(screen.getByRole("text-field")).toBeVisible();
    expect(screen.getByRole("button")).toBeVisible();
    expect(queryByTestId("children")).toBeTruthy();
});

test('Search field elements arrangement', ()=>{
    const onInputChange = jest.fn();

    const props =  {onInputChange : onInputChange , textValue: 'Test', children: '', placeholderValue: 'Search'};
        
    const {getByTestId} = render(<SearchField props={props}/>);

    const parentElement = getByTestId("search-field");
    const child1 = getByTestId("search-element");
    const child2 = getByTestId("children");
    const formControl = screen.getByRole("text-field");
    const searchBtn = screen.getByRole("button");
    const searchIcon = getByTestId("search-icon")

    expect(parentElement).toContainElement(child1);
    expect(parentElement).toContainElement(child2);
    expect(child1).toContainElement(formControl);
    expect(child1).toContainElement(searchBtn);
    expect(searchBtn).toContainElement(searchIcon);
});

describe('Search field component snapshot', () => {
    test('Matches the snapshot', ()=> {

        const onInputChange = jest.fn();
        const props =  {onInputChange : onInputChange , textValue: 'Test', children: '', placeholderValue: 'Search'};

        const searchFieldComponent = create(<SearchField props={props}/>);
        expect(searchFieldComponent.toJSON()).toMatchSnapshot();

    });
});