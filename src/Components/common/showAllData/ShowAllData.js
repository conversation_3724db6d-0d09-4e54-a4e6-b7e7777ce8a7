import React, { useMemo } from "react";
import {
    Badge,
    Button,
    IcIcon,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCheckCircle, faEnvelope, faMobileAlt } from "FaICIconMap";
import { toTitleCaseFromCamelCase } from "Utils";
import DetailsAsLabelValue from "../detailsAsLabelValue/DetailsAsLabelValue";

import "./ShowAllData.scss";

const ShowAllData = ({ show, onHide, modalInfo }) => {
    const modalBodyData = useMemo(() => {
        switch (modalInfo.type) {
            case "availableOptions":
                const availableParentOptionsArr =
                    modalInfo?.data?.availableParentOptions;

                return (
                    <div>
                        <div className="available-options-info mb-2">
                            All enabled options will be indicated by,
                            <ul>
                                <li>
                                    A green
                                    <small className="example-background mx-1 border border-success rounded"></small>
                                    background.
                                </li>
                                <li>
                                    A <IcIcon size="lg" icon={faCheckCircle} />{" "}
                                    check mark next to the option name.
                                </li>
                            </ul>
                        </div>
                        {availableParentOptionsArr.map((option) => {
                            const optionState =
                                modalInfo?.data?.location?.options[
                                    option?.value
                                ];

                            return (
                                <Badge
                                    key={option?.name}
                                    className="py-1 px-4 mx-2 my-2"
                                    variant={
                                        optionState === true
                                            ? "success"
                                            : "default"
                                    }
                                >
                                    <div className="d-flex align-items-center">
                                        <h4 className="m-0">
                                            {toTitleCaseFromCamelCase(
                                                option?.name
                                            )}
                                        </h4>
                                        {optionState === true && (
                                            <IcIcon
                                                size="lg"
                                                className="ml-2"
                                                icon={faCheckCircle}
                                            />
                                        )}
                                    </div>
                                </Badge>
                            );
                        })}
                    </div>
                );
            case "contactInfo":
                return (
                    <>
                        <div className="mb-3">
                            <DetailsAsLabelValue
                                label="Contact Name"
                                value={modalInfo?.data?.location?.contact?.name}
                            />
                        </div>
                        <div className="mb-3">
                            <DetailsAsLabelValue
                                label="Mobile Number"
                                value={
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            size="md"
                                            icon={faMobileAlt}
                                            className="mr-2"
                                        />
                                        {
                                            modalInfo?.data?.location?.contact
                                                ?.mobileNumber
                                        }
                                    </div>
                                }
                            />
                        </div>
                        <div className="mb-3">
                            <DetailsAsLabelValue
                                label="Email"
                                value={
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            size="md"
                                            icon={faEnvelope}
                                            className="mr-2"
                                        />
                                        {
                                            modalInfo?.data?.location?.contact
                                                ?.email
                                        }
                                    </div>
                                }
                            />
                        </div>
                        <div>
                            <DetailsAsLabelValue
                                label="Address"
                                value={
                                    <>
                                        {`${modalInfo?.data?.location?.contact?.address?.line1}, ${modalInfo?.data?.location?.contact?.address?.line2},`}
                                        <br />
                                        {modalInfo?.data?.location?.contact
                                            ?.address?.line3 && (
                                            <div>
                                                {
                                                    modalInfo?.data?.location
                                                        ?.contact?.address
                                                        ?.line3
                                                }
                                                ,
                                                <br />
                                            </div>
                                        )}
                                        {`
                                        ${modalInfo?.data?.location?.contact?.address?.city}, 
                                        ${modalInfo?.data?.location?.contact?.address?.stateOrProvince}, 
                                        ${modalInfo?.data?.location?.contact?.address?.zipOrPostcode},
                                    `}
                                        <br />
                                        {
                                            modalInfo?.data
                                                ?.merchantCountryDetails
                                                ?.countryName
                                        }
                                    </>
                                }
                            />
                        </div>
                    </>
                );
            default:
                return <></>;
        }
    }, [modalInfo]);

    return (
        <Modal
            className="show-all-modal"
            show={show}
            onHide={onHide}
            size="md"
            centered
        >
            <Modal.Header closeButton>
                <Modal.Title>{modalInfo.title}</Modal.Title>
            </Modal.Header>
            <Modal.Body>{modalBodyData}</Modal.Body>
            <Modal.Footer className="mt-3">
                <Button variant="primary" size="sm" onClick={onHide}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ShowAllData;
