import React, {
    use<PERSON><PERSON>back,
    useContext,
    useMemo,
    useState,
    useEffect,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    IcIcon,
    FormSearch,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faFilter, faFilterSlash, faPlus, faSync } from "FaICIconMap";
import { UserContext } from "Contexts";
import { AccessControlContext } from "Contexts/accessControl/accessControlContext";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    GroupStatus,
    UserBoundaryType,
} from "Data";
import { useToggle } from "Hooks";
import { getGroups } from "Services";
import { getDefaultValuesOfFilters, getQueryFilters, toTitleCase } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import QueryParamFilters from "Components/common/queryParamFilters/QueryParamFilters";
import CreateGroupModal from "./create/CreateGroupModal";
import GroupManageTable from "./groupManageTable/GroupManageTable";

import "./GroupManagement.scss";

const groupFilters = [
    { label: "Region", value: "regionId" },
    // { label: "Merchant", value: "merchantId" }, // TODO: [SHTT-465] Settings -> Access Control -> Groups: Add merchant filter to groups (After only selecting a region)
    { label: "Type", value: "type" },
    { label: "Status", value: "status" },
];

const inputKeys = [
    { key: "select", values: ["regionId", "merchantId", "type", "status"] },
];

const userBoundaryTypes = Object.values(UserBoundaryType)
    .filter((uBT) => uBT !== UserBoundaryType.ROOT)
    .map((fUBT) => ({
        label: toTitleCase(fUBT),
        value: fUBT,
    }));

const groupStatus = Object.values(GroupStatus).map((status) => ({
    label: toTitleCase(status),
    value: status,
}));

const defaultSkip = 1,
    defaultLimit = 25;
let timeout;

const GroupManagement = ({ setIsDisabledTab }) => {
    const { organization, isAuthorizedForAction } = useContext(UserContext);
    const { isLoading: isLoadingGlobalGroups, loadGroups: loadGlobalGroups } =
        useContext(AccessControlContext);
    const [isLoading, setIsLoading] = useState(false);
    const [groupList, setGroupList] = useState([]);
    const [groupCount, setGroupCount] = useState(0);
    const [skip, setSkip] = useState(defaultSkip);
    const [limit, setLimit] = useState(defaultLimit);
    const [searchText, setSearchText] = useState("");
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [appliedFilterRows, setAppliedFilterRows] = useState([]);
    const [appliedFilters, setAppliedFilters] = useState([]);
    const [isReloading, setIsReloading] = useState(false);
    const [showCreateGroup, setShowCreateGroup] = useState(false);

    const enableCreateGroup = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.GROUPS,
                AccessPermissionModules[AccessPermissionModuleNames.GROUPS]
                    .actions.CreateGroup
            ),
        [isAuthorizedForAction]
    );

    const enableArchiveGroup = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.GROUPS,
                AccessPermissionModules[AccessPermissionModuleNames.GROUPS]
                    .actions.DeleteGroup
            ),
        [isAuthorizedForAction]
    );

    const enableEditGroup = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.GROUPS,
                AccessPermissionModules[AccessPermissionModuleNames.GROUPS]
                    .actions.UpdateGroup
            ),
        [isAuthorizedForAction]
    );

    const getQueryParamData = useCallback(
        (arrayToReduce) =>
            arrayToReduce.reduce((result, item) => {
                const filterInput =
                    inputKeys.find((iK) => {
                        if (iK.key === "date-range") {
                            return Object.keys(iK.values).includes(item.value);
                        } else {
                            return iK.values.includes(item.value);
                        }
                    })?.key || "";
                let options = [];
                let labelKey = "label";
                let valueKey = "value";
                let groupBy = "";

                const defaultValues = getDefaultValuesOfFilters(
                    { filterInput, filterKey: item.value },
                    inputKeys
                );

                if (filterInput === "select") {
                    switch (item.value) {
                        case "regionId":
                            options = organization?.regions || [];
                            labelKey = "regionName";
                            valueKey = "_id";
                            break;
                        // TODO: [SHTT-465] Settings -> Access Control -> Groups: Add merchant filter to groups (After only selecting a region)
                        // case "merchantId":
                        //     options = merchants || [];
                        //     labelKey = "merchantName";
                        //     valueKey = "_id";
                        //     groupBy = "regionId";
                        //     break;
                        case "type":
                            options = userBoundaryTypes;
                            break;
                        case "status":
                            options = groupStatus;
                            break;
                        default:
                            break;
                    }
                }

                result[item.value] = {
                    filterInput,
                    key: item.value,
                    id: item.value,
                    name: item.value,
                    valueKey,
                    placeholder: item?.label?.toLowerCase() || "",
                    ...defaultValues,
                    ...(filterInput === "select"
                        ? { options, labelKey, groupBy }
                        : {}),
                };

                return result;
            }, {}),
        [organization?.regions]
    );

    const tabQueryFilterData = useMemo(() => {
        let queryParamFilterOptions = groupFilters;
        let queryParamFilterMetadata = getQueryParamData(groupFilters);

        return { queryParamFilterOptions, queryParamFilterMetadata };
    }, [getQueryParamData]);

    const loadGroupsData = useCallback(
        async ({ limit, skip, searchText = "" }, filters = []) => {
            try {
                let queryObj = {
                    limit,
                    skip: (skip - 1) * limit,
                    searchKey: searchText,
                };

                if (filters.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(filters) };
                }

                setIsLoading(true);
                setIsDisabledTab(true);
                const groupData = await getGroups(queryObj);
                setIsDisabledTab(false);
                setIsLoading(false);
                setGroupList(groupData?.items || []);
                setGroupCount(groupData?.total || 0);
            } catch (e) {
                console.error(e);
                setIsDisabledTab(false);
                setIsLoading(false);
                toast.error(
                    <div>
                        Failed to load groups!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [setIsDisabledTab, setIsLoading, setGroupList, setGroupCount]
    );

    const onSearch = useCallback(
        (search) => {
            if (timeout) {
                clearTimeout(timeout);
            }
            setSearchText(search);
            timeout = setTimeout(async () => {
                await loadGroupsData(
                    {
                        limit,
                        skip: defaultSkip,
                        searchText: search,
                    },
                    appliedFilters
                );
                setSkip(defaultSkip);
            }, 2000);
        },
        [limit, appliedFilters, setSearchText, setSkip, loadGroupsData]
    );

    const reloadAllGroupsDataAndResetPagination = useCallback(async () => {
        await Promise.all([
            loadGlobalGroups(),
            loadGroupsData(
                { limit, skip: defaultSkip, searchText },
                appliedFilters
            ),
        ]);
        setSkip(defaultSkip);
    }, [
        limit,
        appliedFilters,
        searchText,
        loadGlobalGroups,
        setSkip,
        loadGroupsData,
    ]);

    const onSetCreateGroup = useCallback(
        (status = true) => {
            setShowCreateGroup(status);
        },
        [setShowCreateGroup]
    );

    const onHideShowCreateGroup = useCallback(
        (e, data) => {
            if (data) {
                reloadAllGroupsDataAndResetPagination();
            }
            setShowCreateGroup(false);
        },
        [reloadAllGroupsDataAndResetPagination, setShowCreateGroup]
    );

    const onReloadGroups = useCallback(async () => {
        setIsReloading(true);
        await loadGroupsData(
            { limit, skip: defaultSkip, searchText },
            appliedFilters
        );
        setSkip(defaultSkip);
        setIsReloading(false);
    }, [
        limit,
        searchText,
        appliedFilters,
        setSkip,
        loadGroupsData,
        setIsReloading,
    ]);

    useEffect(() => {
        setSkip(defaultSkip);
        loadGroupsData(
            { limit, skip: defaultSkip, searchText },
            appliedFilters
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appliedFilters]);

    return (
        <div className="mt-4 group-view">
            <BaseLayout
                topLeft={
                    <div className="mt-2 d-flex flex-row align-items-center action-btn-container">
                        <div className="d-flex justify-content-start align-items-center mx-2">
                            <div className="search-bar-width">
                                <FormSearch
                                    id="search-groups"
                                    placeholder="Search by group name..."
                                    selected={searchText}
                                    disabled={
                                        isLoadingGlobalGroups || isLoading
                                    }
                                    onChange={onSearch}
                                />
                            </div>
                            <Button
                                className="shadow-none"
                                variant="link"
                                size="sm"
                                disabled={
                                    isLoadingGlobalGroups ||
                                    isLoading ||
                                    isReloading
                                }
                                onClick={onReloadGroups}
                            >
                                {!isReloading && (
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            size="md"
                                            className="mr-2"
                                            icon={faSync}
                                        />
                                        Reload Groups
                                    </div>
                                )}
                            </Button>
                            <div style={{ fontSize: "0.9rem" }}>
                                {isReloading && (
                                    <div className="text-primary">
                                        Reloading...
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                }
                topRight={
                    <div className="d-flex align-items-center">
                        <Button
                            size="sm"
                            variant="primary"
                            disabled={
                                isLoadingGlobalGroups ||
                                isLoading ||
                                !enableCreateGroup
                            }
                            onClick={onSetCreateGroup}
                        >
                            <div className="d-flex align-items-center">
                                <IcIcon
                                    className="mr-2"
                                    size="lg"
                                    icon={faPlus}
                                />
                                Create Group
                            </div>
                        </Button>
                        <Button
                            className="ml-2"
                            variant="outline-primary"
                            size="sm"
                            disabled={isLoading || isReloading}
                            onClick={toggleShowFilters}
                        >
                            <IcIcon
                                className="mr-2"
                                size="lg"
                                icon={showFilters ? faFilterSlash : faFilter}
                            />
                            {showFilters ? "Hide Filters" : "Filter By"}
                        </Button>
                    </div>
                }
                bottom={
                    <div className="mt-3">
                        <div>
                            {!showFilters && appliedFilters.length !== 0 && (
                                <div className="d-flex align-items-center">
                                    <h3 className="mb-0 mr-2">
                                        {appliedFilters.length === 1
                                            ? "A filter is "
                                            : appliedFilters.length +
                                                " filters are "}
                                        applied.
                                    </h3>
                                    <Button
                                        variant="info"
                                        size="sm"
                                        disabled={isLoading || isReloading}
                                        onClick={toggleShowFilters}
                                    >
                                        Show Applied Filters
                                    </Button>
                                </div>
                            )}
                            {showFilters && (
                                <QueryParamFilters
                                    multipleFilters
                                    queryParamFilterMetadata={
                                        tabQueryFilterData.queryParamFilterMetadata
                                    }
                                    queryParamFilterOptions={
                                        tabQueryFilterData.queryParamFilterOptions
                                    }
                                    isLoading={isLoading}
                                    appliedFilters={appliedFilters}
                                    appliedFilterRows={appliedFilterRows}
                                    setAppliedFilters={setAppliedFilters}
                                    setAppliedFilterRows={setAppliedFilterRows}
                                />
                            )}
                            <hr />
                        </div>
                        <div className="d-flex justify-content-start">
                            <div className="w-100">
                                <GroupManageTable
                                    groupList={groupList}
                                    isLoading={
                                        isLoadingGlobalGroups || isLoading
                                    }
                                    totalCount={groupCount}
                                    limit={limit}
                                    skip={skip}
                                    searchText={searchText}
                                    appliedFilters={appliedFilters}
                                    setLimit={setLimit}
                                    setSkip={setSkip}
                                    enableArchiveGroup={enableArchiveGroup}
                                    enableEditGroup={enableEditGroup}
                                    setShowCreateGroup={setShowCreateGroup}
                                    loadGroupsData={loadGroupsData}
                                    reloadAllGroupsDataAndResetPagination={
                                        reloadAllGroupsDataAndResetPagination
                                    }
                                />
                            </div>
                        </div>
                    </div>
                }
            />
            {showCreateGroup && (
                <CreateGroupModal
                    show={showCreateGroup}
                    onHide={onHideShowCreateGroup}
                />
            )}
        </div>
    );
};

GroupManagement.defaultProps = { setIsDisabledTab: () => {} };

GroupManagement.propTypes = { setIsDisabledTab: PropTypes.func };

export default GroupManagement;
