import React, {
    forwardRef,
    useContext,
    useImperative<PERSON><PERSON><PERSON>,
    useMemo,
    useRef,
    useState,
} from "react";
import PropTypes from "prop-types";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { CreateGroupContext } from "./context/CreateGroupContext";

const CreateGroupFirstPage = forwardRef(
    ({ name, description, setName, setDescription }, ref) => {
        const [validated, setValidated] = useState(false);
        const formRef = useRef();
        useImperativeHandle(ref, () => ({
            isValidated() {
                const formValid = formRef.current.checkValidity();
                if (!formValid) {
                    setValidated(true);
                }
                return formValid;
            },
            async onClickNext() {
                return new Promise((resolve, reject) => resolve());
            },
        }));

        return (
            <div ref={ref}>
                <Form ref={formRef} validated={validated} noValidate>
                    <Form.Group>
                        <Form.Label>
                            <span>Name</span>
                            <span className="ml-1 text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            type="text"
                            name="name"
                            value={name}
                            onChange={setName}
                            placeholder="Enter a group name..."
                            required
                        />
                        {validated && !name && (
                            <Form.Text className="text-danger">
                                * Group name cannot be empty!
                            </Form.Text>
                        )}
                    </Form.Group>
                    <Form.Group>
                        <Form.Label>
                            <span>Description</span>
                            <span className="ml-1 text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                            type="text"
                            name="name"
                            value={description}
                            onChange={setDescription}
                            placeholder="Enter a description..."
                            required
                        />
                        {validated && !description && (
                            <Form.Text className="text-danger">
                                * Group description cannot be empty!
                            </Form.Text>
                        )}
                    </Form.Group>
                </Form>
            </div>
        );
    }
);

const CreateGroupFirstPageContainer = (props, ref) => {
    const { name, description, setName, setDescription } =
        useContext(CreateGroupContext);

    return useMemo(
        () => (
            <CreateGroupFirstPage
                ref={ref}
                {...props}
                name={name}
                description={description}
                setName={setName}
                setDescription={setDescription}
            />
        ),
        [name, description, setName, setDescription, props, ref]
    );
};

CreateGroupFirstPage.defaultProps = {
    name: "",
    description: "",
    setName: () => {},
    setDescription: () => {},
};

CreateGroupFirstPage.propTypes = {
    name: PropTypes.string,
    description: PropTypes.string,
    setName: PropTypes.func,
    setDescription: PropTypes.func,
};

export { CreateGroupFirstPage };
export default CreateGroupFirstPageContainer;
