import React, { useContext, useMemo } from "react";
import { useLocation } from "react-router-dom";
import PropTypes from "prop-types";
import { SideNavigationBar } from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faBell,
    faChart,
    faCoins,
    faCreditCard,
    faDollarSignAlt,
    faFileGraph,
    faGift,
    faMedal,
    faRing,
    faUsers,
    faWebhook,
} from "FaICIconMap";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { toReplaceSpaceWithHyphen, toTitleCase } from "Utils";
import NavigationLink from "../../navigation/NavigationLink";

import "./SettingsNavigation.scss";

const SettingsNavigation = ({ isLoading, globalPartnerRewards }) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const location = useLocation();

    const navList = useMemo(() => {
        return [
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.UpdateOrganization
            )
                ? [
                      {
                          tab: "Members",
                          path: "/control-panel/settings/members",
                          icon: faUsers,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.CARD,
                AccessPermissionModules[AccessPermissionModuleNames.CARD]
                    .actions.ListCardConfigurations
            )
                ? [
                      {
                          tab: "Cards",
                          path: "/control-panel/settings/cards",
                          icon: faCreditCard,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.UpdateOrganization
            )
                ? [
                      {
                          tab: "Points",
                          path: "/control-panel/settings/points",
                          icon: faCoins,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.SUB_TRANSACTION_TYPE,
                AccessPermissionModules[
                    AccessPermissionModuleNames.SUB_TRANSACTION_TYPE
                ].actions.ListSubTransactionTypes
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.SUB_TRANSACTION_TYPE,
                AccessPermissionModules[
                    AccessPermissionModuleNames.SUB_TRANSACTION_TYPE
                ].actions.CreateSubTransactionType
            )
                ? [
                      {
                          tab: "Sub Transactions",
                          path: "/control-panel/settings/sub-transactions",
                          icon: faDollarSignAlt,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            ) ||
            isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.UpdateOrganization
            )
                ? [
                      {
                          tab: "Tiers",
                          path: "/control-panel/settings/tiers",
                          icon: faMedal,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.REGION,
                AccessPermissionModules[AccessPermissionModuleNames.REGION]
                    .actions.ListRegions
            ) &&
            isAuthorizedForAction(
                AccessPermissionModuleNames.REGION,
                AccessPermissionModules[AccessPermissionModuleNames.REGION]
                    .actions.UpdateRegion
            )
                ? [
                      {
                          tab: "Affinity Groups",
                          path: "/control-panel/settings/affinity-groups",
                          icon: faMedal,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            )
                ? [
                      {
                          tab: "Notifications",
                          path: "/control-panel/settings/notifications",
                          icon: faBell,
                      },
                  ]
                : []),
            {
                tab: "Organizations",
                path: "/control-panel/settings/organizations",
                icon: faChart,
                disabled: !isAuthorizedForAction(
                    AccessPermissionModuleNames.ORGANIZATION,
                    AccessPermissionModules[
                        AccessPermissionModuleNames.ORGANIZATION
                    ].actions.GetOrganization
                ),
            },
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            )
                ? [
                      {
                          tab: "Portal",
                          path: "/control-panel/settings/portal-configurations",
                          icon: faRing,
                          disabled: false,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            )
                ? [
                      {
                          tab: "Reports",
                          path: "/control-panel/settings/reports",
                          icon: faFileGraph,
                          disabled: false,
                      },
                  ]
                : []),
            ...(isAuthorizedForAction(
                AccessPermissionModuleNames.ORGANIZATION,
                AccessPermissionModules[
                    AccessPermissionModuleNames.ORGANIZATION
                ].actions.GetOrganization
            )
                ? [
                      {
                          tab: "Webhooks",
                          path: "/control-panel/settings/webhook-configurations",
                          icon: faWebhook,
                          disabled: false,
                      },
                  ]
                : []),
        ];
    }, [isAuthorizedForAction]);

    const partnerRewardDynamicNavList = useMemo(
        () =>
            globalPartnerRewards.length !== 0
                ? globalPartnerRewards.map((reward) => ({
                      key: reward,
                      tab: toTitleCase(reward),
                      path: `/control-panel/settings/${toReplaceSpaceWithHyphen(
                          reward
                      )}`,
                      icon: faGift,
                  }))
                : [],
        [globalPartnerRewards]
    );

    return (
        <div className="sidebar-wrapper settings-nav-view">
            <SideNavigationBar className="side-navigation" logo="">
                {navList.map((item) => {
                    if (!item.disabled) {
                        return (
                            <NavigationLink
                                key={item.path}
                                path={item.path}
                                tab={item.tab}
                                icon={item.icon}
                                activePath={location.pathname}
                            />
                        );
                    }
                    return null;
                })}
                <>
                    {isLoading ? (
                        <div className="mt-3 pl-3 ml-3 loading-label">
                            <p>Loading...</p>
                        </div>
                    ) : (
                        partnerRewardDynamicNavList.map((navItem) => {
                            return (
                                <NavigationLink
                                    key={navItem.key}
                                    path={navItem.path}
                                    tab={navItem.tab}
                                    icon={navItem.icon}
                                    activePath={location.pathname}
                                />
                            );
                        })
                    )}
                </>
            </SideNavigationBar>
        </div>
    );
};

SettingsNavigation.defaultProps = {
    isLoading: false,
    globalPartnerRewards: [],
};

SettingsNavigation.propTypes = {
    /**
     * Global partner rewards data loading state
     */
    isLoading: PropTypes.bool.isRequired,
    /**
     * Global partner rewards data
     */
    globalPartnerRewards: PropTypes.array.isRequired,
};

export default SettingsNavigation;
