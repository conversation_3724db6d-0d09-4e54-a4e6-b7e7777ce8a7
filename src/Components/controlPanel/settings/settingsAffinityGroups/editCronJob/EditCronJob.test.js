import React from 'react'
import { create } from "react-test-renderer";
import <PERSON><PERSON>ronJ<PERSON> from "./EditCronJob";
import {MemoryRouter} from "react-router-dom";
import App from "../../../../../App";


describe('EditCronJob component snapshot', () => {
    test('Matches the snapshot', ()=> {
        const loadRegions = jest.fn();
        const region = {
                "_id": "6137a9fff48b8eb1845c78cf",
                "organizationId": "6128e3537ed841e246e2e394",
                "regionName": "Sri Lanka",
                "defaultCountryISO2Code": "LK",
                "defaultCurrencyCode": "BBD",
                "regionIconUrl": "https://assets.loyaltybeta.cxforge.com/images/256c0f00-715d-11ee-9947-7d73c3da521d.jpg",
                "pointConfiguration": {
                    "minPointRedemptionAmount": 10,
                    "maxPointRedemptionAmount": 10000,
                    "minPointsBalanceForRedemption": 100,
                    "pointExpiryMethod": "ROLLING",
                    "pointExpiryStartMonth": 1,
                    "pointExpiryEndMonth": 12,
                    "pointExpiryPeriod": 12,
                    "pointExpiryGracePeriod": 90,
                    "currencyAmountPerPoint": 0.1,
                    "regionalPointConversionRates": [
                        "destinationRegionId: 63d8d4c6b8d1ee1fbf0eb85d,\nrate:10\n"
                    ],
                    "jobEnabled": true,
                    "pointExpiryCalculationSubTransactionTypeIds": [
                        "617ecd5dbadda9ca5f85d0f0",
                        "6195534c423d24e396dc41ec",
                    ],
                    "jobFrequency": "0 1 1 * 1",
                    "pointExpiryCalculationJobFrequency": "0 0 1 * 0",
                    "jobKey": "__default__: 63d8d8f89eba8847d86bbd82::America/Port_of_Spain:0 * 1 * *"
                },
                "rewardConfiguration": {
                    "lastBatchId": 2
                },
                "memberConfiguration": {
                    "maxSecondaryAccounts": 25,
                    "defaultAffinityGroupId": "656eefbb842d2653a802be33",
                    "affinityGroupExpiryJobEnabled": false,
                    "affinityGroupExpiryJobFrequency": "0 * 2 * *",
                    "affinityGroupExpiryJobKey": "__default__:6137a9fff48b8eb1845c78cf::America/Port_of_Spain:0 * 2 * *"
                },
                "notificationConfiguration": {
                    "emailConfiguration": {
                        "fromAddress": "<EMAIL>",
                        "fromName": "Test"
                    },
                    "smsConfiguration": {
                        "alphanumericSenderId": "Test",
                        "phoneNumber": "***********"
                    }
                },
                "defaultMerchantId": "6195caa6b41a2d3f53dd3026",
                "defaultMerchantLocationId": "6195d1dbb41a2d3f53dd3131",
                "createdBy": "613dfc56eac5369848619fe0",
                "supportInfo": {
                    "phoneNumbers": [
                        "0115845236"
                    ],
                    "email": "<EMAIL>",
                    "whatsappNumber": "94584520587"
                },
                "timeZone": "America/Port_of_Spain",
                "createdOn": "2023-01-20T06:07:49.008Z",
                "updatedOn": "2024-01-03T06:33:26.745Z",
                "__v": 0,
                "updatedBy": "616680f35958bc8372a1ee88",
                "tierConfiguration": {
                    "tierStatRefreshJob": true,
                    "tierStatRefreshJobFrequency": "0 0 * * *",
                    "jobKey": "__default__:6137a9fff48b8eb1845c78cf::America/Port_of_Spain:0 0 * * *"
                },
                "insightCalculations": {
                    "avgCustomerLifeSpan": 0.054649383449759774,
                    "avgFrequencyRate": 3.62426614481409,
                    "jobEnabled": false
                },
                "affinityGroupConfiguration": {
                    "jobEnabled": false,
                    "jobFrequency": "0 0 * * *"
                }
            }
        ;
        const component = create( <MemoryRouter><App><EditCronJob region={region} loadRegions={loadRegions}/></App></MemoryRouter>);
        expect(component.toJSON()).toMatchSnapshot();

    });
});