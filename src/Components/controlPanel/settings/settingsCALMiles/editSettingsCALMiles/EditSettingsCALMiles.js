import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { toast } from "react-toastify";
import {
    Button,
    Card,
    Col,
    IcIcon,
    Form,
    Row,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes } from "FaICIconMap";
import { DataContext } from "Contexts";
import { updateRewardById } from "Services";
import { areArraysEquals, removeKeysFromObject, toTitleCase } from "Utils";

import "./EditSettingsCALMiles.scss";

const EditSettingsCALMiles = ({ show, cALMileState, onClose }) => {
    const { loadPartnerRewards } = useContext(DataContext);
    const [isUpdating, setIsUpdating] = useState(false);
    const [partnerUoM, setPartnerUoM] = useState("");
    const [pointsBundles, setPointsBundles] = useState([]);
    const [contactNumbers, setContactNumbers] = useState([]);
    const [updatedContactNumbers, setUpdatedContactNumbers] = useState([]);
    const [isValidContactNumbers, setIsValidContactNumbers] = useState([]);
    const [validated, setValidated] = useState(false);

    const isPointsBundlesEqual = useMemo(
        () => areArraysEquals(cALMileState?.pointsBundles, pointsBundles, true),
        [cALMileState?.pointsBundles, pointsBundles]
    );

    const isContactNumbersEqual = useMemo(
        () =>
            areArraysEquals(
                cALMileState?.partnerRewardMetadata?.partnerContactNumber,
                updatedContactNumbers
            ),
        [
            cALMileState?.partnerRewardMetadata?.partnerContactNumber,
            updatedContactNumbers,
        ]
    );

    const onChangePartnerAttrs = useCallback(
        (e) => {
            e.persist();
            switch (e.target.id) {
                case "partnerUoM": {
                    setPartnerUoM(e.target.value);
                    break;
                }
                case "bundle":
                    setPointsBundles((prevBundles) => {
                        return prevBundles.map((prevBundle, i) =>
                            i === Number(e.target.name)
                                ? { ...prevBundle, bundleName: e.target.value }
                                : prevBundle
                        );
                    });
                    break;
                case "value":
                    setPointsBundles((prevBundles) => {
                        return prevBundles.map((prevBundle, i) =>
                            i === Number(e.target.name)
                                ? { ...prevBundle, bundleValue: e.target.value }
                                : prevBundle
                        );
                    });
                    break;
                case "points":
                    setPointsBundles((prevBundles) => {
                        return prevBundles.map((prevBundle, i) =>
                            i === Number(e.target.name)
                                ? { ...prevBundle, points: e.target.value }
                                : prevBundle
                        );
                    });
                    break;
                default:
                    break;
            }
        },
        [setPartnerUoM, setPointsBundles]
    );

    const onChangeContactNumber = useCallback(
        (status, value, countryData, number, formattedNumber, contactIndex) => {
            const updatedContacts = contactNumbers.map((contact, i) =>
                i === Number(contactIndex) ? formattedNumber : contact
            );
            setUpdatedContactNumbers(updatedContacts);
            setIsValidContactNumbers((prevValidContcts) =>
                prevValidContcts.map((prevValidContact, i) =>
                    i === Number(contactIndex) ? status : prevValidContact
                )
            );
        },
        [contactNumbers, setUpdatedContactNumbers, setIsValidContactNumbers]
    );

    const addNewCALMile = useCallback(
        () =>
            setPointsBundles([
                ...pointsBundles,
                { bundleName: "", bundleValue: "", points: "" },
            ]),
        [pointsBundles, setPointsBundles]
    );

    const addContactNo = useCallback(() => {
        setContactNumbers([...contactNumbers, ""]);
        setUpdatedContactNumbers([...updatedContactNumbers, ""]);
        setIsValidContactNumbers([...isValidContactNumbers, false]);
    }, [
        contactNumbers,
        updatedContactNumbers,
        isValidContactNumbers,
        setContactNumbers,
        setUpdatedContactNumbers,
        setIsValidContactNumbers,
    ]);

    const onRemoveCALMile = useCallback(
        (event) => {
            event.preventDefault();
            setPointsBundles(
                pointsBundles.filter(
                    (_, i) => i !== Number(event.currentTarget.id)
                )
            );
        },
        [pointsBundles, setPointsBundles]
    );

    const onRemoveContactNo = useCallback(
        (event) => {
            event.preventDefault();
            setContactNumbers(
                updatedContactNumbers.filter(
                    (_, i) => i !== Number(event.currentTarget.id)
                )
            );
            setUpdatedContactNumbers(
                updatedContactNumbers.filter(
                    (_, i) => i !== Number(event.currentTarget.id)
                )
            );
            setIsValidContactNumbers(
                isValidContactNumbers.filter(
                    (_, i) => i !== Number(event.currentTarget.id)
                )
            );
        },
        [
            updatedContactNumbers,
            isValidContactNumbers,
            setContactNumbers,
            setUpdatedContactNumbers,
            setIsValidContactNumbers,
        ]
    );

    const onSubmit = useCallback(
        async (e) => {
            e.preventDefault();

            if (
                e.target.checkValidity() &&
                isValidContactNumbers.every((item) => item === true)
            ) {
                try {
                    const payload = {
                        ...(!isPointsBundlesEqual
                            ? {
                                pointsBundles: pointsBundles.map((point) =>
                                    removeKeysFromObject(point, ["_id"])
                                ),
                            }
                            : {}),
                        ...(cALMileState?.partnerRewardMetadata
                            ?.partnerBundleUnitOfMeasure !== partnerUoM ||
                        !isContactNumbersEqual
                            ? {
                                subType: cALMileState?.subType,
                                ...(!isContactNumbersEqual
                                    ? {
                                        "partnerRewardMetadata.partnerContactNumber":
                                            updatedContactNumbers,
                                    }
                                    : {}),
                                ...(!cALMileState?.partnerRewardMetadata
                                    ?.partnerBundleUnitOfMeasure !==
                                partnerUoM
                                    ? {
                                        "partnerRewardMetadata.partnerBundleUnitOfMeasure":
                                            partnerUoM,
                                    }
                                    : {}),
                            }
                            : {}),
                    };

                    setIsUpdating(true);
                    const updatedPartnerRewardRes = await updateRewardById(
                        cALMileState?.id,
                        payload
                    );
                    loadPartnerRewards(cALMileState?.regionId);
                    setIsUpdating(false);
                    onClose(null, updatedPartnerRewardRes || {});
                    toast.success(
                        `Successfully updated settings${
                            cALMileState?.regionName
                                ? " of " + cALMileState.regionName
                                : ""
                        }.`
                    );
                } catch (e) {
                    setIsUpdating(false);
                    toast.error(
                        <div>
                            {`Failed to update settings${
                                cALMileState?.regionName
                                    ? " of " + cALMileState.regionName
                                    : ""
                            }!`}
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            isValidContactNumbers,
            isPointsBundlesEqual,
            pointsBundles,
            cALMileState?.id,
            cALMileState.regionId,
            cALMileState.regionName,
            cALMileState?.subType,
            cALMileState?.partnerRewardMetadata?.partnerBundleUnitOfMeasure,
            partnerUoM,
            isContactNumbersEqual,
            updatedContactNumbers,
            loadPartnerRewards,
            onClose,
            setValidated,
            setIsUpdating,
        ]
    );

    useEffect(() => {
        const validatePartnerContactNumbers = Array.isArray(
            cALMileState?.partnerRewardMetadata?.partnerContactNumber
        )
            ? cALMileState.partnerRewardMetadata.partnerContactNumber
            : [];

        setPartnerUoM(
            cALMileState?.partnerRewardMetadata?.partnerBundleUnitOfMeasure ||
                ""
        );
        setPointsBundles(
            Array.isArray(cALMileState?.pointsBundles)
                ? cALMileState.pointsBundles
                : []
        );
        setContactNumbers(
            validatePartnerContactNumbers.length !== 0
                ? validatePartnerContactNumbers
                : [""]
        );
        setUpdatedContactNumbers(
            validatePartnerContactNumbers.length !== 0
                ? validatePartnerContactNumbers
                : [""]
        );
        setIsValidContactNumbers(
            validatePartnerContactNumbers.length !== 0
                ? validatePartnerContactNumbers.map((_) => true)
                : [false]
        );

        return () => {
            setPartnerUoM("");
            setPointsBundles([]);
            setContactNumbers([]);
            setUpdatedContactNumbers([]);
            setIsValidContactNumbers([]);
        };
    }, [
        cALMileState.partnerRewardMetadata?.partnerBundleUnitOfMeasure,
        cALMileState.partnerRewardMetadata.partnerContactNumber,
        cALMileState.pointsBundles,
    ]);

    return (
        <Modal
            show={show}
            onHide={
                isUpdating
                    ? () => {
                        /* Placeholder for empty arrow fuction */
                    }
                    : onClose
            }
            className="create-user border-0"
            size="lg"
            centered
        >
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>{`Edit ${toTitleCase(
                    cALMileState?.name || "Partner Reward"
                )}${
                    cALMileState?.regionName
                        ? " of " + cALMileState.regionName
                        : ""
                }`}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="edit-cal-miles-view mt-3">
                    <Form onSubmit={onSubmit} validated={validated} noValidate>
                        <Form.Group className="w-100">
                            <Form.Label className="d-flex align-items-center">
                                {`${toTitleCase(
                                    cALMileState?.name || "Partner Reward"
                                )} Unit of Measure`}
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Control
                                id="partnerUoM"
                                type="text"
                                placeholder={`Enter ${
                                    cALMileState?.name || "partner reward"
                                } unit of measure`}
                                value={partnerUoM}
                                disabled={isUpdating}
                                onChange={onChangePartnerAttrs}
                                required
                            />
                        </Form.Group>
                        <Form.Group>
                            <Form.Label className="d-flex align-items-center">
                                Points Bundles
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <div className="scrollable-view rounded px-3">
                                {pointsBundles.length > 0 ? (
                                    pointsBundles.map((points, index) => (
                                        <div key={`cal-mile-state-${index}`}>
                                            <Card className="my-2 cal-miles-card">
                                                <Card.Body>
                                                    <div className="text-right">
                                                        <Button
                                                            className="p-0 m-0 btn shadow-none text-danger"
                                                            variant="link"
                                                            size="sm"
                                                            disabled={
                                                                isUpdating
                                                            }
                                                            onClick={
                                                                onRemoveCALMile
                                                            }
                                                            id={index}
                                                        >
                                                            <IcIcon
                                                                size="lg"
                                                                icon={faTimes}
                                                            />
                                                        </Button>
                                                    </div>
                                                    <div className="d-flex justify-content-center w-100">
                                                        <Form.Group className="w-100">
                                                            <Form.Label className="d-flex align-items-center">
                                                                Bundle Name
                                                                <div className="ml-1 text-danger">
                                                                    *
                                                                </div>
                                                            </Form.Label>
                                                            <Form.Control
                                                                id="bundle"
                                                                type="text"
                                                                placeholder="Enter points bundle name"
                                                                value={
                                                                    points.bundleName
                                                                }
                                                                name={index}
                                                                disabled={
                                                                    isUpdating
                                                                }
                                                                onChange={
                                                                    onChangePartnerAttrs
                                                                }
                                                                required
                                                            />
                                                        </Form.Group>
                                                    </div>
                                                    <div className="d-flex justify-content-around align-items-center w-100">
                                                        <Form.Group className="w-50">
                                                            <Form.Label className="d-flex align-items-center">
                                                                Points
                                                                <div className="ml-1 text-danger">
                                                                    *
                                                                </div>
                                                            </Form.Label>
                                                            <Form.Control
                                                                id="points"
                                                                type="number"
                                                                placeholder="Enter points amount"
                                                                value={
                                                                    points.points
                                                                }
                                                                name={index}
                                                                required
                                                                disabled={
                                                                    isUpdating
                                                                }
                                                                onChange={
                                                                    onChangePartnerAttrs
                                                                }
                                                            />
                                                        </Form.Group>
                                                        <div className="mx-3">
                                                            =
                                                        </div>
                                                        <Form.Group className="w-50">
                                                            <Form.Label className="d-flex align-items-center">
                                                                {toTitleCase(
                                                                    partnerUoM ||
                                                                        "Bundle Value"
                                                                )}
                                                                <div className="ml-1 text-danger">
                                                                    *
                                                                </div>
                                                            </Form.Label>
                                                            <Form.Control
                                                                id="value"
                                                                type="number"
                                                                placeholder={`Enter ${
                                                                    partnerUoM?.toLowerCase() ||
                                                                    "bundle value"
                                                                } amount`}
                                                                value={
                                                                    points.bundleValue
                                                                }
                                                                name={index}
                                                                disabled={
                                                                    isUpdating
                                                                }
                                                                onChange={
                                                                    onChangePartnerAttrs
                                                                }
                                                                required
                                                            />
                                                        </Form.Group>
                                                    </div>
                                                </Card.Body>
                                            </Card>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center p-2">
                                        <h3 className="m-0 p-0">
                                            No Points Bundles Found.
                                        </h3>
                                    </div>
                                )}
                            </div>
                            <div>
                                <Button
                                    className="btn shadow-none"
                                    size="sm"
                                    variant="link"
                                    disabled={isUpdating}
                                    onClick={addNewCALMile}
                                >
                                    + Add Points Bundle
                                </Button>
                            </div>
                        </Form.Group>
                        <Form.Group>
                            <Form.Label className="d-flex align-items-center">
                                {`${toTitleCase(
                                    cALMileState?.name || "Partner Reward"
                                )} Contact ${
                                    contactNumbers.length === 1
                                        ? "Number"
                                        : "Numbers"
                                }`}
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            {contactNumbers.map((contactNo, index) => (
                                <Row
                                    key={`${index}-${contactNo}`}
                                    className="mb-2 text-center"
                                >
                                    <Col
                                        xs={10}
                                        md={10}
                                        lg={10}
                                        xl={10}
                                        className="mr-0 pr-0"
                                    >
                                        <div className="d-flex flex-column">
                                            <Form.MobileNumberInput
                                                defaultCountry={
                                                    cALMileState?.defaultCountryISO2Code?.toLowerCase() ||
                                                    ""
                                                }
                                                onPhoneNumberBlur={(
                                                    status,
                                                    value,
                                                    countryData,
                                                    number,
                                                    formattedNumber
                                                ) =>
                                                    onChangeContactNumber(
                                                        status,
                                                        value,
                                                        countryData,
                                                        number,
                                                        formattedNumber,
                                                        index
                                                    )
                                                }
                                                onPhoneNumberChange={(
                                                    status,
                                                    value,
                                                    countryData,
                                                    number,
                                                    formattedNumber
                                                ) =>
                                                    onChangeContactNumber(
                                                        status,
                                                        value,
                                                        countryData,
                                                        number,
                                                        formattedNumber,
                                                        index
                                                    )
                                                }
                                                format={true}
                                                formatOnInit={false}
                                                defaultValue={contactNo}
                                                validated={validated}
                                                disabled={isUpdating}
                                            />
                                        </div>
                                    </Col>
                                    {contactNumbers.length > 1 && (
                                        <Col
                                            xs={2}
                                            md={2}
                                            lg={2}
                                            xl={2}
                                            className="d-flex align-items-center mr-0 pr-0"
                                        >
                                            <Button
                                                className="p-0 m-0 btn shadow-none text-danger"
                                                id={index}
                                                variant="link"
                                                size="sm"
                                                data-index={index}
                                                disabled={isUpdating}
                                                onClick={onRemoveContactNo}
                                            >
                                                <IcIcon
                                                    size="lg"
                                                    icon={faTimes}
                                                />
                                            </Button>
                                        </Col>
                                    )}
                                </Row>
                            ))}
                            {contactNumbers.length < 3 ? (
                                <div>
                                    <Button
                                        className="btn shadow-none"
                                        size="sm"
                                        variant="link"
                                        disabled={isUpdating}
                                        onClick={addContactNo}
                                    >
                                        + Add Contact Number
                                    </Button>
                                </div>
                            ) : null}
                        </Form.Group>
                        <div className="text-right mt-4">
                            <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={onClose}
                                type="button"
                                disabled={isUpdating}
                                className="mr-2"
                            >
                                Cancel
                            </Button>
                            <Button
                                variant="primary"
                                size="sm"
                                type="submit"
                                disabled={
                                    isUpdating ||
                                    (cALMileState?.partnerRewardMetadata
                                        ?.partnerBundleUnitOfMeasure ===
                                        partnerUoM &&
                                        isPointsBundlesEqual &&
                                        isContactNumbersEqual)
                                }
                            >
                                {isUpdating ? "Updating..." : "Update Details"}
                            </Button>
                        </div>
                    </Form>
                </div>
            </Modal.Body>
        </Modal>
    );
};

export default EditSettingsCALMiles;
