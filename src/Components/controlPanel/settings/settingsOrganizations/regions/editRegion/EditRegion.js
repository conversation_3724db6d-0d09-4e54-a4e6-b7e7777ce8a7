import React, { useState, useCallback, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  IcIcon,
  FileUploader,
  Form,
  Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTrash } from "FaICIconMap";
import { uploadImage, editRegion, editOrganizationDetails } from "Services";
import { toast } from "react-toastify";
import { isEqualObjects } from "Utils";

const EditRegion = ({
  onClose,
  selectedRegion,
  refreshEditedItem,
  defaultRegionId,
}) => {
  const [isSave, setIsSave] = useState(false);
  const [selectedRegionEdit, setSelectedRegionEdit] = useState({});
  const [validated, setValidated] = useState(false);
  const [fileUploadVisible, setFileUploadVisible] = useState(false);
  const [isDefaultRegion, setIsDefaultRegion] = useState(false);

  useEffect(() => {
    if (selectedRegion) {
      setSelectedRegionEdit(selectedRegion);

      setIsDefaultRegion(
        defaultRegionId && selectedRegion._id === defaultRegionId
      );
    }
  }, [selectedRegion, defaultRegionId]);

  const onChange = useCallback(
    (e) => {
      setSelectedRegionEdit({
        ...selectedRegionEdit,
        [e.target.name]: e.target.value,
      });
    },
    [setSelectedRegionEdit, selectedRegionEdit]
  );

  const onSetDefaultRegion = useCallback(
    (e) => {
      setIsDefaultRegion(!isDefaultRegion);
    },
    [setIsDefaultRegion, isDefaultRegion]
  );

  const onChangeImage = useCallback(
    async (regionIconUrl) => {
      try {
        setIsSave(true);
        if (regionIconUrl[0]) {
          const imageIntoUrl = await uploadImage(regionIconUrl[0]);
          setSelectedRegionEdit({
            ...selectedRegionEdit,
            regionIconUrl: imageIntoUrl.url,
          });
        }
        setIsSave(false);
      } catch (e) {
        setIsSave(false);
        toast.error("image upload failed");
      }
    },
    [setSelectedRegionEdit, selectedRegionEdit]
  );

  const onSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (e.target.checkValidity()) {
        try {
          setIsSave(true);
          if (!isEqualObjects(selectedRegion, selectedRegionEdit)) {
            await editRegion({
              _id: selectedRegionEdit._id,
              regionName: selectedRegionEdit.regionName,
              defaultCountryISO2Code: selectedRegionEdit.defaultCountryISO2Code,
              defaultCurrencyCode: selectedRegionEdit.defaultCurrencyCode,
              regionIconUrl: selectedRegionEdit.regionIconUrl,
            });
          }
          try {
            if (isDefaultRegion && selectedRegionEdit._id !== defaultRegionId) {
              await editOrganizationDetails({
                configuration: { baseRegionId: selectedRegionEdit._id },
              });
            }
          } catch (error) {
            toast.error(
              error.message ||
                "Couldn't set region as the default region. Please try again"
            );
          }

          onClose();
          refreshEditedItem();
          toast.success("Successfully updated regional information");
        } catch (e) {
          setIsSave(false);
          onClose();
          toast.error("Region information failed to update");
        }
      } else {
        setValidated(true);
      }
    },
    [
      selectedRegionEdit,
      setValidated,
      setIsSave,
      refreshEditedItem,
      onClose,
      isDefaultRegion,
      defaultRegionId,
      selectedRegion
    ]
  );

  const updateImage = useCallback(() => {
    setSelectedRegionEdit({ ...selectedRegionEdit, regionIconUrl: "" });
    setFileUploadVisible(true);
  }, [setSelectedRegionEdit, setFileUploadVisible, selectedRegionEdit]);

  return (
    <Form onSubmit={onSubmit} validated={validated} noValidate>
      <Form.Group>
        <Form.Label className="d-flex align-items-center">
          Region Name
          <div className="ml-1 text-danger">*</div>
        </Form.Label>
        <Form.Control
          type="text"
          placeholder="Region Name"
          value={selectedRegionEdit.regionName}
          name="regionName"
          onChange={onChange}
          required
        />
      </Form.Group>
      <Form.Group>
        <Form.Label>Region Logo</Form.Label>
        <Row>
          <Col sm={7}>
            <Card>
              <Card.Header>
                <img
                  style={{ width: "20px" }}
                  src={selectedRegionEdit.regionIconUrl}
                  className="organizations-logo-width"
                  alt="no"
                />
                <span className="ml-2">image.png</span>
              </Card.Header>
            </Card>
          </Col>
          <Col>
            <div className="float-right">
              <Button
                onClick={updateImage}
                variant="outline-danger"
                name="ARCHIVE_TIERS"
              >
                <IcIcon className="mr-2" icon={faTrash} />
                Delete
              </Button>
            </div>
          </Col>
        </Row>
        <br />
        {fileUploadVisible ? (
          <FileUploader onChange={onChangeImage} multiple={false} />
        ) : null}
      </Form.Group>
      <Form.Group>
        <Form.Label className="d-flex align-items-center">
          Country ISO2 Code
          <div className="ml-1 text-danger">*</div>
        </Form.Label>
        <Form.Control
          type="text"
          placeholder=" ISO2 Code"
          value={selectedRegionEdit.defaultCountryISO2Code}
          name="defaultCountryISO2Code"
          onChange={onChange}
          required
        />
      </Form.Group>

      <Form.Group>
        <Form.Check
          id="defaultRegion"
          label="Set as default region"
          type="switch"
          name="defaultRegion"
          onChange={onSetDefaultRegion}
          checked={isDefaultRegion}
          disabled={selectedRegion._id === defaultRegionId}
        />
      </Form.Group>

      <div className="text-right mt-4">
        <Button
          variant="outline-primary"
          onClick={onClose}
          type="button"
          disabled={isSave}
          className="mr-2"
        >
          Close
        </Button>

        <Button
          variant="primary"
          type="submit"
          disabled={
            isSave ||
            (isEqualObjects(selectedRegion, selectedRegionEdit) &&
              ((!isDefaultRegion && defaultRegionId !== selectedRegion._id) ||
                selectedRegion._id === defaultRegionId))
          }
        >
          {isSave ? "Saving..." : "Update"}
        </Button>
      </div>
    </Form>
  );
};

export default EditRegion;
