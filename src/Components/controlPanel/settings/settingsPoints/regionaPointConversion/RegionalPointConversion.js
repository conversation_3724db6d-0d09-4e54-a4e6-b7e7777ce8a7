import React, {
  useCallback,
  useContext,
  useState,
} from "react";
import {
  Button,
  Col,
  IcIcon,
  Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faPen } from "FaICIconMap";
import RegionalPointConversionCard from "./regionalPointConversionCard/RegionalPointConversionCard";


import PopUpWindow from "../../../shared/popUpWindow/PopUpWindow";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "../../../../../Data";
import AnalyticsAccessControl from "../../../../analyticsView/AnalyticsAccessControl";
const RegionalPointConversion = () => {

  const [show, setShow] = useState(false);
  const [popUpState, setPopUpState] = useState({});
  const { organization, loadOrganization } = useContext(UserContext);

  const onEditRegionsPointClick = useCallback(
    (event) => {
      event.stopPropagation();
      setPopUpState({
        modalHeaderName: "Edit Regional Point Conversion Rates",
        renderingComponentName: "REGIONAL_POINT",
        modalFooterVisibility: false,
        regions:organization.regions,
        baseRegionId:organization.configurations?.baseRegionId
      });
      setShow(true);
    },
    [setPopUpState, setShow, organization.regions,organization.configurations]
  );


  const handleClose = useCallback(() => {
    setShow(false);
  }, [setShow]);


  return (
    <>
      <Row>
          <AnalyticsAccessControl
              moduleName={AccessPermissionModuleNames.ORGANIZATION}
              renderEmpty={true}
              actionNames={[
                  AccessPermissionModules[AccessPermissionModuleNames.ORGANIZATION].actions.UpdateOrganization,
              ]}
              Logic={"OR"}
          >
              <Col>
                  <div className="float-right">
                      <Button variant="primary" size="sm" onClick={onEditRegionsPointClick}>
                          <IcIcon className="mr-2" icon={faPen} />
                          Edit
                      </Button>
                  </div>
              </Col>
          </AnalyticsAccessControl>
      </Row>
      <Row>
        { organization.regions?.map((region, index) => {
              return (
                <>
                  {region?.pointConfiguration?.regionalPointConversionRates.map(
                    ({_id,destinationRegionId,rate}) =>{
                      const otherRegion=organization.regions.find((item)=>item._id===destinationRegionId);
                      return (
                        <RegionalPointConversionCard
                          key={`${_id}-${index}`}
                          regionName={region.regionName}
                          regionIconUrl={region.regionIconUrl}
                          rate={rate}
                          otherRegionName={
                            otherRegion?.regionName
                          }
                          otherRegionIconUrl={
                            otherRegion?.regionIconUrl
                          }
                        />
                      )
                    }
                  )}
                </>
              );
            })
         }
      </Row>
      {show ? (
        <PopUpWindow
          handleClose={handleClose}
          show={show}
          popUpState={popUpState}
          refreshEditedItem={loadOrganization}
        />
      ) : null}
    </>
  );
};

export default RegionalPointConversion;
