import React, { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import {
  Button,
  Form,
  DropdownButton,
  DropdownItem,
  Row,
  Col,
  Card,
} from "@shoutout-labs/shoutout-themes-enterprise";

import "./RegionalPointConversionEdit.scss";
import { isEmptyObject, removeKeysFromObject } from "Utils";
import { updateRegion } from "Services";
let selectedRegionalPointCopy = {};
const RegionalPointConversionEdit = ({
  onClose,
  refreshEditedItem,
  regions,
  baseRegionId,
}) => {
  const [isSave, setIsSave] = useState(false);

  const [selectedRegion, setSelectedRegion] = useState({});
  const [validated, setValidated] = useState(false);

  const [selectedPointConversion, setSelectedPointConversion] = useState({});

  const nonSelectedRegions = useMemo(() => {
    if (selectedRegion._id) {
      return regions.filter((item) => item._id !== selectedRegion._id);
    }
    return regions;
  }, [selectedRegion, regions]);

  useEffect(() => {
    if (selectedRegion._id) {
      setSelectedPointConversion(
        selectedRegion.pointConfiguration?.regionalPointConversionRates?.reduce(
          (result,{ destinationRegionId, rate }) => {
            result[destinationRegionId] = rate;
            return result;
          },
          {}
        ) || {}
      );
    } else {
      setSelectedPointConversion({});
    }
  }, [selectedRegion]);

  useEffect(() => {
    if (regions.length > 0) {
      if (baseRegionId) {
        setSelectedRegion({
          ...(regions.find((item) => item._id === baseRegionId) || {}),
        });
      } else {
        setSelectedRegion({ ...regions[0] });
      }
    }
  }, [regions, baseRegionId]);
  const onSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (e.target.checkValidity()) {
        try {
          setIsSave(true);

          const updateResponse = await updateRegion(
            selectedRegion._id,
            removeKeysFromObject(
              {
                pointConfiguration: {
                  ...selectedRegion.pointConfiguration,
                  regionalPointConversionRates: Object.entries(
                    selectedPointConversion
                  ).map(([key, value]) => ({
                    destinationRegionId: key,
                    rate: Number(value),
                  })),
                },
              },
              [
                "_id",
                "organizationId",
                "createdBy",
                "createdOn",
                "updatedOn",
                "__v",
                "updatedBy",
              ]
            )
          ); // [MLS-1126] TODO: Remove this checking. Temporary added due to backend not support for partial updates at the moment
          refreshEditedItem();
          onClose(updateResponse);

          toast.success("Successfully Edit Regional Point Conversion Rates");
        } catch (e) {
          setIsSave(false);
          toast.error(" Regional Point Conversion Rates  failed to Edit");
        }
      } else {
        setValidated(true);
      }
    },
    [
      setValidated,
      setIsSave,
      onClose,
      refreshEditedItem,
      selectedPointConversion,
      selectedRegion,
    ]
  );

  const onSelectRegion = useCallback(
    (regionIndex) => {
      const regionSelected = regions[Number(regionIndex)];

      setSelectedRegion({ ...regionSelected });
    },
    [setSelectedRegion, regions]
  );

  const onChangeRate = useCallback(
    (e) => {
      const destinationId = e.target.name;
      const value = e.target.value;

      setSelectedPointConversion({
        ...selectedPointConversion,
        [destinationId]: value,
      });
    },
    [setSelectedPointConversion, selectedPointConversion]
  );

  console.debug(selectedPointConversion);
  return (
    <Form
      onSubmit={onSubmit}
      validated={validated}
      className="reward-logistic-edit"
      noValidate
    >
      <Row>
        <Col>
          <Form.Group>
            <Form.Label>Source Territory</Form.Label>
            <DropdownButton
              title={
                !isEmptyObject(selectedRegion) && (
                  <div>
                    {selectedRegion.regionIconUrl && (
                      <img
                        src={selectedRegion.regionIconUrl}
                        className="img-width-main mr-2"
                        alt={selectedRegion.regionName}
                      />
                    )}
                    <span className="mr-1">{selectedRegion.regionName}</span>
                  </div>
                )
              }
              size="sm"
              variant=""
              className="btn-custom"
              onSelect={onSelectRegion}
              disabled={!!baseRegionId}
            >
              {regions.map(({ _id: id, regionName, regionIconUrl }, index) => {
                return (
                  <DropdownItem eventKey={index} key={id}>
                    {regionIconUrl && (
                      <img
                        src={regionIconUrl}
                        className="img-width mr-2"
                        alt={regionName}
                      />
                    )}
                    {regionName}
                  </DropdownItem>
                );
              })}
            </DropdownButton>
          </Form.Group>
        </Col>
      </Row>

      {!isEmptyObject(selectedRegion) &&
        nonSelectedRegions.map(({ _id, regionName, regionIconUrl }) => {
          return (
            <Card key={`n-${_id}`} className="mb-3">
              <Card.Body>
                <Row>
                  <Col>
                    <div className="d-flex flex-row align-items-center">
                      <img
                        src={selectedRegion.regionIconUrl}
                        height="12"
                        alt={regionName}
                      />

                      <small className="ml-2 ">
                        {selectedRegion.regionName}
                      </small>
                    </div>
                  </Col>
                  <Col sm={1}/>
                  <Col>
                    <div className="d-flex flex-row align-items-center">
                      <img src={regionIconUrl} height="12" alt="no" />
                      <small className="ml-2 ">{regionName}</small>
                    </div>
                  </Col>
                </Row>

                <Row className="mt-2">
                  <Col>
                    <Form.Group className="mb-0">
                      <Form.Control
                        type="text"
                        value="1 Points"
                        disabled
                        size="sm"
                      />
                    </Form.Group>
                  </Col>
                  <Col sm={1} className="d-flex align-items-center">
                    =
                  </Col>
                  <Col>
                    <Form.Group className="mb-0">
                      <Form.Control
                        type="number"
                        value={selectedPointConversion[_id] || 0}
                        name={_id}
                        required
                        onChange={onChangeRate}
                        size="sm"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          );
        })}

      <div className="text-right mt-4">
        <Button
          variant="outline-primary"
          onClick={onClose}
          type="button"
          disabled={isSave}
          className="mr-2"
          size='sm'
        >
          Cancel
        </Button>
        {JSON.stringify(selectedRegionalPointCopy) ===
        JSON.stringify(selectedRegion) ? (
          <Button variant="primary" size='sm' type="submit" disabled={true}>
            Update Details
          </Button>
        ) : (
          <Button variant="primary" size='sm' type="submit" disabled={isSave}>
            {isSave ? "Saving..." : "Update Details"}
          </Button>
        )}
      </div>
    </Form>
  );
};

export default RegionalPointConversionEdit;
