import React, { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    Card,
    IcIcon,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { getAllSubTransactionTypes } from "Services";
import { toTitleCase } from "Utils";
import { LoadingComponent } from "Components/utils";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

import "./ShowExpirySubTransactions.scss";

const ShowExpirySubTransactions = ({
    show,
    regionName,
    regionPointsExpirySubTransactionIds,
    onHide,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [allSubTransactions, setAllSubTransactions] = useState([]);
    const [isReloading, setIsReloading] = useState(false);

    const regionPointsExpirySubTransactions = useMemo(
        () =>
            allSubTransactions.filter((sT) =>
                regionPointsExpirySubTransactionIds.includes(sT?._id)
            ) || [],
        [allSubTransactions, regionPointsExpirySubTransactionIds]
    );

    const loadAllSubTransactions = useCallback(async () => {
        try {
            setIsLoading(true);
            const allSubTransactionsResponse = await getAllSubTransactionTypes({
                withSystemSubTransactionTypes: true,
            });
            setAllSubTransactions(allSubTransactionsResponse);
            setIsLoading(false);
        } catch (e) {
            setIsLoading(false);
            toast.error(
                <div>
                    Failed to load all sub transactions!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [setIsLoading, setAllSubTransactions]);

    const onReload = useCallback(async () => {
        setIsReloading(true);
        await loadAllSubTransactions();
        setIsReloading(false);
    }, [loadAllSubTransactions, setIsReloading]);

    useEffect(() => {
        loadAllSubTransactions();

        return () => {
            setIsLoading(false);
            setAllSubTransactions([]);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Modal
            className="expiry-sub-transaction-modal"
            show={show}
            onHide={isLoading ? () => {} : onHide}
            size="lg"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isLoading}>
                <Modal.Title>
                    {`Points Expiry Sub Transactions for ${
                        regionName || "~ unknown"
                    }`}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="text-center my-2">
                    {isReloading ? (
                        <small className="ml-3 text-primary">
                            Reloading...
                        </small>
                    ) : (
                        <Button
                            className="shadow-none"
                            size="sm"
                            variant="link"
                            disabled={isLoading || isReloading}
                            onClick={onReload}
                        >
                            <IcIcon size="md" className="mr-2" icon={faSync} />
                            Reload Sub Transactions
                        </Button>
                    )}
                </div>
                <Card className="expiry-sub-transaction mb-3">
                    {isLoading ? (
                        <LoadingComponent />
                    ) : (
                        <Card.Body>
                            {regionPointsExpirySubTransactions.length > 0 ? (
                                regionPointsExpirySubTransactions.map((sT) => {
                                    const transactionTypeLabel =
                                        sT?.transactionType
                                            ? toTitleCase(sT.transactionType)
                                            : "~ unknown";

                                    return (
                                        <Card
                                            key={sT?._id}
                                            className="mb-3 p-3"
                                        >
                                            <div className="d-flex justify-content-between align-items-center">
                                                <div className="mx-3 w-100">
                                                    <DetailsAsLabelValue
                                                        label={
                                                            <div className="d-flex align-items-center">
                                                                {`Sub Transaction
                                                                Name `}
                                                                {!sT?.isVisibleToUser && (
                                                                    <div className="ml-2 mb-1">
                                                                        <small>
                                                                            <Badge
                                                                                className="px-2 py-1"
                                                                                variant="info"
                                                                                size="sm"
                                                                            >
                                                                                System
                                                                            </Badge>
                                                                        </small>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        }
                                                        value={
                                                            sT?.name ||
                                                            "~ unknown"
                                                        }
                                                    />
                                                </div>
                                                <div className="mx-3 w-50">
                                                    <DetailsAsLabelValue
                                                        label="Type"
                                                        value={
                                                            <Badge
                                                                className="px-3 py-2"
                                                                variant={
                                                                    sT?.transactionType ||
                                                                    "default"
                                                                }
                                                            >
                                                                {
                                                                    transactionTypeLabel
                                                                }
                                                            </Badge>
                                                        }
                                                    />
                                                </div>
                                            </div>
                                        </Card>
                                    );
                                })
                            ) : (
                                <h3 className="mb-0 text-center text-danger">
                                    No sub transctions used for points expiry
                                    calculation found.
                                </h3>
                            )}
                        </Card.Body>
                    )}
                </Card>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="primary"
                    size="sm"
                    disabled={isLoading}
                    onClick={onHide}
                >
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ShowExpirySubTransactions;
