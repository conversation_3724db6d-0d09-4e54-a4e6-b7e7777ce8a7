import React, { useCallback, useContext, useState } from "react";
import { SubHeading } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { toTitleCaseFromCamelCase } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import PopUpWindow from "Components/controlPanel/shared/popUpWindow/PopUpWindow";
import { LoadingComponent } from "Components/utils";
import { EditConfigurationType } from "../data";
import AllowSelfSignup from "./views/AllowSelfSignup";
import AllowedOriginsView from "./views/AllowedOriginsView";
import IdpMetadataView from "./views/IdpMetadataView";

import "./SettingsPortalConfigurations.scss";

const SettingsPortalConfigurations = () => {
    const { isLoadingOrganization, organization, loadOrganization } =
        useContext(UserContext);
    const [show, setShow] = useState(false);
    const [popUpState, setPopUpState] = useState({});

    const onShowPopup = useCallback(
        (event) => {
            event.stopPropagation();
            setPopUpState({
                submitButtonName: "Update",
                modalHeaderName: `Edit ${
                    event.currentTarget.name
                        ? toTitleCaseFromCamelCase(event.currentTarget.name)
                        : "Portal Configurations"
                }`,
                renderingComponentName: "EDIT_ORGANIZATION_CONFIGURATIONS",
                modalFooterVisibility: false,
                organizationInfo: organization,
                configurationType: EditConfigurationType.PORTAL,
                configurationData: {
                    portalAttrToUpdate: event.currentTarget.name,
                    toastSuccessMsg:
                        "Successfully updated organization's allowed origins.",
                    toastErrorMsg:
                        "Failed to update organization's allowed origins!",
                    disabledBtnOverlayText: "Allowed origins have not changed.",
                },
            });
            setShow(true);
        },
        [setShow, setPopUpState, organization]
    );

    const onClosePopup = useCallback(() => setShow(false), [setShow]);

    return (
        <div className="settings-portal-configurations-view">
            <BaseLayout
                topLeft={<SubHeading text="Portal Configurations" />}
                bottom={
                    <div className="border-top pt-3">
                        {isLoadingOrganization ? (
                            <div className="font-weight-bold text-center rounded default-info-box-bg">
                                <LoadingComponent />
                                <h3 className="pb-3">
                                    Loading organization configurations...
                                </h3>
                            </div>
                        ) : (
                            <>
                                {organization?.configuration
                                    ?.portalConfiguration &&
                                Object.keys(
                                    organization.configuration
                                        .portalConfiguration
                                ).length !== 0 ? (
                                    <div>
                                        <AllowSelfSignup
                                            key="allowSelfSignup"
                                            keyValue="allowSelfSignup"
                                            value={
                                                !!organization.configuration
                                                    .portalConfiguration
                                                    ?.allowSelfSignup
                                            }
                                        />
                                        {Object.entries(
                                            organization.configuration
                                                .portalConfiguration
                                        ).map(([key, value]) => {
                                            switch (key) {
                                                case "idpMetadata":
                                                    return (
                                                        <IdpMetadataView
                                                            key="idpMetadata"
                                                            keyValue="idpMetadata"
                                                            idpMetadata={
                                                                value || {}
                                                            }
                                                        />
                                                    );
                                                case "allowedOrigins":
                                                    return (
                                                        <AllowedOriginsView
                                                            key="allowedOrigins"
                                                            keyValue="allowedOrigins"
                                                            allowedOrigins={
                                                                value || []
                                                            }
                                                            onEdit={onShowPopup}
                                                        />
                                                    );
                                                default:
                                                    return null;
                                            }
                                        })}
                                        {show && (
                                            <PopUpWindow
                                                show={show}
                                                size="lg"
                                                popUpState={popUpState}
                                                refreshEditedItem={
                                                    loadOrganization
                                                }
                                                handleClose={onClosePopup}
                                            />
                                        )}
                                    </div>
                                ) : (
                                    <div className="font-weight-bold text-center text-danger p-3 rounded default-info-box-bg">
                                        <h3 className="mb-0">
                                            No portal configurations found for
                                            this organization.
                                        </h3>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                }
            />
        </div>
    );
};

export default SettingsPortalConfigurations;
