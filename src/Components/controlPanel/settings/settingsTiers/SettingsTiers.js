import React, { useCallback, useContext, useEffect, useState } from "react";
import BaseLayout from "../../../../Layout/BaseLayout";
import { Button, Card, Col, Form, IcIcon, Row, SubHeading } from "@shoutout-labs/shoutout-themes-enterprise";
import {faPen} from "FaICIconMap";
import PopUpWindow from "../../shared/popUpWindow/PopUpWindow";
import {UserContext} from "Contexts";
import AnalyticsAccessControl from "../../../analyticsView/AnalyticsAccessControl";
import { AccessPermissionModuleNames, AccessPermissionModules } from "../../../../Data";
import { editOrganizationDetails } from "../../../../Services";
import { toast } from "react-toastify";

const SettingsTiers =()=>{
    const {organization,isLoadingModules,loadOrganization} = useContext(UserContext);
    const [jobEnabled, setJobEnabled] = useState(false);
    const [isEditingTierJob, setIsEditingTierJob] = useState(false);
    const [show, setShow] = useState(false);
    const [popUpState, setPopUpState] = useState(null);

    const onEditTierSettingClick = useCallback(async (event) => {
            event.stopPropagation()
            setPopUpState({
                submitButtonName:"Update",
                modalHeaderName:"Edit Tier Point Calculation",
                renderingComponentName:"EDIT_TS",
                editTierSettingInfo:organization,
                modalFooterVisibility:false
            })
            setShow(true)

        },
        [setShow,setPopUpState,organization]
    );

    const editTierJobRunningStatus = useCallback(async (e) => {
        e.stopPropagation();
        try {
            setIsEditingTierJob(true);
             await editOrganizationDetails({
                configuration:{
                    tierConfiguration:{
                        jobEnabled,
                    }
                } }
            );
            setShow(false);
            toast.success("Successfully updated Tier Settings");
        }catch (e) {
            toast.error("Tier Settings failed to update");
            setShow(false);
        }finally {
            loadOrganization();
            setIsEditingTierJob(false);
        }
    }, [jobEnabled,setShow,loadOrganization,setIsEditingTierJob]);

    const onSetJobEnabled= useCallback((e) => {
        e.stopPropagation();
        setPopUpState({
            modalHeaderName:"Tier Job",
            renderingComponentName:`${organization?.configuration?.tierConfiguration?.jobEnabled?"TIER_JOB_OFF":"TIER_JOB_ON"}`,
            submitButtonName:"Yes",
            closeButtonName:"No",
            modalFooterVisibility:true
        });
        setShow(true);
        setJobEnabled(!organization?.configuration?.tierConfiguration?.jobEnabled);

    }, [organization,setShow,setJobEnabled,setPopUpState]);


    const handleClose= useCallback(() => {
        setShow(false)
    }, [setShow]);

    useEffect(() => {
        if (organization?.configuration) {
            setJobEnabled(!!organization?.configuration?.tierConfiguration?.jobEnabled);
        }
    }, [organization]);

    return(
        <BaseLayout
            topLeft={<SubHeading text="Tiers" />}
            bottom={
                <>
                    {!isLoadingModules ?
                     <>
                         {organization.configuration?
                             <>
                                 <Card>
                                     <Card.Header>
                                         <Row>
                                             <Col>
                                                 <SubHeading  text="Tier Job" />
                                             </Col>
                                             <Col>
                                                 <div className="mt-2 d-flex justify-content-end">
                                                     <Form.Group>
                                                         <div className="d-flex justify-content-center">
                                                             <Form.Label>{`Tier Job Turn ${ jobEnabled ?"On":"Off"}`}</Form.Label>
                                                             <Form.Check
                                                                 id="jobEnabled"
                                                                 type="switch"
                                                                 className="ml-2"
                                                                 name="jobEnabled"
                                                                 onChange={onSetJobEnabled}
                                                                 checked={jobEnabled}
                                                                 disabled={isEditingTierJob}
                                                             />
                                                         </div>
                                                     </Form.Group>
                                                 </div>
                                             </Col>
                                         </Row>
                                         <Row className='p-2'>
                                             <Col>
                                                 <p>Tier Job Running interval </p>
                                                 <span>{organization?.configuration?.tierConfiguration?.tierCalculationInterval||"-"} Hours</span>
                                             </Col>
                                             <div className="border-left"/>
                                             <Col>
                                                 <p>Last Tier Job Run Date </p>
                                                <span>{organization?.configuration?.tierConfiguration?.lastTierJobRunDate||"-"}</span>
                                             </Col>
                                             <div className="border-left"/>
                                             <Col>
                                                 <p> Updated Members</p>
                                                 <span>{organization?.configuration?.tierConfiguration?.updatedMembers||"-"}</span>
                                             </Col>
                                         </Row>
                                     </Card.Header>
                                 </Card>
                                 <br/>
                                 <Card>
                                     <Card.Header>
                                         <SubHeading  text="Tier Point Calculation" />
                                         <Row className='p-2'>
                                             <Col>
                                                 <p>Tier Point Calculation Window </p>
                                                  <span>{organization?.configuration?.tierConfiguration?.tierCalculationWindow||"-"} Days</span>
                                             </Col>
                                             <AnalyticsAccessControl
                                                 moduleName={AccessPermissionModuleNames.ORGANIZATION}
                                                 renderEmpty={true}
                                                 actionNames={[
                                                     AccessPermissionModules[AccessPermissionModuleNames.ORGANIZATION].actions.UpdateOrganization,
                                                 ]}
                                                 Logic={"OR"}
                                             >
                                                 <Col>
                                                     <div className='float-right mr-3 mt-2'>
                                                         <Button
                                                             variant="primary"
                                                             onClick={onEditTierSettingClick}
                                                             size='sm'
                                                             disabled={isEditingTierJob}
                                                         >
                                                             <IcIcon className="mr-2" icon={faPen} />
                                                             Edit
                                                         </Button>
                                                     </div>
                                                 </Col>
                                             </AnalyticsAccessControl>
                                         </Row>
                                     </Card.Header>
                                 </Card>
                             </>:null
                         }
                     </>
                    :null}
                    {show? <PopUpWindow
                        handleClose={handleClose}
                        show={show}
                        popUpState={popUpState}
                        refreshEditedItem={loadOrganization}
                        detailsSubmitHandler={editTierJobRunningStatus}
                    />:null}
                </>
            }
        />
    )
}

export default SettingsTiers
