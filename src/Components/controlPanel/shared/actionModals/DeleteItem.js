import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { Modal, Button } from "@shoutout-labs/shoutout-themes-enterprise";
import { toTitleCase } from "Utils";

const DeleteItem = ({
    showModal,
    onHide,
    itemType,
    itemName,
    isDeleting,
    onDeleteItem,
}) => {
    const btnText = useMemo(() => {
        if (itemType === "GROUP") {
            return isDeleting ? "Deleting..." : "Delete";
        } else {
            return isDeleting ? "Archiving..." : "Archive";
        }
    }, [itemType, isDeleting]);

    return (
        <Modal show={showModal} onHide={onHide} centered backdrop="static">
            <Modal.Header closeButton={!isDeleting}>
                <Modal.Title>
                    {`${
                        itemType === "GROUP" ? "Delete" : "Archive"
                    } ${toTitleCase(itemType || "Item")}`}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div>
                    {`Are you sure you want to ${
                        itemType === "GROUP" ? "delete" : "archive"
                    } `}
                    {itemName
                        ? `the ${itemType?.toLowerCase() || "item"}`
                        : `this ${itemType?.toLowerCase() || "item"}`}
                    {itemName ? (
                        <span className="font-weight-bold"> "{itemName}"</span>
                    ) : (
                        ""
                    )}
                    ?
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isDeleting}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant="danger"
                    onClick={onDeleteItem}
                    disabled={isDeleting}
                >
                    {btnText}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

DeleteItem.defaultProps = {
    itemType: "item",
    itemName: "",
    isDeleting: false,
    onDeleteItem: () => {},
};

DeleteItem.propTypes = {
    showModal: PropTypes.bool.isRequired,
    itemType: PropTypes.string,
    itemName: PropTypes.string,
    isDeleting: PropTypes.bool,
    onHide: PropTypes.func.isRequired,
    onDeleteItem: PropTypes.func,
};

export default DeleteItem;
