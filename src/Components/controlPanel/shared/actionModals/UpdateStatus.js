import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { Button, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { toTitleCase } from "Utils";

const UpdateStatus = ({
    showModal,
    onHide,
    itemType,
    itemName,
    status,
    isUpdating,
    onUpdateStatus,
}) => {
    const actionBtnColor = useMemo(() => {
        switch (status) {
            case "SUSPEND":
                return "orange";
            case "DISABLE":
                return "warning";
            default:
                return "success";
        }
    }, [status]);

    return (
        <Modal show={showModal} onHide={onHide} centered backdrop="static">
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>
                    Update {toTitleCase(itemType || "Item")} Status
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div>
                    {`Are you sure you want to `}
                    <span className="font-weight-bold">{status}</span>
                    {itemName
                        ? ` the ${itemType?.toLowerCase() || "item"}`
                        : ` this ${itemType?.toLowerCase() || "item"}`}
                    {itemName ? (
                        <span className="font-weight-bold"> "{itemName}"</span>
                    ) : (
                        ""
                    )}
                    ?
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isUpdating}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant={actionBtnColor}
                    onClick={onUpdateStatus}
                    disabled={isUpdating}
                >
                    {isUpdating ? "Updating..." : `${toTitleCase(status)}`}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

UpdateStatus.defaultProps = {
    itemType: "item",
    itemName: "",
    status: "",
    isUpdating: false,
    onUpdateStatus: () => {},
};

UpdateStatus.propTypes = {
    showModal: PropTypes.bool.isRequired,
    itemType: PropTypes.string,
    itemName: PropTypes.string,
    status: PropTypes.string,
    isUpdating: PropTypes.bool,
    onHide: PropTypes.func.isRequired,
    onUpdateStatus: PropTypes.func,
};

export default UpdateStatus;
