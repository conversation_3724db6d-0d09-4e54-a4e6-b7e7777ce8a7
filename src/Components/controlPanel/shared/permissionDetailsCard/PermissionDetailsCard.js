import React from "react";
import PropTypes from "prop-types";
import { Badge, Card } from "@shoutout-labs/shoutout-themes-enterprise";
import { PermissionTypeColorMap } from "Data";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

import "./PermissionDetailsCard.scss";

const PermissionDetailsCard = ({
    serviceName = "",
    moduleName = "",
    moduleDescription = "",
    selectedActions = [],
}) => {
    return (
        <Card className="p-3 my-3 permission-details-card-view light-bg">
            <div className="my-2 d-flex justify-content-between align-items-center">
                <div className="w-100 mx-2">
                    <DetailsAsLabelValue
                        label="Permission Module"
                        value={moduleName || "~ unknown"}
                    />
                </div>
                <div className="w-100 mx-2">
                    <DetailsAsLabelValue
                        label="Service"
                        value={serviceName || "~ unknown"}
                    />
                </div>
            </div>
            <div className="mx-2 my-2">
                <DetailsAsLabelValue
                    label="Permission Module Description"
                    value={moduleDescription || "~ unknown"}
                />
            </div>
            <div className="mx-2 my-2">
                <DetailsAsLabelValue
                    label="Permission Module Actions"
                    value={
                        selectedActions.length !== 0 ? (
                            <div className="p-2 rounded actions-view">
                                {selectedActions.map((item) => (
                                    <Badge
                                        className="mr-2 my-1 px-3 py-2"
                                        key={item?._id || item?.action}
                                        variant={
                                            PermissionTypeColorMap[
                                                item?.groupName
                                            ] || "default"
                                        }
                                    >
                                        {item?.action || "~ unknown"}
                                    </Badge>
                                ))}
                            </div>
                        ) : (
                            "No actions found."
                        )
                    }
                    isCustomValue={selectedActions.length !== 0}
                />
            </div>
        </Card>
    );
};

PermissionDetailsCard.propTypes = {
    serviceName: PropTypes.string,
    moduleName: PropTypes.string,
    moduleDescription: PropTypes.string,
    selectedActions: PropTypes.array,
};

export default PermissionDetailsCard;
