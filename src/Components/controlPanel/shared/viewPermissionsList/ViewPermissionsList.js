import React, { useCallback, useContext, useMemo, useState } from "react";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import PropTypes from "prop-types";
import {
    Badge,
    BootstrapTable,
    Button,
    FormSelect,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSearch, faTimes } from "FaICIconMap";
import { UserContext } from "Contexts";
import { PermissionTypeColorMap, PermissionTypes } from "Data";
import { toTitleCase } from "Utils";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import PermissionDetailsCard from "../permissionDetailsCard/PermissionDetailsCard";

import "./ViewPermissionsList.scss";

const NoData = () => <h4 className="mb-3">Permission module not assigned.</h4>;

const defaultSkip = 1;
const defaultLimit = 3;

const ViewPermissionsList = ({ permissions = [], disabled = false }) => {
    const { systemModules } = useContext(UserContext);
    const [permissionsList, setPermissionsList] = useState(permissions || []);
    const [skip, setSkip] = useState(defaultSkip);
    const [selectedModuleToSearch, setSelectedModuleToSearch] = useState([]);
    const [isSearched, setIsSearched] = useState(false);
    const [searchedModule, setSearchedModule] = useState([]);

    const onSetSelectedModule = useCallback(
        (module) => setSelectedModuleToSearch(module),
        [setSelectedModuleToSearch]
    );

    const onSearchPermission = useCallback(() => {
        setPermissionsList(
            permissions.filter((p) => p?._id === selectedModuleToSearch[0]?._id)
        );
        setSearchedModule(selectedModuleToSearch);
        setIsSearched(true);
    }, [
        permissions,
        selectedModuleToSearch,
        setPermissionsList,
        setSearchedModule,
        setIsSearched,
    ]);

    const onClearSearchPermission = useCallback(() => {
        setSelectedModuleToSearch([]);
        setPermissionsList(permissions);
        setSearchedModule([]);
        setIsSearched(false);
    }, [
        permissions,
        setSelectedModuleToSearch,
        setPermissionsList,
        setSearchedModule,
        setIsSearched,
    ]);

    const data = useMemo(
        () =>
            permissionsList
                .map((item) => ({
                    id: item?._id || item?.moduleName,
                    "": (
                        <PermissionDetailsCard
                            key={item?._id || item?.moduleName}
                            serviceName={item?.serviceName || ""}
                            moduleName={item?.moduleName || ""}
                            moduleDescription={item?.moduleDescription || ""}
                            selectedActions={item?.selectedActions || []}
                        />
                    ),
                }))
                .slice(
                    (skip - 1) * defaultLimit,
                    (skip - 1) * defaultLimit + defaultLimit
                ),
        [permissionsList, skip]
    );

    const onChangePagination = useCallback(
        (newSkip) => setSkip(newSkip),
        [setSkip]
    );

    const options = useMemo(
        () => ({
            page: skip,
            sizePerPage: defaultLimit,
            totalSize: permissionsList.length,
            pageStartIndex: 1,
            paginationSize: defaultLimit,
            showTotal: true,
            withFirstAndLast: true,
            hideSizePerPage: true,
            hidePageListOnlyOnePage: true,
            onPageChange: onChangePagination,
        }),
        [permissionsList.length, skip, onChangePagination]
    );

    const onTableChange = (_type, _newState) => {
        // * When "remote" is enabled, the "onTableChange" prop is activated automatically, so if we don't provide this method react-bootstrap will throw an error.
    };

    return (
        <div className="view-permissions-list-view">
            {permissions?.length !== 0 ? (
                <div className="border rounded p-3">
                    <div className="mx-2 mb-3">
                        <DetailsAsLabelValue
                            label="Permission Module Action Color Codes"
                            value={
                                <div className="p-2 light-bg border rounded d-flex justify-content-around align-items-center">
                                    {Object.values(PermissionTypes).map(
                                        (aPT) => (
                                            <div
                                                key={aPT}
                                                className="font-weight-bold d-flex align-items-center"
                                            >
                                                {`${toTitleCase(aPT)} Action:`}
                                                <Badge
                                                    className="ml-2 px-4 py-2"
                                                    variant={
                                                        PermissionTypeColorMap[
                                                            aPT
                                                        ]
                                                    }
                                                >
                                                    {" "}
                                                </Badge>
                                            </div>
                                        )
                                    )}
                                    <div className="font-weight-bold d-flex align-items-center">
                                        Action Unknown:
                                        <Badge
                                            className="ml-2 px-4 py-2"
                                            variant="default"
                                        >
                                            {" "}
                                        </Badge>
                                    </div>
                                </div>
                            }
                            isCustomValue
                        />
                    </div>
                    <hr />
                    <div className="p-2 d-flex align-items-center">
                        <div className="w-50">
                            <FormSelect
                                id="search-permission-module"
                                labelKey="moduleName"
                                options={systemModules || []}
                                placeholder="Search for a permission module..."
                                selected={selectedModuleToSearch}
                                disabled={disabled}
                                onChange={onSetSelectedModule}
                                required
                            />
                        </div>
                        <Button
                            className="ml-2"
                            variant="primary"
                            size="sm"
                            disabled={
                                disabled ||
                                selectedModuleToSearch.length === 0 ||
                                (isSearched &&
                                    searchedModule[0]?._id ===
                                        selectedModuleToSearch[0]?._id)
                            }
                            onClick={onSearchPermission}
                        >
                            <div className="d-flex justify-content-center align-items-center">
                                <IcIcon
                                    className="mr-2"
                                    size="lg"
                                    icon={faSearch}
                                />
                                Search
                            </div>
                        </Button>
                        {isSearched && (
                            <Button
                                className="ml-2"
                                variant="outline-danger"
                                size="sm"
                                disabled={disabled}
                                onClick={onClearSearchPermission}
                            >
                                <div className="d-flex justify-content-center align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faTimes}
                                    />
                                    Clear
                                </div>
                            </Button>
                        )}
                    </div>
                    {isSearched && searchedModule.length !== 0 && (
                        <div className="px-2 mt-2 d-flex align-items-center font-weight-bold">
                            Searched Permission Module:
                            <div className="ml-3">
                                {applyBadgeStyling(
                                    searchedModule[0]?.moduleName
                                        ? {
                                            text: searchedModule[0]
                                                .moduleName,
                                            variant: "secondary",
                                        }
                                        : {
                                            customValue:
                                                "~ permission module unknown",
                                        }
                                )}
                            </div>
                        </div>
                    )}
                    <hr />
                    <ToolkitProvider
                        keyField="id"
                        data={data}
                        columns={[
                            {
                                dataField: "id",
                                name: "id",
                                hidden: true,
                            },
                            {
                                dataField: "",
                                name: "",
                                sort: false,
                                headerStyle: { width: "10%" },
                            },
                        ]}
                    >
                        {(props) => (
                            <div>
                                <BootstrapTable
                                    {...props.baseProps}
                                    remote={{
                                        pagination: true,
                                        filter: false,
                                        sort: false,
                                    }}
                                    pagination={paginationFactory(options)}
                                    onTableChange={onTableChange}
                                    noDataIndication={<NoData />}
                                />
                            </div>
                        )}
                    </ToolkitProvider>
                </div>
            ) : (
                <h2 className="mb-0 rounded text-center grey-bg">
                    No permissions found.
                </h2>
            )}
        </div>
    );
};

ViewPermissionsList.propTypes = {
    permissions: PropTypes.array.isRequired,
    disabled: PropTypes.bool,
    baseProps: PropTypes.any,
};

export default ViewPermissionsList;
