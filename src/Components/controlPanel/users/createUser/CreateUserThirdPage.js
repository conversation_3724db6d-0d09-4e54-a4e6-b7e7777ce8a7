import React, { useContext, useEffect } from "react";
import { Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { AddUserPermissionContext } from "Contexts/accessControl/addUserPermissionContext";
import { UserBoundaryType } from "Data";
import { CreateUserContext } from "./context/CreateUserContext";
import AddUserPermissionContainer from "Components/controlPanel/users/userProfile/permissions/AddPermission";

const CreateUserThirdPage = (props, ref) => {
    const { organization } = useContext(UserContext);
    const { addRegion, permissionSets, isLoadingModal } = useContext(
        AddUserPermissionContext
    );
    const {
        selectedRegion,
        selectedMerchant,
        accessType,
        addUserPermissions,
        setAddUserPermissions,
    } = useContext(CreateUserContext);

    useEffect(() => {
        if (
            accessType !== UserBoundaryType.GLOBAL &&
            permissionSets.length === 0
        ) {
            addRegion();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [accessType]);

    return (
        <div className="mt-5" ref={ref}>
            <Form.Group className="d-flex">
                <Form.Label>Add Permissions to User</Form.Label>
                <Form.Check
                    className="ml-3"
                    id="addUserPermissions"
                    type="switch"
                    name="addUserPermissions"
                    checked={addUserPermissions}
                    disabled={isLoadingModal}
                    onChange={setAddUserPermissions}
                />
                <Form.Text className="text-info">
                    * You can add permissions later inside the user's profile.
                </Form.Text>
            </Form.Group>
            {accessType === UserBoundaryType.GLOBAL &&
                addUserPermissions &&
                permissionSets?.length !== organization?.regions?.length && (
                    <div className="text-right mb-3">
                        <Button
                            type="button"
                            onClick={addRegion}
                            variant="primary"
                            disabled={isLoadingModal}
                            size="sm"
                        >
                            Add Region
                        </Button>
                    </div>
                )}
            {addUserPermissions && permissionSets?.length !== 0 && (
                <div className="border rounded p-3 grey-bg">
                    {permissionSets.map((item, index) => (
                        <AddUserPermissionContainer
                            key={item.index}
                            {...item}
                            index={index}
                            currentPermissions={[]}
                            showDeleteRegion={true}
                            boundary={accessType}
                            boundaryRegionId={selectedRegion[0]?._id}
                            boundaryMerchantId={selectedMerchant[0]?._id}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default CreateUserThirdPage;
