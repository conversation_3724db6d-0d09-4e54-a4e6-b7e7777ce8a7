import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useReducer,
} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { AddUserPermissionContext } from "Contexts/accessControl/addUserPermissionContext";
import { UserBoundaryType } from "Data";
import { createUser, getAllMerchants } from "Services";

const CreateUserContext = React.createContext();

const initialState = {
    firstName: "",
    lastName: "",
    employeeId: "",
    username: "",
    email: "",
    useEmailAsUsername: false,
    mobileNumber: "",
    accessType: UserBoundaryType.GLOBAL,
    selectedRegion: [],
    selectedMerchant: [],
    merchants: [],
    isLoadingMerchants: false,
    showCreateUserWizard: false,
    isCreating: false,
    isCreateUserFromMerchant: false,
    addUserPermissions: false,
    createUserSuccessResponse: null,
    isCopied: false,
};

const CreateUserContextActions = {
    SET_KEY_VALUE: "setKeyValue",
    SET_EMAIL: "setEmail",
    SET_MOBILE_NUMBER: "setMobileNumber",
    SET_SHOW_WIZARD: "setShowWizard",
    SET_IS_CREATING: "setIsCreating",
    RESET: "reset",
};

const reducer = (state, action) => {
    switch (action.type) {
        case CreateUserContextActions.SET_KEY_VALUE: {
            return {
                ...state,
                [action.key]: action.value,
            };
        }
        case CreateUserContextActions.SET_EMAIL: {
            return {
                ...state,
                email: action.email,
            };
        }
        case CreateUserContextActions.SET_MOBILE_NUMBER: {
            return {
                ...state,
                mobileNumber: action.mobileNumber,
            };
        }
        case CreateUserContextActions.SET_SHOW_WIZARD: {
            return {
                ...state,
                showCreateUserWizard: action.showCreateUserWizard,
                isCreateUserFromMerchant: action.fromMerchant,
            };
        }
        case CreateUserContextActions.SET_IS_CREATING: {
            return {
                ...state,
                isCreating: action.status,
            };
        }
        case CreateUserContextActions.RESET: {
            return { ...initialState };
        }
        default:
            return state;
    }
};

const CreateUserContextProvider = (props) => {
    const [state, dispatch] = useReducer(reducer, initialState);
    const { saveUserPermissions } = useContext(AddUserPermissionContext);

    const setKeyValue = useCallback(
        ({ key = "", value = "" }) => {
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key,
                value,
            });
        },
        [dispatch]
    );

    const setPageValue = useCallback(
        (event) => {
            const key = event.target.name || "";

            if (key === "useEmailAsUsername" && !event.target.checked)
                setKeyValue({ key: "username", value: "" });

            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key,
                value:
                    key === "useEmailAsUsername"
                        ? event.target.checked
                        : event.target.value,
            });
        },
        [setKeyValue, dispatch]
    );

    const setEmail = useCallback(
        (email) => {
            dispatch({
                type: CreateUserContextActions.SET_EMAIL,
                email,
            });
        },
        [dispatch]
    );

    const setMobileNo = useCallback(
        (mobileNumber) => {
            dispatch({
                type: CreateUserContextActions.SET_MOBILE_NUMBER,
                mobileNumber,
            });
        },
        [dispatch]
    );

    const setCreateUserWizard = useCallback(
        (showCreateUserWizard, fromMerchant = false) => {
            dispatch({
                type: CreateUserContextActions.SET_SHOW_WIZARD,
                showCreateUserWizard,
                fromMerchant,
            });
        },
        [dispatch]
    );

    const setAccessType = useCallback(
        (accessType) => {
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "accessType",
                value: accessType,
            });
        },
        [dispatch]
    );

    const setSelectedRegion = useCallback(
        async (region) => {
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "selectedRegion",
                value: region,
            });
        },
        [dispatch]
    );

    const setSelectedMerchant = useCallback(
        (merchant) => {
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "selectedMerchant",
                value: merchant,
            });
        },
        [dispatch]
    );

    const setIsCopied = useCallback(
        () =>
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "isCopied",
                value: true,
            }),
        [dispatch]
    );

    const closeUserCreateResponse = useCallback(() => {
        dispatch({
            type: CreateUserContextActions.SET_KEY_VALUE,
            key: "createUserSuccessResponse",
            value: null,
        });
        dispatch({
            type: CreateUserContextActions.SET_KEY_VALUE,
            key: "isCopied",
            value: false,
        });
        dispatch({ type: CreateUserContextActions.RESET });
    }, [dispatch]);

    const setAddUserPermissions = useCallback(() => {
        dispatch({
            type: CreateUserContextActions.SET_KEY_VALUE,
            key: "addUserPermissions",
            value: !state.addUserPermissions,
        });
    }, [state.addUserPermissions, dispatch]);

    const loadMerchants = useCallback(
        async (regionId) => {
            try {
                dispatch({
                    type: CreateUserContextActions.SET_KEY_VALUE,
                    key: "isLoadingMerchants",
                    value: true,
                });
                dispatch({
                    type: CreateUserContextActions.SET_KEY_VALUE,
                    key: "merchants",
                    value: [],
                });

                const merchantsResponse = await getAllMerchants({ regionId });
                dispatch({
                    type: CreateUserContextActions.SET_KEY_VALUE,
                    key: "isLoadingMerchants",
                    value: false,
                });

                const merchants = merchantsResponse?.map((mR) => ({
                    ...mR,
                    merchantName: `${mR?.merchantName || "~ name unknown"} (${
                        mR?.status || "~ status uknown"
                    })`,
                }));

                dispatch({
                    type: CreateUserContextActions.SET_KEY_VALUE,
                    key: "merchants",
                    value: merchants,
                });
            } catch (error) {
                console.error(error);
                toast.error(
                    <div>
                        Failed to load merchants!
                        <br />
                        {error.message
                            ? `Error: ${error.message}`
                            : "Please try again later."}
                    </div>
                );
                dispatch({
                    type: CreateUserContextActions.SET_KEY_VALUE,
                    key: "isLoadingMerchants",
                    value: false,
                });
            }
        },
        [dispatch]
    );

    const onCreateUser = useCallback(async () => {
        try {
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "isCreating",
                value: true,
            });

            const requestObject = {
                boundary: state.accessType,
                userData: {
                    firstName: state.firstName,
                    username: state.username,
                    lastName: state.lastName,
                    ...(state.employeeId
                        ? { employeeId: state.employeeId }
                        : {}),
                    contact: {
                        mobileNumber: state.mobileNumber,
                        email: state.email,
                    },
                },
            };
            let userCreationMsg = "";

            if (state.accessType !== UserBoundaryType.GLOBAL) {
                requestObject.regionId = state.selectedRegion[0]?._id;

                if (state.accessType === UserBoundaryType.MERCHANT) {
                    requestObject.merchantId = state.selectedMerchant[0]?._id;
                }
            }

            const createUserResponse = await createUser(requestObject);
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "isCreating",
                value: false,
            });
            userCreationMsg = `Successfully created the user${
                createUserResponse?.username
                    ? ' "' + createUserResponse.username + '"'
                    : ""
            }`;

            if (state.addUserPermissions) {
                try {
                    dispatch({
                        type: CreateUserContextActions.SET_KEY_VALUE,
                        key: "isAddingPermissions",
                        value: true,
                    });
                    await saveUserPermissions(createUserResponse._id, []);
                    dispatch({
                        type: CreateUserContextActions.SET_KEY_VALUE,
                        key: "isAddingPermissions",
                        value: false,
                    });
                    toast.success(
                        `${userCreationMsg} and assigned the configured permissions.`
                    );
                } catch (e) {
                    toast.warn(
                        `${userCreationMsg}, but failed to assign permissions. Please add permissions manually in the user profile.`
                    );
                    dispatch({
                        type: CreateUserContextActions.SET_KEY_VALUE,
                        key: "isAddingPermissions",
                        value: false,
                    });
                }
            } else {
                toast.success(
                    `${userCreationMsg} without permissions. Please add permissions manually in the user profile.`
                );
            }
            setCreateUserWizard(false);
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "createUserSuccessResponse",
                value: createUserResponse,
            });

            return createUserResponse;
        } catch (e) {
            console.error(e);
            dispatch({
                type: CreateUserContextActions.SET_KEY_VALUE,
                key: "isCreating",
                value: false,
            });
            toast.error(
                <div>
                    Failed to create a new user!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
            return Promise.reject(e);
        }
    }, [
        state.firstName,
        state.lastName,
        state.email,
        state.mobileNumber,
        state.accessType,
        state.selectedRegion,
        state.selectedMerchant,
        state.username,
        state.employeeId,
        state.addUserPermissions,
        saveUserPermissions,
        dispatch,
        setCreateUserWizard,
    ]);

    const reset = useCallback(() => {
        dispatch({ type: CreateUserContextActions.RESET });
    }, [dispatch]);

    useEffect(() => {
        if (
            state.accessType === UserBoundaryType.MERCHANT &&
            state.selectedRegion.length > 0
        ) {
            loadMerchants(state.selectedRegion[0]._id);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.accessType, state.selectedRegion]);

    const value = useMemo(
        () => ({
            ...state,
            setKeyValue,
            setPageValue,
            setEmail,
            setMobileNo,
            setCreateUserWizard,
            reset,
            setAccessType,
            setSelectedRegion,
            setSelectedMerchant,
            onCreateUser,
            closeUserCreateResponse,
            setAddUserPermissions,
            loadMerchants,
            setIsCopied,
        }),
        [
            state,
            setKeyValue,
            setPageValue,
            setEmail,
            setMobileNo,
            setCreateUserWizard,
            reset,
            setAccessType,
            setSelectedRegion,
            setSelectedMerchant,
            onCreateUser,
            closeUserCreateResponse,
            setAddUserPermissions,
            loadMerchants,
            setIsCopied,
        ]
    );

    return (
        <CreateUserContext.Provider value={value}>
            {props.children}
        </CreateUserContext.Provider>
    );
};

CreateUserContextProvider.propTypes = { children: PropTypes.any };

const CreateUserContextConsumer = CreateUserContext.Consumer;

export {
    CreateUserContextProvider,
    CreateUserContext,
    CreateUserContextConsumer,
};
