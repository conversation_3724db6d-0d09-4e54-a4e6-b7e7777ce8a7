import React from "react";
import { DropdownButton, DropdownItem, IcIcon, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimesCircle } from "FaICIconMap";

const Permissions = ({ organization, merchant, location, group, onChangeHandler }) => {
    return (
        <div className="mt-2 d-flex flex-row">
            <div className="mr-3">
                <Form.Control
                as="select"
                placeholder="Select"
                name="country"
                value={merchant}
                onChange={onChangeHandler}
                required
                >
                <option value="" disabled className="text-muted">
                    Merchants
                </option>
                {organization.regions
                    ? organization.regions.map(({regionName,_id}) => {
                        return (
                        <option value={regionName} key={_id} id={_id}>
                            {regionName}
                        </option>
                        );
                    })
                    : null}
                </Form.Control>
            </div>
            <div className="mr-3">
                <Form.Control
                as="select"
                placeholder="Select"
                name="country"
                value={location}
                onChange={onChangeHandler}
                required
                >
                <option value="" disabled className="text-muted">
                    Locations
                </option>
                {organization.regions
                    ? organization.regions.map(({regionName,_id}) => {
                        return (
                        <option value={regionName} key={_id} id={_id}>
                            {regionName}
                        </option>
                        );
                    })
                    : null}
                </Form.Control>
            </div>
            <div className="mr-3">
                <Form.Control
                as="select"
                placeholder="Select"
                name="country"
                value={group}
                onChange={onChangeHandler}
                required
                >
                <option value="" disabled className="text-muted">
                    Groups
                </option>
                {organization.regions
                    ? organization.regions.map(({regionName,_id}) => {
                        return (
                        <option value={regionName} key={_id} id={_id}>
                            {regionName}
                        </option>
                        );
                    })
                    : null}
                </Form.Control>
            </div>
            <div className="mt-1">
                <DropdownButton data-testid="permissions-dropdown" bsPrefix="dropdown-btn single-dropdown-toggle"  className="none" >
                    <DropdownItem
                        eventKey="Delete Account"
                        key="Delete Account"
                        className="text-danger"
                    >
                        <IcIcon className="mr-2" size="w-16" icon={faTimesCircle} />
                        <span>Delete</span>
                    </DropdownItem>
                </DropdownButton>
            </div>
        </div>
    );
};

export default Permissions;