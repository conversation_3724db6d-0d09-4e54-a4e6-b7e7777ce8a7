import React, { useContext, useState, useCallback, useEffect } from 'react';
import { Card, IcIcon, Row, Col, Dropdown} from '@shoutout-labs/shoutout-themes-enterprise';
import { faSun, faMoon, faAngleRight } from 'FaICIconMa';
import DropdownButton from 'react-bootstrap/DropdownButton';
import { Link } from "react-router-dom";
import moment from 'moment';
import SummaryCard from './topPanel/SummaryCard';
import TopBranches from './topBranches/TopBranches';
import TopRewards from './topRewards/TopRewards';
import BaseLayout from "../../Layout/BaseLayout";
import { UserContext } from '../../Contexts/userContext';
//import ChartWidget from '../utils/chartWidget/ChartWidget';
import { LoadingComponent } from '../utils/UtilComponents';
import numeral from 'numeral';
import EBar<PERSON>hart from './echart/EBarChart';
import AppContext from './../../AppContext';
import { getDatesSeries, getTopUsedRewards, getBranches, getSalesStat, getUserSeries,  getUserCount, getPointsCount, getRewardsCount} from '../../Services';
import DefaultGraphImage from './../../assets/images/defaultImages/Graph 01.svg';
import './Dashboard.css';
// numeral.zeroFormat('N/A');
numeral.nullFormat('N/A');

const datePeriods = {
    "Today": {
        toDate: moment().format("YYYY-MM-DD"),
        fromDate: moment().startOf('day').format('YYYY-MM-DD')
    },
    "Last 7 Days": {
        toDate: moment().subtract(1, 'day').format("YYYY-MM-DD"),
        fromDate: moment().subtract(7, 'day').format("YYYY-MM-DD")
    },
    "Last 28 Days": {
        toDate: moment().subtract(1, 'day').format("YYYY-MM-DD"),
        fromDate: moment().subtract(28, 'day').format("YYYY-MM-DD")
    },
    "Last 90 Days": {
        toDate: moment().subtract(1, 'day').format("YYYY-MM-DD"),
        fromDate: moment().subtract(90, 'day').format("YYYY-MM-DD")
    },
    "Last 365 Days": {
        toDate: moment().subtract(1, 'day').format("YYYY-MM-DD"),
        fromDate: moment().subtract(365, 'day').format("YYYY-MM-DD")
    }
}

const Dashboard = () => {

    const { username, isAuth } = useContext(UserContext);

    const [selectedPeriod, setSelectedPeriod] = useState("Last 7 Days");
    const [isLoading, setIsLoading] = useState(false);
    const [cardData, setCardData] = useState({});
    const [saleStat, setSaleStat] = useState({});
    const [activeMemberData, setActiveMemberData] = useState([]);
    const [topRewardData, setTopRewardData] = useState([]);
    const [topBranchesData, setTopBranchesData] = useState([]);
    //const [chartData, setChartData] = useState([]);
    const [rewardsCount, setRewardsCount] = useState({});
    const [dates, setDates] = useState([]);
    const [activeNumber, setActiveNumber] = useState([]);


    const loadAllData = useCallback(async () => {

        try {

            setIsLoading(true);
            const { fromDate, toDate } = datePeriods[selectedPeriod];
            const [cardData, summary, topRewardData, topBranchesData, activeMemberStat, saleStat, rewardData] = await Promise.all([getPointsCount({fromDate, toDate}),  getUserCount({fromDate, toDate}), getTopUsedRewards({fromDate, toDate}), getBranches({fromDate, toDate}), getUserSeries({fromDate, toDate}), getSalesStat({fromDate, toDate}),getRewardsCount({fromDate, toDate})])

            setIsLoading(false);
            setCardData(cardData);
            setSaleStat(saleStat);
            setActiveMemberData(summary);
            setRewardsCount(rewardData);

            const topRewards = (topRewardData && topRewardData.length) > 10 ? topRewardData.slice(0, 9) : topRewardData;
            const topBranches = (topBranchesData && topBranchesData.length) > 10 ? topBranchesData.slice(0, 9) : topBranchesData;
            setTopRewardData(topRewards);
            setTopBranchesData(topBranches);

            const dateSeries = getDatesSeries(fromDate,toDate);

            const dateSeriesForMemberSeries = dateSeries.reduce((result, data) => {
                result[data] = { key: moment(data).format("MMM-DD"), activeMembers: 0 };
                return result;
              }, {});

            const memberSeriesDataSourceWise = activeMemberStat.reduce((result,  { date, activeCount }) => {
                if (result[date]) {
                  result[date] = { key: moment(date).format("MMM-DD"), activeMembers: activeCount }
                }
                return result;
              },  dateSeriesForMemberSeries)
        
            const memberSeriesArray = Object.values(memberSeriesDataSourceWise);

            // const transformedChartData = activeMemberStat.reduce((result, data) => {
            //     result.push({ key: moment(data["statDay"]).format("MMM-DD"), activeMembers: data.activeCount })
            //     return result;
            // }, [])

            const dateArray = memberSeriesArray.reduce((result, {key}) => {
                result.push(key);
                return result;
            }, []);
            
            const activeCountArray = memberSeriesArray.reduce((result, {activeMembers}) => {
                result.push(activeMembers);
                return result;
            }, []);

            setDates(dateArray);
            setActiveNumber(activeCountArray);

            //setChartData(memberSeriesArray);

        } catch (e) {
            console.error(e);
        }

    }, [setTopRewardData, setTopBranchesData, setActiveMemberData, setCardData,setRewardsCount,setSaleStat, setDates,setActiveNumber,selectedPeriod])

    useEffect(() => {
        if (isAuth) {
            loadAllData()
        }

        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedPeriod, isAuth])

    return (
        <BaseLayout
            topLeft={
                <h4 className="mt-2 mb-0">
                    {moment().hour() < 12 ? <span className="App-welcome-text"><IcIcon className="mr-2" icon={faSun}/>Good Morning, {username} </span> : <span><IcIcon className="mr-2" icon={faMoon}/>Good Evening, {username} </span>}
                </h4>
            }
            topRight={
                <img src={AppContext.logo} height="50" alt='logo' />
            }
            bottom={
                <>

                    <div className="d-flex flex-column align-items-end">
                        <DropdownButton bsPrefix="text-capitalize btn btn-sm" id="dropdown-basic-button" title={selectedPeriod} className="mr-n2" onSelect={setSelectedPeriod}>
                            {Object.keys(datePeriods).map((key) => {
                                return (
                                    <Dropdown.Item eventKey={key} key={key}>{key}</Dropdown.Item>
                                )
                            })}

                        </DropdownButton>
                    </div>

                    <Row className="">
                        <Col lg={3} md={6} sm={12} xs={12} className="my-sm-3 " >
                            <SummaryCard title={'Total Transactions'} amount={numeral(saleStat.sales?.value).format('0a')} isLoading={isLoading} />
                        </Col>
                        <Col lg={3} md={6} sm={12} xs={12} className="my-sm-3 ">
                            <SummaryCard title={'Issued Points'} amount={numeral(cardData.collected?.amount).format('0a')} isLoading={isLoading} />
                        </Col>
                        <Col lg={3} md={6} sm={12} xs={12} className="my-sm-3 ">
                            <SummaryCard title={'Redeemed Points'} amount={numeral(cardData.redeemed?.amount).format('0a')} isLoading={isLoading} />
                        </Col>
                        <Col lg={3} md={6} sm={12} xs={12} className="my-sm-3 ">
                            <SummaryCard title={'Redeemed Reward'} amount={numeral(rewardsCount.redeemed?.count).format('0a')} isLoading={isLoading} />
                        </Col>
                    </Row>

                    <Card className="my-2">
                        <Row noGutters={true} className="py-3 mx-4">
                            {
                                isLoading ?
                                    <LoadingComponent />
                                    :
                                    <>
                                        <Col lg={3} md={6} sm={12} xs={12} className="my-auto pl-2">
                                            <div className="member-analysis" id="new-member-analysis">
                                                <h6>New Enrollments</h6>
                                                <h1 className="font-weight-bold" id="new-member-amount">{numeral(activeMemberData.new?.count).format('0a')}</h1>
                                            </div>
                                            <div className="member-analysis">
                                                <h6> Active Members</h6>
                                                <h1 className="font-weight-bold">{numeral(activeMemberData.active?.count).format('0a')}</h1>
                                            </div>
                                            <div>
                                                <h6>Total Members</h6>
                                                <h1 className="font-weight-bold">{numeral(activeMemberData.total?.count).format('0a')}</h1>
                                            </div>

                                            <Link to={"/members"}>
                                                See Members <IcIcon icon={faAngleRight} />
                                            </Link>
                                        </Col>
                                        <Col lg={9} md={6} sm={12} xs={12} className="my-atuo">

                                            {
                                                activeNumber.length > 0 ?
                                                <EBarChart dates={dates} activeNumber={activeNumber}/>
                                                    
                                            :
                                                <img src={DefaultGraphImage} className="img-fluid p-4" alt='default graph' />
                                            }
                                        </Col>
                                        {/* <Col lg={9} md={6} sm={12} xs={12} className="my-atuo">

                                            {
                                                chartData.length > 0 ?
                                                    <ChartWidget
                                                        layout="horizontal"
                                                        dataset={chartData}
                                                        barOptions={{ xAxis: { key: "key", type: "category" }, bar: [{ key: 'activeMembers' }] }}
                                                    />
                                                    :
                                                    <img src="/assets/images/defaultImages/Graph 01.svg" className="img-fluid p-4" alt='default graph' />
                                            }
                                        </Col> */}
                                    </>
                            }
                        </Row>
                    </Card>

                    <Row>
                        <Col lg={6} md={6} sm={12} xs={12} className="my-3">
                            <TopRewards rewardData={topRewardData} isLoading={isLoading} />
                        </Col>
                        <Col lg={6} md={6} sm={12} xs={12} className="my-3">
                            <TopBranches branchesData={topBranchesData} isLoading={isLoading} />
                        </Col>
                    </Row>
                </>
            }
        />
    )
}

export default Dashboard
