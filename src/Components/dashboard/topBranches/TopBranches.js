import React from 'react';
import { Card, ListGroup, Col, Row, Avatar } from '@shoutout-labs/shoutout-themes-enterprise';
import { LoadingComponent } from '../../utils/UtilComponents';
import './TopBranches.css';

const TopBranches = ({ branchesData, isLoading }) => {
    return (
        <Card className="shadow p-4 top-table-height">
            <Card.Title className="top-branches-title">Top Branches</Card.Title>
            {
                isLoading ?
                    <LoadingComponent />
                    :
                    <>
                        {
                            branchesData.length > 0 ?
                                <ListGroup variant="flush" className="list-branches">
                                    {
                                        branchesData.map(({ location, salesCount }, branchIndex) => {
                                            return <ListGroup.Item className="top-branches-items" key={`b-${branchIndex}`}>
                                                <Row className="my-0 py-0">
                                                    <Col className="my-auto py-0" lg={2} md={3} sm={3} xs={3}>
                                                        <Avatar className=" UserAvatar pt-2"
                                                            colors={[
                                                                '#000',
                                                                '#000'
                                                            ]}
                                                            name={String(branchIndex + 1)}
                                                            size="25"
                                                        />
                                                    </Col>
                                                    <Col className="my-auto py-0" lg={7} md={6} sm={6} xs={6}>
                                                        {location}
                                                    </Col>
                                                    <Col className="my-auto py-0" lg={2} md={3} sm={3} xs={3}>
                                                        {salesCount}
                                                    </Col>
                                                </Row>
                                            </ListGroup.Item>
                                        })
                                    }
                                </ListGroup>
                                : <div className="m-auto">
                                    <p className='no-branches-text'> No top branches found</p>
                                </div>
                        }
                    </>
            }
        </Card>
    )
}

export default TopBranches
