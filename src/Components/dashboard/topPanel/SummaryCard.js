import React from 'react';
import { Card} from '@shoutout-labs/shoutout-themes-enterprise';
import { LoadingComponent } from '../../utils/UtilComponents';

const SummaryCard = ({title, amount, isLoading}) => {
    return (
        <>
            <Card data-testid="summaryCard">
                <Card.Body>
                   { isLoading ? 
                    <LoadingComponent data-testid="loading"/>
                    :
                       <>  <h6 data-testid="title">{title}</h6>
                         <h2 className="font-weight-bold mb-0" data-testid="amount">{amount}</h2>
                        </>
                    }
                </Card.Body>
            </Card> 
                
                {/* <StatBox
                    className="shadow rounded-0 mx-1"
                    number={amount}
                    title= {title}
                    variant="success"
                /> */}

        </>
    )
}

export default SummaryCard
