import React, { useCallback, useContext, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Heading,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes } from "FaICIconMap";
import { FindMembersContext } from "./context/FindMembersContext";
import BaseLayout from "Layout/BaseLayout";
import SearchBar from "./searchBar/SearchBar";
import FindMembersTable from "./findMembersTable/FindMembersTable";

import "./FindMembers.scss";

const defaultSkip = 1;

const FindMember = () => {
    const {
        limit,
        skip,
        totalCount,
        isLoadingMembers,
        foundMembers,
        isSearched,
        searchedValue,
        setIsSearched,
        setLimit,
        setSkip,
        onFindMembers,
        searchField,
        setSearchField,
    } = useContext(FindMembersContext);
    const [searchText, setSearchText] = useState("");
    const [isFinding, setIsFinding] = useState(false);
    const [isClearing, setIsClearing] = useState(false);

    const onChangeSearchText = useCallback(
        (e) => setSearchText(e.currentTarget.value),
        [setSearchText]
    );

    const onSearchMember = useCallback(async () => {
        let skipVal = skip;

        setIsFinding(true);

        if (
            searchText !== "" &&
            searchedValue !== "" &&
            searchText.toLowerCase() !== searchedValue.toLowerCase()
        ) {
            skipVal = defaultSkip;
            setSkip(defaultSkip);
        }

        await onFindMembers(
            { limit, skip: skipVal, countMembers: true },
            searchText,
            searchField
        );
        setSearchText("");
        setIsFinding(false);
    }, [
        searchText,
        searchedValue,
        limit,
        skip,
        searchField,
        onFindMembers,
        setSkip,
        setIsFinding,
        setSearchText,
    ]);

    const onClearSearch = useCallback(async () => {
        setIsClearing(true);
        setSearchText("");
        await onFindMembers({ limit, skip: defaultSkip });
        setSkip(defaultSkip);
        setIsSearched(false);
        setIsClearing(false);
    }, [
        limit,
        onFindMembers,
        setIsSearched,
        setSkip,
        setSearchText,
        setIsClearing,
    ]);

    return (
        <BaseLayout
            containerClassName="find-members-view"
            topLeft={<Heading text="Find Members" />}
            bottom={
                <div className="mt-3">
                    <Card className="rounded-corners">
                        <Card.Body>
                            <SearchBar
                                isFinding={isFinding || isLoadingMembers}
                                isClearing={isClearing}
                                searchText={searchText}
                                onChangeSearchText={onChangeSearchText}
                                onSearchMember={onSearchMember}
                                onSelectSearchField={setSearchField}
                                searchField={searchField}
                            />
                            {(isSearched || searchedValue) && (
                                <div className="d-flex align-items-center my-3 find-members-default-font">
                                    Searched Value:
                                    <Button
                                        className="ml-3 py-0 px-4 find-members-default-font searched-value-btn"
                                        variant="secondary"
                                        size="lg"
                                        disabled={isLoadingMembers}
                                        onClick={onClearSearch}
                                    >
                                        <div className="d-flex align-items-center">
                                            {searchedValue}
                                            <IcIcon
                                                className="ml-3"
                                                icon={faTimes}
                                            />
                                        </div>
                                    </Button>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                    <Card className="rounded-corners my-3">
                        <Card.Body>
                            <FindMembersTable
                                limit={limit}
                                skip={skip}
                                totalCount={totalCount}
                                foundMembers={foundMembers}
                                searchField={searchField}
                                isLoading={
                                    isLoadingMembers || isFinding || isClearing
                                }
                                isSearched={isSearched}
                                searchedValue={searchedValue}
                                setLimit={setLimit}
                                setSkip={setSkip}
                                onFindMembers={onFindMembers}
                            />
                        </Card.Body>
                    </Card>
                </div>
            }
        />
    );
};

export default FindMember;
