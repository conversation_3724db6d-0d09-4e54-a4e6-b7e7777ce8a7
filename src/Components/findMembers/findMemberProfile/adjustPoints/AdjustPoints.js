import React, {
    useContext,
    useRef,
    useState,
    useCallback,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import moment from "moment";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext } from "Contexts";
import {
    MerchantLocationStatusObj,
    MerchantStatus,
    TransactionTypes,
} from "Data";
import { adjustPointKlip } from "Services";
import { toTitleCase } from "Utils";
import { FindMembersContext } from "../../context/FindMembersContext";

import "./AdjustPoints.scss";

const AdjustPoints = ({ show, onHide }) => {
    const { merchants, merchantLocations, subTransactionTypes } =
        useContext(DataContext);
    const { selectedMember } = useContext(FindMembersContext);
    const [isAdjusting, setIsAdjusting] = useState(false);
    const [validated, setValidated] = useState(false);
    const [selectedMerchant, setSelectedMerchant] = useState([]);
    const [selectedLocation, setSelectedLocation] = useState([]);
    const [selectedSubTransactionType, setSelectedSubTransactionType] =
        useState([]);
    const [adjustPointsStatus, setAdjustPointsStatus] = useState({
        pointsAmount: "",
        notes: "",
        otpCode: "",
    });
    const formRef = useRef();

    const merchantsWithAdjustPointsOption = useMemo(
        () =>
            merchants
                .filter(
                    (merchant) =>
                        merchant?.status === MerchantStatus.ACTIVE &&
                        merchant?.options?.adjustPoints
                )
                .map((filteredMerchant) => ({
                    merchantId: filteredMerchant?._id || "Unknown Id",
                    name: toTitleCase(
                        filteredMerchant?.merchantName || "Unknown merchant"
                    ),
                })),
        [merchants]
    );

    const locations = useMemo(
        () =>
            (selectedMerchant.length !== 0 &&
                merchantLocations[selectedMerchant[0]?.merchantId] &&
                Object.values(
                    merchantLocations[selectedMerchant[0]?.merchantId]
                )
                    .filter(
                        (location) =>
                            location?.status ===
                                MerchantLocationStatusObj.ACTIVE &&
                            location?.options?.adjustPoints &&
                            location?.isShownInCreateTransactions
                    )
                    .map((filteredLocation) => ({
                        locationId: filteredLocation?._id || "Unknown Id",
                        name: toTitleCase(
                            filteredLocation?.locationName || "Unknown location"
                        ),
                    }))) ||
            [],
        [merchantLocations, selectedMerchant]
    );

    const subTransactionTypeOptions = useMemo(
        () =>
            subTransactionTypes
                .filter(
                    (subTransactionType) =>
                        subTransactionType?.transactionType ===
                        TransactionTypes.ADJUSTMENT
                )
                .map((fST) => ({
                    id: fST?._id || "Unknown Id",
                    name: toTitleCase(
                        fST?.name || "Unknown sub transaction type"
                    ),
                })) || [],
        [subTransactionTypes]
    );

    const handleChange = useCallback(
        (event) => {
            setAdjustPointsStatus({
                ...adjustPointsStatus,
                [event.target.name]: event.target.value,
            });
        },
        [adjustPointsStatus, setAdjustPointsStatus]
    );

    const onSelectMerchant = useCallback(
        (e) => {
            if (selectedLocation.length !== 0) {
                setSelectedLocation([]);
            }
            setSelectedMerchant(e);
        },
        [selectedLocation.length, setSelectedMerchant, setSelectedLocation]
    );

    const onAdjustPoints = useCallback(async () => {
        try {
            const formValid = formRef.current.checkValidity();
            if (formValid) {
                const payload = {
                    memberId: selectedMember?._id,
                    merchantId: selectedMerchant[0]?.merchantId,
                    merchantLocationId: selectedLocation[0]?.locationId,
                    notes: adjustPointsStatus.notes,
                    pointsAmount: adjustPointsStatus.pointsAmount,
                    transactionSubTypeId: selectedSubTransactionType[0]?.id,
                    transactionDate: moment().toISOString(),
                };

                setIsAdjusting(true);
                const adjustedRes = await adjustPointKlip(payload);
                setIsAdjusting(false);
                toast.success(
                    `Successfully adjusted ${
                        adjustPointsStatus.pointsAmount + " " || ""
                    }points.`
                );
                onHide(null, adjustedRes);
            } else {
                setValidated(true);
            }
        } catch (e) {
            console.error(e);
            setIsAdjusting(false);
            toast.error(
                <div>
                    Failed to adjust points!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        adjustPointsStatus.notes,
        adjustPointsStatus.pointsAmount,
        selectedLocation,
        selectedMember?._id,
        selectedMerchant,
        selectedSubTransactionType,
        onHide,
        setIsAdjusting,
        setValidated,
    ]);

    return (
        <Modal
            className="adjust-points-modal-view"
            show={show}
            size="lg"
            onHide={
                isAdjusting
                    ? () => {
                        /* Placeholder for empty arrow function body. */
                    }
                    : onHide
            }
            centered
            backdrop="static"
        >
            <Modal.Header
                className="modal-header-font"
                closeButton={!isAdjusting}
            >
                Adjust Points
            </Modal.Header>
            <Modal.Body className="mt-3">
                <Form noValidate validated={validated} ref={formRef}>
                    <div className="mb-3">
                        <Form.Group controlId="merchant">
                            <Form.Label className="d-flex align-items-center">
                                Merchant
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Select
                                id="merchant-select"
                                labelKey="name"
                                clearButton
                                selected={selectedMerchant}
                                placeholder={
                                    merchantsWithAdjustPointsOption.length === 0
                                        ? "Must be Active and must have 'adjustPoints' option enabled."
                                        : "Select a merchant..."
                                }
                                options={merchantsWithAdjustPointsOption}
                                disabled={
                                    isAdjusting ||
                                    merchantsWithAdjustPointsOption.length === 0
                                }
                                onChange={onSelectMerchant}
                                required
                            />
                        </Form.Group>
                        <Form.Group controlId="merchantLocation">
                            <Form.Label className="d-flex align-items-center">
                                Merchant Location
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Select
                                id="select-location"
                                labelKey="name"
                                clearButton
                                selected={selectedLocation}
                                placeholder={
                                    locations.length === 0
                                        ? "No merchant locations found."
                                        : "Select a location..."
                                }
                                options={locations}
                                disabled={isAdjusting || locations.length === 0}
                                onChange={setSelectedLocation}
                                required
                            />
                            {selectedMerchant.length === 0 && (
                                <small className="font-smaller text-orange">
                                    * Please select a merchant to list down the
                                    merchant's locations.
                                </small>
                            )}
                            {selectedMerchant.length !== 0 &&
                                locations.length === 0 && (
                                    <small className="font-smaller text-danger">
                                        * Locations must be Active and must have
                                        'adjustPoints' option enabled.
                                    </small>
                                )}
                        </Form.Group>
                        <Form.Group
                            className="transaction-types-widget-view"
                            controlId="subTransactionType"
                        >
                            <Form.Label className="d-flex align-items-center">
                                Sub Transaction Types
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Select
                                id="sub-transaction-type-selector"
                                name="subTransactionType"
                                labelKey="name"
                                clearButton
                                selected={selectedSubTransactionType}
                                placeholder={
                                    subTransactionTypeOptions.length === 0
                                        ? "No sub transaction types found."
                                        : "Select a sub transaction type..."
                                }
                                options={subTransactionTypeOptions}
                                disabled={
                                    isAdjusting ||
                                    subTransactionTypeOptions.length === 0
                                }
                                onChange={setSelectedSubTransactionType}
                                required
                            />
                            {subTransactionTypeOptions.length === 0 && (
                                <small className="font-smaller text-danger">
                                    * Sub transaction type is required.
                                </small>
                            )}
                        </Form.Group>
                        <Form.Group>
                            <Form.Label className="d-flex align-items-center">
                                Point Amount
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Control
                                className="custom-height input-font-size"
                                type="number"
                                placeholder="Enter points amount"
                                value={adjustPointsStatus.pointsAmount}
                                name="pointsAmount"
                                disabled={isAdjusting}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>
                        <Form.Group>
                            <Form.Label className="d-flex align-items-center">
                                Notes
                                <div className="ml-1 text-danger">*</div>
                            </Form.Label>
                            <Form.Control
                                className="input-font-size"
                                type="text"
                                as="textarea"
                                rows={5}
                                placeholder="Enter notes"
                                name="notes"
                                value={adjustPointsStatus.notes}
                                disabled={isAdjusting}
                                onChange={handleChange}
                                required
                            />
                        </Form.Group>
                    </div>
                </Form>
            </Modal.Body>
            <Modal.Footer className="d-flex justify-content-center">
                <Button
                    className="mr-3 button-font-size adjust-points-rounded-corners"
                    size="lg"
                    variant="outline-secondary"
                    disabled={isAdjusting}
                    onClick={onHide}
                >
                    Cancel
                </Button>
                <Button
                    className="ml-3 button-font-size adjust-points-rounded-corners"
                    size="lg"
                    variant="secondary"
                    disabled={isAdjusting}
                    onClick={onAdjustPoints}
                >
                    {isAdjusting ? "Adjusting..." : "Adjust Points"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};
export default AdjustPoints;
