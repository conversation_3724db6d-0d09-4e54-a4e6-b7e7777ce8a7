import React, {
    useContext,
    useRef,
    useState,
    useCallback,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext } from "Contexts";
import {
    MerchantLocationStatusObj,
    MerchantStatus,
    TransactionTypes,
} from "Data";
import { useToggle } from "Hooks";
import {
    redeemPointsWithOtpKlip,
    redeemPointsWithOtpRequestKlip,
} from "Services";
import { toTitleCase } from "Utils";
import { FindMembersContext } from "../../context/FindMembersContext";

import "./RedeemPoints.scss";

const RedeemPoints = ({ show, onHide }) => {
    const { merchants, merchantLocations, subTransactionTypes } =
        useContext(DataContext);
    const { selectedMember } = useContext(FindMembersContext);
    const [isRequesting, setIsRequesting] = useState(false);
    // TODO: [MLS-3528] Implement resend otp in Find Members -> Member Profile -> Redeem Points
    // const [isResending, setIsResending] = useState(false);
    const [isRedeeming, setIsRedeeming] = useState(false);
    const [validated, setValidated] = useState(false);
    const [redemptionToken, setRedemptionToken] = useState("");
    const [isRequestingOtp, toggleIsRequestingOtp] = useToggle(false);
    const [selectedMerchant, setSelectedMerchant] = useState([]);
    const [selectedLocation, setSelectedLocation] = useState([]);
    const [selectedSubTransactionType, setSelectedSubTransactionType] =
        useState([]);
    const [redeemPointStatus, setRedeemPointStatus] = useState({
        pointsAmount: "",
        notes: "",
        otpCode: "",
    });
    const formRef = useRef();

    const merchantsWithRedeemPointsOption = useMemo(
        () =>
            merchants
                .filter(
                    (merchant) =>
                        merchant?.status === MerchantStatus.ACTIVE &&
                        merchant?.options?.redeemPoints
                )
                .map((filteredMerchant) => ({
                    merchantId: filteredMerchant?._id || "Unknown Id",
                    name: toTitleCase(
                        filteredMerchant?.merchantName || "Unknown merchant"
                    ),
                })),
        [merchants]
    );

    const locations = useMemo(
        () =>
            (selectedMerchant.length !== 0 &&
                merchantLocations[selectedMerchant[0]?.merchantId] &&
                Object.values(
                    merchantLocations[selectedMerchant[0]?.merchantId]
                )
                    .filter(
                        (location) =>
                            location?.status ===
                                MerchantLocationStatusObj.ACTIVE &&
                            location?.options?.redeemPoints &&
                            location?.isShownInCreateTransactions
                    )
                    .map((filteredLocation) => ({
                        locationId: filteredLocation?._id || "Unknown Id",
                        name: toTitleCase(
                            filteredLocation?.locationName || "Unknown location"
                        ),
                    }))) ||
            [],
        [merchantLocations, selectedMerchant]
    );

    const subTransactionTypeOptions = useMemo(
        () =>
            subTransactionTypes
                .filter(
                    (subTransactionType) =>
                        subTransactionType?.transactionType ===
                        TransactionTypes.REDEMPTION
                )
                .map((fST) => ({
                    id: fST?._id || "Unknown Id",
                    name: toTitleCase(
                        fST?.name || "Unknown sub transaction type"
                    ),
                })) || [],
        [subTransactionTypes]
    );

    const handleChange = useCallback(
        (event) => {
            setRedeemPointStatus({
                ...redeemPointStatus,
                [event.target.name]: event.target.value,
            });
        },
        [redeemPointStatus, setRedeemPointStatus]
    );

    const onSelectMerchant = useCallback(
        (e) => {
            if (selectedLocation.length !== 0) {
                setSelectedLocation([]);
            }
            setSelectedMerchant(e);
        },
        [selectedLocation.length, setSelectedMerchant, setSelectedLocation]
    );

    const onRequestOTPCode = useCallback(
        async (event) => {
            event.preventDefault();
            try {
                const formValid = formRef.current.checkValidity();
                if (formValid) {
                    if (selectedLocation.length === 0) {
                        throw new Error("Merchant location is required!");
                    }

                    if (selectedSubTransactionType.length === 0) {
                        throw new Error(
                            "A sub transaction type must be selected."
                        );
                    }

                    setIsRequesting(true);
                    const token = await redeemPointsWithOtpRequestKlip({
                        notes: redeemPointStatus.notes,
                        pointsAmount: redeemPointStatus.pointsAmount,
                        memberId: selectedMember._id,
                        merchantId: selectedMerchant[0]?.merchantId,
                        merchantLocationId: selectedLocation[0]?.locationId,
                        transactionSubTypeId: selectedSubTransactionType[0]?.id,
                    });
                    setRedemptionToken(token?.otpToken);
                    setIsRequesting(false);
                    toast.success(
                        "Successfully sent an OTP to the member's registered mobile number."
                    );
                    toggleIsRequestingOtp();
                } else {
                    setValidated(true);
                }
            } catch (e) {
                console.error(e);
                setIsRequesting(false);
                toast.error(
                    <div>
                        Failed to request OTP code!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            selectedMerchant,
            selectedLocation,
            selectedSubTransactionType,
            selectedMember._id,
            redeemPointStatus.notes,
            redeemPointStatus.pointsAmount,
            toggleIsRequestingOtp,
            setIsRequesting,
            setRedemptionToken,
            setValidated,
        ]
    );

    // TODO: [MLS-3528] Implement resend otp in Find Members -> Member Profile -> Redeem Points
    // const onResendOtp = useCallback(async () => {
    //     try {
    //         setIsResending(true);
    //         const token = await redeemPointsWithOtpRequestKlip({
    //             notes: redeemPointStatus.notes,
    //             pointsAmount: redeemPointStatus.pointsAmount,
    //             memberId: selectedMember._id,
    //             merchantId: selectedMerchant[0]?.merchantId,
    //             merchantLocationId: selectedLocation[0]?.locationId,
    //             transactionSubTypeId: selectedSubTransactionType[0]?.id,
    //         });
    //         setRedemptionToken(token?.otpToken);
    //         setIsResending(false);
    //         toast.success(
    //             "Successfully resent an OTP to the member's registered mobile number."
    //         );
    //     } catch (e) {
    //         console.error(e);
    //         setIsRequesting(false);
    //         toast.error(
    //             <div>
    //                 Failed to resend OTP code!
    //                 <br />
    //                 {e.message
    //                     ? `Error: ${e.message}`
    //                     : "Please try again later."}
    //             </div>
    //         );
    //     }
    // }, [
    //     redeemPointStatus.notes,
    //     redeemPointStatus.pointsAmount,
    //     selectedLocation,
    //     selectedMember._id,
    //     selectedMerchant,
    //     selectedSubTransactionType,
    //     setIsResending,
    // ]);

    const onRedeemPoints = useCallback(async () => {
        try {
            const formValid = formRef.current.checkValidity();
            if (formValid) {
                setIsRedeeming(true);
                const redeemedRes = await redeemPointsWithOtpKlip({
                    redemptionToken: redemptionToken,
                    otpCode: redeemPointStatus.otpCode,
                });
                setIsRedeeming(false);
                toast.success(
                    `Successfully redeemed ${
                        redeemPointStatus.pointsAmount + " " || ""
                    }points.`
                );
                onHide(null, redeemedRes);
            } else {
                setValidated(true);
            }
        } catch (e) {
            console.error(e);
            setIsRedeeming(false);
            toast.error(
                <div>
                    Failed to redeem points!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        onHide,
        redeemPointStatus,
        redemptionToken,
        setIsRedeeming,
        setValidated,
    ]);

    return (
        <Modal
            className="redeem-points-modal-view"
            show={show}
            size="lg"
            onHide={
                isRequesting || isRedeeming
                    ? () => {
                        /* Placeholder for empty arrow function body. */
                    }
                    : onHide
            }
            centered
            backdrop="static"
        >
            <Modal.Header
                className="modal-header-font"
                closeButton={!(isRequesting || isRedeeming)}
            >
                {!isRequestingOtp ? "Redeem Points" : "Point Redeem with OTP"}
            </Modal.Header>
            <Modal.Body className="mt-3">
                <Form noValidate validated={validated} ref={formRef}>
                    {!isRequestingOtp ? (
                        <>
                            <div className="mb-3">
                                <Form.Group controlId="merchant">
                                    <Form.Label className="d-flex align-items-center">
                                        Merchant
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Select
                                        id="merchant-select"
                                        labelKey="name"
                                        clearButton
                                        selected={selectedMerchant}
                                        placeholder={
                                            merchantsWithRedeemPointsOption.length ===
                                            0
                                                ? "Must be Active and must have 'redeemPoints' option enabled."
                                                : "Select a merchant..."
                                        }
                                        options={
                                            merchantsWithRedeemPointsOption
                                        }
                                        disabled={
                                            isRequesting ||
                                            merchantsWithRedeemPointsOption.length ===
                                                0
                                        }
                                        onChange={onSelectMerchant}
                                        required
                                    />
                                </Form.Group>
                                <Form.Group controlId="merchantLocation">
                                    <Form.Label className="d-flex align-items-center">
                                        Merchant Location
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Select
                                        id="select-location"
                                        labelKey="name"
                                        clearButton
                                        selected={selectedLocation}
                                        placeholder={
                                            locations.length === 0
                                                ? "No merchant locations found."
                                                : "Select a location..."
                                        }
                                        options={locations}
                                        disabled={
                                            isRequesting ||
                                            locations.length === 0
                                        }
                                        onChange={setSelectedLocation}
                                        required
                                    />
                                    {selectedMerchant.length === 0 && (
                                        <small className="font-smaller text-orange">
                                            * Please select a merchant to list
                                            down the merchant's locations.
                                        </small>
                                    )}
                                    {selectedMerchant.length !== 0 &&
                                        locations.length === 0 && (
                                            <small className="font-smaller text-danger">
                                                * Locations must be Active and
                                                must have 'redeemPoints' option
                                                enabled.
                                            </small>
                                        )}
                                </Form.Group>
                                <Form.Group
                                    className="transaction-types-widget-view"
                                    controlId="subTransactionType"
                                >
                                    <Form.Label className="d-flex align-items-center">
                                        Sub Transaction Types
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Select
                                        id="sub-transaction-type-selector"
                                        name="subTransactionType"
                                        labelKey="name"
                                        clearButton
                                        selected={selectedSubTransactionType}
                                        placeholder={
                                            subTransactionTypeOptions.length ===
                                            0
                                                ? "No sub transaction types found."
                                                : "Select a sub transaction type..."
                                        }
                                        options={subTransactionTypeOptions}
                                        disabled={
                                            isRequesting ||
                                            subTransactionTypeOptions.length ===
                                                0
                                        }
                                        onChange={setSelectedSubTransactionType}
                                        required
                                    />
                                    {subTransactionTypeOptions.length === 0 && (
                                        <small className="font-smaller text-danger">
                                            * Sub transaction type is required.
                                        </small>
                                    )}
                                </Form.Group>
                                <Form.Group>
                                    <Form.Label className="d-flex align-items-center">
                                        Point Amount
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Control
                                        className="custom-height input-font-size"
                                        type="number"
                                        placeholder="Enter points amount"
                                        value={redeemPointStatus.pointsAmount}
                                        name="pointsAmount"
                                        disabled={isRequesting}
                                        onChange={handleChange}
                                        required
                                    />
                                </Form.Group>
                                <Form.Group>
                                    <Form.Label className="d-flex align-items-center">
                                        Notes
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Control
                                        className="input-font-size"
                                        type="text"
                                        as="textarea"
                                        rows={5}
                                        placeholder="Enter notes"
                                        name="notes"
                                        value={redeemPointStatus.notes}
                                        disabled={isRequesting}
                                        onChange={handleChange}
                                        required
                                    />
                                </Form.Group>
                            </div>
                        </>
                    ) : (
                        <>
                            <Form.Group>
                                <Form.Label>OTP</Form.Label>
                                <Form.Control
                                    className="custom-height input-font-size"
                                    name="otpCode"
                                    type="text"
                                    placeholder="Enter OTP"
                                    value={redeemPointStatus.otpCode}
                                    disabled={isRequesting || isRedeeming}
                                    onChange={handleChange}
                                    required
                                />
                            </Form.Group>
                            {/* 
                                // TODO: [MLS-3528] Implement resend otp in Find Members -> Member Profile -> Redeem Points 
                            */}
                            {/* <div className="text-center mb-3">
                                <Button
                                    className="btn shadow-none"
                                    size="lg"
                                    variant="link"
                                    disabled={
                                        isRequesting ||
                                        isRedeeming ||
                                        isResending
                                    }
                                    onClick={onResendOtp}
                                >
                                    {isResending
                                        ? "Resending Otp..."
                                        : "Resend"}
                                </Button>
                            </div> */}
                        </>
                    )}
                </Form>
            </Modal.Body>
            <Modal.Footer className="d-flex justify-content-center">
                <Button
                    className="mr-3 button-font-size redeem-points-rounded-corners"
                    size="lg"
                    variant="outline-secondary"
                    disabled={isRequesting || isRedeeming}
                    onClick={onHide}
                >
                    Cancel
                </Button>
                <Button
                    className="ml-3 button-font-size redeem-points-rounded-corners"
                    size="lg"
                    variant="secondary"
                    disabled={isRequesting || isRedeeming}
                    onClick={
                        !isRequestingOtp ? onRequestOTPCode : onRedeemPoints
                    }
                >
                    {!isRequestingOtp ? (
                        <>
                            {isRequesting && "Requesting OTP..."}
                            {!isRequesting && "Request OTP"}
                        </>
                    ) : (
                        <>
                            {isRedeeming && "Redeeming Points..."}
                            {!isRedeeming && "Redeem Points"}
                        </>
                    )}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};
export default RedeemPoints;
