import React, { useContext } from "react";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { RewardTabValues } from "Data";
import { toTitleCase } from "Utils";
import { FindMembersContext } from "Components/findMembers/context/FindMembersContext";
import RedeemedRewardsView from "./tabViews/redeemed/RedeemedRewardsView";
import RewardsTable from "./tabViews/shared/rewardsTable/RewardsTable";

const RewardTabs = () => {
    const { isLoadingMember, selectedMember, tab, setTab, isLoadingRewards } = useContext(FindMembersContext);

    return(
        <div>
            <Tabs
                className="
                    reward-tabs-font
                    d-flex 
                    align-items-center
                    justify-content-around
                    mb-3
                "
                activeKey={tab}
                transition={false}
                id="find-member-rewards-tabs"
                onSelect={setTab}
            >
                <Tab
                    eventKey={RewardTabValues.UNLOCKED}
                    title={`${toTitleCase(RewardTabValues.UNLOCKED)} Rewards`}
                    disabled={isLoadingMember || isLoadingRewards || !selectedMember}
                />
                <Tab
                    eventKey={RewardTabValues.REDEEMED}
                    title={`${toTitleCase(RewardTabValues.REDEEMED)} Rewards`}
                    disabled={isLoadingMember || isLoadingRewards || !selectedMember}
                />
                <Tab
                    eventKey={RewardTabValues.LOCKED}
                    title={`${toTitleCase(RewardTabValues.LOCKED)} Rewards`}
                    disabled={isLoadingMember || isLoadingRewards || !selectedMember}
                />
            </Tabs>
            <div>
                {tab === RewardTabValues.REDEEMED ?
                    <RedeemedRewardsView />
                    :
                    <RewardsTable />
                }
            </div>
        </div>
    );
};

export default RewardTabs;