import React, { useCallback, useState } from "react";
import { toast } from "react-toastify";
import { Button, FormControl, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { claimReward } from "Services";

import "./ClaimReward.scss";

const ClaimReward = ({
    show,
    onHide,
}) => {
    const [isClaiming, setIsClaiming] = useState(false);
    const [code, setCode] = useState("");

    const onChangeCode = useCallback(e => setCode(e.currentTarget.value), [setCode]);

    const onClaimReward = useCallback(async () => {
        try {
            setIsClaiming(true);
            const response = await claimReward({ voucherCode:code });
            toast.success("Reward claim was successful.");
            onHide(null, response);
            setIsClaiming(false);
        } catch (e) {
            console.error(e);
            toast.error(<div>Failed to Claim <PERSON>ward!<br/>Error: {e.message || "Please try again."}</div>);
            setIsClaiming(false);
        }
    }, [code, onHide, setIsClaiming]);

    return (
        <Modal
            show={show}
            onHide={isClaiming ?
                () => { /* // * To disable close modal action while reward is claiming. */ }
                : onHide
            }
            size="lg"
            centered
            className="claim-reward-modal-view"
        >
            <Modal.Header closeButton={!isClaiming}>Claim Reward</Modal.Header>
            <Modal.Body>
                <div className="">
                    <div>Coupon Code <span className="text-danger">*</span></div>
                    <FormControl
                        className="my-3 coupon-code-input"
                        placeholder="Enter Coupon Code"
                        onChange={onChangeCode}
                        value={code}
                    />
                </div>
            </Modal.Body>
            <Modal.Footer className="d-flex justify-content-center">
                <Button
                    className="mr-3 button-font-size claim-reward-rounded-corners"
                    size="lg"
                    variant="outline-secondary"
                    disabled={isClaiming}
                    onClick={onHide}
                >
                    Cancel
                </Button>
                <Button
                    className="ml-3 button-font-size claim-reward-rounded-corners"
                    size="lg"
                    variant="secondary"
                    disabled={isClaiming || !code}
                    onClick={onClaimReward}
                >
                    {isClaiming ? "Claiming..." : "Claim Reward"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ClaimReward;
