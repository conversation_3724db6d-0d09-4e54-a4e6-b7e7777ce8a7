import React from "react";
import { Route, Switch } from "react-router-dom";
import AuditLogPage from "./auditLog/AuditLogPage";
import FraudPage from "./FraudPage";
import { AccessPermissionModuleNames, AccessPermissionModules } from "../../Data";
import { FraudContextProvider } from "./context/FraudContext";
import IncidentInformation from "./fraudIncidents/incidentInformation/IncidentInformation";
import UnauthorizedAccessControl from "../utils/unauthorizedAccessControl/UnauthorizedAccessControl";


const FraudulencePage = () => {
    return (
        <Switch>
            <Route name="AuditLogPage" exact path="/fraudulence/audit-log" component={AuditLogPage} />
            <FraudContextProvider>
                <Route name="FraudPage" exact path="/fraudulence/fraud" component={FraudPage} />
                <Route name="FraudPage" exact path="/fraudulence/fraud/:id">
                    <UnauthorizedAccessControl
                        actionList={
                            {
                                [`${AccessPermissionModuleNames.INCIDENT}`]: [
                                    AccessPermissionModules[AccessPermissionModuleNames.INCIDENT].actions.GetIncident,
                                    AccessPermissionModules[AccessPermissionModuleNames.INCIDENT].actions.UpdateIncident,
                                ],
                                [`${AccessPermissionModuleNames.RULE_TYPE}`]: [
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE_TYPE].actions.ListRuleTypes,
                                ],
                                [`${AccessPermissionModuleNames.RULE}`]: [
                                    AccessPermissionModules[AccessPermissionModuleNames.RULE].actions.ListRules,
                                ],
                            }
                        }
                        logic={"OR"}
                    >
                        <IncidentInformation />
                    </UnauthorizedAccessControl>
                </Route>
            </FraudContextProvider>
        </Switch>
    );
};
export default FraudulencePage;
