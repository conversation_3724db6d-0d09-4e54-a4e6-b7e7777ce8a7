import React from "react";
import { create } from "react-test-renderer";
import { DataContext, UserContext } from "Contexts";
import AuditLog from "./AuditLog";

const regionId = "6128e3537ed841e246e2e396";
const merchants = [
    {
        _id: "619541b1423d24e396dbf07e",
        organizationId: "6128e3537ed841e246e2e394",
        regionId: "6137a9fff48b8eb1845c78cf",
        merchantName: "Price's Electronics",
        merchantLogoImageUrl:
            "https://api.loyaltybeta.cxforge.com/api/coreservice/files/52b15b00-47cf-11ec-8acd-ddef44280f8a.jpeg",
        contact: {
            name: "Price's Electronics",
            mobileNumber: "***********",
            email: "<EMAIL>",
            address: {
                line1: "21",
                line3: "12",
                city: "Greenland",
                stateOrProvince: "<PERSON> Andrew",
            },
        },
        status: "ACTIVE",
        type: "EXTERNAL",
        locationsCount: 4,
        countryName: "Barbados",
        businessRegistrationNumber: "102360023",
        options: {
            enroll: true,
            earn: true,
            redeemPoints: true,
            redeemRewards: true,
            refund: true,
            void: true,
            claimReward: false,
        },
        createdBy: "616680f35958bc8372a1ee88",
        billingContacts: [
            {
                name: "Gongalegoda Banda",
                mobileNumber: "250234",
                email: "<EMAIL>",
                address: {
                    line1: "1234",
                    line2: "1234",
                    line3: "1234",
                    city: "black heart",
                    stateOrProvince: "black heart SP",
                    zipOrPostcode: "12345432",
                },
            },
        ],
        technicalContacts: [
            {
                name: "Gongalegoda Banda",
                mobileNumber: "250234",
                email: "<EMAIL>",
                address: {
                    line1: "1234",
                    line2: "1234",
                    line3: "1234",
                    city: "black heart",
                    stateOrProvince: "black heart SP",
                    zipOrPostcode: "12345432",
                },
            },
        ],
        createdOn: "2021-11-17T17:53:53.763Z",
        updatedOn: "2022-04-11T06:57:00.785Z",
        __v: 0,
        updatedBy: "616680f35958bc8372a1ee88",
        visibleForNearestLocations: true,
        countryISO2Code: "BB",
    },
];

describe("AuditLog component snapshot", () => {
    test("Matches the snapshot", () => {
        const component = create(
            <UserContext.Provider value={regionId}>
                <DataContext.Provider value={{ merchants }}>
                    <AuditLog />
                </DataContext.Provider>
            </UserContext.Provider>
        );
        expect(component.toJSON()).toMatchSnapshot();
    });
});
