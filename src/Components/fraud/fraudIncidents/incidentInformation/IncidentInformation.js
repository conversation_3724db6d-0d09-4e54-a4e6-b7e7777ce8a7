import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState,
} from "react";
import { useHistory, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import {
    But<PERSON>,
    Heading,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faAngleLeftB,
    faBuilding,
    faCalendar,
    faCoins,
    faCreditCard,
    faExport,
    faMap,
    faObjectGroup,
    faSync,
} from "FaICIconMap";
import { DataContext, UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    FraudIncidentStatus,
    TransactionTypeColorCode,
} from "Data";
import {
    exportListOfTransactions,
    getFraudIncidentById,
    getFraudIncidentTransactions,
    getMemberById,
} from "Services";
import {
    defaultColumnTemplate,
    downloadLink,
    formatToCommonReadableFormat,
    getTruncatedStringWithTooltip,
    toTitleCase,
} from "Utils";
import BaseLayout from "Layout/BaseLayout";
import { LoadingComponent } from "Components/utils";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import UnauthorizedAccessControl from "../../../utils/unauthorizedAccessControl/UnauthorizedAccessControl";
import FraudTableView from "../../shared/fraudTableView/FraudTableView";
import { FraudFunctionalAccessControl } from "../../FraudFunctionalAccessControl";
import IncidentProfileDetails from "./IncidentProfileDetails";
import ResolveFraudIncident from "../resolveFraudIncident/ResolveFraudIncident";

import "./IncidentInformation.scss";

const columnDataProperties = [
    {
        dataField: "transactionId",
        displayName: "Transaction Id",
        icon: faCreditCard,
    },
    {
        dataField: "cardNo",
        displayName: "Card Number",
        headerStyle: { width: "18%" },
        icon: faCreditCard,
    },
    {
        dataField: "transactionOn",
        displayName: "Transaction On",
        headerStyle: { width: "15%" },
        icon: faCalendar,
    },
    {
        dataField: "points",
        displayName: "Points",
        headerStyle: { width: "10%" },
        icon: faCoins,
    },
    { dataField: "transactionType", displayName: "Type", icon: faObjectGroup },
    {
        dataField: "merchant",
        displayName: "Merchant",
        icon: faBuilding,
    },
    {
        dataField: "merchantLocation",
        displayName: "Merchant Location",
        icon: faMap,
    },
];

const columns = columnDataProperties.map((cDP) =>
    defaultColumnTemplate({ ...cDP })
);

const getTransactionType = (transactionType) =>
    applyBadgeStyling({
        text: transactionType ? toTitleCase(transactionType) : "",
        variant: TransactionTypeColorCode[transactionType],
    });

const defaultSkip = 1,
    defaultLimit = 25;

const IncidentInformation = () => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const { merchants, merchantLocations } = useContext(DataContext);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [isExporting, setIsExporting] = useState(false);
    const [data, setData] = useState([]);
    const [incidentsData, setIncidentsData] = useState({});
    const [isLoadingIncidentsData, setIsLoadingIncidentsData] = useState(false);
    const [isLoadingMemberData, setIsLoadingMemberData] = useState(false);
    const [totalCount, setTotalCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [isReloading, setIsReloading] = useState(false);
    const [showResolveIncident, setShowResolveIncident] = useState(false);
    const { id } = useParams();
    const history = useHistory();

    const isAllowedToViewMember = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.MEMBER,
                AccessPermissionModules[AccessPermissionModuleNames.MEMBER]
                    .actions.GetMember
            ),
        [isAuthorizedForAction]
    );

    const merchantLocation = useCallback(
        (merchantId, merchantLocationId) =>
            merchantLocations?.[merchantId]?.[merchantLocationId]
                ?.locationName ??
            merchantLocationId ??
            "",
        [merchantLocations]
    );

    const loadIncidentData = useCallback(async () => {
        try {
            setIsLoadingIncidentsData(true);
            const fraudIncidentData = await getFraudIncidentById(id);
            setIncidentsData(fraudIncidentData);
            setIsLoadingIncidentsData(false);
        } catch (e) {
            setIsLoadingIncidentsData(false);
            setIncidentsData({});
            console.error(e);
            toast.error(
                <div>
                    Failed to load fraud incident data!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [id, setIsLoadingIncidentsData, setIncidentsData]);

    const loadIncidentMemberProfile = useCallback(
        async (memberId) => {
            try {
                setIsLoadingMemberData(true);
                const member = await getMemberById(memberId);
                setIncidentsData((prevIncidentData) => ({
                    ...prevIncidentData,
                    memberData: member,
                }));
                setIsLoadingMemberData(false);
            } catch (e) {
                setIsLoadingMemberData(false);
                console.error(e);
                toast.error(
                    <div>
                        Failed to load fraud incident's member data!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [setIncidentsData, setIsLoadingMemberData]
    );

    const loadIncidentTransactions = useCallback(
        async ({ skip, limit }) => {
            try {
                setData([]);
                setTotalCount(0);
                setIsLoading(true);
                const transactions = await getFraudIncidentTransactions({
                    limit: limit,
                    skip: (skip - 1) * limit,
                    incidentId: id,
                });
                setTotalCount(transactions.total);
                setData(
                    transactions.items.length !== 0
                        ? transactions.items.map(
                            ({
                                transactionOn,
                                transactionId,
                                cardNo,
                                redeemablePoints,
                                type,
                                merchantLocationId,
                                merchantId,
                                ...rest
                            }) => ({
                                transactionId: getTruncatedStringWithTooltip({
                                    value: transactionId,
                                    valueMaxLength: 18,
                                    customUnknownValue: applyBadgeStyling({
                                        customValue:
                                            "Transaction id not found.",
                                    }),
                                }),
                                cardNo: getTruncatedStringWithTooltip({
                                    value: cardNo,
                                    valueMaxLength: 18,
                                    customUnknownValue: applyBadgeStyling({
                                        customValue: "Card number not found.",
                                    }),
                                }),
                                transactionOn: getTruncatedStringWithTooltip({
                                    value: transactionOn
                                        ? formatToCommonReadableFormat(
                                            transactionOn
                                        )
                                        : "",
                                    customUnknownValue: applyBadgeStyling({
                                        customValue:
                                            "Transaction date not found.",
                                    }),
                                }),
                                points:
                                    redeemablePoints ??
                                    applyBadgeStyling({
                                        customValue: "Points not found.",
                                    }),
                                transactionType:
                                    getTransactionType(type) ??
                                    applyBadgeStyling({
                                        customValue:
                                            "Transaction type not found.",
                                    }),
                                merchant: getTruncatedStringWithTooltip({
                                    value:
                                        merchants.length !== 0
                                            ? merchants.filter(
                                                (merchant) =>
                                                    merchant._id ===
                                                    merchantId
                                            )[0]?.merchantName ?? merchantId
                                            : "",
                                    valueMaxLength: 18,
                                    customUnknownValue: applyBadgeStyling({
                                        customValue: "Merchant not found.",
                                    }),
                                }),
                                merchantLocation:
                                    getTruncatedStringWithTooltip({
                                        value: merchantLocation(
                                            merchantId,
                                            merchantLocationId
                                        ),
                                        valueMaxLength: 18,
                                        customUnknownValue: applyBadgeStyling(
                                            {
                                                customValue:
                                                    "Location not found.",
                                            }
                                        ),
                                    }),
                                ...rest,
                            })
                        )
                        : []
                );
                setIsLoading(false);
            } catch (e) {
                console.error(e);
                setData([]);
                setTotalCount(0);
                toast.error(
                    <div>
                        Failed to load fraud incident's transactions!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
                setIsLoading(false);
            }
        },
        [setData, setIsLoading, setTotalCount, id, merchantLocation, merchants]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadIncidentTransactions({
                skip: newSkip,
                limit,
            });
        },
        [limit, loadIncidentTransactions, setSkip]
    );

    const onExportClick = useCallback(async () => {
        try {
            setIsExporting(true);
            const url = await exportListOfTransactions({ incidentId: id });
            downloadLink(url.url);
            setIsExporting(false);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to export fraud incident's transactions!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
            setIsExporting(false);
        }
    }, [setIsExporting, id]);

    const resetAllIncidentRelatedData = useCallback(async () => {
        await Promise.all([
            loadIncidentData(),
            loadIncidentTransactions({
                skip: defaultSkip,
                limit: defaultLimit,
            }),
        ]);
        setLimit(defaultLimit);
        setSkip(defaultSkip);

        if (isAllowedToViewMember && incidentsData?.memberId)
            await loadIncidentMemberProfile(incidentsData.memberId);
    }, [
        isAllowedToViewMember,
        incidentsData?.memberId,
        loadIncidentData,
        loadIncidentTransactions,
        loadIncidentMemberProfile,
        setLimit,
        setSkip,
    ]);

    const onShowResolveFraudIncident = useCallback(() => {
        setShowResolveIncident(true);
    }, [setShowResolveIncident]);

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadIncidentTransactions({
                skip: defaultSkip,
                limit: newLimit,
            });
        },
        [loadIncidentTransactions, setLimit]
    );

    const onHideResolveFraudIncident = useCallback(
        (e, data) => {
            if (data) {
                resetAllIncidentRelatedData();
            }
            setShowResolveIncident(false);
        },
        [resetAllIncidentRelatedData, setShowResolveIncident]
    );

    const onReloadIncidentData = useCallback(async () => {
        setIsReloading(true);
        await resetAllIncidentRelatedData();
        setIsReloading(false);
    }, [resetAllIncidentRelatedData, setIsReloading]);

    const onClickBack = useCallback(() => {
        history.push({
            pathname: "/fraudulence/fraud",
        });
    }, [history]);

    useEffect(() => {
        if (
            id &&
            FraudFunctionalAccessControl({
                isAuthorizedForAction: isAuthorizedForAction,
                actionList: {
                    [`${AccessPermissionModuleNames.INCIDENT}`]: [
                        AccessPermissionModules[
                            AccessPermissionModuleNames.INCIDENT
                        ].actions.ListIncidents,
                    ],
                },
                logic: "AND",
            })
        ) {
            loadIncidentData();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [id, isAuthorizedForAction]);

    useEffect(() => {
        if (incidentsData?.memberId && isAllowedToViewMember) {
            loadIncidentMemberProfile(incidentsData.memberId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [incidentsData.memberId, isAuthorizedForAction]);

    useEffect(() => {
        loadIncidentTransactions({
            skip: defaultSkip,
            limit: defaultLimit,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <BaseLayout
            containerClassName="incident-profile"
            topLeft={
                <div className="d-flex align-items-center">
                    <Heading text="Incident Information" />
                    <div className="ml-3">
                        {isReloading ? (
                            <small className="ml-3 text-primary">
                                Reloading...
                            </small>
                        ) : (
                            <Button
                                className="shadow-none"
                                size="sm"
                                variant="link"
                                disabled={
                                    isLoading ||
                                    isExporting ||
                                    isLoadingIncidentsData ||
                                    isLoadingMemberData
                                }
                                onClick={onReloadIncidentData}
                            >
                                <IcIcon
                                    size="md"
                                    className="mr-2"
                                    icon={faSync}
                                />
                                Reload Incident Data
                            </Button>
                        )}
                    </div>
                </div>
            }
            topRight={
                <div className="d-flex align-items-center">
                    <div className="d-flex justify-content-end">
                        <Button
                            className="btn shadow-none"
                            variant="link"
                            disabled={
                                isLoading ||
                                isExporting ||
                                isLoadingIncidentsData ||
                                isLoadingMemberData
                            }
                            onClick={onClickBack}
                        >
                            <div className="d-flex align-items-center">
                                <IcIcon icon={faAngleLeftB} size="lg" />
                                Back
                            </div>
                        </Button>
                    </div>
                    {incidentsData?.status === FraudIncidentStatus.PENDING && (
                        <UnauthorizedAccessControl
                            actionList={{
                                [`${AccessPermissionModuleNames.INCIDENT}`]: [
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.INCIDENT
                                    ].actions.UpdateIncident,
                                ],
                            }}
                            logic={"OR"}
                            renderEmpty="true"
                        >
                            <div className="d-flex justify-content-end">
                                <Button
                                    variant="primary"
                                    size="sm"
                                    disabled={
                                        isLoading ||
                                        isExporting ||
                                        isLoadingIncidentsData ||
                                        isLoadingMemberData
                                    }
                                    onClick={onShowResolveFraudIncident}
                                >
                                    Resolve Fraud Incident
                                </Button>
                            </div>
                        </UnauthorizedAccessControl>
                    )}
                </div>
            }
            bottom={
                isLoadingIncidentsData || isReloading ? (
                    <LoadingComponent />
                ) : (
                    <>
                        <UnauthorizedAccessControl
                            actionList={{
                                [`${AccessPermissionModuleNames.INCIDENT}`]: [
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.INCIDENT
                                    ].actions.GetIncident,
                                ],
                                [`${AccessPermissionModuleNames.RULE_TYPE}`]: [
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.RULE_TYPE
                                    ].actions.ListRuleTypes,
                                ],
                                [`${AccessPermissionModuleNames.RULE}`]: [
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.RULE
                                    ].actions.ListRules,
                                ],
                                [`${AccessPermissionModuleNames.MEMBER}`]: [
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.MEMBER
                                    ].actions.GetMember,
                                ],
                            }}
                            logic={"AND"}
                            renderEmpty="true"
                        >
                            <div className="w-100 px-3">
                                <IncidentProfileDetails
                                    incidentDetails={incidentsData}
                                    isLoadingIncidentsData={
                                        isLoadingIncidentsData
                                    }
                                    isLoadingIncidentsMemberData={
                                        isLoadingMemberData
                                    }
                                    isAllowedToViewMember={
                                        isAllowedToViewMember
                                    }
                                    onReloadFraudMember={
                                        loadIncidentMemberProfile
                                    }
                                />
                            </div>
                        </UnauthorizedAccessControl>
                        <div className="d-flex justify-content-between mt-5">
                            <div className="ml-1">
                                <h3 className="font-weight-bold">{`Total-${totalCount}`}</h3>
                            </div>
                            <UnauthorizedAccessControl
                                actionList={{
                                    [`${AccessPermissionModuleNames.INCIDENT}`]:
                                        [
                                            AccessPermissionModules[
                                                AccessPermissionModuleNames
                                                    .INCIDENT
                                            ].actions.ExportIncident,
                                        ],
                                }}
                                logic={"AND"}
                                renderEmpty="true"
                            >
                                <div>
                                    <Button
                                        variant="dark"
                                        size="sm"
                                        disabled={
                                            isLoading ||
                                            isExporting ||
                                            isLoadingIncidentsData ||
                                            isLoadingMemberData
                                        }
                                        onClick={onExportClick}
                                    >
                                        <IcIcon
                                            className="mr-2"
                                            size="md"
                                            icon={faExport}
                                        />
                                        {isExporting
                                            ? "Exporting..."
                                            : "Export Transaction"}
                                    </Button>
                                </div>
                            </UnauthorizedAccessControl>
                        </div>
                        <FraudTableView
                            columns={columns}
                            data={data}
                            sizePerPage={limit}
                            page={skip}
                            onChangePagination={onChangePagination}
                            onChangePageSize={onChangePageSize}
                            isLoading={isLoading}
                            totalCount={totalCount}
                        />
                        <UnauthorizedAccessControl
                            actionList={{
                                [`${AccessPermissionModuleNames.INCIDENT}`]: [
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.INCIDENT
                                    ].actions.UpdateIncident,
                                ],
                            }}
                            logic={"OR"}
                            renderEmpty="true"
                        >
                            {showResolveIncident && (
                                <ResolveFraudIncident
                                    show={showResolveIncident}
                                    fraudIncidentId={id}
                                    memberId={incidentsData?.memberId}
                                    memberStatus={
                                        incidentsData?.memberData?.status
                                    }
                                    onHide={onHideResolveFraudIncident}
                                />
                            )}
                        </UnauthorizedAccessControl>
                    </>
                )
            }
        />
    );
};

export default IncidentInformation;
