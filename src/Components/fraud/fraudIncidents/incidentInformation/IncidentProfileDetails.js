import React, { useContext, useMemo } from "react";
import {
    Badge,
    Button,
    Col,
    IcIcon,
    OverlayTrigger,
    Row,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faExternalLink, faSync } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    FraudIncidentStatusVariant,
    MemberPrimaryAttributeObjectPropertyName,
    MemberStatusColorCodes,
} from "Data";
import {
    convertStringToCamelCase,
    formatToCommonReadableFormat,
    getMemberFullName,
    getTruncatedStringWithTooltip,
    toTitleCase,
} from "Utils";
import { LoadingComponent } from "Components/utils";
import { applyBadgeStyling } from "Components/utils/styling/Styling";

const IncidentProfileDetails = ({
    incidentDetails,
    isLoadingIncidentsData,
    isLoadingIncidentsMemberData,
    isAllowedToViewMember,
    onReloadFraudMember,
}) => {
    const { organization } = useContext(UserContext);

    const memberIdentifierLabel = useMemo(
        () =>
            MemberPrimaryAttributeObjectPropertyName[
                organization?.configuration?.memberPrimaryAttribute
            ] ?? "",
        [organization?.configuration?.memberPrimaryAttribute]
    );

    const memberName = useMemo(
        () =>
            getTruncatedStringWithTooltip({
                value: getMemberFullName({
                    firstName: incidentDetails?.memberData?.firstName,
                    lastName: incidentDetails?.memberData?.lastName,
                }),
                valueMaxLength: 21,
                customUnknownValue: incidentDetails?.memberId || "~unknown",
            }),
        [
            incidentDetails?.memberId,
            incidentDetails?.memberData?.firstName,
            incidentDetails?.memberData?.lastName,
        ]
    );

    return (
        <Row className="grey-bg">
            {isLoadingIncidentsData ? (
                <div className="w-100 text-center">
                    <LoadingComponent />
                </div>
            ) : (
                <>
                    <Col
                        xl="3"
                        lg="3"
                        md="3"
                        className="p-0 border-right d-flex flex-column justify-content-around"
                    >
                        {isLoadingIncidentsMemberData ? (
                            <LoadingComponent />
                        ) : (
                            <>
                                <div className="border-bottom h-100 px-3 py-2 d-flex flex-column justify-content-center">
                                    <div className="d-flex justify-content-between align-items-center">
                                        Fraudulent Member
                                        {isAllowedToViewMember &&
                                            incidentDetails?.memberId && (
                                                <OverlayTrigger
                                                    placement="auto"
                                                    overlay={
                                                        <Tooltip id="tooltip-disabled">
                                                            Reload Fraudulent
                                                            Member
                                                        </Tooltip>
                                                    }
                                                >
                                                    <span className="d-inline-block">
                                                        <Button
                                                            className="shadow-none p-0 m-0"
                                                            variant="link"
                                                            size="sm"
                                                            disabled={
                                                                isLoadingIncidentsData ||
                                                                isLoadingIncidentsMemberData
                                                            }
                                                            onClick={() =>
                                                                onReloadFraudMember(
                                                                    incidentDetails.memberId
                                                                )
                                                            }
                                                        >
                                                            <IcIcon
                                                                size="md"
                                                                icon={faSync}
                                                            />
                                                        </Button>
                                                    </span>
                                                </OverlayTrigger>
                                            )}
                                    </div>
                                    <div className="font-weight-bold d-flex justify-content-between align-items-center pt-2 px-2">
                                        {incidentDetails?.memberId ? (
                                            <>
                                                {isAllowedToViewMember ? (
                                                    <a
                                                        href={`/members/${incidentDetails.memberId}`}
                                                        target="_blank"
                                                        rel="noreferrer"
                                                        title="View Member Profile"
                                                        className="d-flex align-items-center"
                                                    >
                                                        {memberName}
                                                        <IcIcon
                                                            className="ml-2 pb-1"
                                                            size="lg"
                                                            icon={
                                                                faExternalLink
                                                            }
                                                        />
                                                    </a>
                                                ) : (
                                                    memberName
                                                )}
                                            </>
                                        ) : (
                                            "~unknown"
                                        )}
                                        {isAllowedToViewMember &&
                                            incidentDetails?.memberData
                                                ?.status && (
                                                <Badge
                                                    className="px-2 py-1"
                                                    variant={
                                                        MemberStatusColorCodes[
                                                            incidentDetails
                                                                ?.memberData
                                                                ?.status
                                                        ] || "default"
                                                    }
                                                >
                                                    {toTitleCase(
                                                        incidentDetails
                                                            .memberData.status
                                                    )}
                                                </Badge>
                                            )}
                                    </div>
                                </div>
                                <div className="h-100 px-3 py-2 d-flex flex-column justify-content-center">
                                    <div>
                                        {memberIdentifierLabel
                                            ? toTitleCase(memberIdentifierLabel)
                                            : "Primary Contact"}
                                    </div>
                                    <div className="font-weight-bold my-2 px-2">
                                        {getTruncatedStringWithTooltip({
                                            value: memberIdentifierLabel
                                                ? incidentDetails?.memberData?.[
                                                    convertStringToCamelCase(
                                                        memberIdentifierLabel
                                                    )
                                                ]
                                                : incidentDetails?.memberIdentifier ?? "",
                                        })}
                                    </div>
                                </div>
                            </>
                        )}
                    </Col>
                    <Col
                        xl="3"
                        lg="3"
                        md="3"
                        className="p-0 border-right d-flex flex-column justify-content-around"
                    >
                        <div className="border-bottom h-100 px-3 py-2 d-flex flex-column justify-content-center">
                            <div>Incident Id</div>
                            <div className="font-weight-bold my-2 px-2">
                                {incidentDetails?._id ?? "~ unknown"}
                            </div>
                        </div>
                        <div className="h-100 px-3 py-2 d-flex flex-column justify-content-center">
                            <div>Generated Date</div>
                            <div className="font-weight-bold my-2 px-2">
                                {incidentDetails?.createdOn
                                    ? formatToCommonReadableFormat(
                                        incidentDetails.createdOn
                                    )
                                    : "~ unknown"}
                            </div>
                        </div>
                    </Col>
                    <Col
                        xl="3"
                        lg="3"
                        md="3"
                        className="p-0 border-right d-flex flex-column justify-content-around"
                    >
                        <div className="border-bottom h-100 px-3 py-2 d-flex flex-column justify-content-center">
                            <div>Rule Name</div>
                            <div className="font-weight-bold my-2 px-2">
                                {getTruncatedStringWithTooltip({
                                    value: incidentDetails?.rule?.name ?? "",
                                    valueMaxLength: 36,
                                })}
                            </div>
                        </div>
                        <div className="h-100 px-3 py-2 d-flex flex-column justify-content-center">
                            <div>Rule Type</div>
                            <div className="font-weight-bold my-2 px-2">
                                {getTruncatedStringWithTooltip({
                                    value:
                                        incidentDetails?.ruleType?.name ?? "",
                                    valueMaxLength: 36,
                                })}
                            </div>
                        </div>
                    </Col>
                    <Col
                        xl="3"
                        lg="3"
                        md="3"
                        className="p-0 d-flex flex-column justify-content-around"
                    >
                        <div className="border-bottom h-100 px-3 py-2 d-flex flex-column justify-content-center">
                            <div>Rule Description</div>
                            <div className="font-weight-bold my-2 px-2">
                                {getTruncatedStringWithTooltip({
                                    value:
                                        incidentDetails?.rule?.description ??
                                        "",
                                    valueMaxLength: 36,
                                })}
                            </div>
                        </div>
                        <div className="h-100 px-3 py-2 d-flex flex-column justify-content-center">
                            <div>Incident Status</div>
                            <div className="mt-2 px-2">
                                {applyBadgeStyling({
                                    text: incidentDetails?.status,
                                    variant:
                                        FraudIncidentStatusVariant[
                                            incidentDetails?.status
                                        ],
                                })}
                            </div>
                        </div>
                    </Col>
                </>
            )}
        </Row>
    );
};

export default IncidentProfileDetails;
