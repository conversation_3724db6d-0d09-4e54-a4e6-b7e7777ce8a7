import React, { useCallback, useContext, useMemo } from "react";
import { <PERSON><PERSON>, Wizard } from "@shoutout-labs/shoutout-themes-enterprise";
import CreateFraudRuleWizardFirstPage from "./CreateFraudRuleWizardFirstPage";
import CreateFraudRuleWizardSecondPage from "./CreateFraudRuleWizardSecondPage";
import CreateFraudRuleWizardThirdPage from "./CreateFraudRuleWizardThirdPage";
import { FraudContext } from "../../context/FraudContext";
import "./CreateFraudRuleWizard.scss";

const CreateFraudRuleWizard=()=>{
    const {setStates, show, isCreatingFraudRule,reset,isEditingFraudRule,wizardType} = useContext(FraudContext);

    const handleClose = useCallback(
        () => {
            setStates({show: false })
            reset()
        }
        , [reset, setStates]
    );

    return useMemo(
        () => <CreateFraudRuleWizardView
            handleClose={handleClose}
            show={show}
            isImporting={isCreatingFraudRule}
            isEditing={isEditingFraudRule}
            wizardType={wizardType}
        />,
        [handleClose, show, isCreatingFraudRule,isEditingFraudRule,wizardType]
    );

}

const CreateFraudRuleWizardView = ({ show, handleClose, isImporting ,wizardType,isEditing}) => {
    return (
        <Modal
            show={show}
            onHide={handleClose}
            size="lg"
            centered={true}
        >
            <Modal.Header
                className="border-0 mb-0 pb-0 px-2 mx-2"
                closeButton
            >
                <Modal.Title
                    bsPrefix="modal-title"
                >
                    {wizardType==="CREATE"?"Create Fraud Rule":"Edit Fraud Rule"}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body
                className="pt-3 mt-2 px-2 create-fraud-rule-wizard-view"
            >
                <Wizard
                    validate={true}
                    finishButtonClick={()=>{}}
                    navSteps
                    color="primary"
                    heightClass="point-wizard-height"
                    disabled={isImporting||isEditing}
                    finishButtonText={wizardType==="CREATE"?(isImporting?"Creating...":"Create Fraud Rule"):(isEditing?"Updating...":"Update Fraud Rule")}
                    steps={[
                        {
                            stepName: "Basic Information",

                            component: CreateFraudRuleWizardFirstPage,

                            showNextBtn: true,
                        },
                        {
                            stepName: "Configurations",

                            component: CreateFraudRuleWizardSecondPage,

                            showNextBtn: true,
                        },
                        {
                            stepName: "Notification",

                            component: CreateFraudRuleWizardThirdPage,

                            showNextBtn: true,
                        }
                    ]}
                />
            </Modal.Body>
        </Modal>
    );
};
export default CreateFraudRuleWizardView;

export {CreateFraudRuleWizard};
