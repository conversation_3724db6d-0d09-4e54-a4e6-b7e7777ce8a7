import React, { useCallback, useContext, useMemo } from "react";
import { FraudContext } from "../../context/FraudContext";
import { Button, ShIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { CreateFraudRuleWizard } from "./CreateFraudRuleWizard";
import { faPlus } from "../../../../FaICIconMap";

const CreateFraudRuleWizardPage=()=>{
    const { setStates, isLoading} = useContext(FraudContext);

    const handleShow = useCallback(
        () => setStates({ show:true,wizardType:"CREATE"}),
        [setStates]
    );

    return useMemo(() => (
            <CreateFraudRuleWizardPageView
                isLoading={isLoading}
                handleShow={handleShow}
            />
        ), [isLoading,handleShow]
    );

}
const CreateFraudRuleWizardPageView=({ isLoading, handleShow })=>{
    return(
        <>
            <Button
                variant="primary"
                size="sm"
                className="mx-2"
                disabled={isLoading}
                onClick={handleShow}
            >
                <ShIcon icon={faPlus} className="mr-2" />
                Create Fraud Rule
            </Button>
            <CreateFraudRuleWizard />
        </>
    )
}

export default CreateFraudRuleWizardPage;
