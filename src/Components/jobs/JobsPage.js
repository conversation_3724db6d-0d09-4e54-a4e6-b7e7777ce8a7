import React from "react";
import { Route, Switch } from "react-router-dom";
import { JobsContextProvider } from "./context/JobsContext";
import Jobs from "./Jobs";
import JobProfile from "./jobProfile/JobProfile";

import "./JobsPage.scss";

const JobsPage = () => {
    return (
        <div className="jobs-page-view">
            <JobsContextProvider>
                <Switch>
                    <Route
                        name="JobsProfile"
                        exact
                        path="/jobs/:id"
                        component={JobProfile}
                    />
                    <Route component={Jobs} />
                </Switch>
            </JobsContextProvider>
        </div>
    );
};

export default JobsPage;
