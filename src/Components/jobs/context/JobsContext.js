import React, { useCallback, useContext, useEffect, useReducer } from "react";
import { toast } from "react-toastify";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { archiveJob, getJobs } from "Services";

const JobsContext = React.createContext();

const defaultLimit = 30,
    defaultSkip = 1;

const resetJobState = {
    limit: defaultLimit,
    skip: defaultSkip,
    jobsList: [],
    jobsCount: 0,
    isLoading: false,
};

const initialState = {
    ...resetJobState,
    searchText: "",
    selectedJobType: [],
    appliedJobType: [],
    selectedStatus: [],
    appliedStatus: [],
    filterSet: false,
    filterApplied: false,
    showArchiveModal: false,
    isArchiving: false,
};

const JobsContextActions = {
    SET_ATTR_VALUES: "setAttrValues",
    SET_JOBS_DATA: "setJobsData",
    RESET: "reset",
};

const reducer = (state, action) => {
    switch (action.type) {
        case JobsContextActions.SET_ATTR_VALUES: {
            return {
                ...state,
                [action.key]: action.value,
            };
        }
        case JobsContextActions.SET_JOBS_DATA: {
            return {
                ...state,
                jobsList: action?.items || [],
                jobsCount: action?.total || 0,
            };
        }
        case JobsContextActions.RESET: {
            return { ...initialState };
        }
        default:
            return state;
    }
};

const JobsContextProvider = (props) => {
    const { regionId,isAuthorizedForAction} = useContext(UserContext);
    const [state, dispatch] = useReducer(reducer, initialState);

    const setAttrValues = useCallback(
        (key, value) =>
            dispatch({
                type: JobsContextActions.SET_ATTR_VALUES,
                key,
                value,
            }),
        [dispatch]
    );

    const setShowArchiveModal = useCallback(
        (value) =>
            dispatch({
                type: JobsContextActions.SET_ATTR_VALUES,
                key: "showArchiveModal",
                value,
            }),
        [dispatch]
    );

    const reset = useCallback(
        () => dispatch({ type: JobsContextActions.RESET }),
        [dispatch]
    );

    const loadJobs = useCallback(
        async ({ limit, skip, searchText = "" }, filters = null) => {
            if(isAuthorizedForAction(
                AccessPermissionModuleNames.JOBS,
                AccessPermissionModules[AccessPermissionModuleNames.JOBS]
                    .actions.ListJobs
            )){
            let payload = {
                regionId,
                limit,
                skip: (skip - 1) * limit,
                searchKey: searchText,
            };
            try {
                if (filters) {
                    payload = { ...payload, ...filters };
                }

                setAttrValues("isLoading", true);
                const jobsResponse = await getJobs(payload);
                dispatch({
                    type: JobsContextActions.SET_JOBS_DATA,
                    ...jobsResponse,
                });
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load jobs!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setAttrValues("isLoading", false);
            }
        }
        },
        [regionId, dispatch, setAttrValues,isAuthorizedForAction]
    );

    const onArchiveJob = useCallback(
        async (job) => {
            try {
                setAttrValues("isArchiving", true);
                const archiveRes = await archiveJob(job?.jobId);
                toast.success(
                    <div>
                        Successfully archived the job "
                        {job?.name || "~ unknown"}".
                    </div>
                );
                setAttrValues("isArchiving", false);
                return archiveRes;
            } catch (e) {
                console.error(e);
                setAttrValues("isArchiving", false);
                toast.error(
                    <div>
                        Failed to archive job!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [setAttrValues]
    );

    useEffect(() => {
        if (regionId) {
            loadJobs({ limit: defaultLimit, skip: defaultSkip });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [regionId]);

    const value = {
        ...state,
        setAttrValues,
        setShowArchiveModal,
        reset,
        loadJobs,
        onArchiveJob,
    };

    return (
        <JobsContext.Provider value={value}>
            {props.children}
        </JobsContext.Provider>
    );
};

export { JobsContext, JobsContextProvider };
