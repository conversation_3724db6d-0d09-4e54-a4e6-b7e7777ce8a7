import React, { useCallback, useContext, useEffect, useMemo } from "react";
import { Button, IcIcon } from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus } from "FaICIconMap";
import {
    CreateJobContext,
    CreateJobContextProvider,
} from "./context/CreateJobContext";
import CreateJobWizard from "./createJobWizardViews/CreateJobWizard";

const CreateJobWizardPage = ({ isLoading }) => {
    const {
        showCreateJobWizard: show,
        isCreating,
        isCreated,
        setShowCreateJobWizard,
        setIsCreated,
        reset,
    } = useContext(CreateJobContext);

    const onShowCreateJobWizard = useCallback(
        () => setShowCreateJobWizard(true),
        [setShowCreateJobWizard]
    );

    const onCloseCreateJobWizard = useCallback(() => {
        reset();
        setShowCreateJobWizard(false);
    }, [reset, setShowCreateJobWizard]);

    useEffect(() => {
        if (isCreated) {
            onCloseCreateJobWizard();
            setIsCreated(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isCreated, setIsCreated]);

    return useMemo(
        () => (
            <CreateJobWizardView
                show={show}
                isLoading={isLoading}
                isCreating={isCreating}
                onShow={onShowCreateJobWizard}
                onHide={onCloseCreateJobWizard}
            />
        ),
        [
            isLoading,
            show,
            isCreating,
            onShowCreateJobWizard,
            onCloseCreateJobWizard,
        ]
    );
};

const CreateJobWizardView = ({
    isLoading,
    show,
    isCreating,
    onShow,
    onHide,
}) => (
    <>
        <Button
            className="action-btn float-right"
            size="sm"
            variant="primary"
            disabled={isLoading}
            onClick={onShow}
        >
            <IcIcon className="mr-2" size="lg" icon={faPlus} />
            Create Job
        </Button>
        <CreateJobWizard show={show} onHide={onHide} isCreating={isCreating} />
    </>
);

const CreateJob = ({ isLoading }) => (
    <CreateJobContextProvider>
        <CreateJobWizardPage isLoading={isLoading} />
    </CreateJobContextProvider>
);

export default CreateJob;
