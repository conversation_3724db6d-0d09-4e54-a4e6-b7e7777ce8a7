import React, {
    useContext,
    useImperativeHandle,
    useRef,
    useState,
} from "react";
import { Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { NotificationsStatus } from "Data";
import { objectToLabelValueArray } from "Utils";
import { CreateJobContext } from "../context/CreateJobContext";
import EmailsInput from "../../../common/emailInput/EmailsInput";

const NotificationsStatusOptions = objectToLabelValueArray(NotificationsStatus);

const CreateJobFourthPage = (props, ref) => {
    const {
        notificationStatus,
        emailsForNotifications,
        isCreating,
        setAttributeValue,
        addEmailItem,
        removeEmailItem,
        setEmailValue,
        onCreateJob,
    } = useContext(CreateJobContext);
    const [validated, setValidated] = useState(false);
    const formRef = useRef();

    useImperativeHandle(ref, () => ({
        isValidated() {
            const formValid = formRef.current.checkValidity();
            if (!formValid) {
                setValidated(true);
            }
            return formValid;
        },
        async onClickNext() {
            await onCreateJob();
        },
    }));

    return (
        <div ref={ref} className="mt-5">
            <Form ref={formRef} validated={validated}>
                <Form.Group>
                    <Form.Label className="d-flex align-items-center">
                        Notifications
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <Form.Group className="input-group mt-3">
                        {NotificationsStatusOptions.map((option, index) => (
                            <Form.Check
                                key={`${index}-${option}`}
                                className="rounded-0 input-check mr-3"
                                custom
                                checked={notificationStatus === option.value}
                                disabled={isCreating}
                                onChange={setAttributeValue}
                                value={option.value}
                                id={option.value}
                                label={option.label}
                                name="notificationStatus"
                                type="radio"
                                required
                            />
                        ))}
                    </Form.Group>
                    {notificationStatus === NotificationsStatus.ENABLED && (
                        <>
                            <Form.Group>
                                <div>
                                    <Form.Label className="d-flex align-items-center">
                                        {`Email ${
                                            emailsForNotifications.length === 1
                                                ? "Address"
                                                : "Addresses"
                                        }`}
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <div className="text-center">
                                        {emailsForNotifications.length < 1 &&
                                            "No emails specified."}
                                    </div>
                                </div>
                                {emailsForNotifications.map((item, index) => (
                                    <EmailsInput
                                        key={index}
                                        index={index}
                                        item={item}
                                        emailsLength={
                                            emailsForNotifications.length
                                        }
                                        disabled={isCreating}
                                        removeEmailItem={removeEmailItem}
                                        setEmailValue={setEmailValue}
                                    />
                                ))}
                            </Form.Group>
                            <div className="text-left">
                                <Button
                                    className="btn shadow-none"
                                    size="sm"
                                    variant="link"
                                    disabled={isCreating}
                                    onClick={addEmailItem}
                                >
                                    + Add Email
                                </Button>
                            </div>
                        </>
                    )}
                </Form.Group>
            </Form>
        </div>
    );
};

export default CreateJobFourthPage;
