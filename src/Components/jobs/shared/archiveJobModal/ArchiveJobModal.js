import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@shoutout-labs/shoutout-themes-enterprise";

const ArchiveJobModal = ({
    show,
    onHide,
    isArchiving,
    jobName,
    onConfirmArchiveJob,
}) => {
    return (
        <Modal
            show={show}
            onHide={
                isArchiving
                    ? () => {
                        /* Placeholder for empty arrow function body. */
                    }
                    : onHide
            }
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isArchiving}>
                <Modal.Title>Archive Job</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                Are you sure you want to archive the job "{jobName}" ?
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    disabled={isArchiving}
                    onClick={onHide}
                >
                    Close
                </Button>
                <Button
                    size="sm"
                    variant="danger"
                    disabled={isArchiving}
                    onClick={onConfirmArchiveJob}
                >
                    {isArchiving ? "Archiving..." : "Archive"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ArchiveJobModal;
