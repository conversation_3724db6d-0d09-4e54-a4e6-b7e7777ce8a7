import React from "react";
import PropTypes from "prop-types";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";

const JobsDropDown = ({
    labelKey,
    onChangeSelect,
    selectOptions,
    placeHolder,
    selectedValue,
    isLoading,
}) => {
    return (
        <Form.Select
            id="basic-typeahead-single"
            labelKey={labelKey}
            onChange={onChangeSelect}
            options={selectOptions}
            placeholder={placeHolder}
            selected={selectedValue}
            disabled={isLoading || !selectOptions}
        />
    );
};

JobsDropDown.propTypes = {
    onChangeSelect: PropTypes.func,
    selectOptions: PropTypes.array,
    placeHolder: PropTypes.string,
    selectedValue: PropTypes.array,
};

export default JobsDropDown;
