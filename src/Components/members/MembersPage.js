import React from 'react'
import { Route, Switch } from 'react-router-dom';
import { MembersContextProvider } from "../../Contexts"
import Members from './Members'
import MemberProfilePage from './memberProfile/MemberProfilePage';
import { MembersContextCaller } from "../../Data";

const MembersPage = () => {
    return (
        <MembersContextProvider mainCaller={MembersContextCaller.MEMBERS_VIEW}>
            <Switch>
                <Route name="MemberProfile" exact path="/members/:id" component={MemberProfilePage} />
                <Route component={Members} />
            </Switch>
        </MembersContextProvider>
    );
}

export default MembersPage;
