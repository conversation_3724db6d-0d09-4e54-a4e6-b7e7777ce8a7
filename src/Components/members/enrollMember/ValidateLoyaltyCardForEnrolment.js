import React, { use<PERSON><PERSON>back, useContext, useMemo, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    Form,
    Modal,
    Tab,
    Tabs,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { CardStatus, CardTypes, DigitalCardGenerationOptions } from "Data";
import { getCardByCardNumber } from "Services";
import { toTitleCase } from "Utils";
import { defaultAdditonalData } from "../memberProfile/memberAbout/secondaryAccounts/SecondaryAccounts";
import { CreateMemberContext } from "../shared/enrollMemberWizard/context/EnrollMembersContext";

import "./EnrollMember.scss";

const DigitalCardGenerationRadioOptions = Object.values(
    DigitalCardGenerationOptions
).map((item) => ({ label: toTitleCase(item), value: item }));
const defaultGenerationOption = DigitalCardGenerationOptions.MANUAL;
const digitalCardText = toTitleCase(CardTypes.DIGITAL_CARD);

const ValidateLoyaltyCardForEnrolment = ({
    memberAction,
    addtionalData,
    setAddtionalData,
}) => {
    const { organization, selectedRegion } = useContext(UserContext);
    const {
        showCardSearch: show,
        setShowCardSearch,
        setLoyaltyCardNo,
        setSystemGenerateDigitalCard,
        setManualGenerateDigitalCard,
        setMemberAction,
        setMemberId,
        setShowMemberEnrollWizard,
    } = useContext(CreateMemberContext);
    const [tab, setTab] = useState(
        (addtionalData?.cardType &&
        addtionalData.cardType !== CardTypes.DIGITAL_CARD
            ? CardTypes.INSTANT_CARD
            : addtionalData.cardType) || CardTypes.INSTANT_CARD
    );
    const [loyaltyCard, setLoyaltyCard] = useState(
        addtionalData?.cardNumber || ""
    );
    const [generationOption, setGenerationOption] = useState(
        addtionalData?.generationOption || defaultGenerationOption
    );
    const [isSearching, setIsSearching] = useState(false);
    const [validated, setValidated] = useState(false);
    const [isValidCardNumber, setIsValidCardNumber] = useState(false);

    const orgCardConfigs = useMemo(
        () =>
            organization?.configuration?.cardConfiguration || {
                allowManualCardGeneration: false,
                loyaltyCardNumberLength: 14,
            },
        [organization?.configuration?.cardConfiguration]
    );

    const loyaltyCardNumberLength = useMemo(() => {
        if (!orgCardConfigs?.loyaltyCardNumberLength) return 14;

        return (
            orgCardConfigs.loyaltyCardNumberLength +
            (Math.round(orgCardConfigs.loyaltyCardNumberLength / 4) - 1)
        );
    }, [orgCardConfigs?.loyaltyCardNumberLength]);

    const resetCardSearchModalData = useCallback(() => {
        setLoyaltyCard("");
        setGenerationOption(defaultGenerationOption);
        setIsSearching(false);
        setValidated(false);
    }, [setLoyaltyCard, setGenerationOption, setIsSearching, setValidated]);

    const onSetTab = useCallback(
        (tabKey) => {
            setTab(tabKey);
            resetCardSearchModalData();
        },
        [setTab, resetCardSearchModalData]
    );

    const clearPredefinedData = useCallback(() => {
        setAddtionalData(defaultAdditonalData);
        resetCardSearchModalData();
    }, [setAddtionalData, resetCardSearchModalData]);

    const onCloseShowCard = useCallback(() => {
        clearPredefinedData();
        setTab(CardTypes.INSTANT_CARD);
        setShowCardSearch(false);
    }, [setShowCardSearch, clearPredefinedData]);

const onChangeLoyaltyCard = useCallback(
    (e) => {
        const input = e.target.value
            .replace(/[^\dA-Z]/g, "") 
            .replace(/(.{4})/g, "$1 ")
            .trim();

        setLoyaltyCard(input);

        const defaultInput = input.replace(/\s/g, "");

        if (defaultInput.length === orgCardConfigs.loyaltyCardNumberLength) {
            setIsValidCardNumber(true);
        } else {
            setIsValidCardNumber(false);
        }
    },
    [setLoyaltyCard, orgCardConfigs.loyaltyCardNumberLength]
);

    const onChangeGenerationOption = useCallback(
        (e) => {
            setGenerationOption(e.currentTarget.value);
            setLoyaltyCard("");
        },
        [setGenerationOption, setLoyaltyCard]
    );

    const searchAndValidateCard = useCallback(
        async (e) => {
            e.preventDefault();

            if (e.target.checkValidity()) {
                try {
                    if (
                        tab === CardTypes.DIGITAL_CARD &&
                        orgCardConfigs?.allowManualCardGeneration &&
                        generationOption === DigitalCardGenerationOptions.AUTO
                    ) {
                        setSystemGenerateDigitalCard(true);
                        setLoyaltyCardNo("");
                        onCloseShowCard();
                        setShowMemberEnrollWizard(true);

                        return;
                    }

                    setIsSearching(true);
                    const card = await getCardByCardNumber(
                        loyaltyCard.replace(/\s/g, ""),
                        selectedRegion._id
                    );
                    setIsSearching(false);

                    if (
                        tab === CardTypes.DIGITAL_CARD &&
                        orgCardConfigs?.allowManualCardGeneration &&
                        generationOption === DigitalCardGenerationOptions.MANUAL
                    ) {
                        let cardStatus;
                        if (card.items.length !== 0) {
                            cardStatus = card.items[0]?.status;
                            if (cardStatus !== CardStatus.ACTIVE) {
                                toast.error(
                                    `A card number${
                                        cardStatus
                                            ? " with the status " +
                                                cardStatus +
                                                " and assigned to a member"
                                            : ""
                                    } already exists in the system for the given value!`
                                );
                                return;
                            }
                        }

                        setManualGenerateDigitalCard(
                            cardStatus !== CardStatus.ACTIVE
                        );
                        setLoyaltyCardNo(loyaltyCard.replace(/\s/g, ""));
                        onCloseShowCard();
                        setShowMemberEnrollWizard(true);

                        return;
                    }

                    if (
                        ((tab === CardTypes.DIGITAL_CARD &&
                            !orgCardConfigs?.allowManualCardGeneration) ||
                            tab !== CardTypes.DIGITAL_CARD) &&
                        card.items.length === 0
                    ) {
                        toast.error(
                            `No card number found! Try again with another ${CardStatus.ACTIVE} card.`
                        );
                        return;
                    }

                    if (card.items[0].status !== CardStatus.ACTIVE) {
                        toast.error(
                            `This card number is not active! Try again with an ${CardStatus.ACTIVE} card.`
                        );
                        return;
                    }

                    if (
                        tab === CardTypes.DIGITAL_CARD &&
                        card.items[0].type !== CardTypes.DIGITAL_CARD
                    ) {
                        toast.error(
                            `This card is of type ${
                                card.items[0].type
                            }. Please pick a valid ${digitalCardText.toLowerCase()}.`
                        );
                        return;
                    }

                    if (
                        tab !== CardTypes.DIGITAL_CARD &&
                        card.items[0].type === CardTypes.DIGITAL_CARD
                    ) {
                        toast.error(
                            `This card is of type ${card.items[0].type}. Please pick a valid physical card.`
                        );
                        return;
                    }

                    if (memberAction === "addSecondary") {
                        setMemberAction(memberAction);
                        setMemberId(addtionalData?.memberId);
                    }

                    setLoyaltyCardNo(loyaltyCard.replace(/\s/g, ""));
                    onCloseShowCard();
                    setShowMemberEnrollWizard(true);
                } catch (e) {
                    console.error(e);
                    setIsSearching(false);
                    toast.error(
                        <div>
                            {`Failed to search and validate the ${
                                tab === CardTypes.DIGITAL_CARD
                                    ? digitalCardText.toLowerCase()
                                    : "physical card"
                            }!`}
                            <br />
                            {e.message
                                ? `Error: ${e.message}`
                                : "Please try again later."}
                        </div>
                    );
                }
            } else {
                setValidated(true);
            }
        },
        [
            memberAction,
            addtionalData?.memberId,
            orgCardConfigs?.allowManualCardGeneration,
            selectedRegion._id,
            tab,
            loyaltyCard,
            generationOption,
            setLoyaltyCardNo,
            setSystemGenerateDigitalCard,
            setManualGenerateDigitalCard,
            setShowMemberEnrollWizard,
            setMemberAction,
            setMemberId,
            setIsSearching,
            setValidated,
            onCloseShowCard,
        ]
    );

    return (
        <>
            <Modal
                show={show}
                className="enroll-member"
                size="lg"
                centered
                backdrop="static"
                onHide={onCloseShowCard}
            >
                <Modal.Header closeButton={!isSearching}>
                    <Modal.Title>
                        Validate Loyalty Card Number
                        {memberAction === "addSecondary"
                            ? " for Secondary Member"
                            : ""}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form
                        onSubmit={searchAndValidateCard}
                        validated={validated}
                        noValidate
                    >
                        <Tabs
                            className="mb-4 border-bottom"
                            activeKey={tab}
                            transition={false}
                            onSelect={onSetTab}
                        >
                            <Tab
                                eventKey={CardTypes.INSTANT_CARD}
                                title="Physical Card"
                                disabled={
                                    isSearching ||
                                    addtionalData?.disableModalChanges
                                }
                            >
                                {tab !== CardTypes.DIGITAL_CARD && (
                                    <Form.Group className="px-3">
                                        <Form.Label>
                                            Enter Loyalty Card Number
                                            <span className="text-danger">
                                                *
                                            </span>
                                        </Form.Label>
                                        <Form.Control
                                            placeholder={`Enter an ${CardStatus.ACTIVE} physical card number from the card pool...`}
                                            value={loyaltyCard}
                                            disabled={
                                                isSearching ||
                                                addtionalData?.disableModalChanges
                                            }
                                            onChange={onChangeLoyaltyCard}
                                            required
                                            minLength={orgCardConfigs.loyaltyCardNumberLength} 
                                            maxLength={loyaltyCardNumberLength}                    
                                            isInvalid={validated && (!isValidCardNumber || loyaltyCard.replace(/\s/g, "").length !== orgCardConfigs.loyaltyCardNumberLength)}
                                        />
                                        
                                        {validated && !loyaltyCard && (
                                            <Form.Text className="text-danger">
                                                * Loyalty card number cannot be
                                                empty!
                                            </Form.Text>
                                        )}
                                    </Form.Group>
                                )}
                            </Tab>
                            <Tab
                                eventKey={CardTypes.DIGITAL_CARD}
                                title="Digital Card"
                                disabled={
                                    isSearching ||
                                    addtionalData?.disableModalChanges
                                }
                            >
                                {tab === CardTypes.DIGITAL_CARD && (
                                    <>
                                        {orgCardConfigs?.allowManualCardGeneration ? (
                                            <>
                                                <Form.Group className="px-3">                                            
                                                    <Form.Label>              
                                                        Digital Card Generation
                                                        <span className="text-danger">
                                                            *
                                                        </span>                                                    
                                                    </Form.Label>
                                                    <Form.Group className="input-group mt-3">
                                                        {DigitalCardGenerationRadioOptions.map(
                                                            (option, index) => (
                                                                <Form.Check
                                                                    key={`${index}-${option}`}
                                                                    className="rounded-0 input-check mr-3"
                                                                    custom
                                                                    checked={
                                                                        generationOption ===
                                                                        option.value
                                                                    }
                                                                    disabled={
                                                                        isSearching ||
                                                                        addtionalData?.disableModalChanges
                                                                    }
                                                                    onChange={
                                                                        onChangeGenerationOption
                                                                    }
                                                                    value={
                                                                        option.value
                                                                    }
                                                                    id={
                                                                        option.value
                                                                    }
                                                                    label={
                                                                        option.label
                                                                    }
                                                                    name="generationOption"
                                                                    type="radio"
                                                                    required
                                                                />
                                                            )
                                                        )}
                                                    </Form.Group>
                                                    <Form.Group>                                                       
                                                        {generationOption ===
                                                        DigitalCardGenerationOptions.MANUAL ? (
                                                            <>
                                                                <Form.Label>
                                                                    Enter the
                                                                    Number to
                                                                    Add as a
                                                                    Loyalty Card
                                                                    <span className="text-danger">
                                                                        *
                                                                    </span>
                                                                </Form.Label>
                                                                <Form.Control
                                                                    placeholder="Enter a valid numarical value..."
                                                                    value={
                                                                        loyaltyCard
                                                                    }
                                                                    disabled={
                                                                        isSearching ||
                                                                        addtionalData?.disableModalChanges
                                                                    }
                                                                    onChange={
                                                                        onChangeLoyaltyCard
                                                                    }
                                                                    required
                                                                    minLength={
                                                                        orgCardConfigs.loyaltyCardNumberLength
                                                                    } 
                                                                    maxLength={
                                                                        loyaltyCardNumberLength
                                                                    }
                                                                    isInvalid={validated && (!isValidCardNumber || loyaltyCard.replace(/\s/g, "").length !== orgCardConfigs.loyaltyCardNumberLength)}
/>
                                                                {validated &&
                                                                    !loyaltyCard && (
                                                                        <Form.Text className="text-danger">
                                                                            *
                                                                            Value
                                                                            for
                                                                            card
                                                                            number
                                                                            cannot
                                                                            be
                                                                            empty!
                                                                        </Form.Text>
                                                                    )}
                                                                <div className="text-info mt-3">
                                                                    <small>
                                                                        <ul className="pl-3 mb-0">
                                                                            <li className="my-2">
                                                                                {
                                                                                    "For the entered value, a "
                                                                                }
                                                                                <span className="font-weight-bold">
                                                                                    {
                                                                                        digitalCardText
                                                                                    }
                                                                                </span>
                                                                                {
                                                                                    " will be generated and assigned to the member."
                                                                                }
                                                                            </li>
                                                                            <li className="my-2">
                                                                                {
                                                                                    "If an "
                                                                                }
                                                                                <span className="font-weight-bold">
                                                                                    {`${CardStatus.ACTIVE} ${digitalCardText}`}
                                                                                </span>
                                                                                {
                                                                                    " already exists for the entered value, that "
                                                                                }
                                                                                <span className="font-weight-bold">
                                                                                    {
                                                                                        digitalCardText
                                                                                    }
                                                                                </span>
                                                                                {
                                                                                    " will be assigned to the member."
                                                                                }
                                                                            </li>
                                                                        </ul>
                                                                    </small>
                                                                </div>
                                                            </>
                                                        ) : (
                                                            <div className="text-info">
                                                                <small>
                                                                    <ul className="pl-3 mb-0">
                                                                        <li>
                                                                            {
                                                                                "A "
                                                                            }
                                                                            <span className="font-weight-bold">
                                                                                {
                                                                                    digitalCardText
                                                                                }
                                                                            </span>
                                                                            {
                                                                                " will be auto generated and assigned to the member by the system."
                                                                            }
                                                                        </li>
                                                                    </ul>
                                                                </small>
                                                            </div>
                                                        )}
                                                    </Form.Group>
                                                </Form.Group>
                                            </>
                                        ) : (
                                            <Form.Group className="px-3">
                                                <Form.Label>
                                                    Enter Loyalty Card Number
                                                    <span className="text-danger">
                                                        *
                                                    </span>
                                                </Form.Label>
                                                <Form.Control
                                                    placeholder={`Enter an ${
                                                        CardStatus.ACTIVE
                                                    } ${digitalCardText.toLowerCase()} number from the card pool...`}
                                                    value={loyaltyCard}
                                                    disabled={
                                                        isSearching ||
                                                        addtionalData?.disableModalChanges
                                                    }
                                                    onChange={
                                                        onChangeLoyaltyCard
                                                    }
                                                    required
                                                    minLength={
                                                        orgCardConfigs.loyaltyCardNumberLength
                                                    } 
                                                    maxLength={
                                                        loyaltyCardNumberLength
                                                    }                                                 
                                                    isInvalid={validated && (!isValidCardNumber || loyaltyCard.replace(/\s/g, "").length !== orgCardConfigs.loyaltyCardNumberLength)}
                                                    />
                                                {validated && !loyaltyCard && (
                                                    <Form.Text className="text-danger">
                                                        * Loyalty card number
                                                        cannot be empty!
                                                    </Form.Text>
                                                )}
                                            </Form.Group>
                                        )}
                                    </>
                                )}
                            </Tab>
                        </Tabs>
                        <div
                            className={`mt-5 d-flex justify-content-${
                                addtionalData?.disableModalChanges
                                    ? "between"
                                    : "end"
                            } align-items-center`}
                        >
                            {addtionalData?.disableModalChanges ? (
                                <div className="text-center">
                                    <Button
                                        variant="link"
                                        size="sm"
                                        disabled={isSearching}
                                        onClick={clearPredefinedData}
                                    >
                                        Clear Predefined Data
                                    </Button>
                                </div>
                            ) : null}
                            <div className="d-flex align-items-center">
                                <Button
                                    className="mr-2 add-card-action-btn"
                                    variant="outline-primary"
                                    size="sm"
                                    disabled={isSearching}
                                    onClick={onCloseShowCard}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="add-card-action-btn"
                                    type="submit"
                                    variant="primary"
                                    size="sm"
                                    disabled={isSearching}
                                >
                                    {tab === CardTypes.DIGITAL_CARD &&
                                    orgCardConfigs?.allowManualCardGeneration &&
                                    generationOption ===
                                        DigitalCardGenerationOptions.AUTO ? (
                                        "Next"
                                    ) : (
                                        <>
                                            {isSearching
                                                ? "Validating..."
                                                : "Search & Validate"}
                                        </>
                                    )}
                                </Button>
                            </div>
                        </div>
                    </Form>
                </Modal.Body>
            </Modal>
        </>
    );
};

ValidateLoyaltyCardForEnrolment.defaultProps = {
    memberAction: "",
    addtionalData: {},
    setAddtionalData: () => {},
};

ValidateLoyaltyCardForEnrolment.propTypes = {
    memberAction: PropTypes.string,
    addtionalData: PropTypes.object,
    setAddtionalData: PropTypes.func,
};

export default ValidateLoyaltyCardForEnrolment;
