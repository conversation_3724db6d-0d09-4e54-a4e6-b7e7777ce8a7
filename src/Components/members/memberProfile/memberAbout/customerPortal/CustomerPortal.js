import React, {useState, useCallback} from 'react';
import {Card, IcIcon} from '@shoutout-labs/shoutout-themes-enterprise';
import {faLaptop, faMobileAlt} from 'FaICIconMap';
import ResetPassword from './ResetPassword';

const CustomerPortal = ({portalData}) => {

  const [showModel, setShowModel] = useState(false);

  // const showResetPassword = useCallback(()=> {
  //   setShowModel(true);
  // }, [setShowModel]);

  const onHideModel = useCallback(()=> {
    setShowModel(false);
  }, [setShowModel]);

    return (
        <>
          <Card className="mb-3">
             <div className="d-flex flex-row jestify-content-between pt-2 px-3 mb-2">
                 <h4 className="mb-0">Customer Portal</h4>
                 </div>
                 <div className="d-flex flex-row jestify-content-between pt-2 px-3">
                    <IcIcon className="mr-2 text-primary" size="w-10" icon={faLaptop} />
                      <div className="d-flex flex-row mr-auto">
                          <p className="text-muted mb-0 pb-0">Web Portal</p>
                       </div>
                       {/* <Button size="sm" variant="link" className="mt-0 cursor-pointer p-0" onClick={showResetPassword}><small>Reset Password</small></Button>  */}
                  </div>

                  {/* TODO : to remove this hard coded username in both web & mobile later */}
                  <p className="mb-0 pb-0 pr-3 customer-portal-web">{portalData?.username || '-'}</p>
                  <div className="d-flex flex-row jestify-content-between pt-2 px-3 mt-2">
                    <IcIcon className="mr-3 text-primary" size="w-10" icon={faMobileAlt} />
                      <div className="d-flex flex-row mr-auto">
                          <p className="text-muted mb-0 pb-0">Mobile App</p>
                       </div>
                  </div>
                  {portalData?.mobile_app ?
                    <p className="mb-3 pb-0 pr-3 customer-portal-web"> {portalData?.mobileApp || '-'} </p> :
                    <p className="mb-3 pb-0 pr-3 customer-portal-web"> - </p>
                  }
          </Card>
          <ResetPassword show={showModel} onHide={onHideModel}/>
        </>
    )
}

export default CustomerPortal;
