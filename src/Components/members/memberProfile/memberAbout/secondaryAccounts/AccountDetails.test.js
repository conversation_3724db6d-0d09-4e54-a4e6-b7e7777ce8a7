import React from "react";
import { create } from "react-test-renderer";
import { UserContext } from "Contexts";
import AccountDetails from "./AccountDetails";

const regionId = "6128e3537ed841e246e2e396";

describe("Secondary Account Details component snapshot", () => {
    const onHide = jest.fn();
    const loadSecondaryAccounts = jest.fn();

    const props = {
        member: { _id: "63c1b6861c5798e8267b7d22" },
        show: true,
        parentId: "63c1b6861c5798e8267b7d00",
        onHide: onHide,
        loadSecondaryAccounts,
    };

    test("Matches the snapshot", () => {
        const component = create(
            <UserContext.Provider value={regionId}>
                <AccountDetails props={props} />
            </UserContext.Provider>
        );
        expect(component.toJSON()).toMatchSnapshot();
    });
});
