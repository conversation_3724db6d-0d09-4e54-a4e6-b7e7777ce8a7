import React from "react";
import { create } from "react-test-renderer";
import { UserContext } from "Contexts";
import AddOrRemoveTag from "./AddOrRemoveTag";

const regionId = "6128e3537ed841e246e2e396";
const memberId = "6128e3537ed841e246e2e667";

describe("AddOrRemoveTag component snapshot", () => {
    test("Matches the snapshot", () => {
        const onHide = jest.fn();

        const props = {
            show: true,
            modalType: "ADD_TAG",
            profileType: "MEMBER",
            memberId,
            tags: [],
            onHide,
        };

        const component = create(
            <UserContext.Provider value={regionId}>
                <AddOrRemoveTag props={props} />
            </UserContext.Provider>
        );
        expect(component.toJSON()).toMatchSnapshot();
    });
});
