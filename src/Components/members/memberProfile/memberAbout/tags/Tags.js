import React, { useState, useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import {
    Card,
    IcIcon,
    <PERSON><PERSON>,
    Badge,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes } from "FaICIconMap";
import { getTruncatedStringWithTooltip } from "Utils";
import AddOrRemoveTag from "./AddOrRemoveTag";

import "./Tags.scss";

const Tags = ({
    profileType,
    memberId,
    memberTags,
    hasEditPermission,
    isLoading,
    loadProfile,
}) => {
    const [tags, setTags] = useState([]);
    const [modalType, setModalType] = useState("");
    const [tagToRemove, setTagToRemove] = useState("");
    const [showModal, setShowModal] = useState(false);

    const onClickAddTag = useCallback(() => {
        setModalType("ADD_TAG");
        setShowModal(true);
    }, [setModalType, setShowModal]);

    const onClickRemoveTag = useCallback(
        (e) => {
            setModalType("REMOVE_TAG");
            setTagToRemove(e.currentTarget.id || "");
            setShowModal(true);
        },
        [setModalType, setTagToRemove, setShowModal]
    );

    const onCloseModal = useCallback(
        (e, data) => {
            if (data) {
                loadProfile();
            }
            setModalType("");
            setTagToRemove("");
            setShowModal(false);
        },
        [loadProfile, setModalType, setTagToRemove, setShowModal]
    );

    useEffect(() => {
        if (memberTags) {
            setTags(Array.isArray(memberTags) ? memberTags : []);
        }

        return () => {
            setTags([]);
            setModalType("");
            setTagToRemove("");
            setShowModal(false);
        };
    }, [memberTags]);

    return (
        <>
            <Card className="tags-view mb-3">
                <div className="d-flex justify-content-between my-2 px-3">
                    <div className="d-flex mr-auto">
                        <h4 className="mb-0">Tags</h4>
                    </div>
                    {hasEditPermission && (
                        <Button
                            className="m-0 p-0 shadow-none"
                            variant="link"
                            size="sm"
                            disabled={isLoading}
                            onClick={onClickAddTag}
                        >
                            <small>Add</small>
                        </Button>
                    )}
                </div>
                <div className="tags-list mx-3 mb-2">
                    {tags.length > 0 ? (
                        tags.map((tag) => (
                            <Badge
                                key={tag}
                                className="py-0 px-2 mx-1 my-2"
                                variant="tag"
                            >
                                <div className="d-flex justify-content-between align-items-center">
                                    {getTruncatedStringWithTooltip({
                                        value: tag || "~ unknown",
                                        valueMaxLength: 24,
                                    })}
                                    {hasEditPermission && (
                                        <Button
                                            className="ml-1 p-0 shadow-none"
                                            id={tag}
                                            variant="link"
                                            size="sm"
                                            disabled={isLoading}
                                            onClick={onClickRemoveTag}
                                        >
                                            <IcIcon
                                                className="text-danger"
                                                size="md"
                                                icon={faTimes}
                                            />
                                        </Button>
                                    )}
                                </div>
                            </Badge>
                        ))
                    ) : (
                        <div className="text-center grey-bg rounded mx-3 mb-3 p-2">
                            No tags found.
                        </div>
                    )}
                </div>
            </Card>
            {hasEditPermission && showModal && (
                <AddOrRemoveTag
                    show={showModal}
                    modalType={modalType}
                    profileType={profileType}
                    memberId={memberId}
                    tags={tags}
                    tagToRemove={tagToRemove}
                    onHide={onCloseModal}
                />
            )}
        </>
    );
};

Tags.defaultProps = {
    profileType: "",
    memberId: "",
    memberTags: [],
    hasEditPermission: false,
    isLoading: false,
    loadProfile: () => {},
};

Tags.propTypes = {
    profileType: PropTypes.string,
    memberId: PropTypes.string,
    memberTags: PropTypes.array,
    hasEditPermission: PropTypes.bool,
    isLoading: PropTypes.bool,
    loadProfile: PropTypes.func,
};

export default Tags;
