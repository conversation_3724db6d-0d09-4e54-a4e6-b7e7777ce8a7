import React, {
    useState,
    useRef,
    useContext,
    useImperativeHandle,
    useCallback,
    useMemo,
    forwardRef,
} from "react";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { CardStatus, MemberReactivateOptions } from "Data";
import { MemberReactivateContext } from "../context/MemberReactivateContext";

const ReactivateOptions = ({ prevCard = {}, suspendedCards = [] }) => {
    return Object.values(MemberReactivateOptions)
        .filter((option) => {
            if (prevCard?.status === CardStatus.ASSIGNED) {
                return option.id === MemberReactivateOptions.NO_CARD.id;
            } else if (suspendedCards.length === 0) {
                return (
                    option.id !==
                    MemberReactivateOptions.REACTIVATE_SUSPENDED_CARD.id
                );
            } else {
                return option;
            }
        })
        .map((mRO) => {
            const mappedOptions = {
                option: mRO.id,
                title: mRO.title,
                description: "~ unknown",
            };

            switch (mRO.id) {
                case MemberReactivateOptions.REACTIVATE_SUSPENDED_CARD.id:
                    mappedOptions.title = mRO.title;
                    mappedOptions.description =
                        "The loyalty member can choose to re-assign a previously used suspended card for loyalty transactions. If the loyalty card is a physical one, kindly verify whether the member still has the card with them.";
                    break;
                case MemberReactivateOptions.ADD_NEW_CARD.id:
                    mappedOptions.title = mRO.title;
                    mappedOptions.description =
                        "If the loyalty member does not currently have the card, add a new loyalty card to his/her account.";
                    break;
                case MemberReactivateOptions.NO_CARD.id:
                    mappedOptions.title = mRO.title;
                    mappedOptions.description =
                        "If a customer needs to reactivate the account without an assigned card, please note that they won't be able to perform any loyalty related transactions such as points earning and redemptions";
                    break;
                default:
                    break;
            }

            return mappedOptions;
        });
};

const MemberReactivateWizardFirstPage = forwardRef(
    (
        {
            memberPrevCard,
            memberSuspendedCards,
            reactivateOption,
            isActivating,
            setAttribute,
            resetSecondPage,
        },
        ref
    ) => {
        const formRef = useRef();
        const [validated, setValidated] = useState(false);

        const validatedClasses = useMemo(() => {
            const classes = {
                borderAndMainClass: "border-primary",
                descriptionClass: "text-muted",
            };
            if (validated) {
                if (reactivateOption) {
                    classes.borderAndMainClass = "border-success text-success";
                    classes.descriptionClass = "text-success";
                } else {
                    classes.borderAndMainClass = "border-danger text-danger";
                    classes.descriptionClass = "text-danger";
                }
            }
            return classes;
        }, [reactivateOption, validated]);

        const onClickOption = useCallback(
            (e) => {
                const currentTargetObj = e.currentTarget.id
                    ? JSON.parse(e.currentTarget.id)
                    : e.currentTarget.id;
                setAttribute({
                    key: "reactivateOption",
                    value: currentTargetObj.option || "",
                });
                setAttribute({
                    key: "reactivateOptionTitle",
                    value: currentTargetObj.title || "",
                });
                resetSecondPage();
            },
            [setAttribute, resetSecondPage]
        );

        useImperativeHandle(ref, () => ({
            isValidated() {
                let formValid = formRef.current.checkValidity();

                if (!formValid) {
                    setValidated(true);
                }
                return formValid;
            },
            async onClickNext() {
                return new Promise((resolve, reject) => {
                    resolve();
                });
            },
        }));

        return (
            <div ref={ref} className="mt-5">
                <Form ref={formRef} validated={validated}>
                    <Form.Group controlId="reactivateOption">
                        <Form.Label className="mb-3">
                            Select a Card Reactivate Option
                            <span className="ml-1 text-danger">*</span>
                        </Form.Label>
                        {ReactivateOptions({
                            prevCard: memberPrevCard,
                            suspendedCards: memberSuspendedCards,
                        }).map((rO) => (
                            <Form.Group
                                key={rO.option}
                                className={`border ${
                                    validatedClasses.borderAndMainClass
                                } rounded p-3 cursor-pointer reactivate-option-card${
                                    rO.option === reactivateOption
                                        ? "-selected"
                                        : ""
                                }`}
                                id={JSON.stringify(rO)}
                                dataset-title={rO.title}
                                onClick={onClickOption}
                            >
                                <Form.Check
                                    id={rO.option}
                                    type="radio"
                                    name="reactivateOption"
                                    label={
                                        <span className="cursor-pointer">
                                            {rO.title}
                                        </span>
                                    }
                                    value={rO.option}
                                    checked={rO.option === reactivateOption}
                                    disabled={isActivating}
                                    required
                                    custom
                                />
                                <Form.Text
                                    className={`ml-4 ${validatedClasses.descriptionClass}`}
                                >
                                    {rO.description}
                                </Form.Text>
                            </Form.Group>
                        ))}
                        {validated && !reactivateOption && (
                            <Form.Text className="text-danger">
                                * Reactivate card option must be selected.
                            </Form.Text>
                        )}
                    </Form.Group>
                </Form>
            </div>
        );
    }
);

const MemberReactivateWizardFirstPageContainer = (props, ref) => {
    const {
        memberSuspendedCards,
        memberPrevCard,
        reactivateOption,
        isActivating,
        setAttribute,
        resetSecondPage,
    } = useContext(MemberReactivateContext);

    return useMemo(
        () => (
            <MemberReactivateWizardFirstPage
                ref={ref}
                {...props}
                memberPrevCard={memberPrevCard}
                memberSuspendedCards={memberSuspendedCards}
                reactivateOption={reactivateOption}
                isActivating={isActivating}
                setAttribute={setAttribute}
                resetSecondPage={resetSecondPage}
            />
        ),
        [
            ref,
            props,
            memberPrevCard,
            memberSuspendedCards,
            reactivateOption,
            isActivating,
            setAttribute,
            resetSecondPage,
        ]
    );
};

export default MemberReactivateWizardFirstPageContainer;
