import React, { useCallback, useContext, useMemo, useState } from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import { toast } from "react-toastify";
import {
    Badge,
    BootstrapTable,
    Form,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { CardStatus, MemberReactivateOptions } from "Data";
import { toTitleCase } from "Utils";
import { BootstrapTableOverlay } from "Components/utils";
import { MemberReactivateContext } from "../../context/MemberReactivateContext";

const SecondaryMemberReactivateSetup = ({
    validated = false,
    loyaltyCardNumberLength,
    secondaryMemberIndex,
    option = "",
    memberId = "",
    secondaryMemberPrevCardId = "",
    secondaryMemberSuspendedCards = [],
    cardId = "",
    cardNumber = "",
    waiveCardReplacementFee = false,
    isActivating = false,
    setSecondaryMemberAttribute = () => {},
}) => {
    const [cardNo, setCardNo] = useState(cardNumber);

    const validatedClasses = useMemo(() => {
        if (validated) {
            return cardId
                ? "border-success text-success"
                : "border-danger text-danger";
        }
        return "border-primary";
    }, [cardId, validated]);

    const onClickSelectSuspendedCard = useCallback(
        (e) => {
            try {
                e.preventDefault();
                e.stopPropagation();

                const currentTargetObj = e.currentTarget.id
                    ? JSON.parse(e.currentTarget.id)
                    : e.currentTarget.id;

                setSecondaryMemberAttribute({
                    secondaryMemberIndex,
                    key: "cardId",
                    value: currentTargetObj?.cardId,
                });
            } catch (e) {
                toast.error(
                    <div>
                        Failed to select secondary memeber suspended card!
                        <br />
                        {e.message ? `Error: ${e.message}` : "Unknown error."}
                    </div>
                );
            }
        },
        [secondaryMemberIndex, setSecondaryMemberAttribute]
    );

    const onChangeCardNumber = useCallback(
        (e) => {
            try {
                e.preventDefault();
                e.stopPropagation();
                const cardNoValue = e.currentTarget.value
                    ?.replace(/[^\dA-Z]/g, "")
                    .trim();

                setCardNo(cardNoValue);
            } catch (e) {
                toast.error(
                    <div>
                        Failed to enter card number!
                        <br />
                        {e.message ? `Error: ${e.message}` : "Unknown error."}
                    </div>
                );
            }
        },
        [setCardNo]
    );

    const onSetCardNumberToContext = useCallback(
        (e) => {
            e.preventDefault();
            e.stopPropagation();
            const cardNoValue = e.currentTarget.value
                ?.replace(/[^\dA-Z]/g, "")
                .trim();

            if (cardNo && cardNo !== cardNumber)
                setSecondaryMemberAttribute({
                    secondaryMemberIndex,
                    key: "cardNumber",
                    value: cardNoValue,
                });
        },
        [cardNo, cardNumber, secondaryMemberIndex, setSecondaryMemberAttribute]
    );

    const onChangeWaiveCardReplacementFee = useCallback(
        (e) => {
            try {
                e.preventDefault();
                e.stopPropagation();
                setSecondaryMemberAttribute({
                    secondaryMemberIndex,
                    key: "waiveCardReplacementFee",
                    value: e.currentTarget.checked,
                });
            } catch (e) {
                toast.error(
                    <div>
                        Failed to select waive card replacement fee!
                        <br />
                        {e.message ? `Error: ${e.message}` : "Unknown error."}
                    </div>
                );
            }
        },
        [secondaryMemberIndex, setSecondaryMemberAttribute]
    );

    switch (option) {
        case MemberReactivateOptions.REACTIVATE_SUSPENDED_CARD.id:
            return (
                <div className="border border-primary rounded secondary-members-config-bg px-5 pt-3">
                    <Form.Group>
                        <Form.Label>
                            Select a Card to Reassign
                            <span className="ml-1 text-danger">*</span>
                        </Form.Label>
                        <div className="px-2 px-3 border border-primary rounded secondary-member-suspended-cards-list">
                            {secondaryMemberSuspendedCards.map(
                                (suspendedCard) => (
                                    <Form.Group
                                        key={suspendedCard?._id}
                                        className={`my-3 border ${validatedClasses} rounded p-2 cursor-pointer reactivate-option-card${
                                            suspendedCard?._id === cardId
                                                ? "-selected"
                                                : ""
                                        }`}
                                        id={JSON.stringify({
                                            memberId,
                                            cardId: suspendedCard?._id,
                                        })}
                                        onClick={onClickSelectSuspendedCard}
                                    >
                                        <Form.Check
                                            id={suspendedCard?._id}
                                            type="radio"
                                            name={`${memberId}-cardId`}
                                            label={
                                                <div className="d-flex align-items-center cursor-pointer">
                                                    {`Card Number:`}
                                                    <span className="ml-2 font-weight-bold">
                                                        {suspendedCard?.cardNoStr ||
                                                            "~ unknown"}
                                                    </span>
                                                    {secondaryMemberPrevCardId ===
                                                        suspendedCard?._id && (
                                                        <Badge
                                                            className="ml-2"
                                                            variant="success"
                                                        >
                                                            Last Used
                                                        </Badge>
                                                    )}
                                                </div>
                                            }
                                            value={suspendedCard?._id}
                                            checked={
                                                suspendedCard?._id === cardId
                                            }
                                            disabled={isActivating}
                                            required
                                            custom
                                        />
                                    </Form.Group>
                                )
                            )}
                        </div>
                        {validated && !cardId && (
                            <>
                                <hr />
                                <Form.Text className="text-danger">
                                    * Please select a card to be re-assigned!
                                </Form.Text>
                            </>
                        )}
                    </Form.Group>
                </div>
            );
        case MemberReactivateOptions.ADD_NEW_CARD.id:
            return (
                <div className="border border-primary rounded secondary-members-config-bg px-5 py-3">
                    <Form.Group>
                        <Form.Label className="d-flex align-items-center">
                            New Loyalty Card Number
                            <div className="ml-1 text-danger">*</div>
                        </Form.Label>
                        <Form.Control
                            id={`${memberId}-cardNumber`}
                            name={`${memberId}-cardNumber`}
                            type="text"
                            placeholder="Enter new loyalty card number"
                            value={cardNo}
                            disabled={isActivating}
                            onChange={onChangeCardNumber}
                            onBlur={onSetCardNumberToContext}
                            onMouseLeave={onSetCardNumberToContext}
                            minLength={loyaltyCardNumberLength}
                            maxLength={loyaltyCardNumberLength}
                            required
                        />
                    </Form.Group>
                    <Form.Group>
                        <Form.Check
                            id={`${memberId}-waiveCardReplacementFee`}
                            name={`${memberId}-waiveCardReplacementFee`}
                            type="checkbox"
                            label="Waive card replacement fee"
                            checked={waiveCardReplacementFee}
                            disabled={isActivating}
                            onChange={onChangeWaiveCardReplacementFee}
                        />
                    </Form.Group>
                </div>
            );
        default:
            return null;
    }
};

const NoData = ({ loading }) => {
    if (loading) return null;
    return <div className="text-center">No secondary member data found.</div>;
};

const columns = [
    { dataField: "id", name: "id", hidden: true },
    { dataField: "secondaryMember", name: "", text: "" },
];

const defaultSkip = 1,
    defaultLimit = 2;

const ReactivateSecondaryMembers = ({ validated = false }) => {
    const {
        loyaltyCardNumberLength,
        secondaryMembers,
        secondaryMembersCardData,
        isActivating,
        setSecondaryMemberAttribute,
    } = useContext(MemberReactivateContext);
    const [skip, setSkip] = useState(defaultSkip);

    const secondaryMemberReactivateOptions = useMemo(
        () =>
            secondaryMembers.reduce((result, sM) => {
                result[sM._id] = Object.values(MemberReactivateOptions).filter(
                    (option) => {
                        if (
                            secondaryMembersCardData[sM._id]
                                ?.secondaryMemberPrevCardStatus ===
                            CardStatus.ASSIGNED
                        ) {
                            return (
                                option.id === MemberReactivateOptions.NO_CARD.id
                            );
                        } else if (
                            secondaryMembersCardData[sM._id]
                                ?.secondaryMemberSuspendedCards?.length === 0
                        ) {
                            return (
                                option.id !==
                                MemberReactivateOptions
                                    .REACTIVATE_SUSPENDED_CARD.id
                            );
                        } else {
                            return option;
                        }
                    }
                );

                return result;
            }, {}),
        [secondaryMembers, secondaryMembersCardData]
    );

    const onClickOption = useCallback(
        (e) => {
            try {
                e.preventDefault();
                e.stopPropagation();

                const currentTargetObj = e.currentTarget.id
                    ? JSON.parse(e.currentTarget.id)
                    : e.currentTarget.id;

                setSecondaryMemberAttribute({
                    secondaryMemberIndex: currentTargetObj?.index,
                    key: "reactivateOption",
                    value: currentTargetObj?.id,
                });
            } catch (e) {
                toast.error(
                    <div>
                        Failed to select card reactivate option!
                        <br />
                        {e.message ? `Error: ${e.message}` : "Unknown error."}
                    </div>
                );
            }
        },
        [setSecondaryMemberAttribute]
    );

    const data = useMemo(
        () =>
            secondaryMembers
                .map((secondaryMember, index) => {
                    return {
                        id: secondaryMember?._id,
                        secondaryMember: (
                            <div className="secondary-members-data border border-primary rounded p-3">
                                <div className="mb-3 secondary-member-name border-bottom border-primary pb-3">
                                    Name:
                                    <span className="ml-2 font-weight-bold">
                                        {secondaryMember?.name
                                            ? toTitleCase(secondaryMember.name)
                                            : "~ unknown"}
                                    </span>
                                    {secondaryMembersCardData[
                                        secondaryMember?._id
                                    ]?.secondaryMemberPrevCardStatus ===
                                        CardStatus.ASSIGNED && (
                                        <Badge
                                            className="ml-2"
                                            variant="secondary"
                                        >
                                            Member already has an assigned card
                                        </Badge>
                                    )}
                                </div>
                                <div className="px-3">
                                    <Form.Label>
                                        Select a Card Reactivate Option
                                        <span className="ml-1 text-danger">
                                            *
                                        </span>
                                    </Form.Label>
                                    <Form.Group>
                                        {secondaryMemberReactivateOptions[
                                            secondaryMember?._id
                                        ]?.map((sMRO) => {
                                            return (
                                                <Form.Group
                                                    key={sMRO.id}
                                                    id={JSON.stringify({
                                                        index,
                                                        ...sMRO,
                                                    })}
                                                    onClick={onClickOption}
                                                >
                                                    <Form.Check
                                                        key={sMRO.id}
                                                        id={`${secondaryMember?._id}-reactivateOption`}
                                                        type="radio"
                                                        name={`${secondaryMember?._id}-reactivateOption`}
                                                        label={
                                                            <span className="cursor-pointer">
                                                                {sMRO.title}
                                                            </span>
                                                        }
                                                        value={sMRO.id}
                                                        checked={
                                                            sMRO.id ===
                                                            secondaryMember?.reactivateOption
                                                        }
                                                        disabled={isActivating}
                                                        required
                                                        custom
                                                    />
                                                </Form.Group>
                                            );
                                        })}
                                    </Form.Group>
                                    <SecondaryMemberReactivateSetup
                                        validated={validated}
                                        loyaltyCardNumberLength={
                                            loyaltyCardNumberLength
                                        }
                                        secondaryMemberIndex={index}
                                        option={
                                            secondaryMember?.reactivateOption
                                        }
                                        memberId={secondaryMember?._id}
                                        secondaryMemberPrevCardId={
                                            secondaryMembersCardData[
                                                secondaryMember?._id
                                            ]?.secondaryMemberPrevCardId
                                        }
                                        secondaryMemberSuspendedCards={
                                            secondaryMembersCardData[
                                                secondaryMember?._id
                                            ]?.secondaryMemberSuspendedCards
                                        }
                                        cardId={secondaryMember?.cardId}
                                        cardNumber={secondaryMember?.cardNumber}
                                        waiveCardReplacementFee={
                                            secondaryMember?.waiveCardReplacementFee
                                        }
                                        isActivating={isActivating}
                                        setSecondaryMemberAttribute={
                                            setSecondaryMemberAttribute
                                        }
                                    />
                                </div>
                            </div>
                        ),
                    };
                })
                .slice(
                    (skip - 1) * defaultLimit,
                    (skip - 1) * defaultLimit + defaultLimit
                ),
        [
            validated,
            skip,
            secondaryMembers,
            secondaryMembersCardData,
            secondaryMemberReactivateOptions,
            loyaltyCardNumberLength,
            isActivating,
            setSecondaryMemberAttribute,
            onClickOption,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => setSkip(newSkip),
        [setSkip]
    );

    const options = {
        page: skip,
        sizePerPage: defaultLimit,
        totalSize: secondaryMembers.length,
        pageStartIndex: 1,
        paginationSize: defaultLimit,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [],
        onPageChange: onChangePagination,
    };

    const onTableChange = (_type, _newState) => {
        // * When "remote" is enabled, the "onTableChange" prop is activated automatically, so if we don't provide this method react-bootstrap will throw an error.
    };

    return (
        <PaginationProvider
            pagination={paginationFactory(options)}
            keyField="id"
            columns={columns}
            data={data}
        >
            {({ paginationTableProps }) => (
                <ToolkitProvider
                    keyField="id"
                    data={data}
                    columns={columns}
                    columnToggle
                >
                    {(props) => (
                        <div>
                            <BootstrapTable
                                {...paginationTableProps}
                                remote={{
                                    search: true,
                                    pagination: true,
                                }}
                                keyField="id"
                                onTableChange={onTableChange}
                                noDataIndication={<NoData loading={false} />}
                                overlay={BootstrapTableOverlay}
                                {...props.baseProps}
                            />
                        </div>
                    )}
                </ToolkitProvider>
            )}
        </PaginationProvider>
    );
};

export default ReactivateSecondaryMembers;
