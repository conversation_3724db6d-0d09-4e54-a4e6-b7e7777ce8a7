import React, {
    useContext,
    useRef,
    useState,
    useCallback,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext } from "Contexts";
import {
    MerchantLocationStatusObj,
    MerchantStatus,
} from "Data";
import { useToggle } from "Hooks";
import {
    reverseBill,
    reverseBillWithOtpRequest,
    reverseBillWithOtp,
} from "Services";
import { toTitleCase } from "Utils";

const ReverseBillOptions = {
    WITH_OTP: "withOTP",
    WITHOUT_OTP: "withoutOTP",
};

const ReverseBill = ({ show, onHide, memberId }) => {
    const { merchants, merchantLocations, 
    } =
        useContext(DataContext);
    const [isRequesting, setIsRequesting] = useState(false);
    const [isReversing, setIsReversing] = useState(false);
    const [validated, setValidated] = useState(false);
    const [pointsReversalToken, setPointsReversalToken] = useState("");
    const [isOtpView, toggleIsOtpView] = useToggle(false);
    const [selectedMerchant, setSelectedMerchant] = useState([]);
    const [selectedLocation, setSelectedLocation] = useState([]);
    const [selectedOption, setSelectedOption] = useState(
        ReverseBillOptions.WITH_OTP
    );
    const [reverseBillStatus, setReverseBillStatus] = useState({
        reversalBillReference: "",
        notes: "",
        otpCode: "",
    });
    const formRef = useRef();
    
    const merchantsWithReverseBillOption = useMemo(
       
        () =>
            merchants
                .filter(
                    (merchant) =>
                        merchant?.status === MerchantStatus.ACTIVE &&
                        merchant?.options?.adjustPoints
                        
                )
                .map((filteredMerchant) => ({
                    merchantId: filteredMerchant?._id || "Unknown Id",
                    name: toTitleCase(
                        filteredMerchant?.merchantName || "Unknown merchant"
                    ),
                })),
        [merchants]
    );

    const locations = useMemo(
        () =>
            (selectedMerchant.length !== 0 &&
                merchantLocations[selectedMerchant[0]?.merchantId] &&
                Object.values(
                    merchantLocations[selectedMerchant[0]?.merchantId]
                )
                    .filter(
                        (location) =>
                            location?.status ===
                                MerchantLocationStatusObj.ACTIVE &&
                            location?.options?.adjustPoints
                        
                             &&
                            location?.isShownInCreateTransactions
                    )
                    .map((filteredLocation) => ({
                        locationId: filteredLocation?._id || "Unknown Id",
                        name: toTitleCase(
                            filteredLocation?.locationName || "Unknown location"
                        ),
                    }))) ||
            [],
        [merchantLocations, selectedMerchant]
    );


    const onChangeOption = useCallback(
        (e) => {
            setSelectedOption(e.target.value);
        },
        [setSelectedOption]
    );

    const handleChange = useCallback(
        (event) => {
            setReverseBillStatus({
                ...reverseBillStatus,
                [event.target.name]: event.target.value,
            });
        },
        [reverseBillStatus, setReverseBillStatus]
    );

    const onSelectMerchant = useCallback(
        (e) => {
            if (selectedLocation.length !== 0) {
                setSelectedLocation([]);
            }
            setSelectedMerchant(e);
        },
        [selectedLocation.length, setSelectedMerchant, setSelectedLocation]
    );

    const onRequestOTPCode = useCallback(
        async (event) => {
            event.preventDefault();
            try {
                const payload = {
                    memberId,
                    merchantId: selectedMerchant[0]?.merchantId,
                    merchantLocationId: selectedLocation[0]?.locationId,
                    reversalBillReference: reverseBillStatus.reversalBillReference,
                    notes: reverseBillStatus.notes,
                };
                const formValid = formRef.current.checkValidity();

                if (formValid) {
                    if (selectedLocation.length === 0) {
                        throw new Error("Merchant location is required!");
                    }


                    setIsRequesting(true);
                    const token = await reverseBillWithOtpRequest(payload);
                    setPointsReversalToken(token?.pointsReversalToken);
                    setIsRequesting(false);
                    toast.success(
                        "Successfully sent an OTP to the member's registered mobile number."
                    );
                    toggleIsOtpView();
                } else {
                    setValidated(true);
                }
            } catch (e) {
                console.error(e);
                setIsRequesting(false);
                toast.error(
                    <div>
                        Failed to request OTP code!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            memberId,
            selectedMerchant,
            selectedLocation,
            reverseBillStatus.notes,
            reverseBillStatus.reversalBillReference,
            toggleIsOtpView,
            setIsRequesting,
            setPointsReversalToken,
            setValidated,
        ]
    );

    const onReverseBill = useCallback(async () => {
        try {
            const payload = {
                memberId,
                merchantId: selectedMerchant[0]?.merchantId,
                merchantLocationId: selectedLocation[0]?.locationId,
                reversalBillReference: reverseBillStatus.reversalBillReference,
                notes: reverseBillStatus.notes,
            };
            const formValid = formRef.current.checkValidity();
            let reverseBillRes = null;

            if (formValid) {
                setIsReversing(true);

                if (selectedOption === ReverseBillOptions.WITH_OTP) {
                    reverseBillRes = await reverseBillWithOtp({
                        pointsReversalToken: pointsReversalToken,
                        otpCode: reverseBillStatus.otpCode,
                    });
                } else {
                    reverseBillRes = await reverseBill(payload);
                    
                }
                setIsReversing(false);
                toast.success(
                    `Successfully reversed the transaction/s for invoice number ${
                        reverseBillStatus.reversalBillReference + " " || ""
                    } .`
                );
                onHide(null, reverseBillRes);
            } else {
                setValidated(true);
            }
        } catch (e) {
            console.error(e);
            setIsReversing(false);
            toast.error(
                <div>
                    Failed to reverse the bill!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        memberId,
        onHide,
        reverseBillStatus.notes,
        reverseBillStatus.otpCode,
        reverseBillStatus.reversalBillReference,
        pointsReversalToken,
        selectedLocation,
        selectedMerchant,
        selectedOption,
        setIsReversing,
        setValidated,
    ]);

    const reverseBillButton = useMemo(
        () =>
            selectedOption === ReverseBillOptions.WITHOUT_OTP ? (
                <Button
                    size="sm"
                    variant="primary"
                    disabled={isRequesting || isReversing}
                    onClick={onReverseBill}
                >
                    {isReversing && "Reversing Bill..."}
                    {!isReversing && "Reverse Bill"}
                </Button> 
            ) : (
                <Button
                    size="sm"
                    variant="primary"
                    disabled={isRequesting || isReversing}
                    onClick={!isOtpView ? onRequestOTPCode : onReverseBill}
                >
                    {!isOtpView ? (
                        <>
                            {isRequesting && "Requesting OTP..."}
                            {!isRequesting && "Request OTP"}
                        </>
                    ) : (
                        <>
                            {isReversing && "Reversing bill..."}
                            {!isReversing && "Reverse bill"}
                        </>
                    )}
                </Button>
            ),
        [
            isReversing,
            isRequesting,
            isOtpView,
            selectedOption,
            onReverseBill,
            onRequestOTPCode,
        ]
    );

    return (
        <Modal
            show={show}
            size="md"
            onHide={
                isRequesting || isReversing
                    ? () => {
                       
                    }
                    : onHide
            }
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!(isRequesting || isReversing)}>
                <Modal.Title>
                    {!isOtpView ? "Reverse Bill" : "Reverse Bill with OTP"}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form noValidate validated={validated} ref={formRef}>
                    {!isOtpView ? (
                        <>
                            <div>
                                <Form.Group controlId="merchant">
                                    <Form.Label className="d-flex align-items-center">
                                        Merchant
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Select
                                        id="merchant-select"
                                        labelKey="name"
                                        clearButton
                                        selected={selectedMerchant}
                                        placeholder={
                                            merchantsWithReverseBillOption.length ===
                                            0
                                                ? "Must be Active and must have 'reverseBill' option enabled."
                                                : "Select a merchant..."
                                        }
                                        options={
                                            merchantsWithReverseBillOption
                                        }
                                        disabled={
                                            isRequesting ||
                                            isReversing||
                                            merchantsWithReverseBillOption.length ===
                                                0
                                        }
                                        onChange={onSelectMerchant}
                                        required
                                    />
                                </Form.Group>
                                <Form.Group controlId="merchantLocation">
                                    <Form.Label className="d-flex align-items-center">
                                        Merchant Location
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Select
                                        id="select-location"
                                        labelKey="name"
                                        clearButton
                                        selected={selectedLocation}
                                        placeholder={
                                            locations.length === 0
                                                ? "No merchant locations found."
                                                : "Select a location..."
                                        }
                                        options={locations}
                                        disabled={
                                            isRequesting ||
                                            isReversing ||
                                            locations.length === 0
                                        }
                                        onChange={setSelectedLocation}
                                        required
                                    />
                                    {selectedMerchant.length === 0 && (
                                        <small className="font-smaller text-orange">
                                            * Please select a merchant to list
                                            down the merchant's locations.
                                        </small>
                                    )}
                                    {selectedMerchant.length !== 0 &&
                                        locations.length === 0 && (
                                            <small className="font-smaller text-danger">
                                                * Locations must be Active and
                                                must have 'reverse bill' option
                                                enabled.
                                            </small>
                                        )}
                                </Form.Group>
                                <Form.Group>
                                    <Form.Label className="d-flex align-items-center">
                                        Bill Reference Number
                                        <div className="ml-1 text-danger">*</div>
                                    </Form.Label>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter Bill Reference Number"
                                        value={reverseBillStatus.reversalBillReference?.toString() || ''} 
                                        name="reversalBillReference"
                                        disabled={isRequesting || isReversing}
                                        onChange={(e) =>
                                            handleChange({
                                                target: {
                                                    name: e.target.name,
                                                    value: e.target.value, 
                                                },
                                            })
                                        }
                                        required
                                    />
                                </Form.Group>

                                <Form.Label className="d-flex align-items-center">
                                    Method of Reverse Bill
                                    <div className="ml-1 text-danger">*</div>
                                </Form.Label>
                                <Form.Group
                                    controlId="option"
                                    className="input-group"
                                >
                                    <Form.Check
                                        className="rounded-0 input-check mr-4"
                                        custom
                                        disabled={isRequesting || isReversing}
                                        onChange={onChangeOption}
                                        id={ReverseBillOptions.WITH_OTP}
                                        value={ReverseBillOptions.WITH_OTP}
                                        label="With OTP"
                                        name="selectedOption"
                                        checked={
                                            selectedOption ===
                                            ReverseBillOptions.WITH_OTP
                                        }
                                        type="radio"
                                        required
                                    />
                                    <Form.Check
                                        className="rounded-0 input-check mr-4"
                                        custom
                                        disabled={isRequesting || isReversing}
                                        id={ReverseBillOptions.WITHOUT_OTP}
                                        label="Without OTP"
                                        value={ReverseBillOptions.WITHOUT_OTP}
                                        onChange={onChangeOption}
                                        checked={
                                            selectedOption ===
                                            ReverseBillOptions.WITHOUT_OTP
                                        }
                                        name="selectedOption"
                                        type="radio"
                                        required
                                    />
                                </Form.Group>
                                <Form.Group>
                                    <Form.Label className="d-flex align-items-center">
                                        Notes
                                        <div className="ml-1 text-danger">
                                            *
                                        </div>
                                    </Form.Label>
                                    <Form.Control
                                        type="text"
                                        as="textarea"
                                        rows={5}
                                        placeholder="Enter notes"
                                        name="notes"
                                        value={reverseBillStatus.notes}
                                        disabled={isRequesting || isReversing}
                                        onChange={handleChange}
                                        required
                                    />
                                </Form.Group>
                            </div>
                        </>
                    ) : (
                        <>
                            <Form.Group>
                                <Form.Label className="d-flex align-items-center">
                                    OTP
                                    <div className="ml-1 text-danger">*</div>
                                </Form.Label>
                                <Form.Control
                                    name="otpCode"
                                    type="text"
                                    placeholder="Enter OTP"
                                    value={reverseBillStatus.otpCode}
                                    disabled={isRequesting || isReversing}
                                    onChange={handleChange}
                                    required
                                />
                            </Form.Group>
                        </>
                    )}
                </Form>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    disabled={isRequesting || isReversing}
                    onClick={onHide}
                >
                    Cancel
                </Button>
                {reverseBillButton}
            </Modal.Footer>
        </Modal>
    );
};
export default ReverseBill;
