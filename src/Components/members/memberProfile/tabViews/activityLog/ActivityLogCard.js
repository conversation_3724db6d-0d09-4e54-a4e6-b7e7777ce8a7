import React, { useEffect, useMemo, useState } from "react";
import PropTypes from "prop-types";
import {
    <PERSON>ton,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faAngleDown,
    faAngleUp,
    faCreditCard,
    faCoins,
    faGift,
    faFileBookmarkAlt,
} from "FaICIconMap";
import { useToggle } from "Hooks";
import {
    formatToCommonReadableFormat,
    formatToCommonReadableFormatDateOnly,
    isEmptyObject,
    toTitleCase,
    toTitleCaseFromSnakeAndCamel,
} from "Utils";

import "./ActivityLogCard.scss";

const generateShowMoreBtnText = (length = 0) =>
    Number(length - 2) < 10
        ? `0${Number(length - 2)}`
        : `${Number(length - 2)} more`;

const ActivityIcons = {
    12: { icon: faCreditCard },
    10: { icon: faCoins },
    11: { icon: faCoins },
    13: { icon: faGift },
    23: { icon: faGift },
    15: { icon: faGift },
};

const ActivityLogCard = ({ activityLog }) => {
    const [activityLogKeysData, setActivityLogKeysData] = useState([]);
    const [activityLogData, setActivityLogData] = useState([]);
    const [showMore, toggleShowMore] = useToggle(false);

    const activityLogToArr = useMemo(() => {
        if (activityLogData.length > 3) {
            return showMore ? activityLogData : activityLogData.slice(0, 3);
        } else {
            return activityLogData;
        }
    }, [activityLogData, showMore]);

    const lastKeyEl = useMemo(
        () => activityLogKeysData.splice(-1),
        [activityLogKeysData]
    );

    useEffect(() => {
        if (!isEmptyObject(activityLog)) {
            Object.keys(activityLog?.activityData).forEach(
                (key) =>
                    (Array.isArray(activityLog?.activityData[key]) ||
                        typeof activityLog?.activityData[key] === "object") &&
                    delete activityLog?.activityData?.[key]
            );
            setActivityLogData(Object.entries(activityLog?.activityData));
            setActivityLogKeysData(Object.keys(activityLog?.activityData));
        }
    }, [activityLog]);

    return (
        <>
            {activityLog && (
                <Card className="card-body p-2 my-3">
                    <Card.Header className="p-3 font-weight-bold d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                            <IcIcon
                                className="mr-3 text-primary"
                                size="lg"
                                icon={
                                    ActivityIcons[activityLog?.templateId]
                                        ?.icon || faFileBookmarkAlt
                                }
                            />
                            {activityLog?.activityName
                                ? toTitleCase(activityLog.activityName)
                                : "~ unknown activity"}
                        </div>
                        {activityLog?.createdOn
                            ? formatToCommonReadableFormat(
                                activityLog?.createdOn
                            )
                            : "~ date unknown"}
                    </Card.Header>
                    <Card.Body className="p-2">
                        <div>
                            {activityLogToArr.map(([key, value]) => {
                                let pointBundleComp = null;

                                switch (key) {
                                    case "point_bundle":
                                        pointBundleComp = (
                                            <div className="mt-3">
                                                {(() => {
                                                    delete value._id;
                                                    const lastPointBundleKey =
                                                        Object.keys(
                                                            value
                                                        ).splice(-1);

                                                    return Object.entries(
                                                        value
                                                    ).map(
                                                        ([
                                                            pointBundleKey,
                                                            pointBundleValue,
                                                        ]) => (
                                                            <div
                                                                key={
                                                                    pointBundleKey
                                                                }
                                                                className={`
                                                                    d-flex flex-row justify-content-between align-items-center p-2 mx-4 detail-row
                                                                    ${
                                                                        lastPointBundleKey[0] !==
                                                                            pointBundleKey &&
                                                                        "border-bottom"
                                                                    }
                                                                `}
                                                            >
                                                                <div className="text-muted">
                                                                    {pointBundleKey
                                                                        ? toTitleCaseFromSnakeAndCamel(
                                                                            pointBundleKey
                                                                        )
                                                                        : ""}
                                                                </div>
                                                                <div>
                                                                    {pointBundleValue ||
                                                                        "-"}
                                                                </div>
                                                            </div>
                                                        )
                                                    );
                                                })()}
                                            </div>
                                        );
                                        break;
                                    case "gender":
                                        pointBundleComp = (
                                            <div>
                                                {value
                                                    ? toTitleCase(value)
                                                    : "~ unknown"}
                                            </div>
                                        );
                                        break;
                                    case "transactionDate":
                                        pointBundleComp = (
                                            <div>
                                                {value
                                                    ? formatToCommonReadableFormat(
                                                        value
                                                    )
                                                    : "~ date unknown"}
                                            </div>
                                        );
                                        break;
                                    case "birthDate":
                                        pointBundleComp = (
                                            <div>
                                                {value
                                                    ? formatToCommonReadableFormatDateOnly(
                                                        value
                                                    )
                                                    : "~ date unknown"}
                                            </div>
                                        );
                                        break;
                                    default:
                                        pointBundleComp = (
                                            <div>{value || "~ unknown"}</div>
                                        );
                                }

                                return (
                                    <div
                                        key={key}
                                        className={`
                                            d-flex ${
                                                key === "point_bundle"
                                                    ? "flex-column align-items-between"
                                                    : "justify-content-between"
                                            } ${
                                            lastKeyEl[0] !== key &&
                                            "border-bottom"
                                        } py-3 px-2
                                        `}
                                    >
                                        <div className="text-muted">
                                            {key
                                                ? toTitleCaseFromSnakeAndCamel(
                                                    key
                                                )
                                                : ""}
                                        </div>
                                        <div>{pointBundleComp}</div>
                                    </div>
                                );
                            })}
                            {activityLogKeysData.length > 3 && (
                                <div className="detail-row">
                                    <Button
                                        className="btn shadow-none"
                                        variant="none"
                                        size="sm"
                                        onClick={toggleShowMore}
                                    >
                                        <div className="d-flex align-items-center">
                                            {`See ${
                                                showMore
                                                    ? "less"
                                                    : generateShowMoreBtnText(
                                                        activityLogKeysData.length
                                                    )
                                            }
                                            `}
                                            <IcIcon
                                                size="lg"
                                                className="ml-1"
                                                icon={
                                                    showMore
                                                        ? faAngleUp
                                                        : faAngleDown
                                                }
                                            />
                                        </div>
                                    </Button>
                                </div>
                            )}
                        </div>
                    </Card.Body>
                </Card>
            )}
        </>
    );
};

ActivityLogCard.propTypes = { activityLog: PropTypes.object.isRequired };

export default ActivityLogCard;
