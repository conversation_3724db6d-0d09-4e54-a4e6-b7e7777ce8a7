import React, { useCallback, useState, useEffect, useContext } from "react";
import { toast } from "react-toastify";
import { Form, Button } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { addMemberNotes, getMemberNotes } from "Services"
import { LoadingComponent } from "Components/utils";
import Note from "./Note";
import UnauthorizedAccessControl from "../../../../utils/unauthorizedAccessControl/UnauthorizedAccessControl";

const limit = 10, skip = 0;

const Notes = ({ memberId, setIsDisabledTab }) => {
  const { isAuthorizedForAction } = useContext(UserContext);
  const [note, setNote] = useState("");
  const [notes, setNotes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const onChangeNote = useCallback(e => setNote(e.target.value), [setNote]);

  const loadNotes = useCallback(async () => {
    const queryObj = { limit, skip, memberId };

    try {
      setIsDisabledTab(true);
      setIsLoading(true);
      const notesReponses = await getMemberNotes(queryObj);
      setNotes(notesReponses?.items);
    } catch(e) {
      console.error(e);
      toast.error(e.message || "Could not load member notes!");
    } finally {
      setIsDisabledTab(false);
      setIsLoading(false);
    }
  }, [memberId, setNotes, setIsDisabledTab, setIsLoading]);

  const addNote = useCallback(async () => {
    const payload = { memberId, content: note };

    try {
      setIsSaving(true);
      await addMemberNotes(payload);
      toast.success("Note added successfully.");
      setNote("");
      setIsSaving(false);
      loadNotes();
    } catch(e) {
      console.error(e);
      setIsSaving(false);
      const changedErrorMsg = e.message.replace(`"content"`, "Note");
      toast.error(changedErrorMsg || "Failed to add note! Please try again.");
    }
  }, [note, memberId, loadNotes, setNote, setIsSaving])

  useEffect(() => {
    if(memberId) {
      loadNotes();
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [memberId]);

  return (
    <div className="d-flex flex-column">
      {
        isAuthorizedForAction(
          AccessPermissionModuleNames.MEMBER_NOTE,
          AccessPermissionModules[AccessPermissionModuleNames.MEMBER_NOTE].actions.CreateMemberNote
        ) &&
        <div className="py-3 mb-3">
          <p>Note</p>
          <Form.Control
            as="textarea"
            rows={10}
            value={note}
            disabled={isLoading || isSaving}
            onChange={onChangeNote}
            placeholder="Enter note here.."
          />
          <UnauthorizedAccessControl
              actionList={{ [`${AccessPermissionModuleNames.MEMBER_NOTE}`]:[
                  AccessPermissionModules[AccessPermissionModuleNames.MEMBER_NOTE].actions.CreateMemberNote,
                ]}}
              logic={"AND"}
          >
            <Button
                variant="primary"
                size="sm"
                className="float-right mt-3"
                onClick={addNote}
                disabled={isLoading || isSaving || note === ""}
            >
              {isSaving ? "Adding..." : "Add Note"}
            </Button>
          </UnauthorizedAccessControl>
        </div>
      }
      {
        isAuthorizedForAction(
          AccessPermissionModuleNames.MEMBER_NOTE,
          AccessPermissionModules[AccessPermissionModuleNames.MEMBER_NOTE].actions.ListMemberNotes
        ) &&
        <div className="">
          {isLoading ?
            <LoadingComponent />
            :
            <div>
              {notes.length === 0 ?
                <p className="text-center"> No notes attatched to this member.</p>
                :
                notes.map(note =>
                  <Note
                    key={note._id}
                    id={note._id}
                    note={note.content}
                    date={note.createdOn}
                    addedBy={note.createdByName}
                    isAddingNote={isSaving}
                    loadNotes={loadNotes}
                  />
                )
              }
            </div>
          }
        </div>
      }
    </div>
  );
};

export default Notes;
