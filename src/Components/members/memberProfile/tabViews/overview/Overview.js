import React, {
    useState,
    useCallback,
    useEffect,
    useContext,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import moment from "moment";
import PropTypes from "prop-types";
import {
    Row,
    Col,
    DropdownButton,
    IcIcon,
    Dropdown,
    Card,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCalendar } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    AccessPermissionModuleNames,
    AccessPermissionModules,
    PredefinedDatePeriods,
    RewardStatus,
} from "Data";
import {
    getActivities,
    getMemberInsights,
    getMemberPointsBreakdownBySubTransactions,
    getRewards,
} from "Services";
import { LoadingComponent } from "Components/utils/UtilComponents";
import MemberInsights from "./MemberInsights";
import PerformanceMatrices from "./PerformanceMatrices";
import UnlockedRewards from "./UnlockedRewards";
import PointsBreakdownOverview from "./pointsBreakdown/PointsBreakdownOverview";
import ActivityLogCard from "../activityLog/ActivityLogCard";
import MemberPointStats from "./memberPointStats/MemberPointStats";

const Overview = ({
    memberId,
    points,
    regionId,
    setIsDisabledTab,
    memberInsight,
    pointStats,
}) => {
    const { isAuthorizedForAction, selectedRegion } = useContext(UserContext);
    const [isLoadingActivities, setIsLoadingActivities] = useState(false);
    const [selectedPeriod, setSelectedPeriod] = useState("90 Days");
    const [isLoadingMemberInsights, setIsLoadingMemberInsights] =
        useState(false);
    const [activitySet, setActivitySet] = useState([]);
    const [insights, setInsights] = useState([]);
    const [
        isLoadingPointsBreakdownOverview,
        setIsLoadingPointsBreakdownOverview,
    ] = useState(false);
    const [pointsBreakdownOverview, setPointsBreakdownOverview] = useState([]);
    const [isLoadingRewards, setIsLoadingRewards] = useState(false);
    const [unlockedRewards, setUnlockedRewards] = useState([]);

    const currencyCode = useMemo(() => {
        if (selectedRegion) {
            return selectedRegion.defaultCurrencyCode || "$";
        }
        return "$";
    }, [selectedRegion]);

    const loadActivities = useCallback(async () => {
        try {
            setIsDisabledTab(true);
            setIsLoadingActivities(true);
            const queryParams = {
                skip: 0,
                limit: 1000,
                memberId,
                fromDate: moment()
                    .subtract(364, "day")
                    .startOf("day")
                    .toISOString(),
                toDate: moment().endOf("day").toISOString(),
            };
            const activitiesResponse = await getActivities(queryParams);
            setActivitySet(activitiesResponse.items.splice(0, 3));
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load activities!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsDisabledTab(false);
            setIsLoadingActivities(false);
        }
    }, [memberId, setIsDisabledTab, setActivitySet, setIsLoadingActivities]);

    const loadMemberInsights = useCallback(async () => {
        try {
            setIsLoadingMemberInsights(true);
            const memberInsights = await getMemberInsights({
                regionId,
                id: memberId,
                toDate: PredefinedDatePeriods[selectedPeriod]?.toDate,
                fromDate: PredefinedDatePeriods[selectedPeriod]?.fromDate,
            });
            setInsights(memberInsights?.data);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load member insights!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingMemberInsights(false);
        }
    }, [
        memberId,
        regionId,
        selectedPeriod,
        setIsLoadingMemberInsights,
        setInsights,
    ]);

    const loadPointsBreakdownBySubTransactions = useCallback(async () => {
        try {
            setIsLoadingPointsBreakdownOverview(true);
            // * Get the top five points breakdown values.
            const topFivePointsBreakdown =
                await getMemberPointsBreakdownBySubTransactions({
                    limit: 5,
                    skip: 0,
                    regionId,
                    memberId,
                    toDate: PredefinedDatePeriods[selectedPeriod]?.toDate,
                    fromDate: PredefinedDatePeriods[selectedPeriod]?.fromDate,
                });
            setPointsBreakdownOverview(topFivePointsBreakdown?.items);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load points breakdown by sub transaction types!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingPointsBreakdownOverview(false);
        }
    }, [
        memberId,
        regionId,
        selectedPeriod,
        setIsLoadingPointsBreakdownOverview,
        setPointsBreakdownOverview,
    ]);

    const loadRewards = useCallback(async () => {
        const queryObj = {
            limit: 5,
            skip: 0,
            regionId,
            status: RewardStatus.ENABLED,
            pointsUpperMargin: points || 1,
        };
        try {
            setIsLoadingRewards(true);
            const rewardsResponse = await getRewards(queryObj);
            setUnlockedRewards(rewardsResponse.items);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load unlocked rewards!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingRewards(false);
        }
    }, [regionId, points, setUnlockedRewards, setIsLoadingRewards]);

    useEffect(() => {
        loadRewards();
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (memberId) {
            loadActivities();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [memberId]);

    useEffect(() => {
        if (memberId && selectedPeriod) {
            loadMemberInsights();
            loadPointsBreakdownBySubTransactions();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [memberId, selectedPeriod]);

    return (
        <Row>
            {isAuthorizedForAction(
                AccessPermissionModuleNames.ACTIVITY,
                AccessPermissionModules[AccessPermissionModuleNames.ACTIVITY]
                    .actions.ListActivities
            ) && (
                <Col>
                    {isLoadingActivities ? (
                        <LoadingComponent />
                    ) : (
                        <>
                            {activitySet.length > 0 ? (
                                activitySet.map((activityLog) => (
                                    <ActivityLogCard
                                        key={activityLog._id}
                                        activityLog={activityLog}
                                    />
                                ))
                            ) : (
                                <div className="mx-auto my-4">
                                    <p className="no-rewards-text text-center">
                                        No activity logs found.
                                    </p>
                                </div>
                            )}
                        </>
                    )}
                </Col>
            )}
            <Col className="mt-3" xl="5" lg="5" md="5">
                <Card className="px-3 pt-3 mb-3">
                    <div className="d-flex justify-content-end mb-2">
                        <DropdownButton
                            bsPrefix="text-capitalize btn btn-sm p-0"
                            id="dropdown-basic-button"
                            title={
                                <>
                                    <IcIcon
                                        className="mx-2"
                                        size="w-16"
                                        icon={faCalendar}
                                    />
                                    <span>{selectedPeriod}</span>
                                </>
                            }
                            onSelect={setSelectedPeriod}
                            className="mr-n2"
                        >
                            {Object.keys(PredefinedDatePeriods).map((key) => {
                                return (
                                    <Dropdown.Item
                                        eventKey={key}
                                        key={key}
                                        data-key={key}
                                    >
                                        {key}
                                    </Dropdown.Item>
                                );
                            })}
                        </DropdownButton>
                    </div>
                    <PerformanceMatrices
                        customerLifeTimeValue={
                            memberInsight?.customerLifeTimeValue || 0
                        }
                        isLoadingMemberInsights={isLoadingMemberInsights}
                        currencyCode={currencyCode}
                    />
                    <MemberInsights
                        avgBasketSizeDollar={insights?.avgBasketSizeDollar || 0}
                        totalBasketSizeDollar={
                            insights?.totalBasketSizeDollar || 0
                        }
                        avgBasketSizePoints={insights?.avgBasketSizePoints || 0}
                        totalBasketSizePoints={
                            insights?.totalBasketSizePoints || 0
                        }
                        avgNumberOfVisits={insights?.avgNumberOfVisits || 0}
                        isLoadingMemberInsights={isLoadingMemberInsights}
                        currencyCode={currencyCode}
                    />
                    <PointsBreakdownOverview
                        regionId={regionId}
                        memberId={memberId}
                        selectedPeriod={selectedPeriod}
                        isLoadingPointsBreakdownOverview={
                            isLoadingPointsBreakdownOverview
                        }
                        pointsBreakdownOverview={pointsBreakdownOverview}
                    />
                </Card>
                {pointStats &&
                    typeof pointStats === "object" &&
                    Object.keys(pointStats).length !== 0 && (
                        <Card className="px-3 pt-3 mb-3">
                            <MemberPointStats pointStats={pointStats} />
                        </Card>
                    )}
                {unlockedRewards.length !== 0 && (
                    <UnlockedRewards
                        isLoading={isLoadingRewards}
                        rewardData={unlockedRewards}
                    />
                )}
            </Col>
        </Row>
    );
};

Overview.defaultProps = {
    regionId: "",
    memberId: "",
    memberInsight: {},
    setIsDisabledTab: () => {},
};

Overview.propTypes = {
    regionId: PropTypes.string,
    memberId: PropTypes.string,
    points: PropTypes.number,
    memberInsight: PropTypes.object,
    setIsDisabledTab: PropTypes.func,
};

export default Overview;
