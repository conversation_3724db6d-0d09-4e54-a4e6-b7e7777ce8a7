// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PointsTable component snapshot Matches the snapshot 1`] = `
<div
  className="h-100"
>
  <div
    className="bs-table-overlay"
  >
    <div
      className="loading-overlay "
    >
      <div
        className="loading-component"
        data-testid="loading-component"
      >
        <div
          className="loader-content"
          data-testid="loading-content"
        />
      </div>
    </div>
    <div
      className="react-bootstrap-table"
    >
      <table
        className="table table-bordered bs-table table-borderless table-hover"
      >
        <thead>
          <tr
            className="bs-header"
          >
            <th
              style={
                Object {
                  "width": "22%",
                }
              }
              tabIndex={0}
            >
              <div
                className="d-flex align-items-center"
              >
                <svg
                  className="ic-icon icon-lg mr-2 text-black"
                  fill="currentColor"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M19,4H17V3a1,1,0,0,0-2,0V4H9V3A1,1,0,0,0,7,3V4H5A3,3,0,0,0,2,7V19a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V7A3,3,0,0,0,19,4Zm1,15a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V12H20Zm0-9H4V7A1,1,0,0,1,5,6H7V7A1,1,0,0,0,9,7V6h6V7a1,1,0,0,0,2,0V6h2a1,1,0,0,1,1,1Z"
                  />
                </svg>
                <div
                  className="py-1"
                >
                  Transaction Date
                </div>
              </div>
            </th>
            <th
              style={
                Object {
                  "width": "15%",
                }
              }
              tabIndex={0}
            >
              <div
                className="d-flex align-items-center"
              >
                <svg
                  className="ic-icon icon-lg mr-2 text-black"
                  fill="currentColor"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M21.22,12A3,3,0,0,0,22,10a3,3,0,0,0-3-3H13.82A3,3,0,0,0,11,3H5A3,3,0,0,0,2,6a3,3,0,0,0,.78,2,3,3,0,0,0,0,4,3,3,0,0,0,0,4A3,3,0,0,0,2,18a3,3,0,0,0,3,3H19a3,3,0,0,0,2.22-5,3,3,0,0,0,0-4ZM11,19H5a1,1,0,0,1,0-2h6a1,1,0,0,1,0,2Zm0-4H5a1,1,0,0,1,0-2h6a1,1,0,0,1,0,2Zm0-4H5A1,1,0,0,1,5,9h6a1,1,0,0,1,0,2Zm0-4H5A1,1,0,0,1,5,5h6a1,1,0,0,1,0,2Zm8.69,11.71A.93.93,0,0,1,19,19H13.82a2.87,2.87,0,0,0,0-2H19a1,1,0,0,1,1,1A1,1,0,0,1,19.69,18.71Zm0-4A.93.93,0,0,1,19,15H13.82a2.87,2.87,0,0,0,0-2H19a1,1,0,0,1,1,1A1,1,0,0,1,19.69,14.71Zm0-4A.93.93,0,0,1,19,11H13.82a2.87,2.87,0,0,0,0-2H19a1,1,0,0,1,1,1A1,1,0,0,1,19.69,10.71Z"
                  />
                </svg>
                <div
                  className="py-1"
                >
                  Points
                </div>
              </div>
            </th>
            <th
              style={
                Object {
                  "width": "20%",
                }
              }
              tabIndex={0}
            >
              <div
                className="d-flex align-items-center"
              >
                <svg
                  className="ic-icon icon-lg mr-2 text-black"
                  fill="currentColor"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22,7.82a1.25,1.25,0,0,0,0-.19v0h0l-2-5A1,1,0,0,0,19,2H5a1,1,0,0,0-.93.63l-2,5h0v0a1.25,1.25,0,0,0,0,.19A.58.58,0,0,0,2,8H2V8a4,4,0,0,0,2,3.4V21a1,1,0,0,0,1,1H19a1,1,0,0,0,1-1V11.44A4,4,0,0,0,22,8V8h0A.58.58,0,0,0,22,7.82ZM13,20H11V16h2Zm5,0H15V15a1,1,0,0,0-1-1H10a1,1,0,0,0-1,1v5H6V12a4,4,0,0,0,3-1.38,4,4,0,0,0,6,0A4,4,0,0,0,18,12Zm0-10a2,2,0,0,1-2-2,1,1,0,0,0-2,0,2,2,0,0,1-4,0A1,1,0,0,0,8,8a2,2,0,0,1-4,.15L5.68,4H18.32L20,8.15A2,2,0,0,1,18,10Z"
                  />
                </svg>
                <div
                  className="py-1"
                >
                  Merchant
                </div>
              </div>
            </th>
            <th
              style={
                Object {
                  "width": "20%",
                }
              }
              tabIndex={0}
            >
              <div
                className="d-flex align-items-center"
              >
                <svg
                  className="ic-icon icon-lg mr-2 text-black"
                  fill="currentColor"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12,10.8a2,2,0,1,0-2-2A2,2,0,0,0,12,10.8Zm-.71,6.91a1,1,0,0,0,1.42,0l4.09-4.1a6.79,6.79,0,1,0-9.6,0ZM7.23,8.34A4.81,4.81,0,0,1,9.36,4.79a4.81,4.81,0,0,1,5.28,0,4.82,4.82,0,0,1,.75,7.41L12,15.59,8.61,12.2A4.77,4.77,0,0,1,7.23,8.34ZM19,20H5a1,1,0,0,0,0,2H19a1,1,0,0,0,0-2Z"
                  />
                </svg>
                <div
                  className="py-1"
                >
                  Location
                </div>
              </div>
            </th>
            <th
              style={
                Object {
                  "width": "20%",
                }
              }
              tabIndex={0}
            >
              <div
                className="d-flex align-items-center"
              >
                <svg
                  className="ic-icon icon-lg mr-2 text-black"
                  fill="currentColor"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M7,15h3a1,1,0,0,0,0-2H7a1,1,0,0,0,0,2ZM19,5H5A3,3,0,0,0,2,8v9a3,3,0,0,0,3,3H19a3,3,0,0,0,3-3V8A3,3,0,0,0,19,5Zm1,12a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V11H20Zm0-8H4V8A1,1,0,0,1,5,7H19a1,1,0,0,1,1,1Z"
                  />
                </svg>
                <div
                  className="py-1"
                >
                  Card Number
                </div>
              </div>
            </th>
            <th
              style={
                Object {
                  "width": "20%",
                }
              }
              tabIndex={0}
            >
              <div
                className="d-flex align-items-center"
              >
                <svg
                  className="ic-icon icon-lg mr-2 text-black"
                  fill="currentColor"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16,10H14V8a1,1,0,0,0-1-1H8A1,1,0,0,0,7,8v5a1,1,0,0,0,1,1h2v2a1,1,0,0,0,1,1h5a1,1,0,0,0,1-1V11A1,1,0,0,0,16,10Zm-6,1v1H9V9h3v1H11A1,1,0,0,0,10,11Zm5,4H12V12h3Zm6,3.28V5.72A2,2,0,1,0,18.28,3H5.72A2,2,0,1,0,3,5.72V18.28A2,2,0,1,0,5.72,21H18.28A2,2,0,1,0,21,18.28Zm-2,0a1.91,1.91,0,0,0-.72.72H5.72A1.91,1.91,0,0,0,5,18.28V5.72A1.91,1.91,0,0,0,5.72,5H18.28a1.91,1.91,0,0,0,.72.72Z"
                  />
                </svg>
                <div
                  className="py-1"
                >
                  Sub Type
                </div>
              </div>
            </th>
          </tr>
        </thead>
        <tbody
          className="bs-body"
        >
          <tr>
            <td
              className="react-bs-table-no-data"
              colSpan={6}
              data-toggle="collapse"
            >
              <div>
                No data found.
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div
    className="row react-bootstrap-table-pagination"
  >
    <div
      className="col-md-6 col-xs-6 col-sm-6 col-lg-6"
    >
      <span
        className="react-bs-table-sizePerPage-dropdown dropdown"
        style={
          Object {
            "visibility": "visible",
          }
        }
      >
        <button
          aria-expanded={false}
          className="btn btn-default btn-secondary dropdown-toggle"
          data-toggle="dropdown"
          id="pageDropDown"
          onBlur={[Function]}
          onClick={[Function]}
          type="button"
        >
          10
           
        </button>
        <ul
          aria-labelledby="pageDropDown"
          className="dropdown-menu "
          role="menu"
        >
          <a
            className="dropdown-item"
            data-page={10}
            href="#"
            onMouseDown={[Function]}
            role="menuitem"
            tabIndex="-1"
          >
            10
          </a>
          <a
            className="dropdown-item"
            data-page={25}
            href="#"
            onMouseDown={[Function]}
            role="menuitem"
            tabIndex="-1"
          >
            25
          </a>
          <a
            className="dropdown-item"
            data-page={30}
            href="#"
            onMouseDown={[Function]}
            role="menuitem"
            tabIndex="-1"
          >
            30
          </a>
          <a
            className="dropdown-item"
            data-page={50}
            href="#"
            onMouseDown={[Function]}
            role="menuitem"
            tabIndex="-1"
          >
            50
          </a>
        </ul>
      </span>
    </div>
    <div
      className="react-bootstrap-table-pagination-list col-md-6 col-xs-6 col-sm-6 col-lg-6"
    >
      <ul
        className="pagination react-bootstrap-table-page-btns-ul"
      />
    </div>
  </div>
</div>
`;
