import React, { useCallback, useState, useContext } from "react";
import { toast } from "react-toastify";
import {
    Button,
    FormControl,
    FormLabel,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { cancelReward, claimReward } from "Services";

const RedeemedRewardsActions = ({
    actionType,
    show,
    onHide,
    currentRedemption,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [isClaiming, setIsClaiming] = useState(false);
    const [isCanceling, setIsCanceling] = useState(false);
    const [voucherCode, setVoucherCode] = useState("");
    const [cancelNotes, setCancelNotes] = useState("");

    const onChange = useCallback(
        (e) => {
            if (actionType === "Claim") {
                setVoucherCode(e.target.value);
            } else {
                setCancelNotes(e.target.value);
            }
        },
        [actionType, setVoucherCode, setCancelNotes]
    );

    const onClaimReward = useCallback(async () => {
        const paylaod = { voucherCode };

        try {
            setIsClaiming(true);
            const claimedRes = await claimReward(paylaod);
            setVoucherCode("");
            setIsClaiming(false);
            toast.success(
                `Successfully claimed the reward${
                    " - " + currentRedemption?.reward?.name || ""
                }.`
            );
            onHide(null, claimedRes);
        } catch (e) {
            setIsClaiming(false);
            console.error(e);
            toast.error(
                <div>
                    Failed to claim the reward!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        voucherCode,
        currentRedemption?.reward?.name,
        onHide,
        setIsClaiming,
        setVoucherCode,
    ]);

    const onCancelReward = useCallback(async () => {
        const paylaod = { notes: cancelNotes };

        try {
            setIsCanceling(true);
            const canceledRes = await cancelReward(
                currentRedemption?._id,
                paylaod
            );
            setCancelNotes("");
            setIsCanceling(false);
            toast.success(
                `Successfully cancelled the reward${
                    " - " + currentRedemption?.reward?.name || ""
                }.`
            );
            onHide(null, canceledRes);
        } catch (e) {
            setIsCanceling(false);
            console.error(e);
            toast.error(
                <div>
                    Failed to cancel the reward!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        currentRedemption?._id,
        currentRedemption?.reward?.name,
        cancelNotes,
        onHide,
        setIsCanceling,
        setCancelNotes,
    ]);

    return (
        <Modal
            show={show}
            onHide={isClaiming || isCanceling ? () => {} : onHide}
            backdrop={isClaiming || isCanceling ? "static" : true}
            centered
        >
            <Modal.Header closeButton={!(isClaiming || isCanceling)}>
                <Modal.Title>{actionType} Reward</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div>
                    {`Do you wish to ${actionType.toLowerCase()} this reward?`}
                    {actionType === "Claim" && (
                        <div className="my-3">
                            <FormLabel className="d-flex align-items-center">
                                Enter Voucher Code
                                <div className="ml-1 text-danger">*</div>
                            </FormLabel>
                            <FormControl
                                value={voucherCode}
                                placeholder="Enter voucher code"
                                disabled={isClaiming}
                                onChange={onChange}
                            />
                        </div>
                    )}
                    {actionType === "Cancel" && (
                        <div className="my-3">
                            <FormLabel className="d-flex align-items-center">
                                Notes
                                <div className="ml-1 text-danger">*</div>
                            </FormLabel>
                            <FormControl
                                as="textarea"
                                rows={3}
                                placeholder="Enter reason to cancel"
                                value={cancelNotes}
                                disabled={isCanceling}
                                onChange={onChange}
                            />
                        </div>
                    )}
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    variant="outline-primary"
                    size="sm"
                    disabled={actionType === "Claim" ? isClaiming : isCanceling}
                    onClick={onHide}
                >
                    Close
                </Button>
                {(isAuthorizedForAction(
                    AccessPermissionModuleNames.REWARD,
                    AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                        .actions.CancelLogItem
                ) ||
                    isAuthorizedForAction(
                        AccessPermissionModuleNames.REWARD,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.REWARD
                        ].actions.ClaimReward
                    )) && (
                    <Button
                        variant={
                            actionType === "Claim"
                                ? "outline-secondary"
                                : "outline-danger"
                        }
                        size="sm"
                        disabled={
                            actionType === "Claim"
                                ? isClaiming || !voucherCode
                                : isCanceling || !cancelNotes
                        }
                        onClick={
                            actionType === "Claim"
                                ? onClaimReward
                                : onCancelReward
                        }
                    >
                        {actionType === "Claim" ? (
                            <div>
                                {isClaiming && "Claiming..."}
                                {!isClaiming && "Claim Reward"}
                            </div>
                        ) : (
                            <div>
                                {isCanceling && "Canceling..."}
                                {!isCanceling && "Cancel Reward"}
                            </div>
                        )}
                    </Button>
                )}
            </Modal.Footer>
        </Modal>
    );
};

export default RedeemedRewardsActions;
