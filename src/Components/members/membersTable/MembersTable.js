import React, { useContext, useCallback, useMemo } from "react";
import { useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import {
    Badge,
    Avatar,
    IcIcon,
    BootstrapTable,
    Row,
    Col,
    OverlayTrigger,
    Tooltip,
} from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faUser,
    faCoins,
    faMedal,
    faCalendar,
    faEnvelope,
    faMobileAlt,
    faAddressCard,
} from "FaICIconMap";
import { MembersContext, DataContext, UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import {
    toTitleCase,
    getNonSystemAttributes,
    truncateLongString,
    dateFormat,
    getTruncatedStringWithTooltip,
    getMemberFullName,
} from "Utils";
import VerifiedCheck from "Components/common/verifiedCheck/VerifiedCheck";
import { BootstrapTableOverlay } from "Components/utils/UtilComponents";
import ColumnSelection from "../columnSelection/ColumnSelection";
import SizePerPageRenderer from "../../utils/table/sizePerPageRenderer/SizePerPageRenderer";

import "./MembersTable.scss";

const maxColumns = 6;

const sortCaret = (order) => {
    return <span className={`${order ? "caret-4-" + order : "order-4"}`} />;
};

const nameRow = ({ name, image, loyaltyId, isVerified = false }) => {
    return (
        <div className="name-container stroke-left d-flex flex-row align-items-center">
            <Avatar
                name={!image && (name || "U")}
                size="md"
                src={image}
                alt="Profile Avatar"
            />
            <div className="ml-3">
                <div className="d-flex align-items-center">
                    {getTruncatedStringWithTooltip({
                        value: name,
                        valueMaxLength: 12,
                    })}
                    {isVerified && (
                        <VerifiedCheck
                            className="ml-2"
                            showTooltip
                            tooltipText="Verified Member"
                            placement="right"
                        />
                    )}
                </div>
                <div className="text-muted">{loyaltyId}</div>
            </div>
        </div>
    );
};

const nameContact = ({ email, mobileNumber }) => (
    <OverlayTrigger
        placement="bottom"
        overlay={
            <Tooltip id="tooltip-contact">
                {`Email: ${email || "~ unknown"} | Mobile Number: ${
                    mobileNumber || "~ unknown"
                }`}
            </Tooltip>
        }
    >
        <div>
            <div className="d-flex align-items-center">
                <IcIcon icon={faEnvelope} size="md" className="mr-2" />
                {email ? truncateLongString(email, 15) : "~ unknown"}
            </div>
            <div className="d-flex align-items-center">
                <IcIcon icon={faMobileAlt} size="md" className="mr-2" />
                {mobileNumber || "~ unknown"}
            </div>
        </div>
    </OverlayTrigger>
);

const defaultColumnTemplate = ({ name, icon, type, ...rest }) => {
    const isDateField = type === "Date";
    const displayName = isDateField
        ? toTitleCase(name).replace(/On$/, "") + " On"
        : toTitleCase(name).replace(/\(\)$/, "");
    return {
        dataField: name,
        text: (
            <>
                {icon && <IcIcon className="mr-2 text-black" icon={icon} />}
                {displayName}
            </>
        ),
        sort: true,
        sortCaret,
        formatter: isDateField ? (cell) => dateFormat(cell) : undefined,
        ...rest,
    };
};

const defaultColumns = [
    {
        name: "name",
        icon: faUser,
        hidden: false,
        headerStyle: { width: "20%" },
    },
    {
        name: "contact",
        icon: faAddressCard,
        hidden: false,
        sort: false,
        headerStyle: { width: "50%" },
    },
    { name: "points", hidden: false, icon: faCoins },
    { name: "tier", hidden: false, icon: faMedal },
    { name: "createdOn", hidden: false, icon: faCalendar },
    { name: "lastSeenOn", hidden: false, icon: faCalendar },
];

const NoDataIndication = ({ loading }) => {
    if (loading) return null;
    return <div>No members found.</div>;
};

const MembersTable = () => {
    const { config, isAuthorizedForAction } = useContext(UserContext);
    const { contactAttributes, affinityGroups } = useContext(DataContext);
    const {
        totalCount,
        setSortingOrder,
        memberList,
        isLoading,
        isExporting,
        onChangePagination,
        limit = 25,
        skip = 1,
        countMembers,
    } = useContext(MembersContext);
    const history = useHistory();

    const selectedTableColumns = useMemo(() => {
        return new Set(config.memberTableColumns || []);
    }, [config.memberTableColumns]);

    const getAffinityGroupName = useCallback(
        (affinityGroupId) => {
            if (affinityGroupId) {
                const affinityGroup = affinityGroups.filter(
                    (affinityGroup) => affinityGroup?._id === affinityGroupId
                );
                if (affinityGroup && affinityGroup.length !== 0) {
                    return affinityGroup[0].name;
                } else {
                    return "~ unknown";
                }
            } else {
                return "~ unknown";
            }
        },
        [affinityGroups]
    );

    const onRowClick = useCallback(
        (rowData) => {
            if (
                isAuthorizedForAction(
                    AccessPermissionModuleNames.MEMBER,
                    AccessPermissionModules[AccessPermissionModuleNames.MEMBER]
                        .actions.GetMember
                )
            ) {
                history.push(`/members/${rowData.id}`);
            }
        },
        [history, isAuthorizedForAction]
    );

    const columns = useMemo(() => {
        const columns = [];
        defaultColumns.forEach((item) => {
            const columnType = contactAttributes[item.name]?.type || null;
            columns.push(
                defaultColumnTemplate({
                    name: item.name,
                    icon: item.icon,
                    type: columnType,
                    hidden: !selectedTableColumns.has(item.name),
                })
            );
        });

        const groupedColumns = new Set([
            "email",
            "mobileNumber",
            "firstName",
            "lastName",
            "name",
            "loyaltyId",
            "createdOn",
            "lastSeenOn",
            "points",
            "tier",
            "rewardMetadata",
            "residentialAddress",
            "memberInsight",
            "pointsToExpire.label",
            "pointsToExpire.type",
            "customAttributes.label",
            "customAttributes.type",
            "customAttributes.legacyId",
            "notificationPreference.allowPromotionalNotifications",
            "notificationPreference.preferredChannel",
            "tier.tierId",
            "pointsToExpire",
            "pointsExpireOn",
            "isVerified",
            "__v",
        ]);

        try {
            // * Destructured and extracted out the "pointStats" attribute instead of deleting, to avoid any potential reference errors of the "pointStats" attribute in other places of the code base.
            // eslint-disable-next-line no-unused-vars
            const { pointStats, ...restOfContactAttributes } =
                contactAttributes;

            Object.keys(getNonSystemAttributes(restOfContactAttributes || {}))
                .sort((a, b) => toTitleCase(a) - toTitleCase(b))
                .forEach((key) => {
                    if (
                        !groupedColumns.has(key) &&
                        !key.startsWith("customAttributes") &&
                        restOfContactAttributes[key]
                    ) {
                        columns.push(
                            defaultColumnTemplate({
                                dataField: key,
                                name:
                                    restOfContactAttributes[key]?.label || key,
                                hidden: !selectedTableColumns.has(key),
                            })
                        );
                    }
                });
        } catch (err) {
            console.error(err);
            toast.error(
                <div>
                    Failed to generate dynamic columns!
                    <br />
                    {err.message
                        ? `Error: ${err.message}`
                        : "Please try again later."}
                </div>
            );
        }
        ///remove all nested attribute from columns
        return columns.filter(col => !col.dataField || !col.dataField.includes('.'));
    }, [contactAttributes, selectedTableColumns]);

    const Data = useMemo(
        () =>
            memberList.map((member) => {
                const newMapData = {};

                if (!("residentialAddress" in member)) {
                    member["residentialAddress"] = null;
                }
                if (!("tierData" in member)) {
                    member["tierData"] = null;
                }
                if (!("tier" in member)) {
                    member["tier"] = null;
                }
                if (!("rewardMetadata" in member)) {
                    member["rewardMetadata"] = null;
                }

                Object.keys(member).forEach((key) => {
                    switch (key) {
                        case "_id":
                            newMapData.id = member[key];
                            break;
                        case "firstName":
                        case "lastName":
                        case "isVerified": {
                            newMapData.name = nameRow({
                                name: getMemberFullName({
                                    firstName: member?.firstName,
                                    lastName: member?.lastName,
                                }),
                                image: member?.profilePicture,
                                loyaltyId: member?.loyaltyId,
                                isVerified: member?.isVerified,
                            });
                            break;
                        }
                        case "affinityGroup":
                            newMapData.affinityGroup = getAffinityGroupName(
                                member?.affinityGroup?.affinityGroupId ||
                                    undefined
                            );
                            break;
                        case "email":
                        case "mobileNumber":
                            newMapData.contact = nameContact({
                                email: member.email,
                                mobileNumber: member.mobileNumber,
                            });
                            break;
                        case "status":
                            newMapData.status = (
                                <Badge
                                    className="py-2 px-3"
                                    variant={member?.status || "default"}
                                >
                                    {member?.status
                                        ? toTitleCase(member.status)
                                        : "~ unknown"}
                                </Badge>
                            );
                            break;
                        case "type":
                            newMapData.type = (
                                <Badge
                                    className="py-2 px-3"
                                    variant={member?.type || "default"}
                                >
                                    {member?.type
                                        ? toTitleCase(member.type)
                                        : "~ unknown"}
                                </Badge>
                            );
                            break;
                        case "tier":
                            newMapData.tier = member?.tierData ? (
                                <Badge className="py-2 px-3" variant="warning">
                                    {member?.tierData?.name ||
                                        member?.tierData?._id}
                                </Badge>
                            ) : (
                                <Badge className="py-2 px-3" variant="default">
                                    No Tier Found
                                </Badge>
                            );
                            break;
                        case "createdOn":
                            newMapData.createdOn = dateFormat(member.createdOn);
                            break;
                        case "updatedOn":
                            newMapData.updatedOn = dateFormat(member.updatedOn);
                            break;
                        case "birthDate":
                            newMapData.birthDate =
                                member.hasOwnProperty("birthDate") &&
                                member.birthDate
                                    ? member.birthDate.split("T")[0]
                                    : "-";
                            break;
                        case "lastTransactionOn":
                            newMapData.lastTransactionOn = dateFormat(
                                member.lastTransactionOn
                            );
                            break;
                        case "lastSeenOn":
                            newMapData.lastSeenOn = dateFormat(
                                member.lastSeenOn
                            );
                            break;
                        case "registeredOn":
                            newMapData.registeredOn = dateFormat(
                                member.registeredOn
                            );
                            break;
                        case "loyaltyId":
                            newMapData.loyaltyId = member.loyaltyId;
                            break;
                        default:
                            newMapData[key] = member[key];
                    }
                    return null;
                });

                delete newMapData.pointStats;

                return newMapData;
            }),
        [memberList, getAffinityGroupName]
    );

    const handleTableChange = useCallback(
        (type, data) => {
            if (type === "sort") {
                if (data.sortField === "name") {
                    data.sortField = "firstName";
                } else if (data.sortField === "contact") {
                    data.sortField = "email";
                }
                setSortingOrder({
                    sortBy: data.sortField,
                    sortDirection: data.sortOrder,
                });
            }
        },
        [setSortingOrder]
    );

    const tableRowEvents = useMemo(
        () => ({
            ...(!isExporting ? { onClick: (e, row) => onRowClick(row) } : {}),
        }),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [isExporting]
    );

    const options = {
        page: skip,
        sizePerPage: limit,
        totalSize: totalCount,
        paginationSize: 5,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageRenderer: SizePerPageRenderer,
        sizePerPageList: [
            {
                text: "5",
                value: 5,
            },
            {
                text: "25",
                value: 25,
            },
            {
                text: "50",
                value: 50,
            },
            {
                text: "100",
                value: 100,
            },
        ],
        onPageChange: (page) => {
            onChangePagination({
                skip: page,
            });
        },
        onSizePerPageChange: (sizePerPage) => {
            onChangePagination({
                limit: sizePerPage,
            });
        },
    };

    return (
        <>
            <div className="h-100 member-table">
                <PaginationProvider
                    pagination={paginationFactory(options)}
                    keyField="id"
                    columns={columns}
                    data={Data}
                >
                    {({ paginationTableProps }) => (
                        <ToolkitProvider
                            keyField="id"
                            data={Data}
                            columns={columns}
                            columnToggle
                        >
                            {(props) => (
                                <div>
                                    <Row noGutters={true} className=" pt-2">
                                        <Col>
                                            <small className="mb-0">
                                                {countMembers && isLoading ? (
                                                    "Loading..."
                                                ) : (
                                                    <>
                                                        {`Total Members: `}
                                                        <span className="font-weight-bold">
                                                            {totalCount}
                                                        </span>
                                                    </>
                                                )}
                                            </small>
                                        </Col>
                                        <Col>
                                            <div className="float-right">
                                                <ColumnSelection
                                                    {...props.columnToggleProps}
                                                    fixedColumns={
                                                        new Set(["name"])
                                                    }
                                                    maxColumns={maxColumns}
                                                    isLoading={isLoading}
                                                />
                                            </div>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <BootstrapTable
                                        {...paginationTableProps}
                                        remote={{
                                            search: true,
                                            pagination: true,
                                        }}
                                        rowEvents={tableRowEvents}
                                        onTableChange={handleTableChange}
                                        loading={isLoading}
                                        noDataIndication={() => (
                                            <NoDataIndication
                                                loading={isLoading}
                                            />
                                        )}
                                        overlay={BootstrapTableOverlay}
                                        {...props.baseProps}
                                    />
                                </div>
                            )}
                        </ToolkitProvider>
                    )}
                </PaginationProvider>
            </div>
        </>
    );
};

export default MembersTable;
