import React, {
    useCallback,
    useContext,
    useImperativeHandle,
    useRef,
    useState,
} from "react";
import { toast } from "react-toastify";
import {
    Button,
    Form,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { MemberTypes, PreferredContactMethods } from "Data";
import { getMemberFullName, toTitleCase } from "Utils";
import CopyItemToClipboard from "Components/common/copyItemToClipboard/CopyItemToClipboard";
import { CreateMemberContext } from "./context/EnrollMembersContext";

const EnrollMemberThirdPage = (props, ref) => {
    const {
        setPageValue,
        nearStore,
        preferredContact,
        createMember,
        createCharity,
        memberAction,
        landLineNumber,
        isAgreedPromotions,
        onClickOnCheckbox,
        nearestStore,
        isLoadingNearestStore,
        isGenerating,
        isCreating,
        isAssigning,
        onSelectNearestStore,
        getNearestStore,
    } = useContext(CreateMemberContext);
    const [validated, setValidated] = useState(false);
    const formRef = useRef();

    const onChangeHandler = useCallback(
        (event) => setPageValue(event),
        [setPageValue]
    );

    useImperativeHandle(ref, () => ({
        isValidated() {
            const formValid = formRef.current.checkValidity();
            if (!formValid) {
                setValidated(true);
            }
            return formValid;
        },
        async onClickNext() {
            let data;
            let createdItemName;

            try {
                if (memberAction === "charity") {
                    data = await createCharity();
                    createdItemName = data?.response?.preferredName;
                } else {
                    data = await createMember();
                    createdItemName = getMemberFullName({
                        firstName: data?.response?.firstName,
                        lastName: data?.response?.lastName,
                    });
                }

                if (data?.error === "CARD_ASSIGN_FAILED") {
                    toast.warning(
                        <div>
                            {"Failed to assign the loyalty card "}
                            <span className="font-weight-bold">
                                {data?.failedToAssignCard || "~ unknown"}
                            </span>
                            {` to the ${
                                memberAction === "addSecondary"
                                    ? MemberTypes.SECONDARY + " "
                                    : ""
                            }member!`}
                            <br />
                            Please assign it later inside the member's profile.
                            {data?.failedToAssignCard ? (
                                <div className="mt-3">
                                    <CopyItemToClipboard
                                        dataToCopy={data.failedToAssignCard}
                                        copyToast={`Copied failed to assign loyalty card "${data.failedToAssignCard}" to clipboard.`}
                                        noToolTip
                                    />
                                </div>
                            ) : null}
                        </div>,
                        { autoClose: false }
                    );
                }

                toast.success(
                    `Successfully ${
                        memberAction === "charity"
                            ? "created the charity"
                            : "enrolled the member"
                    } "${createdItemName || "~ unknown"}"${
                        memberAction === "addSecondary"
                            ? " as a " + MemberTypes.SECONDARY + " member"
                            : ""
                    }.`
                );
            } catch (err) {
                return Promise.reject(err);
            }
        },
    }));

    return (
        <div ref={ref} className="mt-5">
            <Form ref={formRef} validated={validated}>
                <Form.Group>
                    <Form.Label className="d-flex align-items-center">
                        {isLoadingNearestStore ? (
                            "Loading store locations..."
                        ) : (
                            <>
                                Nearest Store
                                <div className="ml-1 text-danger">*</div>
                            </>
                        )}
                    </Form.Label>
                    <div className="w-100 d-flex align-items-center">
                        <div className="w-100">
                            <Form.Select
                                clearButton
                                id="basic-typeahead-multi"
                                labelKey="locationName"
                                onChange={onSelectNearestStore}
                                options={nearestStore}
                                selected={nearStore}
                                placeholder={
                                    nearestStore.length === 0
                                        ? "No nearest store locations found."
                                        : "Select nearest store location"
                                }
                                disabled={
                                    isLoadingNearestStore ||
                                    isGenerating ||
                                    isCreating ||
                                    isAssigning ||
                                    nearestStore.length === 0
                                }
                                groupBy="merchantName"
                                required
                            />
                        </div>
                        {!isLoadingNearestStore && (
                            <Button
                                className="ml-3 p-0 btn shadow-none"
                                size="sm"
                                variant="link"
                                disabled={
                                    isGenerating || isCreating || isAssigning
                                }
                                onClick={getNearestStore}
                            >
                                <IcIcon size="lg" icon={faSync} />
                            </Button>
                        )}
                    </div>
                    {!isLoadingNearestStore && nearestStore.length === 0 && (
                        <small className="text-danger">
                            * Nearest store location is required.
                        </small>
                    )}
                </Form.Group>
                <Form.Group>
                    <Form.Label>Preferred Contact</Form.Label>
                    <Form.Control
                        as="select"
                        name="preferredContact"
                        placeholder="Select a preferred contact"
                        value={preferredContact}
                        disabled={
                            isLoadingNearestStore ||
                            isGenerating ||
                            isCreating ||
                            isAssigning
                        }
                        onChange={onChangeHandler}
                        required
                    >
                        <option value="" disabled>
                            Select preferred contact
                        </option>
                        {Object.values(PreferredContactMethods).map(
                            (method) => {
                                if (
                                    method ===
                                        PreferredContactMethods.Landline &&
                                    landLineNumber === ""
                                ) {
                                    return null;
                                }
                                return (
                                    <option
                                        value={method}
                                        key={method}
                                        id={method}
                                    >
                                        {toTitleCase(method)}
                                    </option>
                                );
                            }
                        )}
                        {!preferredContact && (
                            <small className="text-danger">
                                * Preferred contact cannot be empty!
                            </small>
                        )}
                    </Form.Control>
                    <Form.Group>
                        <Form.Check
                            className="member-checkbox-margin"
                            name="isAgreedPromotions"
                            id="checkbox"
                            type="checkbox"
                            checked={isAgreedPromotions}
                            disabled={
                                isLoadingNearestStore ||
                                isGenerating ||
                                isCreating ||
                                isAssigning
                            }
                            onChange={onClickOnCheckbox}
                            label="Agree to receive Marketing/Promotions "
                        />
                    </Form.Group>
                </Form.Group>
                <h5 className="ml-4">
                    By clicking on Enrol, the customer has accepted program
                    terms & conditions
                </h5>
            </Form>
        </div>
    );
};

export default EnrollMemberThirdPage;
