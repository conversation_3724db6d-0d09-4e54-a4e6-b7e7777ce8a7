import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import { Country } from "country-state-city";
import PropTypes from "prop-types";
import  { Modal, Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { MerchantsContext } from "Contexts/merchantsContext";
import { updateMerchant } from "Services";
import { isEmptyObject, isEqualObjects } from "Utils";
import AddressInputs from "Components/common/addressInputs/AddressInputs";

const EditAddress = ({ show, onHide, currentDetails, merchantId }) => {
    const { onRefreshMerchants } = useContext(MerchantsContext);
    const [validated, setValidated] = useState(false);
    const [address, setAddress] = useState({});
    const [isUpdating, setIsUpdating] = useState(false);
    const [stateOrProvince, setStateOrProvince] = useState([]);
    const [city, setCity] = useState([]);

    const merchantCountry = useMemo(() =>
        currentDetails?.countryDetails?.isoCode ?
            [Country.getCountryByCode(currentDetails?.countryDetails?.isoCode)] :
            [Country.getAllCountries().find(country => country.name === currentDetails?.countryDetails?.name)],
        [currentDetails?.countryDetails]
    );

    const onChangeHandler  = useCallback(e => {
        switch(e.target.name) {
            case "line1":
                setAddress({ ...address, [e.target.name]: e.target.value});
                break;
            case "line2":
                setAddress({ ...address, [e.target.name]: e.target.value});
                break;
            case "line3":
                setAddress({ ...address, [e.target.name]: e.target.value});
                break;
            case "zipOrPostcode":
                setAddress({ ...address, [e.target.name]: e.target.value});
                break;
            default:
                return null;
        }
    }, [address, setAddress]);

    const onChangeState = useCallback(e => {
        setStateOrProvince(e);
        setCity([]);
        setAddress({ ...address, stateOrProvince: e.length > 0 && e[0].value, city: "" });
    }, [address, setStateOrProvince, setCity, setAddress]);

    const onChangeCity = useCallback(e => {
        setCity(e);
        setAddress({ ...address, city: e.length > 0 && e[0].value });
    }, [address, setCity, setAddress]);

    const onSubmit = useCallback(async e => {
        e.preventDefault();

        if (e.target.checkValidity()) {
            try {
                const addressPayload = {
                    contact: {
                        ...currentDetails?.contact,
                        address: {
                            line1: address.line1,
                            line2: address.line2 || undefined,
                            line3: address.line3 || undefined,
                            stateOrProvince: address?.stateOrProvince || undefined,
                            city: address?.city,
                            zipOrPostcode: address?.zipOrPostcode || undefined
                        }
                    }
                };

                setIsUpdating(true);
                const updatedMerchant = await updateMerchant(merchantId, addressPayload);
                onRefreshMerchants();
                setIsUpdating(false);
                toast.success("Successfully updated merchant's address.");
                onHide(null, updatedMerchant)
            } catch (err) {
                setIsUpdating(false);
                toast.error(err.message || "Could not update merchant's address!");
            }
        } else {
            setValidated(true);
        }
    },
    [merchantId, currentDetails, address, onHide, setValidated, setIsUpdating, onRefreshMerchants]);

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setAddress(currentDetails.contact.address || "");
            setStateOrProvince(currentDetails.contact.address?.stateOrProvince || []);
            setCity(currentDetails.contact.address?.city || []);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentDetails]);

    return (
        <Modal show={show} onHide={isUpdating ? () => { /* Placeholder for function body */ } : onHide} centered>
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>Edit Address</Modal.Title>
            </Modal.Header>
            <Form onSubmit={onSubmit} validated={validated} noValidate>
                <Modal.Body>
                    <AddressInputs
                        usedIn="editMerchant"
                        countryISOCode={merchantCountry ? (merchantCountry[0]?.isoCode || "") : ""}
                        country={merchantCountry || ""}
                        stateOrProvince={stateOrProvince || ""}
                        line1={address?.line1 || ""}
                        line2={address?.line2 || ""}
                        line3={address?.line3 || ""}
                        city={city || ""}
                        zipOrPostcode={address?.zipOrPostcode || ""}
                        isChanging={isUpdating}
                        onChangeHandler={onChangeHandler}
                        onChangeState={onChangeState}
                        onChangeCity={onChangeCity}
                    />
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={onHide}
                        type="button"
                        disabled={isUpdating}
                    >
                        Cancel
                    </Button>
                    <Button
                        size="sm"
                        variant="primary"
                        type="submit"
                        disabled={
                            isUpdating ||
                            isEqualObjects(address||{}, currentDetails.contact.address||{})
                        }
                    >
                        {isUpdating ? "Updating..." : "Update"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

EditAddress.propTypes = {
  /**
   * Show edit view
   */
  show: PropTypes.bool.isRequired,
  /**
   * Callback on close
   */
  onHide: PropTypes.func.isRequired,
  /**
   * Current known data
   */
  currentDetails: PropTypes.object,
  /**
   * Merchant Id
   */
  merchantId: PropTypes.string.isRequired,
};

export default EditAddress;
