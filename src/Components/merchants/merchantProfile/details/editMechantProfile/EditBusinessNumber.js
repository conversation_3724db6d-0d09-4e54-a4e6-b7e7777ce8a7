import React, { useCallback, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Form, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { MerchantsContext } from "Contexts/merchantsContext";
import { updateMerchant } from "Services";
import { isEmptyObject } from "Utils";

const EditBusinessNumber = ({ show, onHide, currentDetails, merchantId }) => {
    const { onRefreshMerchants } = useContext(MerchantsContext);
    const [isUpdating, setIsUpdating] = useState(false);
    const [businessNumber, setBusinessNumber] = useState("");

    const onChangeBusinessNumber = useCallback(e => setBusinessNumber(e.target.value), [setBusinessNumber]);

    const onSubmit = useCallback(async e => {
        e.preventDefault();
        try {
            const businessNumberPayload = {
                businessRegistrationNumber: businessNumber === "" ? undefined : businessNumber
            };

            setIsUpdating(true);
            const updatedMerchant = await updateMerchant(merchantId, businessNumberPayload);
            onRefreshMerchants();
            setIsUpdating(false);
            toast.success("Successfully updated merchant's business registration number.");
            onHide(null, updatedMerchant);
        } catch(err) {
            setIsUpdating(false);
            toast.error(err.message || "Could not update merchant's business registration number! Please try again.");
        }
    }, [merchantId, businessNumber, onHide, setIsUpdating, onRefreshMerchants]);

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setBusinessNumber(currentDetails?.businessRegistrationNumber);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentDetails]);

    return (
        <Modal show={show} onHide={isUpdating ? () => { /* Placeholder for function body */ } : onHide} centered>
          <Modal.Header closeButton={!isUpdating}>
            <Modal.Title>Edit Business Registration Number</Modal.Title>
          </Modal.Header>
          <Form onSubmit={onSubmit}>
            <Modal.Body>
                <Form.Group>
                    <Form.Label>Business Registration Number</Form.Label>
                    <Form.Control
                    type="text"
                    value={businessNumber || ""}
                    onChange={onChangeBusinessNumber}
                    disabled={isUpdating}
                    placeholder="Enter Business Registration Number"
                    />
                </Form.Group>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    type="button"
                    disabled={isUpdating}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant="primary"
                    type="submit"
                    disabled={
                        isUpdating ||
                        businessNumber === currentDetails?.businessRegistrationNumber
                    }
                >
                    {isUpdating ? "Updating..." : "Update"}
                </Button>
            </Modal.Footer>
          </Form>
        </Modal>
      );
};

EditBusinessNumber.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,
    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,
    /**
     * Current known data
     */
    currentDetails: PropTypes.object,
    /**
     * Merchant Id
     */
    merchantId: PropTypes.string.isRequired,
};

export default EditBusinessNumber;
