import React, { useCallback, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Modal, Button, Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { MerchantsContext } from "Contexts/merchantsContext";
import { updateMerchant } from "Services";
import { isEmptyObject } from "Utils";

const EditEmail = ({ show, onHide, currentDetails, merchantId }) => {
    const { onRefreshMerchants } = useContext(MerchantsContext);
    const [validated, setValidated] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [email, setEmail] = useState("");

    const onChangeMerchantEmail = useCallback(e => setEmail(e.target.value), [setEmail]);

    const onSubmit = useCallback(async e => {
        e.preventDefault();
        if (e.target.checkValidity()) {
            delete currentDetails.contact._id;
            delete currentDetails.contact.address._id;

            try {
                const emailPayload = {
                    contact: { ...currentDetails?.contact, email }
                };
                setIsUpdating(true);
                const updatedMerchant = await updateMerchant(merchantId, emailPayload);
                onRefreshMerchants();
                setIsUpdating(false);
                toast.success("Successfully updated merchant's email.");
                onHide(null, updatedMerchant);
            } catch (err) {
                setIsUpdating(false);
                toast.error(err.message || "Could not update merchant's email address! Please recheck and try again.");
            }
        } else {
            setValidated(true);
        }
    }, [merchantId, currentDetails, email, onHide, setValidated, setIsUpdating, onRefreshMerchants]);

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setEmail(currentDetails?.contact?.email || "");
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentDetails]);

    return (
        <Modal show={show} onHide={isUpdating ? () => { /* Placeholder for function body */ } : onHide} centered>
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>Edit Email</Modal.Title>
            </Modal.Header>
            <Form onSubmit={onSubmit} validated={validated} noValidate>
                <Modal.Body>
                    <Form.Group>
                        <Form.Label>Merchant Email</Form.Label>
                        <Form.Control
                        type="email"
                        required
                        value={email || ""}
                        disabled={isUpdating}
                        onChange={onChangeMerchantEmail}
                        placeholder="Enter Merchant Email"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        size="sm"
                        variant="outline-primary"
                        onClick={onHide}
                        type="button"
                        disabled={isUpdating}
                    >
                        Cancel
                    </Button>
                    <Button
                        size="sm"
                        variant="primary"
                        type="submit"
                        disabled={
                            isUpdating ||
                            email === "" ||
                            email === currentDetails?.contact?.email
                        }
                    >
                        {isUpdating ? "Updating..." : "Update"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

EditEmail.propTypes = {
  /**
   * Show edit view
   */
  show: PropTypes.bool.isRequired,
  /**
   * Callback on close
   */
  onHide: PropTypes.func.isRequired,
  /**
   * Current known data
   */
  currentDetails: PropTypes.object,
  /**
   * Merchant Id
   */
  merchantId: PropTypes.string.isRequired,
};

export default EditEmail;
