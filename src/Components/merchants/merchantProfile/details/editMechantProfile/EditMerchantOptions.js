import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import { Button, FormGroup, FormLabel, FormSelect, IcIcon, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { faExclamationCircle, faMapMarkerInfo } from "FaICIconMap";
import { DataContext } from "Contexts";
import { MerchantsContext } from "Contexts/merchantsContext";
import { MerchantOptions, MerchantProfileTabs } from "Data";
import { updateMerchant } from "Services";
import { isEmptyObject, isEqualObjects } from "Utils";
import { convertArrayToObject } from "Components/merchants/utils/MerchantUtility";

const EditMerchantOptions = ({ 
    show, 
    onHide, 
    merchantId, 
    options,
    setUsedInOptionsData,
    toggleShowOptions,
    onCloseUsedInModal,
    setActiveTab
}) => {
    const { merchantLocations } = useContext(DataContext);
    const { onRefreshMerchants } = useContext(MerchantsContext);
    const [isSaving, setIsSaving] = useState(false);
    const [availableOptions, setAvailableOptions] = useState({});
    
    const selectedMerchantOptions = useMemo(() => 
        MerchantOptions.filter(option => 
            availableOptions.hasOwnProperty(option.value) && availableOptions[option.value] ?
                option : null
        ) || [], 
        [availableOptions]
    );

    const isMerchantOptionsUpdated = useMemo(() => 
        !isEqualObjects(options, availableOptions),
        [options, availableOptions]
    );
    
    const locationsWithAttachedOptions = useMemo(() => 
        merchantLocations[merchantId] && !isEmptyObject(merchantLocations[merchantId]) ? 
            Object.values(merchantLocations[merchantId])
                .map(location => {
                    const attachedOptionsArr = Object.keys(options)
                        .map(key => location?.options.hasOwnProperty(key) && location?.options[key] && key)
                        .reduce((keys, value) => ({ ...keys, ...(value && { [value]: true }) }), {});

                    return ({ ...location, options: attachedOptionsArr });
                })
                .filter(locationWithCurrOptions => !isEmptyObject(locationWithCurrOptions?.options)) 
        : [], [options, merchantId, merchantLocations]
    );

    const isUsedOptionsRemoved = useMemo(() => {
        let usedOptions = {};

        if(locationsWithAttachedOptions?.length !== 0) {
            locationsWithAttachedOptions.forEach(location => {
                usedOptions = { ...usedOptions, ...location?.options };
            });
        }

        return Object.keys(usedOptions).filter(option => !Object.keys(availableOptions).includes(option)).length !== 0;
    }, [availableOptions, locationsWithAttachedOptions]);

    const onSetAvailableOptions = useCallback(e => {
        const optionsObject = convertArrayToObject(e, "value", true);
        setAvailableOptions(optionsObject);
    }, [setAvailableOptions]);

    const onShowLocationsWithOptions = useCallback(() => {
        setActiveTab(MerchantProfileTabs.LOCATIONS);
        onCloseUsedInModal();
    }, [setActiveTab, onCloseUsedInModal]);

    const onViewUsedInOptions = useCallback(() => {
        setUsedInOptionsData({
            title: `
                Show ${locationsWithAttachedOptions?.length === 1 ? "Location " : "Locations "}
                and Used 
            `,
            usedLocations: locationsWithAttachedOptions,
            onShowLocations: onShowLocationsWithOptions,
            onCloseModal: onCloseUsedInModal
        });
        toggleShowOptions();
    }, [
        locationsWithAttachedOptions, 
        setUsedInOptionsData, 
        toggleShowOptions,
        onShowLocationsWithOptions,
        onCloseUsedInModal
    ]);

    const onSaveChanges = useCallback(async () => {
        try {
            const editedMerchantOptionsPayload = { options: availableOptions };

            setIsSaving(true);
            const updatedMerchant = await updateMerchant(merchantId, editedMerchantOptionsPayload);
            onRefreshMerchants();
            setIsSaving(false);
            toast.success("Successfully updated merchant's options.");
            onHide(null, updatedMerchant);                
        } catch(e) {
            setIsSaving(false);
            toast.error(
                <div>
                    Could not update merchant's options!
                    <br />
                    {e.message}
                </div>
            );
        }
    }, [merchantId, availableOptions, onHide, setIsSaving, onRefreshMerchants]);

    useEffect(() => {
        if(!isEmptyObject(options)) {
            setAvailableOptions(options);
        }
    }, [options]);

    return (
        <>
            <Modal show={show} onHide={isSaving ? () => { /* Placeholder for function body */ } : onHide} centered>
                <Modal.Header closeButton={!isSaving}>
                    <Modal.Title>
                        Update Merchant Options
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <FormLabel>
                        Available Options
                    </FormLabel>
                    <FormGroup>
                        <FormSelect
                            clearButton
                            id="basic-typeahead-multi"
                            labelKey="name"
                            multiple
                            onChange={onSetAvailableOptions}
                            options={MerchantOptions}
                            placeholder={"Select available options"}
                            selected={selectedMerchantOptions}
                            disabled={isSaving}
                        />
                    </FormGroup>
                    {isUsedOptionsRemoved &&
                            <div className="mx-2">
                                <div className="text-orange d-flex">
                                    <IcIcon className="mr-2" size="lg" icon={faExclamationCircle} />
                                    {`${locationsWithAttachedOptions?.length} ${locationsWithAttachedOptions?.length === 1 ? "location has " : `locations have `}`} 
                                    one or more unselected option(s) already in use.
                                </div>
                                <br />
                                Please update 
                                {`${locationsWithAttachedOptions?.length === 1 ? " this location's " : ` these locations' `}`}
                                Available Options before editing Merchant Options.
                                <div className="my-3 text-center">
                                    <Button 
                                        variant="outline-primary" 
                                        size="sm" 
                                        onClick={onViewUsedInOptions}
                                    >
                                        <div className="d-flex align-items-center">
                                            <IcIcon className="mr-2" size="lg" icon={faMapMarkerInfo} />
                                            View 
                                            {`${locationsWithAttachedOptions?.length === 1 ? " Location " : ` Locations `}`}
                                            and Options In Use
                                        </div>
                                    </Button>
                                </div>
                            </div>
                    }
                </Modal.Body>
                <Modal.Footer>
                    <Button 
                        size="sm" 
                        variant="outline-primary" 
                        onClick={onHide} 
                        disabled={isSaving}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size="sm" 
                        variant="primary" 
                        onClick={onSaveChanges} 
                        disabled={
                            isSaving ||
                            !isMerchantOptionsUpdated ||
                            isUsedOptionsRemoved
                        }
                    >
                        {isSaving ? "Updating..." : "Update"}
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default EditMerchantOptions;