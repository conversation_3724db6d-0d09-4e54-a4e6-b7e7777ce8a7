import React from "react";
import {
    <PERSON><PERSON>,
    Card,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faAngleDown, faAngleUp, faDollarSign } from "FaICIconMap";
import { useToggle } from "Hooks";
import { toTitleCase } from "Utils";
import { LoadingComponent } from "../../../../utils/UtilComponents";

const LocationWisePointTable = ({ title, data, isLoading }) => {
    const [isShowMore, setIsShowMore] = useToggle(false);

    return (
        <Card className="pt-3 mb-3">
            <div className="d-flex flex-row jestify-content-between pt-2 px-3 mb-3">
                <div className="d-flex align-items-center mr-auto">
                    <IcIcon
                        className="mr-2 text-primary"
                        size="lg"
                        icon={faDollarSign}
                    />
                    <div className="font-weight-bold">{title}</div>
                </div>
            </div>
            <div className="d-flex flex-row jestify-content-between pt-2 detail-element px-3 mb-1">
                <div className="d-flex flex-row mr-auto">
                    <p className="font-weight-bold">Location</p>
                </div>
                <div>
                    <p className="font-weight-bold">Points</p>
                </div>
            </div>
            {isLoading ? (
                <LoadingComponent />
            ) : (
                <>
                    {data && data.length > 5 ? (
                        <>
                            {data.map((item, index) => {
                                return (
                                    <div key={item?._id || index}>
                                        {index < 5 && !isShowMore && (
                                            <>
                                                <div className="d-flex flex-row jestify-content-between pt-2 detail-element px-3">
                                                    <div className="d-flex flex-row mr-auto">
                                                        <p className="text-muted">
                                                            {toTitleCase(
                                                                item.location
                                                            )}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="">
                                                            {item.points}
                                                        </p>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                        {isShowMore && (
                                            <>
                                                <div className="d-flex flex-row jestify-content-between pt-2 detail-element px-3">
                                                    <div className="d-flex flex-row mr-auto">
                                                        <p className="text-muted">
                                                            {toTitleCase(
                                                                item.location
                                                            )}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="">
                                                            {item.points}
                                                        </p>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                );
                            })}
                        </>
                    ) : (
                        <>
                            {data.map((item, index) => {
                                return (
                                    <div
                                        key={item?._id || index}
                                        className="d-flex flex-row jestify-content-between pt-2 detail-element px-3"
                                    >
                                        <div className="d-flex flex-row mr-auto">
                                            <p className="text-muted">
                                                {toTitleCase(item.location)}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="">{item.points}</p>
                                        </div>
                                    </div>
                                );
                            })}
                        </>
                    )}
                    {!isShowMore && data?.length > 5 && (
                        <Button
                            className="btn shadow-none show-click"
                            size="md"
                            variant="link"
                            onClick={setIsShowMore}
                        >
                            <div className="d-flex justify-content-center align-items-center">
                                {`See ${(data.length - 5)
                                    .toString()
                                    .padStart(2, "0")} more`}
                                <IcIcon
                                    size="lg"
                                    className="ml-1"
                                    icon={faAngleDown}
                                />
                            </div>
                        </Button>
                    )}
                    {isShowMore && data?.length > 5 && (
                        <Button
                            className="btn shadow-none show-click"
                            size="md"
                            variant="link"
                            onClick={setIsShowMore}
                        >
                            <div className="d-flex justify-content-center align-items-center">
                                See less
                                <IcIcon
                                    size="lg"
                                    className="ml-1"
                                    icon={faAngleUp}
                                />
                            </div>
                        </Button>
                    )}
                </>
            )}
        </Card>
    );
};

export default LocationWisePointTable;
