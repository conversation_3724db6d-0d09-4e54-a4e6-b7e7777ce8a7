import React, { useState, useContext, useCallback,useRef, useEffect} from 'react';
import { Navbar, Nav, Image, Dropdown, Avatar, Overlay,Popover,IcIcon,Button} from '@shoutout-labs/shoutout-themes-enterprise';
import { faUser, faSignOutAlt } from 'FaICIconMap';
import DropdownButton from 'react-bootstrap/DropdownButton';
import { Link } from 'react-router-dom';
import logo from './../../assets/images/logo_full_blue.png';
import { UserContext } from "../../Contexts";
import { getRegions } from '../../Services';
import { AccessPermissionModuleNames, AccessPermissionModules } from "../../Data";
import './MainNavigation.css';

const MainNavigation = () => {

    const { username, logout: userLogout,isAuthorizedForAction} = useContext(UserContext);

    const [region, setRegion] = useState("Barbados");
    const [profileMenuShow, setProfileMenuShow] = useState(false);
    const [regions, setRegions] = useState({});
    const profileRef = useRef();

    const toggleProfileMenu = useCallback(() => {
        setProfileMenuShow(!profileMenuShow);
    }, [setProfileMenuShow,profileMenuShow])

    const hideProfileMenu = useCallback(() => {
        setProfileMenuShow(false)
    }, [setProfileMenuShow])

    const logout = useCallback(() => {
        userLogout();
    }, [userLogout]);

    const getRegionSet = useCallback(async() => {
        try{
            const regionset = await getRegions();
            setRegions(regionset);
        }catch(e){

        }
    },[setRegions])

    useEffect(() => {
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.REGION,
                AccessPermissionModules[AccessPermissionModuleNames.REGION]
                    .actions.ListRegions
            )
        ) {
            getRegionSet();
        }

    //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    return (
                <div className="border-bottom-navigation rounded-0 card w-100 position-absolute ml-n3 main-nav-container" data-testid="main-nav">
                    <Navbar className="top-nav-bar row" bg="white" variant="primary" data-testid="main-navbar">
                        <Navbar.Brand as={Link} to="/" data-testid="main-navbar-brand">
                            <Image className="img-fluid w-50 ml-4" src={logo} alt="ShoutOUT" data-testid="logo"/>
                        </Navbar.Brand>
                        <Navbar.Collapse data-testid="main-nav-collapse">
                            <Nav variant="primary" className="ml-auto" data-testid="region-selection">
                                <DropdownButton data-testid="region-selection-dropdown" bsPrefix="text-capitalize btn btn-sm" title={regions && <>
                                    <img src={regions[region]?.flag} className="flags-img mr-1 mb-1" alt={region} />
                                    <span>{region}</span></>} className="mr-2" onSelect={setRegion}>
                                    {regions && Object.entries(regions).map(([key, item]) => {
                                        return (
                                            <Dropdown.Item eventKey={key} key={key}>
                                                <img src={item.flag} className="flags-img mr-2" alt={item.flag} />
                                                {key}
                                            </Dropdown.Item>
                                        )
                                        })}
                                </DropdownButton>

                                <Nav.Item ref={profileRef} data-testid="username" className="cursor-pointer text-center no-focus-color mx-2"
                                    onClick={toggleProfileMenu}>
                                    <div className="d-flex align-items-center" data-testid="navbar-username">
                                        <Avatar name={(username || 'unknown').charAt(0)} size="20" className="sidebar-user-avatar mb-1" data-testid="avatar"/>
                                        <span className="link-text text-dark" data-testid="name">{username || "Profile"}</span>
                                    </div>
                                </Nav.Item>
                                <Overlay
                                    show={profileMenuShow}
                                    target={profileRef.current}
                                    placement="bottom"
                                    onHide={hideProfileMenu}
                                    rootClose={true}
                                    data-testid="profile-overlay"
                                >
                                    <Popover id="credit-menu" className="sidebar-popover py-3 pl-3 border border-muted" data-testid="profile-popover">
                                        <Link to="/profile" onClick={hideProfileMenu} className="d-flex text-decoration-none text-primary" data-testid="profile-link">
                                            <div data-testid="profile-icon"><IcIcon icon={faUser} /></div>
                                            <div><span className="link-text text-dark" data-testid="profile-name">My Profile</span></div>
                                        </Link>
                                        <hr className="ml-n3 my-2" />
                                        <Button variant="transparent" size="md" onClick={logout} className="d-flex text-decoration-none text-primary p-0" role="button">
                                            <div><IcIcon data-testid="logout-icon" icon={faSignOutAlt} /></div>
                                            <div><span className="link-text text-dark" data-testid="logout">Logout</span></div>
                                            </Button>
                                        </Popover>
                                    </Overlay>
                                </Nav>
                            </Navbar.Collapse>
                        </Navbar>

                    </div>

    );
}

export default MainNavigation;
