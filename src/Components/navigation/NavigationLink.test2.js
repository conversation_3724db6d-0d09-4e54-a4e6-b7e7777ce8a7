//TODO: Temporarily disabled. Need to fix the test issue and re enable this test (Rename the file)
import React from 'react'
import {render, cleanup} from '@testing-library/react';
import { create } from "react-test-renderer";
import NavigationLink from './NavigationLink';
import {MemoryRouter} from 'react-router-dom';
import { faUsers } from 'FaICIconMap';
import '@testing-library/jest-dom'
import App from '../../App'

afterEach(() => {
    cleanup();
});

describe('NavigationLink', () => {

    test('Render NavigationLink', () => {

        const props = {path : '/members', tab: 'Members', icon : faUsers , activePath : '/members'}


        render(
         
            <MemoryRouter>  
              <App>  
                <NavigationLink props={props}/> 
              </App> 
          </MemoryRouter>

          );
    })
});

describe('NavigationLink component snapshot', () => {
  test('Matches the snapshot', ()=> {

    const props = {path : '/members', tab: 'Members', icon : faUsers , activePath : '/members'}


       const navigationLinkComponent = create(<MemoryRouter>  
        <App>  
          <NavigationLink props={props}/> 
        </App> 
    </MemoryRouter>);
      expect(navigationLinkComponent.toJSON()).toMatchSnapshot();

   });
});