import React, { useCallback, useEffect, useState } from "react";
import { toast } from "react-toastify";
import {
    Button,
    IcIcon,
    Modal,
    Tab,
    Tabs,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faPostcard, faSync } from "FaICIconMap";
import { getMemberById } from "Services";
import NotificationsBasicDetails from "./basicDetails/NotificationsBasicDetails";
import MessageLogHistory from "./history/NotificationsHistory";

import "./NotificationsDetailsView.scss";

const NotificationsDetailsView = ({
    show,
    isMessageLogs,
    title,
    onHide,
    selectedRow,
    onShowContentPreview,
}) => {
    const [tab, setTab] = useState("BASIC_DETAILS");
    const [member, setMember] = useState({});
    const [isLoadingMember, setIsLoadingMember] = useState(false);
    const [isReloading, setIsReloading] = useState(false);

    const getMemberDetailsById = useCallback(async () => {
        try {
            setIsLoadingMember(true);
            const memberData = await getMemberById(selectedRow?.memberId);
            setMember(memberData);
            setIsLoadingMember(false);
        } catch (e) {
            setIsLoadingMember(false);
            console.error(e);
            toast.error(
                <div>
                    Failed to load member profile!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [selectedRow?.memberId, setIsLoadingMember, setMember]);

    const onReloadMember = useCallback(async () => {
        setIsReloading(true);
        await getMemberDetailsById();
        setIsReloading(false);
    }, [getMemberDetailsById, setIsReloading]);

    useEffect(() => {
        if (selectedRow?.member) {
            setMember(selectedRow.member);
        } else {
            if (selectedRow?.memberId) {
                getMemberDetailsById();
            }
        }

        return () => {
            setMember({});
            setIsLoadingMember(false);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedRow?.member, selectedRow?.memberId]);

    return (
        <Modal
            className="notifications-details-view"
            show={show}
            onHide={onHide}
            size="lg"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isLoadingMember}>
                <Modal.Title>{title}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div>
                    <Tabs onSelect={setTab} activeKey={tab} transition={false}>
                        <Tab
                            eventKey="BASIC_DETAILS"
                            title={
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-3"
                                        size="lg"
                                        icon={faPostcard}
                                    />
                                    Basic Details
                                </div>
                            }
                            disabled={
                                !selectedRow?.member &&
                                selectedRow?.memberId &&
                                isLoadingMember
                            }
                        >
                            {tab === "BASIC_DETAILS" && (
                                <NotificationsBasicDetails
                                    isMessageLogs={isMessageLogs}
                                    basicData={selectedRow}
                                    member={member}
                                    isLoadingMember={isLoadingMember}
                                    onShowContentPreview={onShowContentPreview}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey="HISTORY"
                            title={
                                <div className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-3"
                                        size="lg"
                                        icon={faPostcard}
                                    />
                                    History
                                </div>
                            }
                            disabled={
                                !selectedRow?.member &&
                                selectedRow?.memberId &&
                                isLoadingMember
                            }
                        >
                            {tab === "HISTORY" && (
                                <MessageLogHistory
                                    historyData={selectedRow?.historyEvents}
                                />
                            )}
                        </Tab>
                    </Tabs>
                </div>
            </Modal.Body>
            <Modal.Footer
                className={
                    !selectedRow?.member &&
                    selectedRow?.memberId &&
                    tab === "BASIC_DETAILS"
                        ? "d-flex justify-content-between align-items-center"
                        : ""
                }
            >
                {!selectedRow?.member &&
                    selectedRow?.memberId &&
                    tab === "BASIC_DETAILS" && (
                        <div>
                            {isReloading ? (
                                <small className="ml-3 text-primary">
                                    Reloading...
                                </small>
                            ) : (
                                <Button
                                    className="shadow-none"
                                    size="sm"
                                    variant="link"
                                    disabled={isLoadingMember || isReloading}
                                    onClick={onReloadMember}
                                >
                                    <IcIcon
                                        size="md"
                                        className="mr-2"
                                        icon={faSync}
                                    />
                                    Reload Member
                                </Button>
                            )}
                        </div>
                    )}
                <Button
                    variant="primary"
                    size="sm"
                    disabled={
                        !selectedRow?.member &&
                        selectedRow?.memberId &&
                        isLoadingMember
                    }
                    onClick={onHide}
                >
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default NotificationsDetailsView;
