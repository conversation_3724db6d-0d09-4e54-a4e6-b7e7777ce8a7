import React, { useContext, useCallback } from "react";
import PropTypes from "prop-types";
import EmailEditorModule, {
    Themes,
} from "@shoutout-labs/shoutout-message-editor-enterprise";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";
import { TransportTypes } from "Data";
import { uploadImage } from "Services";
import { dataURItoBlob } from "../../../common/imageEditor/utils/ImageEditorUtils";
import { NotificationEditContext } from "./context/NotificationEditContext";

const TemplatesEmailEditor = ({ validated, ...props }) => {
    const {
        emailEditorState,
        isEmailBodyValid,
        isEmailSubjectValid,
        onChangeEmailEditor,
        selectedNotification,
        setFrom,
        setSubject,
    } = useContext(NotificationEditContext);

    const onChangeFrom = useCallback(
        (e) => {
            setFrom(e.target.value, TransportTypes.EMAIL.toLowerCase());
        },
        [setFrom]
    );

    const onChangeSubject = useCallback(
        (e) => {
            setSubject(e.target.value, TransportTypes.EMAIL.toLowerCase());
        },
        [setSubject]
    );

    const saveImage = useCallback((base64, fileType) => {
        return new Promise(async (resolve, reject) => {
            try {
                const scaledImageToBlob = dataURItoBlob(base64);
                const imageUploadResponse = await uploadImage(
                    scaledImageToBlob
                );
                resolve(imageUploadResponse.url);
            } catch (e) {
                reject("Failed to upload image" || e);
            }
        });
    }, []);

    return (
        <div className="templates-editor">
            <Form.Group controlId="email-sender-id">
                <Form.Label className="d-flex align-items-center">
                    Sender Id
                    <div className="ml-1 text-danger">*</div>
                </Form.Label>
                <Form.Control
                    type="text"
                    name="from"
                    placeholder="Eg: DEMO<<EMAIL>>"
                    value={
                        selectedNotification[TransportTypes.EMAIL.toLowerCase()]
                            ?.from
                    }
                    onChange={onChangeFrom}
                    disabled // TODO: Currenlty disabled since the user cannot change the email provider
                />
            </Form.Group>
            <Form.Group controlId="email-subject">
                <Form.Label className="d-flex align-items-center">
                    Subject
                    <div className="ml-1 text-danger">*</div>
                </Form.Label>
                <Form.Control
                    type="text"
                    name="subject"
                    placeholder="Eg: Donate points"
                    value={
                        selectedNotification[TransportTypes.EMAIL.toLowerCase()]
                            ?.subject
                    }
                    disabled={!!props?.disabled}
                    onChange={onChangeSubject}
                    required
                />
                {validated && !isEmailSubjectValid && (
                    <Form.Text className="text-danger">
                        * Email subject cannot be empty!
                    </Form.Text>
                )}
            </Form.Group>
            <div className="mx-n2">
                <EmailEditorModule
                    editorState={emailEditorState}
                    onChange={onChangeEmailEditor}
                    selectedTheme={Themes.default}
                    uploadImage={saveImage}
                    {...props}
                />
                {validated && !isEmailBodyValid && (
                    <Form.Text className="text-danger">
                        * Email body cannot be empty!
                    </Form.Text>
                )}
            </div>
        </div>
    );
};

TemplatesEmailEditor.defaultProps = { validated: false };

TemplatesEmailEditor.propTypes = { validated: PropTypes.bool };

export default TemplatesEmailEditor;
