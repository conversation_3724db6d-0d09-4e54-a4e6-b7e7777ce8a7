import React, { useReducer, useCallback, useMemo } from "react";
import {
    exportText,
    DraftJS,
} from "@shoutout-labs/shoutout-message-editor-enterprise";
import { toast } from "react-toastify";
import { TransportTypes, TemplateStatus } from "Data";
import {
    updateNotifications,
    createNotifications,
    convertEditorStateToHtml,
} from "Services";
import { isEmptyObject } from "Utils";

const { EditorState, convertFromRaw, ContentState } = DraftJS;

const defaultSmsObj = { body: "", from: "" };

const defaultEmailObj = {
    htmlType: true,
    body: "",
    editorBody: "",
    from: "",
    subject: "",
};

const defaultTransportObject = {
    [TransportTypes.SMS.toLowerCase()]: defaultSmsObj,
    [TransportTypes.EMAIL.toLowerCase()]: defaultEmailObj,
};

const NotificationEditContext = React.createContext();

const initialState = {
    smsEditorState: null,
    isSmsBodyValid: false,
    emailEditorState: null,
    isEmailBodyValid: false,
    isEmailSubjectValid: false,
    isSaving: false,
    selectedNotification: defaultTransportObject,
    templateId: "",
};

const NotificationContextActions = {
    SET_EDITOR_STATE: "setEditorState",
    SET_IS_SAVING: "setIsSaving",
    SET_ENABLED_TRANSPORT: "setEnabledTransport",
    SET_SELECTED_NOTIFICATION: "setSelectedNotification",
    SET_TRANSPORT_DATA: "setTransportData",
    RESET: "reset",
};

const reducer = (state, action) => {
    switch (action.type) {
        case NotificationContextActions.SET_EDITOR_STATE: {
            switch (action.transport) {
                case TransportTypes.SMS: {
                    return {
                        ...state,
                        smsEditorState: action.editorState,
                        isSmsBodyValid: !!exportText(action.editorState),
                    };
                }
                case TransportTypes.EMAIL: {
                    return {
                        ...state,
                        emailEditorState: action.editorState,
                        isEmailBodyValid: !!convertEditorStateToHtml(
                            action.editorState
                        ),
                    };
                }
                default: {
                    return state;
                }
            }
        }
        case NotificationContextActions.SET_IS_SAVING: {
            return {
                ...state,
                isSaving: action.status,
            };
        }
        case NotificationContextActions.SET_ENABLED_TRANSPORT: {
            const selectedNotification = { ...state.selectedNotification };
            let newTransports = [...selectedNotification.transports];

            if (action.transport === TransportTypes.EMAIL) {
                if (newTransports.includes(TransportTypes.EMAIL)) {
                    newTransports = newTransports.filter(
                        (nT) => nT !== TransportTypes.EMAIL
                    );
                } else {
                    newTransports.push(TransportTypes.EMAIL);
                }
            }

            if (action.transport === TransportTypes.SMS) {
                if (newTransports.includes(TransportTypes.SMS)) {
                    newTransports = newTransports.filter(
                        (nT) => nT !== TransportTypes.SMS
                    );
                } else {
                    newTransports.push(TransportTypes.SMS);
                }
            }

            selectedNotification.transports = newTransports;

            return { ...state, selectedNotification };
        }
        case NotificationContextActions.SET_SELECTED_NOTIFICATION: {
            const notificationToSet =
                action.notification && !isEmptyObject(action.notification)
                    ? action.notification
                    : defaultTransportObject;
            let smsEditorState = null;
            let emailEditorState = null;

            if (!notificationToSet?.[TransportTypes.SMS.toLowerCase()])
                notificationToSet[TransportTypes.SMS.toLowerCase()] =
                    defaultSmsObj;

            if (!notificationToSet?.[TransportTypes.EMAIL.toLowerCase()])
                notificationToSet[TransportTypes.EMAIL.toLowerCase()] =
                    defaultEmailObj;

            if (action.notification && !isEmptyObject(action.notification)) {
                const smsContent =
                    notificationToSet[TransportTypes.SMS.toLowerCase()]?.body;

                if (smsContent) {
                    smsEditorState = EditorState.createWithContent(
                        ContentState.createFromText(smsContent)
                    );
                } else {
                    smsEditorState = EditorState.createEmpty();
                }

                const content =
                    notificationToSet[TransportTypes.EMAIL.toLowerCase()]
                        ?.editorBody;
                if (content) {
                    const raw = JSON.parse(content);
                    // * Transform existing templates
                    try {
                        const transformedBlocks = raw.blocks.map((val) => {
                            const key = Object.keys(raw.entityMap).length;
                            if (val.type === "atomic") {
                                if (val.data.type === "image") {
                                    val.entityRanges.push({
                                        offset: 0,
                                        length: 1,
                                        key: key,
                                    });

                                    raw.entityMap[key] = {
                                        type: "IMAGE",
                                        mutability: "IMMUTABLE",
                                        data: {
                                            width:
                                                val.data.display === "small"
                                                    ? 50
                                                    : val.data.display ===
                                                        "medium"
                                                    ? 75
                                                    : 100,
                                            alignment:
                                                val.data.alignment || "center",
                                            src: val.data.src,
                                        },
                                    };
                                    val.text = " ";
                                    val.data = {};
                                } else if (val.data.type === "button") {
                                    val.entityRanges.push({
                                        offset: 0,
                                        length: 1,
                                        key: key,
                                    });

                                    raw.entityMap[key] = {
                                        type: "button",
                                        mutability: "IMMUTABLE",
                                        data: {
                                            alignment:
                                                val.data.alignment || "center",
                                            name: val.data.name,
                                            url: val.data.url,
                                        },
                                    };
                                    val.text = " ";
                                    val.data = {};
                                }
                            }
                            return val;
                        });
                        raw.blocks = transformedBlocks;
                    } catch (e) {
                        toast.error(
                            <div>
                                Failed to pass the template!
                                <br />
                                {e.message
                                    ? `Error: ${e.message}`
                                    : "Please try again later."}
                            </div>
                        );
                    }
                    const editorState = EditorState.createWithContent(
                        convertFromRaw(raw)
                    );
                    emailEditorState = editorState;
                } else {
                    emailEditorState = EditorState.createEmpty();
                }
            } else {
                smsEditorState = EditorState.createEmpty();
                emailEditorState = EditorState.createEmpty();
            }

            return {
                ...state,
                smsEditorState,
                isSmsBodyValid: !!smsEditorState,
                emailEditorState,
                isEmailBodyValid: !!emailEditorState,
                isEmailSubjectValid:
                    !!notificationToSet[TransportTypes.EMAIL.toLowerCase()]
                        ?.subject,
                selectedNotification: notificationToSet,
                templateId: action.notification ? action.templateId : "",
            };
        }
        case NotificationContextActions.SET_TRANSPORT_DATA: {
            const { type, key, transport, ...rest } = action;
            return {
                ...state,
                ...(key === "subject"
                    ? { isEmailSubjectValid: !!rest?.subject }
                    : {}),
                selectedNotification: {
                    ...state.selectedNotification,
                    [transport]: {
                        ...(state.selectedNotification?.[transport] || {}),
                        ...rest,
                    },
                },
            };
        }
        case NotificationContextActions.RESET: {
            return initialState;
        }
        default:
            return state;
    }
};

const NotificationEditorContextProvider = (props) => {
    const [state, dispatch] = useReducer(reducer, initialState);

    const onChangeSmsEditor = useCallback(
        (editorState) => {
            dispatch({
                type: NotificationContextActions.SET_EDITOR_STATE,
                editorState,
                transport: TransportTypes.SMS,
            });
        },
        [dispatch]
    );

    const onChangeEmailEditor = useCallback(
        (editorState) => {
            dispatch({
                type: NotificationContextActions.SET_EDITOR_STATE,
                editorState,
                transport: TransportTypes.EMAIL,
            });
        },
        [dispatch]
    );

    const toggleSmsEnabled = useCallback(
        (templateId) => {
            dispatch({
                type: NotificationContextActions.SET_ENABLED_TRANSPORT,
                transport: TransportTypes.SMS,
                templateId,
            });
        },
        [dispatch]
    );

    const toggleEmailEnabled = useCallback(
        (templateId) => {
            dispatch({
                type: NotificationContextActions.SET_ENABLED_TRANSPORT,
                transport: TransportTypes.EMAIL,
                templateId,
            });
        },
        [dispatch]
    );

    const setSelectedNotification = useCallback(
        (notification, templateId) => {
            dispatch({
                type: NotificationContextActions.SET_SELECTED_NOTIFICATION,
                notification,
                templateId,
            });
        },
        [dispatch]
    );

    const setFrom = useCallback(
        (from, transport) => {
            dispatch({
                type: NotificationContextActions.SET_TRANSPORT_DATA,
                from,
                transport,
            });
        },
        [dispatch]
    );

    const setSubject = useCallback(
        (subject, transport) => {
            dispatch({
                type: NotificationContextActions.SET_TRANSPORT_DATA,
                key: "subject",
                subject,
                transport,
            });
        },
        [dispatch]
    );

    const reset = useCallback(
        () => dispatch({ type: NotificationContextActions.RESET }),
        [dispatch]
    );

    const onSave = useCallback(
        async (updateNotificationPayload = {}) => {
            let updateNotificationResponse = {};

            try {
                if (
                    !updateNotificationPayload[TransportTypes.SMS.toLowerCase()]
                        ?.from
                )
                    throw new Error("SMS sender id cannot be empty.");

                if (
                    !updateNotificationPayload[
                        TransportTypes.EMAIL.toLowerCase()
                    ]?.from
                )
                    throw new Error("Email sender id cannot be empty.");

                if (
                    !updateNotificationPayload[TransportTypes.SMS.toLowerCase()]
                        ?.body
                )
                    throw new Error("SMS body cannot be empty!");
                if (
                    !updateNotificationPayload[
                        TransportTypes.EMAIL.toLowerCase()
                    ]?.body
                )
                    throw new Error("Email body cannot be empty!");

                dispatch({
                    type: NotificationContextActions.SET_IS_SAVING,
                    status: true,
                });
                if (state.selectedNotification?._id) {
                    updateNotificationResponse = await updateNotifications(
                        state.selectedNotification._id,
                        updateNotificationPayload
                    );
                } else {
                    updateNotificationResponse = await createNotifications({
                        ...updateNotificationPayload,
                        status: TemplateStatus.ENABLED,
                    });
                }
                dispatch({
                    type: NotificationContextActions.SET_IS_SAVING,
                    status: false,
                });

                return Promise.resolve(updateNotificationResponse);
            } catch (e) {
                dispatch({
                    type: NotificationContextActions.SET_IS_SAVING,
                    status: false,
                });
                return Promise.reject(e);
            }
        },
        [state.selectedNotification, dispatch]
    );

    const value = useMemo(
        () => ({
            ...state,
            smsEnabled: state.selectedNotification?.transports?.includes(
                TransportTypes.EMAIL.toLowerCase()
            ),
            emailEnabled: state.selectedNotification?.transports?.includes(
                TransportTypes.SMS.toLowerCase()
            ),
            onChangeSmsEditor,
            toggleSmsEnabled,
            onSave,
            setSelectedNotification,
            toggleEmailEnabled,
            onChangeEmailEditor,
            setFrom,
            setSubject,
            reset,
        }),
        [
            state,
            onChangeEmailEditor,
            onChangeSmsEditor,
            onSave,
            reset,
            setFrom,
            setSelectedNotification,
            setSubject,
            toggleEmailEnabled,
            toggleSmsEnabled,
        ]
    );
console.log("value notification", value);
    return (
        <NotificationEditContext.Provider value={value}>
            {props.children}
        </NotificationEditContext.Provider>
    );
};

const NotificationEditorContextConsumer = NotificationEditContext.Consumer;

export {
    NotificationEditContext,
    NotificationEditorContextProvider,
    NotificationEditorContextConsumer,
};
