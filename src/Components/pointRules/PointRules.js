import React, { useCallback, useContext } from "react";
import {
    IcIcon,
    Heading,
    Tab,
    Tabs,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faUser, faUsers } from "FaICIconMap";
import { PointRuleTypes } from "Data";
import BaseLayout from "Layout/BaseLayout";
import { TransactionRuleContext } from "./context/TransactionalRuleContext";
import { CreatePointRuleContextProvider } from "./pointRuleCreate/context/CreatePointRuleContext";
import TransactionalRules from "./transactionalRules/TransactionalRules";
import NonTransactionalRules from "./nonTransactionalRules/NonTransactionalRules";

import "./PointRules.scss";

const PointRules = () => {
    const { tab, setTab, isLoading, reset } = useContext(
        TransactionRuleContext
    );

    const selectedTab = useCallback(
        (e) => {
            setTab(e.toString());
            reset(e.toString());
        },
        [reset, setTab]
    );

    return (
        <BaseLayout
            topLeft={<Heading text="Point Rules" />}
            bottom={
                <div className="container-fluid point-rules mt-3">
                    <CreatePointRuleContextProvider>
                        <Tabs
                            onSelect={selectedTab}
                            defaultActiveKey={tab}
                            transition={false}
                            id="noanim-tab-example"
                            className="ml-n4 mb-3 border-solid-bottom"
                        >
                            <Tab
                                eventKey={PointRuleTypes.TRANSACTIONAL}
                                title={
                                    <div>
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faUser}
                                        />
                                        Transactional Rules
                                    </div>
                                }
                                disabled={isLoading}
                            >
                                {tab === PointRuleTypes.TRANSACTIONAL && (
                                    <TransactionalRules />
                                )}
                            </Tab>
                            <Tab
                                eventKey={PointRuleTypes.NON_TRANSACTIONAL}
                                title={
                                    <div>
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faUsers}
                                        />
                                        Non Transactional Rules
                                    </div>
                                }
                                disabled={isLoading}
                            >
                                {tab === PointRuleTypes.NON_TRANSACTIONAL && (
                                    <NonTransactionalRules />
                                )}
                            </Tab>
                        </Tabs>
                    </CreatePointRuleContextProvider>
                </div>
            }
        />
    );
};

export default PointRules;
