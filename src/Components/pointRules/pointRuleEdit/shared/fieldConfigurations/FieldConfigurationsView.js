import React, { useCallback, useMemo, useState } from "react";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import PropTypes from "prop-types";
import { v4 as uuidv4 } from "uuid";
import {
    BootstrapTable,
    Button,
    IcIcon,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus } from "FaICIconMap";
import { useToggle } from "Hooks";
import { isEmptyObject } from "Utils";
import { BootstrapTableOverlay } from "Components/utils";
import FieldConfigurationCard from "./FieldConfigurationCard";

const ValidatedFieldConfigMessages = ({
    validatedMsg = "",
    secondaryValidatedMessages = [],
    messages = [],
}) => {
    ValidatedFieldConfigMessages.propTypes = {
        validatedMsg: PropTypes.string.isRequired,
        secondaryValidatedMessages: PropTypes.array.isRequired,
        messages: PropTypes.array.isRequired,
    };

    const [showMore, toggleShowMore] = useToggle(false);
    return (
        <div className="border mt-3 px-4 py-2 border-danger rounded text-danger field-config-validated">
            <h3>{`* ${validatedMsg}`}</h3>
            {secondaryValidatedMessages.map((iAPCS) => (
                <ul key={iAPCS?.id}>
                    <li>
                        <span className="ml-2 font-weight-bold">
                            {iAPCS || "~ unknown"}
                        </span>
                    </li>
                </ul>
            ))}
            <Button
                className="my-3"
                variant="primary"
                size="sm"
                onClick={toggleShowMore}
            >
                Show {showMore ? "Less" : "More"} Details
            </Button>
            {showMore && (
                <div className="border border-danger mb-3 rounded p-2 validated-messages">
                    {messages.map((iAPC) => (
                        <ul key={iAPC?.id}>
                            <li>
                                <span className="ml-2 font-weight-bold">
                                    {iAPC?.fieldConfigNo}.
                                    {iAPC?.fieldName
                                        ? ` "${iAPC?.fieldName}" `
                                        : " <empty> "}
                                    Field Configuration
                                </span>
                            </li>
                            {(iAPC?.emptyFieldNameMsg ||
                                iAPC?.emptyPointsMsg) && (
                                <ul>
                                    {iAPC?.emptyFieldNameMsg && (
                                        <li>{iAPC.emptyFieldNameMsg}</li>
                                    )}
                                    {iAPC?.emptyPointsMsg && (
                                        <li>{iAPC.emptyPointsMsg}</li>
                                    )}
                                </ul>
                            )}
                        </ul>
                    ))}
                </div>
            )}
        </div>
    );
};

const FieldConfigurationsView = ({
    validated,
    isUpdating,
    defaultLimit,
    defaultSkip,
    skip,
    memberAttributes,
    fieldConfigurations,
    isAllFieldConfigsConfigured,
    setSkip,
    onAddRuleDataFieldConfigurations,
    removeFieldConfig,
    setSelectedFieldConfig,
    setFieldConfigPoints,
}) => {
    const [isPageChanged, setIsPageChanged] = useState(false);
    const options = useMemo(
        () => ({
            page: skip,
            sizePerPage: defaultLimit,
            totalSize: fieldConfigurations.length,
            pageStartIndex: 1,
            paginationSize: defaultLimit,
            showTotal: fieldConfigurations.length > defaultLimit,
            withFirstAndLast: true,
            hideSizePerPage: true,
            hidePageListOnlyOnePage: true,
        }),
        [defaultLimit, fieldConfigurations.length, skip]
    );

    const fieldConfigsPerPage = useMemo(
        () =>
            fieldConfigurations.slice(
                (skip - 1) * defaultLimit,
                (skip - 1) * defaultLimit + defaultLimit
            ) || [],
        [defaultLimit, fieldConfigurations, skip]
    );

    const lastPage = useMemo(
        () => Math.floor(fieldConfigurations.length / defaultLimit) + 1,
        [defaultLimit, fieldConfigurations.length]
    );

    const data = useMemo(
        () =>
            fieldConfigsPerPage.map((item, fieldConfigIndex) => {
                const fieldConfigId = item?.fieldId || uuidv4();
                const fieldConfigValidation =
                    isAllFieldConfigsConfigured.messages.find(
                        (msg) =>
                            msg?.id === fieldConfigId ||
                            msg?.fieldName === item?.fieldName
                    ) || {};

                return {
                    id: `m-${fieldConfigId}`,
                    "": (
                        <FieldConfigurationCard
                            key={fieldConfigId}
                            validated={validated}
                            disabled={isUpdating}
                            defaultLimit={defaultLimit}
                            fieldConfigIndex={
                                (skip - 1) * defaultLimit > 0
                                    ? fieldConfigIndex +
                                      (skip - 1) * defaultLimit
                                    : fieldConfigIndex
                            }
                            fieldConfigId={fieldConfigId}
                            skip={skip}
                            memberAttributes={memberAttributes}
                            selectedFieldConfig={
                                !isEmptyObject(item) ? [item] : []
                            }
                            fieldConfigurations={fieldConfigurations}
                            data={fieldConfigsPerPage}
                            fieldConfigValidation={fieldConfigValidation}
                            removeFieldConfig={removeFieldConfig}
                            setSelectedFieldConfig={setSelectedFieldConfig}
                            setFieldConfigPoints={setFieldConfigPoints}
                            setSkip={setSkip}
                            {...item}
                        />
                    ),
                };
            }) || [],
        [
            fieldConfigsPerPage,
            isAllFieldConfigsConfigured.messages,
            validated,
            isUpdating,
            defaultLimit,
            skip,
            memberAttributes,
            fieldConfigurations,
            removeFieldConfig,
            setSelectedFieldConfig,
            setFieldConfigPoints,
            setSkip,
        ]
    );

    const onTableChange = useCallback(
        (type, newState) => {
            setSkip(newState?.page);
            setIsPageChanged(true);
        },
        [setSkip, setIsPageChanged]
    );

    const onAddFieldConfig = useCallback(
        (e) => {
            e.stopPropagation();
            if (fieldConfigurations.length % defaultLimit === 0) {
                const newSkip =
                    skip + 1 === lastPage ||
                    (skip + 1 !== lastPage && !isPageChanged)
                        ? skip + 1
                        : skip;
                setSkip(newSkip);
            }
            onAddRuleDataFieldConfigurations();
        },
        [
            fieldConfigurations.length,
            defaultLimit,
            onAddRuleDataFieldConfigurations,
            skip,
            lastPage,
            isPageChanged,
            setSkip,
        ]
    );

    return (
        <div
            className={`border ${
                validated &&
                (isAllFieldConfigsConfigured.validated ||
                    fieldConfigurations.length === 0)
                    ? "is-invalid-border"
                    : validated && "is-valid-border"
            } rounded px-3 pb-3`}
        >
            <div className="font-weight-bold mt-3 text-center text-info">
                Please edit configurations one at a time.
            </div>
            {fieldConfigsPerPage.length !== 0 ? (
                <>
                    <ToolkitProvider
                        keyField="id"
                        data={data}
                        columns={[
                            {
                                dataField: "id",
                                name: "id",
                                hidden: true,
                            },
                            {
                                dataField: "",
                                name: "",
                                sort: false,
                            },
                        ]}
                    >
                        {(props) => (
                            <div>
                                <BootstrapTable
                                    {...props.baseProps}
                                    remote={{
                                        pagination: true,
                                        filter: false,
                                        sort: false,
                                    }}
                                    pagination={paginationFactory(options)}
                                    onTableChange={onTableChange}
                                    overlay={BootstrapTableOverlay}
                                />
                            </div>
                        )}
                    </ToolkitProvider>
                    {/* 
                        // TODO: BELOW COMMENTED LOGIC WORKS AND NO NEED TO CONFIRM POINTS PER CONFIGURATION BUT NO PAGINATION, HAVE TO ADD IT MANUALLY.
                    */}
                    {/* {fieldConfigsPerPage.map((item, fieldConfigIndex) => {
                        const fieldConfigId = item?.fieldId || uuidv4();
                        const fieldConfigValidation =
                            isAllFieldConfigsConfigured.messages.find(
                                (msg) =>
                                    msg?.id === fieldConfigId ||
                                    msg?.fieldName === item?.fieldName
                            ) || {};

                        return (
                            <FieldConfigurationCard
                                key={fieldConfigId}
                                validated={validated}
                                disabled={isUpdating}
                                defaultLimit={defaultLimit}
                                fieldConfigIndex={
                                    (skip - 1) * defaultLimit > 0
                                        ? fieldConfigIndex +
                                          (skip - 1) * defaultLimit
                                        : fieldConfigIndex
                                }
                                fieldConfigId={fieldConfigId}
                                skip={skip}
                                memberAttributes={memberAttributes}
                                selectedFieldConfig={
                                    !isEmptyObject(item) ? [item] : []
                                }
                                fieldConfigurations={fieldConfigurations}
                                data={fieldConfigsPerPage}
                                fieldConfigValidation={fieldConfigValidation}
                                removeFieldConfig={removeFieldConfig}
                                setSelectedFieldConfig={setSelectedFieldConfig}
                                setFieldConfigPoints={setFieldConfigPoints}
                                setSkip={setSkip}
                                {...item}
                            />
                        );
                    })} */}
                </>
            ) : (
                <div className="border rounded my-3 p-3 grey-bg text-center">
                    <h3 className="mb-0">
                        No field configurations have been configured.
                    </h3>
                </div>
            )}
            {(defaultSkip === lastPage ||
                skip === lastPage ||
                (skip !== lastPage &&
                    fieldConfigurations.length % defaultLimit ===
                        fieldConfigsPerPage.length % defaultLimit &&
                    lastPage - skip === 1)) && (
                <div className="text-center">
                    <Button
                        variant="primary"
                        disabled={isUpdating}
                        onClick={onAddFieldConfig}
                    >
                        <div className="d-flex align-items-center">
                            <IcIcon className="mr-2" size="lg" icon={faPlus} />
                            Add Field Configuration
                        </div>
                    </Button>
                </div>
            )}
            {validated && isAllFieldConfigsConfigured.validated && (
                <ValidatedFieldConfigMessages
                    validatedMsg={isAllFieldConfigsConfigured.validatedMsg}
                    secondaryValidatedMessages={
                        isAllFieldConfigsConfigured.secondaryValidatedMessages
                    }
                    messages={isAllFieldConfigsConfigured.messages}
                />
            )}
        </div>
    );
};

FieldConfigurationsView.defaultProps = {
    validated: false,
    isUpdating: false,
    defaultLimit: 3,
    defaultSkip: 1,
    skip: 0,
    memberAttributes: [],
    fieldConfigurations: [],
    isAllFieldConfigsConfigured: {},
    setSkip: () => {},
    onAddRuleDataFieldConfigurations: () => {},
    removeFieldConfig: () => {},
    setSelectedFieldConfig: () => {},
    setFieldConfigPoints: () => {},
};

FieldConfigurationsView.propTypes = {
    validated: PropTypes.bool,
    isUpdating: PropTypes.bool,
    defaultLimit: PropTypes.number,
    defaultSkip: PropTypes.number,
    skip: PropTypes.number,
    memberAttributes: PropTypes.array,
    fieldConfigurations: PropTypes.array,
    isAllFieldConfigsConfigured: PropTypes.object,
    setSkip: PropTypes.func,
    onAddRuleDataFieldConfigurations: PropTypes.func,
    removeFieldConfig: PropTypes.func,
    setSelectedFieldConfig: PropTypes.func,
    setFieldConfigPoints: PropTypes.func,
};

export default FieldConfigurationsView;
