import React, {useCallback, useState} from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {Button,  Modal} from "@shoutout-labs/shoutout-themes-enterprise";
import { deletePoints } from "Services";

const ArchiveModal = ({
    show,
    onHide,
    skip,
    limit,
    pointRuleMainType,
    merchant, 
    status, 
    subType,
    selectedID,
    loadPointRuleData,
}) => {
    const [isArchiving, setIsArchiving] = useState(false);

    const onClickArchive = useCallback(async () => {
        try {
            setIsArchiving(true);
            await deletePoints(selectedID);
            toast.success("Successfully archived point rule");
            onHide();
            await loadPointRuleData({ skip, limit, tab: pointRuleMainType, merchant, status, subType });
            setIsArchiving(false);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to archive point rule!
                    <br />
                    {e.message}
                </div>
            );
              setIsArchiving();
        }

    },[
        skip,
        limit,
        pointRuleMainType,
        merchant, 
        status, 
        subType,
        selectedID,
        onHide,
        loadPointRuleData,
        setIsArchiving
    ]);

    return(
        <Modal
            show={show}
            onHide={isArchiving ? () => {} : onHide}
            size="md"
            backdrop={true}
            centered
        >
            <Modal.Header closeButton={!isArchiving}>
                <Modal.Title>
                    Archive Point Rule
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>
                    Do you want to archive this point rule ?
                </p>
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isArchiving}
                >
                    Cancel
                </Button>
                 <Button
                    size="sm"
                    variant="danger"
                    onClick={onClickArchive}
                    disabled={isArchiving}
                >
                    {isArchiving ? "Archiving..." : "Archive Point Rule"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

ArchiveModal.propTypes = {
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func.isRequired,
    loadPointRuleData: PropTypes.func,
    skip: PropTypes.number,
    limit: PropTypes.number,
    selectedID: PropTypes.string
}

export default ArchiveModal
