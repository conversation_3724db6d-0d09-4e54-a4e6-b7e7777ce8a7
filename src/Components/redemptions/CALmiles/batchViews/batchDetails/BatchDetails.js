import React, { useState, useCallback, useContext, useMemo } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Row,
    Col,
    IcIcon,
    DropdownButton,
    DropdownItem,
    Button,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faTimes, faCheck, faFileImport, faUndo } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    CALMilesJobStatus,
    AccessPermissionModules,
    AccessPermissionModuleNames,
} from "Data";
import { useToggle } from "Hooks";
import { updateRewardDistributionLogs } from "Services";
import { toTitleCase } from "Utils";
import { formatToEventHistoryCard } from "Components/redemptions/utils/RedemptionUtils";
import ViewEventHistory from "Components/redemptions/shared/viewEventHistory/ViewEventHistory";
import ConfirmationModal from "../../shared/ConfirmationModal";
import ImportResults from "./importResults/ImportResults";
import FailAndRefundModal from "../failAndRefundModal/FailAndRefundModal";

import "./BatchDetails.scss";

const defaultSkip = 1,
    defaultLimit = 25;

const BatchDetails = ({
    rewardName,
    isTabDisabled,
    batchData,
    jobStatus,
    isAllowedUpdateBatchProcess,
    searchText,
    appliedFilters,
    loadBatchData,
    backToBatchView,
    setIsReloadRedemptions,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [selectedStatus, setSeletedStatus] = useState("");
    const [showModel, setShowModel] = useState(false);
    const [showModelImport, setShowModelImport] = useState(false);
    const [isChanging, setIsChanging] = useState(false);
    const [showAllEventHistory, toggleShowAllEventHistory] = useToggle(false);
    const [showFailedAndRefundModal, setShowFailedAndRefundModal] =
        useState(false);

    const historyEventsOfBatch =
        useMemo(
            () =>
                batchData?.history_events &&
                batchData?.history_events.length > 1
                    ? batchData?.history_events.slice(0, 1)
                    : batchData?.history_events,
            [batchData?.history_events]
        ) || [];

    const hideModal = useCallback(() => {
        setShowModel(false);
        setSeletedStatus("");
    }, [setShowModel, setSeletedStatus]);

    const showConfirmModal = useCallback(
        (e) => {
            setShowModel(true);
            setSeletedStatus(e);
        },
        [setShowModel, setSeletedStatus]
    );

    const onShowImportModal = useCallback(
        () => setShowModelImport(true),
        [setShowModelImport]
    );

    const onCloseImportModal = useCallback(
        (e, hasImported) => {
            if (hasImported) {
                setIsReloadRedemptions(true);
            }
            setShowModelImport(false);
        },
        [setIsReloadRedemptions, setShowModelImport]
    );

    const onShowFailedAndRefundModal = useCallback(
        () => setShowFailedAndRefundModal(true),
        [setShowFailedAndRefundModal]
    );

    const onCloseFailedAndRefundModal = useCallback(
        () => setShowFailedAndRefundModal(false),
        [setShowFailedAndRefundModal]
    );

    const onSelectBatchUpdateAction = useCallback(
        (e) => {
            if (e === "FAIL_AND_REFUND") {
                onShowFailedAndRefundModal();
            } else {
                showConfirmModal(e);
            }
        },
        [onShowFailedAndRefundModal, showConfirmModal]
    );

    const updateBatchProcess = useCallback(async () => {
        const payload = { status: selectedStatus };

        try {
            setIsChanging(true);
            await updateRewardDistributionLogs(batchData._id, payload);
            backToBatchView();
            loadBatchData(
                {
                    skip: defaultSkip,
                    limit: defaultLimit,
                    searchKey: searchText,
                },
                appliedFilters
            );
            setIsChanging(false);
            hideModal();
            toast.success(
                `Successfully ${toTitleCase(selectedStatus)} the batch.`
            );
        } catch (e) {
            console.error(e);
            setIsChanging(false);
            toast.error(
                <div>
                    Failed to update batch process!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        selectedStatus,
        batchData,
        searchText,
        appliedFilters,
        setIsChanging,
        backToBatchView,
        loadBatchData,
        hideModal,
    ]);

    const backToBatchViewAfterFailingAndRefunding = useCallback(() => {
        backToBatchView();
        loadBatchData(
            {
                skip: defaultSkip,
                limit: defaultLimit,
                searchKey: searchText,
            },
            appliedFilters
        );
    }, [searchText, appliedFilters, backToBatchView, loadBatchData]);

    return (
        <div className="batch-details">
            {(jobStatus === CALMilesJobStatus.PROCESSING ||
                jobStatus === CALMilesJobStatus.COMPLETED ||
                jobStatus === CALMilesJobStatus.FAILED) && (
                <div className="basic-details-top mt-4">
                    <Row noGutters>
                        {jobStatus !== CALMilesJobStatus.PROCESSING ? (
                            <>
                                <Col
                                    xl="3"
                                    lg="3"
                                    md="3"
                                    className="border-right"
                                >
                                    <Row
                                        noGutters
                                        className="h-100 d-flex align-items-center"
                                    >
                                        <Col className="p-3 ml-3">
                                            <h2 className="mb-0">
                                                {`Batch Id: ${
                                                    batchData?.batch_id ||
                                                    "~ unknown"
                                                }`}
                                            </h2>
                                            <p className="text-muted mb-0">
                                                Created on <span />
                                                {batchData?.date || "~ unknown"}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col
                                    xl="2"
                                    lg="2"
                                    md="2"
                                    className="border-right"
                                >
                                    <Row noGutters className="border-bottom">
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Status
                                            </p>
                                            <p className="pt-1 mb-0 mx-2">
                                                {batchData?.status}
                                            </p>
                                        </Col>
                                    </Row>
                                    <Row noGutters>
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Total Transactions
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.total_transactions ||
                                                    0}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col
                                    xl="2"
                                    lg="2"
                                    md="2"
                                    className="border-right"
                                >
                                    <Row noGutters className="border-bottom">
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Successful Transactions
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.successful_transactions ||
                                                    0}
                                            </p>
                                        </Col>
                                    </Row>
                                    <Row noGutters>
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Failed Transactions
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.failed_transactions ||
                                                    0}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                            </>
                        ) : (
                            <>
                                <Col
                                    xl="2"
                                    lg="2"
                                    md="2"
                                    className="border-right"
                                >
                                    <Row
                                        noGutters
                                        className="h-100 d-flex align-items-center"
                                    >
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Batch Id
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.batch_id ||
                                                    "~ unknown"}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col
                                    xl="3"
                                    lg="3"
                                    md="3"
                                    className="border-right"
                                >
                                    <Row
                                        noGutters
                                        className="h-100 d-flex align-items-center"
                                    >
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Created On
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.date || "~ unknown"}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col
                                    xl="2"
                                    lg="2"
                                    md="2"
                                    className="border-right"
                                >
                                    <Row
                                        noGutters
                                        className="h-100 d-flex align-items-center"
                                    >
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Status
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.status}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                                <Col
                                    xl="2"
                                    lg="2"
                                    md="2"
                                    className="d-flex flex-column"
                                >
                                    <Row
                                        noGutters
                                        className="h-100 d-flex align-items-center"
                                    >
                                        <Col className="p-3">
                                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                                Total Transactions
                                            </p>
                                            <p className="py-1 mb-0 mx-2">
                                                {batchData?.total_transactions ||
                                                    0}
                                            </p>
                                        </Col>
                                    </Row>
                                </Col>
                            </>
                        )}
                        {jobStatus !== CALMilesJobStatus.PROCESSING && (
                            <Col
                                xl="5"
                                lg="5"
                                md="5"
                                className="d-flex align-items-center"
                            >
                                <Col>
                                    <Col className="p-3">
                                        <Row className="d-flex flex-column">
                                            {historyEventsOfBatch.length !==
                                                0 && (
                                                <div className="mb-2">
                                                    <div className="d-flex justify-content-between align-items-center">
                                                        <div>Event History</div>
                                                        {batchData
                                                            ?.history_events
                                                            .length > 1 && (
                                                            <Button
                                                                variant="link"
                                                                size="sm"
                                                                onClick={
                                                                    toggleShowAllEventHistory
                                                                }
                                                            >
                                                                Show More
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                            {historyEventsOfBatch.length !==
                                            0 ? (
                                                <div>
                                                    {formatToEventHistoryCard(
                                                        historyEventsOfBatch
                                                    )}
                                                </div>
                                            ) : (
                                                <div className="text-center">
                                                    No events history found.
                                                </div>
                                            )}
                                        </Row>
                                    </Col>
                                </Col>
                            </Col>
                        )}
                        <Col
                            xl={
                                jobStatus !== CALMilesJobStatus.PROCESSING
                                    ? "0"
                                    : "3"
                            }
                            lg={
                                jobStatus !== CALMilesJobStatus.PROCESSING
                                    ? "0"
                                    : "3"
                            }
                            md={
                                jobStatus !== CALMilesJobStatus.PROCESSING
                                    ? "0"
                                    : "3"
                            }
                            className="border-left d-flex align-items-center"
                        >
                            <Row noGutters className="w-100">
                                {isAuthorizedForAction(
                                    AccessPermissionModuleNames.REWARD,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.REWARD
                                    ].actions.UpdateRewardDistributionJob
                                ) &&
                                    jobStatus ===
                                        CALMilesJobStatus.PROCESSING && (
                                        <Col className="w-100 d-flex justify-content-center align-items-center">
                                            <DropdownButton
                                                disabled={
                                                    !isAllowedUpdateBatchProcess ||
                                                    isTabDisabled
                                                }
                                                variant="outline-info"
                                                size="sm"
                                                title={
                                                    <span className="font-weight-bold">
                                                        Mark As
                                                    </span>
                                                }
                                                onSelect={
                                                    onSelectBatchUpdateAction
                                                }
                                                onClick={(e) =>
                                                    e.stopPropagation()
                                                }
                                            >
                                                <DropdownItem
                                                    eventKey={
                                                        CALMilesJobStatus.COMPLETED
                                                    }
                                                    key={
                                                        CALMilesJobStatus.COMPLETED
                                                    }
                                                    className="text-success"
                                                >
                                                    <div className="d-flex align-items-center">
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="lg"
                                                            icon={faCheck}
                                                        />
                                                        Complete
                                                    </div>
                                                </DropdownItem>
                                                <DropdownItem
                                                    eventKey={
                                                        CALMilesJobStatus.FAILED
                                                    }
                                                    key={
                                                        CALMilesJobStatus.FAILED
                                                    }
                                                    className="text-danger"
                                                >
                                                    <div className="d-flex align-items-center">
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="lg"
                                                            icon={faTimes}
                                                        />
                                                        Fail
                                                    </div>
                                                </DropdownItem>
                                                <DropdownItem
                                                    eventKey="FAIL_AND_REFUND"
                                                    key="FAIL_AND_REFUND"
                                                    className="text-secondary"
                                                >
                                                    <div className="d-flex align-items-center">
                                                        <IcIcon
                                                            className="mr-2"
                                                            size="lg"
                                                            icon={faUndo}
                                                        />
                                                        Fail and Refund
                                                    </div>
                                                </DropdownItem>
                                            </DropdownButton>
                                        </Col>
                                    )}
                                {jobStatus === CALMilesJobStatus.PROCESSING && (
                                    <Col className="w-100 d-flex justify-content-center align-items-center">
                                        <Button
                                            name="IMPORT_FILES"
                                            id={batchData._id}
                                            size="sm"
                                            variant="purple"
                                            disabled={isTabDisabled}
                                            onClick={onShowImportModal}
                                        >
                                            <div className="d-flex align-items-center">
                                                <IcIcon
                                                    className="mr-2"
                                                    size="lg"
                                                    icon={faFileImport}
                                                />
                                                Import
                                            </div>
                                        </Button>
                                    </Col>
                                )}
                            </Row>
                        </Col>
                    </Row>
                </div>
            )}
            <ConfirmationModal
                showModel={showModel}
                hideModal={hideModal}
                selectedStatus={selectedStatus}
                onChangeBatchJobStatus={updateBatchProcess}
                isChanging={isChanging}
                tab={jobStatus}
            />
            {showModelImport && (
                <ImportResults
                    rewardName={rewardName}
                    onHide={onCloseImportModal}
                    showModelImport={showModelImport}
                    batchData={batchData}
                />
            )}
            {showAllEventHistory && (
                <ViewEventHistory
                    show={showAllEventHistory}
                    onHide={toggleShowAllEventHistory}
                    eventHistoryData={batchData?.history_events}
                />
            )}
            {showFailedAndRefundModal && (
                <FailAndRefundModal
                    show={showFailedAndRefundModal}
                    onHide={onCloseFailedAndRefundModal}
                    batchData={batchData}
                    backToBatchView={backToBatchViewAfterFailingAndRefunding}
                />
            )}
        </div>
    );
};

BatchDetails.propTypes = {
    batchData: PropTypes.object.isRequired,
    isAllowedUpdateBatchProcess: PropTypes.bool,
    loadBatchData: PropTypes.func,
    backToBatchView: PropTypes.func,
};

export default BatchDetails;
