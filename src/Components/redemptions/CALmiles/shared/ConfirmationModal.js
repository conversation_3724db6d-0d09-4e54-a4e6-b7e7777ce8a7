import React from "react";
import { <PERSON><PERSON>, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import PropTypes from "prop-types";
import { CALMilesJobStatus, CALMilesProcessStatus } from "Data";
import { toTitleCase } from "Utils";

const buttonData = (status, isChanging) => {
    switch (status) {
        case CALMilesJobStatus.PENDING: {
            const name = isChanging
                ? "Starting Process..."
                : "Confirm Start Process";
            return { variant: "primary", name };
        }
        case CALMilesJobStatus.COMPLETED: {
            const name = isChanging ? "Completing..." : "Complete";
            return { variant: "outline-success", name };
        }
        case CALMilesJobStatus.FAILED: {
            const name = isChanging ? "Failing..." : "Fail";
            return { variant: "outline-danger", name };
        }
        default: {
            return { variant: "primary", name: "" };
        }
    }
};

const ConfirmationModal = ({
    showModel,
    hideModal,
    selectedStatus,
    isChanging,
    selectedItems,
    onChangeBatchJobStatus,
    tab,
    createCalMilesDistributionJobs,
}) => {
    return (
        <Modal
            show={showModel}
            onHide={isChanging ? () => {} : hideModal}
            size="md"
            backdrop={isChanging ? "static" : true}
            centered
        >
            {selectedStatus && (
                <>
                    <Modal.Header closeButton={!isChanging}>
                        <Modal.Title>
                            {tab === CALMilesJobStatus.PENDING &&
                                "Confirm Start Process"}
                            {selectedStatus === CALMilesJobStatus.COMPLETED &&
                                `${toTitleCase(
                                    CALMilesProcessStatus.COMPLETE
                                )} Batch`}
                            {selectedStatus === CALMilesJobStatus.FAILED &&
                                `${toTitleCase(
                                    CALMilesProcessStatus.FAIL
                                )} Batch`}
                        </Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        {tab === CALMilesJobStatus.PENDING && (
                            <p>
                                Do you wish to start the process with the
                                selected{" "}
                                {selectedItems.length === 1
                                    ? "item?"
                                    : `${selectedItems.length} items?`}
                            </p>
                        )}
                        {selectedStatus === CALMilesJobStatus.COMPLETED &&
                            `Do you wish to ${toTitleCase(
                                CALMilesProcessStatus.COMPLETE
                            )} this batch?`}
                        {selectedStatus === CALMilesJobStatus.FAILED &&
                            `Do you wish to ${toTitleCase(
                                CALMilesProcessStatus.FAIL
                            )} this batch?`}
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            size="sm"
                            variant="outline-primary"
                            onClick={hideModal}
                            disabled={isChanging}
                        >
                            Cancel
                        </Button>
                        <Button
                            size="sm"
                            variant={
                                buttonData(
                                    tab === CALMilesJobStatus.PROCESSING
                                        ? selectedStatus
                                        : tab
                                ).variant
                            }
                            onClick={
                                tab === CALMilesJobStatus.PENDING
                                    ? createCalMilesDistributionJobs
                                    : onChangeBatchJobStatus
                            }
                            disabled={isChanging || !selectedStatus}
                        >
                            {
                                buttonData(
                                    tab === CALMilesJobStatus.PROCESSING
                                        ? selectedStatus
                                        : tab,
                                    isChanging
                                ).name
                            }
                        </Button>
                    </Modal.Footer>
                </>
            )}
        </Modal>
    );
};

ConfirmationModal.defaultProps = {
    selectedItems: [],
    createCalMilesDistributionJobs: () => {},
    onChangeBatchJobStatus: () => {},
};

ConfirmationModal.propTypes = {
    tab: PropTypes.string,
    showModel: PropTypes.func,
    hideModal: PropTypes.func,
    selectedStatus: PropTypes.string,
    isChanging: PropTypes.bool,
    selectedItems: PropTypes.array,
    createCalMilesDistributionJobs: PropTypes.func,
    onChangeBatchJobStatus: PropTypes.func,
};

export default ConfirmationModal;
