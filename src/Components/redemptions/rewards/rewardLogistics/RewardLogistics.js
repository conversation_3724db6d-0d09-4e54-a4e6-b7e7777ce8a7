import React, { useState, useContext, useMemo } from "react";
import { Tab, Tabs } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import {
    RewardGenerationJobStatus,
    AccessPermissionModules,
    AccessPermissionModuleNames,
} from "Data";
import BaseLayout from "Layout/BaseLayout";
import RewardLogisticsTabsView from "./shared/RewardLogisticsTabsView";

const RewardLogistics = ({ tab, tabIsLoading, setTabIsLoading }) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [selectedLogisticsTab, setSelectedLogisticsTab] = useState(
        RewardGenerationJobStatus.PENDING
    );

    const isAllowedListDistributionJobs = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.ListRewardDistributionJobs
            ),
        [isAuthorizedForAction]
    );

    return (
        <BaseLayout
            bottom={
                <div className="mt-3">
                    <Tabs
                        transition={false}
                        id="noanim-selected-logistic-tab"
                        className="mb-3 border-solid-bottom"
                        activeKey={selectedLogisticsTab}
                        onSelect={setSelectedLogisticsTab}
                    >
                        <Tab
                            eventKey={RewardGenerationJobStatus.PENDING}
                            title={<span className="mr-2">Pending</span>}
                            disabled={tabIsLoading}
                        >
                            {selectedLogisticsTab ===
                                RewardGenerationJobStatus.PENDING && (
                                <RewardLogisticsTabsView
                                    selectedHeaderTab={tab}
                                    selectedLogisticsTab={selectedLogisticsTab}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={RewardGenerationJobStatus.PROCESSING}
                            title={<span className="mr-2">Processing</span>}
                            disabled={
                                tabIsLoading || !isAllowedListDistributionJobs
                            }
                        >
                            {selectedLogisticsTab ===
                                RewardGenerationJobStatus.PROCESSING && (
                                <RewardLogisticsTabsView
                                    selectedHeaderTab={tab}
                                    selectedLogisticsTab={selectedLogisticsTab}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={RewardGenerationJobStatus.DISPATCHED}
                            title={<span className="mr-2">Dispatched</span>}
                            disabled={
                                tabIsLoading || !isAllowedListDistributionJobs
                            }
                        >
                            {selectedLogisticsTab ===
                                RewardGenerationJobStatus.DISPATCHED && (
                                <RewardLogisticsTabsView
                                    selectedHeaderTab={tab}
                                    selectedLogisticsTab={selectedLogisticsTab}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={RewardGenerationJobStatus.COMPLETED}
                            title={<span className="mr-2">Completed</span>}
                            disabled={
                                tabIsLoading || !isAllowedListDistributionJobs
                            }
                        >
                            {selectedLogisticsTab ===
                                RewardGenerationJobStatus.COMPLETED && (
                                <RewardLogisticsTabsView
                                    selectedHeaderTab={tab}
                                    selectedLogisticsTab={selectedLogisticsTab}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                        <Tab
                            eventKey={RewardGenerationJobStatus.FAILED}
                            title={<span className="mr-2">Failed</span>}
                            disabled={
                                tabIsLoading || !isAllowedListDistributionJobs
                            }
                        >
                            {selectedLogisticsTab ===
                                RewardGenerationJobStatus.FAILED && (
                                <RewardLogisticsTabsView
                                    selectedHeaderTab={tab}
                                    selectedLogisticsTab={selectedLogisticsTab}
                                    setTabIsLoading={setTabIsLoading}
                                />
                            )}
                        </Tab>
                    </Tabs>
                </div>
            }
        />
    );
};

export default RewardLogistics;
