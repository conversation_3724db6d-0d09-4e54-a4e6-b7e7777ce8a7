import React, { useCallback, useMemo, useContext } from "react";
import PropTypes from "prop-types";
import {
    Row,
    Col,
    DropdownButton,
    DropdownItem,
    IcIcon,
    Button,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCheck, faDispatch, faFileExport, faTimes } from "FaICIconMap";
import { UserContext } from "Contexts";
import {
    RewardGenerationJobStatus,
    RewardRedemptionProcessStatus,
    AccessPermissionModuleNames,
    AccessPermissionModules,
} from "Data";
import { useToggle } from "Hooks";
import { formatToEventHistoryCard } from "Components/redemptions/utils/RedemptionUtils";
import ViewEventHistory from "Components/redemptions/shared/viewEventHistory/ViewEventHistory";

import "./RewardLogisticDetails.scss";

const RewardLogisticDetails = ({
    isLoading,
    selectedTab,
    selectedBatch,
    onBatchProcessStatus<PERSON>hange,
    isExporting,
    onExportBatch,
    isAllowedUpdateBatchProcess,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);
    const [showAllEventHistory, toggleShowAllEventHistory] = useToggle(false);

    const historyEventsOfBatch =
        useMemo(
            () =>
                selectedBatch?.history_events &&
                selectedBatch?.history_events.length > 1
                    ? selectedBatch?.history_events.slice(0, 1)
                    : selectedBatch?.history_events,
            [selectedBatch?.history_events]
        ) || [];

    const onSelectMarkAs = useCallback(
        (action) => {
            const selectedBatchObj = {
                _id: selectedBatch._id,
                selectAction: action,
            };
            onBatchProcessStatusChange(selectedBatchObj);
        },
        [selectedBatch._id, onBatchProcessStatusChange]
    );

    return (
        <div className="reward-logistics-container mt-4">
            <Row noGutters>
                <Col
                    xl="3"
                    lg="3"
                    md="3"
                    className="border-right pt-4 d-flex flex-column align-items-left"
                >
                    <Row noGutters className="d-flex flex-column">
                        <Col className="px-3 pt-3 ml-3">
                            <h2 className="mt-3 mb-0">
                                {"Batch ID: "}
                                {selectedBatch?.batch_id || "-"}
                            </h2>
                            <p className="text-muted mb-0">
                                Created on <span />
                                {selectedBatch?.date || "-"}
                            </p>
                        </Col>
                    </Row>
                </Col>
                <Col
                    xl="2"
                    lg="2"
                    md="2"
                    className="border-right d-flex flex-column"
                >
                    <Row noGutters className="border-bottom">
                        <Col className="p-3">
                            <p className="py-1 mb-0 mx-2">Status</p>
                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                {selectedBatch?.status}
                            </p>
                        </Col>
                    </Row>
                    <Row noGutters>
                        <Col className="p-3">
                            <p className="py-1 mb-0 mx-2">No. Rewards</p>
                            <p className="py-1 mb-0 mx-2 font-weight-bold">
                                {selectedBatch.no_rewards || 0}
                            </p>
                        </Col>
                    </Row>
                </Col>
                <Col
                    xl="5"
                    lg="5"
                    md="5"
                    className="border-right d-flex justify-content-start align-items-center"
                >
                    <Col>
                        <Col className="p-3">
                            <Row className="d-flex flex-column">
                                {historyEventsOfBatch.length !== 0 && (
                                    <div className="mb-2">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div>Event History</div>
                                            {selectedBatch?.history_events
                                                .length > 1 && (
                                                <Button
                                                    variant="link"
                                                    size="sm"
                                                    onClick={
                                                        toggleShowAllEventHistory
                                                    }
                                                >
                                                    Show More
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                                {historyEventsOfBatch.length !== 0 ? (
                                    <div>
                                        {formatToEventHistoryCard(
                                            historyEventsOfBatch
                                        )}
                                    </div>
                                ) : (
                                    <div className="text-center">
                                        No events history found.
                                    </div>
                                )}
                            </Row>
                        </Col>
                    </Col>
                </Col>
                <Col
                    xl="2"
                    lg="2"
                    md="2"
                    className="d-flex flex-column align-items-center p-5"
                >
                    {isAuthorizedForAction(
                        AccessPermissionModuleNames.REWARD,
                        AccessPermissionModules[
                            AccessPermissionModuleNames.REWARD
                        ].actions.UpdateLogItem
                    ) && (
                        <Row noGutters className="pb-3">
                            <Col className="px-3">
                                <div>
                                    {selectedTab ===
                                        RewardGenerationJobStatus.PROCESSING && (
                                        <DropdownButton
                                            disabled={
                                                isLoading ||
                                                !isAllowedUpdateBatchProcess
                                            }
                                            variant="outline-info"
                                            size="sm"
                                            title={
                                                <span className="font-weight-bold">
                                                    Mark As
                                                </span>
                                            }
                                            onSelect={onSelectMarkAs}
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <DropdownItem
                                                eventKey={
                                                    RewardRedemptionProcessStatus.DISPATCH
                                                }
                                                key={
                                                    RewardRedemptionProcessStatus.DISPATCH
                                                }
                                                className="text-secondary"
                                            >
                                                <div className="d-flex align-items-center">
                                                    <IcIcon
                                                        className="flipH mr-2"
                                                        size="lg"
                                                        icon={faDispatch}
                                                    />
                                                    Dispatch
                                                </div>
                                            </DropdownItem>
                                        </DropdownButton>
                                    )}
                                    {selectedTab ===
                                        RewardGenerationJobStatus.DISPATCHED && (
                                        <DropdownButton
                                            disabled={
                                                isLoading ||
                                                !isAllowedUpdateBatchProcess
                                            }
                                            variant="outline-info"
                                            size="sm"
                                            title={
                                                <span className="font-weight-bold">
                                                    Mark As
                                                </span>
                                            }
                                            onSelect={onSelectMarkAs}
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <DropdownItem
                                                eventKey={
                                                    RewardRedemptionProcessStatus.COMPLETE
                                                }
                                                key={
                                                    RewardRedemptionProcessStatus.COMPLETE
                                                }
                                                className="text-success"
                                            >
                                                <div className="d-flex align-items-center">
                                                    <IcIcon
                                                        className="mr-2"
                                                        size="lg"
                                                        icon={faCheck}
                                                    />
                                                    Complete
                                                </div>
                                            </DropdownItem>
                                            <DropdownItem
                                                eventKey={
                                                    RewardRedemptionProcessStatus.FAIL
                                                }
                                                key={
                                                    RewardRedemptionProcessStatus.FAIL
                                                }
                                                className="text-danger"
                                            >
                                                <div className="d-flex align-items-center">
                                                    <IcIcon
                                                        className="mr-2"
                                                        size="lg"
                                                        icon={faTimes}
                                                    />
                                                    Fail
                                                </div>
                                            </DropdownItem>
                                        </DropdownButton>
                                    )}
                                </div>
                            </Col>
                        </Row>
                    )}
                    <Row noGutters className="pt-3">
                        <Col className="px-3">
                            <div>
                                <Button
                                    name="EXPORT_BATCH"
                                    id={selectedBatch._id}
                                    size="sm"
                                    variant="outline-primary"
                                    disabled={isLoading || isExporting}
                                    onClick={onExportBatch}
                                >
                                    <div className="d-flex align-items-center">
                                        <IcIcon
                                            className="mr-2"
                                            size="lg"
                                            icon={faFileExport}
                                        />
                                        {isExporting
                                            ? "Exporting..."
                                            : "Export"}
                                    </div>
                                </Button>
                            </div>
                        </Col>
                    </Row>
                </Col>
            </Row>

            {showAllEventHistory && (
                <ViewEventHistory
                    show={showAllEventHistory}
                    onHide={toggleShowAllEventHistory}
                    eventHistoryData={selectedBatch?.history_events}
                />
            )}
        </div>
    );
};

RewardLogisticDetails.defaultProps = {
    selectedBatch: {},
};

RewardLogisticDetails.propTypes = {
    /**
     * Selected reward logistics batch details
     */
    selectedBatch: PropTypes.object.isRequired,
};

export default RewardLogisticDetails;
