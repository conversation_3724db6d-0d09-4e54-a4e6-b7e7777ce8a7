import React, {
    useCallback,
    useState,
    useEffect,
    useContext,
    useMemo,
} from "react";
import { toast } from "react-toastify";
import { DataContext, UserContext } from "Contexts";
import {
    RewardsHeaderTabs,
    RewardGenerationJobGroups,
    RewardGenerationJobStatus,
    RewardRedemptionProcessStatus,
    RewardType,
    RewardSubType,
    AccessPermissionModules,
    AccessPermissionModuleNames,
} from "Data";
import { useToggle } from "Hooks";
import {
    exportRedemptionLogs,
    getRedemptionLogs,
    getRewardDistributionJobs,
    getRewards,
} from "Services";
import { downloadLink, formatToCommonFormat } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import RewardLogisticsTable from "../rewardLogisticsTable/RewardLogisticsTable";
import RewardLogisticsProfile from "../rewardLogisticsProfile/RewardLogisticsProfile";
import RewardLogisticsTopPanel from "./topPanel/RewardLogisticsTopPanel";
import Filters from "./filters/Filters";

import "./RewardLogisticsTabsView.scss";

const defaultLimit = 10,
    defaultSkip = 1;
let timeout;

const RewardLogisticsTabsView = ({
    selectedHeaderTab,
    selectedLogisticsTab,
    setTabIsLoading,
}) => {
    const { regionId, isAuthorizedForAction } = useContext(UserContext);
    const { merchantLocations } = useContext(DataContext);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [searchText, setSearchText] = useState("");
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [rewardLogisticsBatchList, setRewardLogisticsBatchList] = useState(
        []
    );
    const [redemptionLogsList, setRedemptionLogsList] = useState([]);
    const [totalLogistics, setTotalLogistics] = useState(0);
    const [totalRedemptions, setTotalRedemptions] = useState(0);
    const [isLoadingLogistics, setIsLoadingLogistics] = useState(false);
    const [isLoadingRedemptions, setIsLoadingRedemptions] = useState(false);
    const [isReloadingLogistics, setIsReloadingLogistics] = useState(false);
    const [isReloadingRedemptions, setIsReloadingRedemptions] = useState(false);
    const [isLoadingRewards, setIsLoadingRewards] = useState(false);
    const [rewardNames, setRewardNames] = useState([]);
    const [getFromDate, setGetFromDate] = useState("");
    const [getToDate, setGetToDate] = useState("");
    const [selectFilter, setSelectFilter] = useState();
    const [selectedFilter, setSelectedFilter] = useState();
    const [filterValues, setFilterValues] = useState();
    const [isSet, setIsSet] = useState(false);
    const [appliedFilters, setAppliedFilters] = useState();
    const [isApplied, setIsApplied] = useState(false);
    const [batchView, setBatchView] = useState(true);
    const [selectedBatchId, setSelectedBatchId] = useState("");
    const [selectedBatch, setSelectedBatch] = useState({});
    const [selectedItems, setSelectedItems] = useState([]);
    const [changeBatchProcess, setChangeBatchProcess] = useState(false);
    const [batchProcessDetails, setBatchProcessDetails] = useState({});
    const [batchRow, setBatchRow] = useState("");
    const [isExporting, setIsExporting] = useState(false);
    const [memberId, setMemberId] = useState("");
    const [viewMemberDetails, setViewMemberDetails] = useState(false);

    const isAllowedUpdateBatchProcess = useMemo(
        () =>
            isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD]
                    .actions.UpdateRewardDistributionJob
            ),
        [isAuthorizedForAction]
    );

    const allLocationsToFilter = useMemo(() => {
        const mappedToLocations = [];

        Object.values(merchantLocations).forEach((locationId) => {
            Object.values(locationId).forEach((location) => {
                mappedToLocations.push(location);
            });
        });

        return mappedToLocations;
    }, [merchantLocations]);

    const loadRewardLogistics = useCallback(
        async ({ limit, skip }, searchKey) => {
            let queryObj = {
                limit,
                skip: (skip - 1) * limit,
                regionId,
            };

            try {
                setTabIsLoading(true);
                setIsLoadingLogistics(true);

                switch (selectedLogisticsTab) {
                    case RewardGenerationJobStatus.PROCESSING:
                        queryObj = {
                            ...queryObj,
                            status: RewardGenerationJobStatus.PROCESSING,
                        };
                        break;
                    case RewardGenerationJobStatus.DISPATCHED:
                        queryObj = {
                            ...queryObj,
                            status: RewardGenerationJobStatus.DISPATCHED,
                        };
                        break;
                    case RewardGenerationJobStatus.COMPLETED:
                        queryObj = {
                            ...queryObj,
                            status: RewardGenerationJobStatus.COMPLETED,
                        };
                        break;
                    case RewardGenerationJobStatus.FAILED:
                        queryObj = {
                            ...queryObj,
                            status: RewardGenerationJobStatus.FAILED,
                        };
                        break;
                    default:
                        return queryObj;
                }

                if (searchKey) {
                    queryObj = { ...queryObj, searchKey: searchKey };
                }
                const rewardsResponse = await getRewardDistributionJobs(
                    queryObj
                );
                setRewardLogisticsBatchList(rewardsResponse.items);
                setTotalLogistics(rewardsResponse.total);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load reward distribution jobs!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setTabIsLoading(false);
                setIsLoadingLogistics(false);
            }
        },
        [
            regionId,
            selectedLogisticsTab,
            setTabIsLoading,
            setIsLoadingLogistics,
            setRewardLogisticsBatchList,
            setTotalLogistics,
        ]
    );

    const loadRedemptionLogs = useCallback(
        async ({ limit, skip }, filters = null, batchId = null) => {
            let queryObj = {
                limit,
                skip: (skip - 1) * limit,
                regionId,
                rewardType: RewardType.TANGIBLE,
                rewardSubType: RewardSubType.VOUCHER,
            };

            try {
                setTabIsLoading(true);
                setIsLoadingRedemptions(true);

                if (
                    selectedLogisticsTab === RewardGenerationJobStatus.PENDING
                ) {
                    queryObj = {
                        ...queryObj,
                        processingStatus: RewardGenerationJobStatus.PENDING,
                    };
                }

                if (filters) {
                    queryObj = { ...queryObj, ...filters };
                }

                if (batchId) {
                    queryObj = { ...queryObj, distributionJobId: batchId };
                }

                const redemptionLogsResponse = await getRedemptionLogs(
                    queryObj
                );
                setRedemptionLogsList(redemptionLogsResponse.items);
                setTotalRedemptions(redemptionLogsResponse.total);
                return {
                    list: redemptionLogsResponse.items,
                    total: redemptionLogsResponse.total,
                };
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load reward redemption logs!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setTabIsLoading(false);
                setIsLoadingRedemptions(false);
            }
        },
        [
            selectedLogisticsTab,
            setTabIsLoading,
            setIsLoadingRedemptions,
            setRedemptionLogsList,
            setTotalRedemptions,
            regionId,
        ]
    );

    const loadRewards = useCallback(
        async ({ limit, skip }) => {
            const queryObj = { limit, skip, regionId };

            try {
                setIsLoadingRewards(true);
                const rewardResponse = await getRewards(queryObj);
                const rewardNameFilterData = rewardResponse.items.map(
                    (reward) => ({
                        value: reward._id,
                        name: reward.name,
                    })
                );
                setRewardNames(rewardNameFilterData);
                return rewardNameFilterData;
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load reward names for filtering!
                        <br />
                        {e.message ? `Error: ${e.message}` : ""}
                        <br />
                        Please refresh the filters to try again.
                    </div>
                );
            } finally {
                setIsLoadingRewards(false);
            }
        },
        [regionId, setIsLoadingRewards, setRewardNames]
    );

    const onSearch = useCallback(
        (search) => {
            if (timeout) {
                clearTimeout(timeout);
            }
            setSearchText(search);
            timeout = setTimeout(async () => {
                await loadRewardLogistics({ limit, skip: defaultSkip }, search);
                setSkip(defaultSkip);
            }, 2000);
        },
        [limit, setSearchText, loadRewardLogistics, setSkip]
    );

    const onFilterBy = useCallback(async () => {
        toggleShowFilters();
        if ((selectFilter || selectedFilter) && !isApplied && !appliedFilters) {
            setGetFromDate("");
            setGetToDate("");
            setSelectFilter();
            setSelectedFilter();
            setIsSet(false);
        }
    }, [
        toggleShowFilters,
        selectFilter,
        selectedFilter,
        isApplied,
        appliedFilters,
    ]);

    const onSetFilterType = useCallback(
        (e) => {
            setSelectFilter(e);
            if (e[0].value === "REWARD_NAME") {
                setSelectedFilter();
                setFilterValues(rewardNames);
            }
            if (e[0].value === "LOCATION") {
                setSelectedFilter();
                setFilterValues(allLocationsToFilter);
            }
            if (e[0].value === "DATE") {
                setSelectedFilter();
            }
            setIsApplied(false);
            setSelectedFilter();
            setIsSet(false);
        },
        [
            rewardNames,
            allLocationsToFilter,
            setSelectFilter,
            setSelectedFilter,
            setFilterValues,
        ]
    );

    const onSetFilter = useCallback(
        (e, dateType) => {
            setSelectedFilter(e);
            setIsSet(true);
            if (appliedFilters && selectFilter[0].value === "REWARD_NAME") {
                switch (selectFilter[0].value) {
                    case "REWARD_NAME":
                        setIsApplied(e[0].value === appliedFilters.rewardId);
                        break;
                    case "LOCATION":
                        setIsApplied(
                            e[0]?._id === appliedFilters.claimLocationId
                        );
                        break;
                    case "DATE":
                        switch (dateType) {
                            case "FROM_DATE":
                                setIsApplied(
                                    formatToCommonFormat(e) ===
                                        appliedFilters.fromDate
                                );
                                break;
                            case "TO_DATE":
                                setIsApplied(
                                    formatToCommonFormat(e) ===
                                        appliedFilters.toDate
                                );
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
        },
        [selectFilter, appliedFilters, setSelectedFilter, setIsSet]
    );

    const onChangeFromDate = useCallback(
        (date) => {
            setGetFromDate(date);
            onSetFilter(date, "FROM_DATE");
        },
        [onSetFilter, setGetFromDate]
    );

    const onChangeToDate = useCallback(
        (date) => {
            setGetToDate(date);
            onSetFilter(date, "TO_DATE");
        },
        [onSetFilter, setGetToDate]
    );

    const applyFilter = useCallback(async () => {
        if (selectFilter[0].value === "REWARD_NAME") {
            setAppliedFilters({ rewardId: selectedFilter[0].value });
        } else if (selectFilter[0].value === "LOCATION") {
            setAppliedFilters({ claimLocationId: selectedFilter[0]?._id });
        } else if (selectFilter[0].value === "DATE") {
            if (getToDate === "") {
                setAppliedFilters({
                    fromDate: formatToCommonFormat(getFromDate),
                });
            } else if (getFromDate === "") {
                setAppliedFilters({
                    toDate: formatToCommonFormat(getToDate),
                });
            } else {
                setAppliedFilters({
                    fromDate: formatToCommonFormat(getFromDate),
                    toDate: formatToCommonFormat(getToDate),
                });
            }
        }
        setIsApplied(true);
        setSkip(defaultSkip);
    }, [
        selectFilter,
        selectedFilter,
        getFromDate,
        getToDate,
        setIsApplied,
        setSkip,
    ]);

    const removeFromDate = useCallback(() => {
        setGetFromDate("");
        setAppliedFilters();
        if (appliedFilters?.toDate) {
            setAppliedFilters({
                toDate: formatToCommonFormat(getToDate),
            });
        } else {
            setIsSet(false);
            setIsApplied(false);
            setSelectedFilter();
            setSelectFilter();
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        getToDate,
        setGetFromDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSelectedFilter,
        setSelectFilter,
        setSkip,
    ]);

    const removeToDate = useCallback(() => {
        setGetToDate("");
        setAppliedFilters();
        if (appliedFilters?.fromDate) {
            setAppliedFilters({
                fromDate: formatToCommonFormat(getFromDate),
            });
        } else {
            setIsSet(false);
            setIsApplied(false);
            setSelectedFilter();
            setSelectFilter();
        }
        setSkip(defaultSkip);
    }, [
        appliedFilters,
        getFromDate,
        setGetToDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSelectedFilter,
        setSelectFilter,
        setSkip,
    ]);

    const resetFilter = useCallback(() => {
        setSelectFilter();
        setSelectedFilter();
        setGetFromDate("");
        setGetToDate("");
        setAppliedFilters();
        setIsSet(false);
        setIsApplied(false);
        setSkip(defaultSkip);
    }, [
        setSelectFilter,
        setSelectedFilter,
        setGetFromDate,
        setGetToDate,
        setAppliedFilters,
        setIsSet,
        setIsApplied,
        setSkip,
    ]);

    const onNavigatingBack = useCallback(
        (reloadBatch = false) => {
            setBatchView(true);
            if (reloadBatch) {
                setSkip(defaultSkip);
                loadRewardLogistics({ limit, skip: defaultSkip }, searchText);
            }
        },
        [limit, searchText, loadRewardLogistics, setBatchView]
    );

    const reloadRewards = useCallback(async () => {
        const reloadedRewards = await loadRewards({ limit, skip: defaultSkip });
        setFilterValues(reloadedRewards);
    }, [limit, loadRewards, setFilterValues]);

    const reloadRewardLogistics = useCallback(async () => {
        setIsReloadingLogistics(true);
        setSelectedItems([]);
        if (searchText !== "") {
            await loadRewardLogistics({ limit, skip: defaultSkip }, searchText);
        } else {
            await loadRewardLogistics({ limit, skip: defaultSkip });
        }
        setIsReloadingLogistics(false);
        setSkip(defaultSkip);
    }, [
        limit,
        searchText,
        setIsReloadingLogistics,
        setSelectedItems,
        setSkip,
        loadRewardLogistics,
    ]);

    const reloadRedemptionLogs = useCallback(async () => {
        setIsReloadingRedemptions(true);
        if (appliedFilters) {
            await loadRedemptionLogs(
                { limit, skip: defaultSkip },
                appliedFilters
            );
        } else {
            await loadRedemptionLogs({ limit, skip: defaultSkip });
        }
        setIsReloadingRedemptions(false);
        setSkip(defaultSkip);
    }, [
        limit,
        appliedFilters,
        setIsReloadingRedemptions,
        loadRedemptionLogs,
        setSkip,
    ]);

    const resetAfterStartProcess = useCallback(async () => {
        await loadRedemptionLogs({ limit, skip: defaultSkip }, appliedFilters);
        resetFilter();
        if (showFilters) {
            toggleShowFilters();
        }
        setSelectedItems([]);
    }, [
        limit,
        appliedFilters,
        showFilters,
        toggleShowFilters,
        loadRedemptionLogs,
        resetFilter,
    ]);

    const onBatchProcessStatusChange = useCallback(
        (batchProcessObj) => {
            let batchProcessDetailsObj = {
                id: batchProcessObj._id,
                group: RewardGenerationJobGroups.BATCHES,
            };

            switch (batchProcessObj.selectAction) {
                case RewardRedemptionProcessStatus.DISPATCH:
                    batchProcessDetailsObj = {
                        ...batchProcessDetailsObj,
                        action: RewardRedemptionProcessStatus.DISPATCH,
                        status: RewardGenerationJobStatus.DISPATCHED,
                    };
                    break;
                case RewardRedemptionProcessStatus.COMPLETE:
                    batchProcessDetailsObj = {
                        ...batchProcessDetailsObj,
                        action: RewardRedemptionProcessStatus.COMPLETE,
                        status: RewardGenerationJobStatus.COMPLETED,
                    };
                    break;
                case RewardRedemptionProcessStatus.FAIL:
                    batchProcessDetailsObj = {
                        ...batchProcessDetailsObj,
                        action: RewardRedemptionProcessStatus.FAIL,
                        status: RewardGenerationJobStatus.FAILED,
                    };
                    break;
                default:
                    return null;
            }
            setBatchProcessDetails(batchProcessDetailsObj);
            setChangeBatchProcess(true);
        },
        [setBatchProcessDetails, setChangeBatchProcess]
    );

    const onExportBatch = useCallback(
        async (batch) => {
            const queryObj = {
                distributionJobId: batch.currentTarget.id,
            };
            batch.stopPropagation();
            setBatchRow(batch.currentTarget.id);
            try {
                setIsExporting(true);
                const url = await exportRedemptionLogs(queryObj);
                downloadLink(url.url);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to export batch!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setBatchRow("");
                setIsExporting(false);
            }
        },
        [setIsExporting, setBatchRow]
    );

    const onHideBatchProcessStatusChange = useCallback(() => {
        setBatchProcessDetails({});
        setChangeBatchProcess(false);
    }, [setBatchProcessDetails, setChangeBatchProcess]);

    const onShowMemberDetails = useCallback(
        (e) => {
            setMemberId(e.currentTarget.id);
            setViewMemberDetails(true);
        },
        [setMemberId, setViewMemberDetails]
    );

    const onHideMemberDetails = useCallback(() => {
        setMemberId("");
        setViewMemberDetails(false);
    }, [setMemberId, setViewMemberDetails]);

    useEffect(() => {
        if (regionId) {
            loadRewardLogistics({ limit, skip: defaultSkip });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [regionId]);

    useEffect(() => {
        if (
            selectedHeaderTab === RewardsHeaderTabs.REWARD_LOGISTICS &&
            selectedLogisticsTab === RewardGenerationJobStatus.PENDING
        ) {
            loadRedemptionLogs({ limit, skip: defaultSkip }, appliedFilters);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appliedFilters, selectedHeaderTab, selectedLogisticsTab]);

    useEffect(() => {
        if (
            regionId &&
            selectedLogisticsTab === RewardGenerationJobStatus.PENDING &&
            showFilters &&
            rewardNames.length === 0
        ) {
            loadRewards({ limit, skip: defaultSkip });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [regionId, selectedLogisticsTab, showFilters, rewardNames]);

    return (
        <BaseLayout
            bottom={
                <div className="mt-3 reward-logistics-tab-view">
                    {batchView ? (
                        <>
                            <RewardLogisticsTopPanel
                                selectedTab={selectedLogisticsTab}
                                isLoading={
                                    isLoadingLogistics || isLoadingRedemptions
                                }
                                searchText={searchText}
                                onSearch={onSearch}
                                isReloading={
                                    selectedLogisticsTab ===
                                    RewardGenerationJobStatus.PENDING
                                        ? isReloadingRedemptions
                                        : isReloadingLogistics
                                }
                                onReloadData={
                                    selectedLogisticsTab ===
                                    RewardGenerationJobStatus.PENDING
                                        ? reloadRedemptionLogs
                                        : reloadRewardLogistics
                                }
                                toggleShowFilters={onFilterBy}
                                selectedItems={selectedItems}
                                resetAfterStartProcess={resetAfterStartProcess}
                            />
                            <Filters
                                appliedFilters={appliedFilters}
                                showFilters={showFilters}
                                isLoading={
                                    isLoadingLogistics || isLoadingRedemptions
                                }
                                isLoadingRewards={isLoadingRewards}
                                isSet={isSet}
                                isApplied={isApplied}
                                selectFilter={selectFilter}
                                selectedFilter={selectedFilter}
                                filterValues={filterValues}
                                getFromDate={getFromDate}
                                getToDate={getToDate}
                                onSetFilterType={onSetFilterType}
                                onSetFilter={onSetFilter}
                                onChangeFromDate={onChangeFromDate}
                                onChangeToDate={onChangeToDate}
                                applyFilter={applyFilter}
                                removeFromDate={removeFromDate}
                                removeToDate={removeToDate}
                                resetFilter={resetFilter}
                                reloadRewards={reloadRewards}
                            />
                            <div>
                                <div className="w-100">
                                    <RewardLogisticsTable
                                        rewardDistributionList={
                                            rewardLogisticsBatchList
                                        }
                                        redemptionLogsList={redemptionLogsList}
                                        totalCount={
                                            selectedLogisticsTab ===
                                            RewardGenerationJobStatus.PENDING
                                                ? totalRedemptions
                                                : totalLogistics
                                        }
                                        isLoading={
                                            selectedLogisticsTab ===
                                            RewardGenerationJobStatus.PENDING
                                                ? isLoadingRedemptions
                                                : isLoadingLogistics ||
                                                    isLoadingRedemptions
                                        }
                                        rewardStatus={selectedLogisticsTab}
                                        group={
                                            selectedLogisticsTab ===
                                            RewardGenerationJobStatus.PENDING
                                                ? RewardGenerationJobGroups.INDIVIDUAL
                                                : RewardGenerationJobGroups.BATCHES
                                        }
                                        limit={limit}
                                        skip={skip}
                                        setLimit={setLimit}
                                        setSkip={setSkip}
                                        searchText={searchText}
                                        appliedFilters={appliedFilters}
                                        loadRewardLogistics={
                                            loadRewardLogistics
                                        }
                                        loadRedemptionLogs={loadRedemptionLogs}
                                        setSelectedBatchId={setSelectedBatchId}
                                        setSelectedBatch={setSelectedBatch}
                                        setBatchView={setBatchView}
                                        setSelectedItems={setSelectedItems}
                                        selectedItems={selectedItems}
                                        changeBatchProcess={changeBatchProcess}
                                        batchProcessDetails={
                                            batchProcessDetails
                                        }
                                        onBatchProcessStatusChange={
                                            onBatchProcessStatusChange
                                        }
                                        onHideBatchProcessStatusChange={
                                            onHideBatchProcessStatusChange
                                        }
                                        batchRow={batchRow}
                                        isExporting={isExporting}
                                        onExportBatch={onExportBatch}
                                        viewMemberDetails={viewMemberDetails}
                                        memberId={memberId}
                                        onShowMemberDetails={
                                            onShowMemberDetails
                                        }
                                        onHideMemberDetails={
                                            onHideMemberDetails
                                        }
                                        isAllowedUpdateBatchProcess={
                                            isAllowedUpdateBatchProcess
                                        }
                                    />
                                </div>
                            </div>
                        </>
                    ) : (
                        <RewardLogisticsProfile
                            batchId={selectedBatchId}
                            selectedBatch={selectedBatch}
                            isLoading={
                                isLoadingLogistics || isLoadingRedemptions
                            }
                            memberId={memberId}
                            isAllowedUpdateBatchProcess={
                                isAllowedUpdateBatchProcess
                            }
                            group={RewardGenerationJobGroups.INDIVIDUAL}
                            selectedTab={selectedLogisticsTab}
                            batchProcessDetails={batchProcessDetails}
                            viewMemberDetails={viewMemberDetails}
                            changeBatchProcess={changeBatchProcess}
                            isExporting={isExporting}
                            setMemberId={setMemberId}
                            loadRewardLogistics={loadRewardLogistics}
                            loadRedemptionLogs={loadRedemptionLogs}
                            onBatchProcessStatusChange={
                                onBatchProcessStatusChange
                            }
                            onHideBatchProcessStatusChange={
                                onHideBatchProcessStatusChange
                            }
                            onExportBatch={onExportBatch}
                            onShowMemberDetails={onShowMemberDetails}
                            onHideMemberDetails={onHideMemberDetails}
                            onNavigatingBack={onNavigatingBack}
                        />
                    )}
                </div>
            }
        />
    );
};

export default RewardLogisticsTabsView;
