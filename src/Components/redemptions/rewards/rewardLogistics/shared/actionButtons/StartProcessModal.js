import React, { useCallback, useContext, useState } from "react";
import { toast } from "react-toastify";
import { Button, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { createRewardDistributionJob } from "Services";

const StartProcessModal = ({
    show,
    onHide,
    selectedItems,
    resetAfterStartProcess,
}) => {
    const { regionId } = useContext(UserContext);
    const [isStarting, setIsStarting] = useState(false);

    const onStartProcess = useCallback(async () => {
        const distributionPayload = {
            regionId,
            redemptionLogIds: selectedItems,
        };
        try {
            setIsStarting(true);
            const createdRDJobRes = await createRewardDistributionJob(
                distributionPayload
            );
            onHide();
            toast.success(
                <div>
                    Successfully created a new reward distribution job.
                    {createdRDJobRes?.batchId ? (
                        <>
                            <br />
                            Batch Id: {createdRDJobRes?.batchId}
                        </>
                    ) : null}
                </div>
            );
            await resetAfterStartProcess();
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to create a reward distribution job!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsStarting(false);
        }
    }, [
        regionId,
        selectedItems,
        onHide,
        setIsStarting,
        resetAfterStartProcess,
    ]);

    return (
        <Modal
            show={show}
            onHide={isStarting ? () => {} : onHide}
            size="md"
            backdrop={isStarting ? "static" : true}
            centered
        >
            <Modal.Header closeButton={!isStarting}>
                <Modal.Title>Confirm Start Process</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                Do you wish to start the process with the selected{" "}
                {selectedItems.length === 1
                    ? "item?"
                    : `${selectedItems.length} items?`}
            </Modal.Body>
            <Modal.Footer>
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isStarting}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant="primary"
                    onClick={onStartProcess}
                    disabled={isStarting}
                >
                    {isStarting ? "Starting Process..." : "Confirm Start Process"}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default StartProcessModal;
