import React, { useState, useCallback, useMemo } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import {
    RewardGenerationJobGroups,
    RewardRedemptionProcessStatus,
    RewardRedemptionProcessStatusButtonColorCode,
} from "Data";
import {
    updateRewardDistributionLogs,
    updateRewardRedemptionLog,
} from "Services";
import { toTitleCase } from "Utils";

const ChangeProcess = ({
    show,
    limit,
    skip,
    processDetails,
    isNavigateBack,
    onHide,
    loadLogistics,
    navigateBack,
}) => {
    const [isChanging, setIsChanging] = useState(false);

    const processGroup = useMemo(
        () =>
            processDetails.group === RewardGenerationJobGroups.BATCHES
                ? "Batch"
                : "Redemption",
        [processDetails.group]
    );

    const processAction = useMemo(
        () => processDetails.action && toTitleCase(processDetails.action),
        [processDetails.action]
    );

    const onChangeProcess = useCallback(async () => {
        const id = processDetails?.id;
        let processObj = {};
        try {
            setIsChanging(true);
            if (processDetails?.group === RewardGenerationJobGroups.BATCHES) {
                processObj = {
                    status: processDetails?.status,
                };
                await updateRewardDistributionLogs(id, processObj);
                loadLogistics({ limit, skip });
                if (isNavigateBack) {
                    navigateBack();
                }
                onHide();
            } else if (
                processDetails?.group === RewardGenerationJobGroups.INDIVIDUAL
            ) {
                processObj = {
                    status: processDetails?.status,
                    processingStatus: processDetails?.processingStatus,
                };
                const updatedResponse = await updateRewardRedemptionLog(
                    id,
                    processObj
                );
                onHide(null, updatedResponse);
            }
            toast.success(
                `Successfully ${
                    processDetails.group === RewardGenerationJobGroups.BATCHES
                        ? toTitleCase(processDetails.status)
                        : toTitleCase(processDetails.processingStatus)
                } 
                the ${processGroup}.`
            );
            setIsChanging(false);
        } catch (error) {
            setIsChanging(false);
            console.error(error);
            toast.error(
                <div>
                    Failed to change the "{processGroup}" to "{processAction}"!
                    <br />
                    {error.message
                        ? `Error: ${error.message}`
                        : "Please try again later."}
                </div>
            );
        }
    }, [
        limit,
        skip,
        processDetails,
        onHide,
        loadLogistics,
        isNavigateBack,
        navigateBack,
        processGroup,
        processAction,
        setIsChanging,
    ]);

    return (
        <Modal
            show={show}
            onHide={isChanging ? () => {} : onHide}
            size="md"
            backdrop={true}
            centered
        >
            <Modal.Header closeButton={!isChanging}>
                <Modal.Title>
                    {" "}
                    {processAction} {processGroup}{" "}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>
                    Do you wish to {processAction} this {processGroup}?
                </p>
            </Modal.Body>
            <Modal.Footer className="mt-4">
                <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={onHide}
                    disabled={isChanging}
                >
                    Cancel
                </Button>
                <Button
                    size="sm"
                    variant={
                        processDetails?.action &&
                        RewardRedemptionProcessStatusButtonColorCode[
                            processDetails?.action
                        ].OUTLINE
                    }
                    onClick={onChangeProcess}
                    disabled={isChanging}
                >
                    {processDetails?.action ===
                    RewardRedemptionProcessStatus.DISPATCH
                        ? isChanging
                            ? "Dispatching..."
                            : `${processAction} ${processGroup}`
                        : processDetails?.action ===
                            RewardRedemptionProcessStatus.COMPLETE
                        ? isChanging
                            ? "Completing..."
                            : `${processAction} ${processGroup}`
                        : processDetails?.action ===
                            RewardRedemptionProcessStatus.FAIL
                        ? isChanging
                            ? "Failing..."
                            : `${processAction} ${processGroup}`
                        : null}
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

ChangeProcess.defaultProps = {
    show: false,
    processDetails: {},
    isNavigateBack: false,
    onHide: () => {},
    loadLogistics: () => {},
    navigateBack: () => {},
};

ChangeProcess.propTypes = {
    show: PropTypes.bool,
    limit: PropTypes.number.isRequired,
    skip: PropTypes.number.isRequired,
    processDetails: PropTypes.object,
    isNavigateBack: PropTypes.bool,
    onHide: PropTypes.func,
    loadLogistics: PropTypes.func,
    navigateBack: PropTypes.func,
};

export default ChangeProcess;
