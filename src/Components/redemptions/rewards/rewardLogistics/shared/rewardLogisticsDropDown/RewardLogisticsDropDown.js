import React from "react";
import { Form } from "@shoutout-labs/shoutout-themes-enterprise";
import PropTypes from "prop-types";

const RewardLogisticsDropDown = ({
    onChangeSelect,
    selectOptions,
    placeHolder,
    selectedValue,
    isLoading,
    loadingOptions,
    isApplied,
    appliedFilters,
}) => {
    return (
        <>
            {placeHolder ? (
                placeHolder[0].value === "REWARD_NAME" ? (
                    <Form.Select
                        id="basic-typeahead-single"
                        key="value"
                        labelKey="name"
                        onChange={onChangeSelect}
                        options={selectOptions}
                        placeholder={
                            loadingOptions || !selectOptions
                                ? "Loading..."
                                : selectOptions && selectOptions.length === 0
                                ? "No rewards found"
                                : "Select Reward"
                        }
                        selected={selectedValue}
                        disabled={
                            isLoading ||
                            loadingOptions ||
                            !selectOptions ||
                            (selectOptions && selectOptions.length === 0)
                        }
                    />
                ) : placeHolder[0].value === "LOCATION" ? (
                    <Form.Select
                        id="basic-typeahead-single"
                        key="_id"
                        labelKey="locationName"
                        onChange={onChangeSelect}
                        options={selectOptions}
                        placeholder={
                            selectOptions && selectOptions.length === 0
                                ? "No locations found"
                                : "Select Location"
                        }
                        selected={selectedValue}
                        disabled={
                            isLoading ||
                            loadingOptions ||
                            !selectOptions ||
                            (selectOptions && selectOptions.length === 0)
                        }
                    />
                ) : (
                    <Form.Select
                        id="basic-typeahead-single"
                        labelKey="name"
                        onChange={onChangeSelect}
                        options={selectOptions}
                        placeholder={
                            loadingOptions ? "Loading Filters..." : placeHolder
                        }
                        selected={selectedValue}
                        disabled={
                            isLoading ||
                            loadingOptions ||
                            isApplied ||
                            appliedFilters
                        }
                    />
                )
            ) : null}
        </>
    );
};

RewardLogisticsDropDown.propTypes = {
    onChangeSelect: PropTypes.func,
    selectOptions: PropTypes.array,
    placeHolder: PropTypes.any,
    selectedValue: PropTypes.array,
};

export default RewardLogisticsDropDown;
