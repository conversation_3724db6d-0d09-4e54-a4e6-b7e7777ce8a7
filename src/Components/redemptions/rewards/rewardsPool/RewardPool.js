import React, { useCallback, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    IcIcon,
    FormSearch,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import {
    RewardsHeaderTabs,
    AccessPermissionModules,
    AccessPermissionModuleNames,
} from "Data";
import { UserContext } from "Contexts";
import { getRewards } from "Services";
import BaseLayout from "Layout/BaseLayout";
import { RewardPoolContext } from "./context/RewardPoolContext";
import RewardPoolTable from "./rewardPoolTable/RewardPoolTable";
import CreateReward from "./createReward/CreateReward";

import "./RewardPool.scss";

const defaultLimit = 30,
    defaultSkip = 1;
let timeout;

const RewardPools = ({ tab, setTabIsLoading }) => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const { showTopUp, setTopUpRewardModal } = useContext(RewardPoolContext);
    const [isLoadingRewardPool, setIsLoadingRewardPool] = useState(false);
    const [isReloadingRewardPool, setIsReloadingRewardPool] = useState(false);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [rewards, setRewards] = useState([]);
    const [totalRewards, setTotalRewards] = useState(0);
    const [searchText, setSearchText] = useState("");
    const [rewardId, setRewardId] = useState("");

    const loadRewards = useCallback(
        async ({ limit, skip }, searchKey) => {
            let queryObj = {
                limit: limit,
                skip: (skip - 1) * limit,
                regionId: selectedRegion._id,
            };
            try {
                setTabIsLoading(true);
                setIsLoadingRewardPool(true);

                if (searchKey) {
                    queryObj = { ...queryObj, searchKey };
                }

                const rewardResponse = await getRewards(queryObj);
                setRewards(rewardResponse.items);
                setTotalRewards(rewardResponse.total);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to load reward pool!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            } finally {
                setTabIsLoading(false);
                setIsLoadingRewardPool(false);
            }
        },
        [selectedRegion._id, setTabIsLoading, setIsLoadingRewardPool]
    );

    const onSearch = useCallback(
        (search) => {
            if (timeout) {
                clearTimeout(timeout);
            }
            setSearchText(search);
            timeout = setTimeout(async () => {
                await loadRewards({ limit, skip: defaultSkip }, search);
                setSkip(defaultSkip);
            }, 2000);
        },
        [limit, setSearchText, setSkip, loadRewards]
    );

    const reloadRewards = useCallback(async () => {
        setIsReloadingRewardPool(true);
        await loadRewards({ limit, skip: defaultSkip }, searchText);
        setIsReloadingRewardPool(false);
        setSkip(defaultSkip);
    }, [limit, searchText, setIsReloadingRewardPool, loadRewards, setSkip]);

    const onTopUpReward = useCallback(
        (e) => {
            e.stopPropagation();
            setRewardId(e.currentTarget.id);
            setTopUpRewardModal(true);
        },
        [setRewardId, setTopUpRewardModal]
    );

    useEffect(() => {
        if (tab === RewardsHeaderTabs.REWARD_POOL) {
            loadRewards({ limit, skip: defaultSkip }, searchText);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tab]);

    return (
        <div className="rewards-pool-view">
            <BaseLayout
                bottom={
                    <div className="w-100 mt-5">
                        <div className=" d-flex flex-row justify-content-between mb-4">
                            <div className="d-flex flex-row align-items-center">
                                <div className="custom-width">
                                    <FormSearch
                                        disabled={
                                            isLoadingRewardPool ||
                                            isReloadingRewardPool
                                        }
                                        placeholder="Search by reward name..."
                                        selected={searchText}
                                        onChange={onSearch}
                                        id="search-groups"
                                    />
                                </div>
                                <Button
                                    className="btn shadow-none"
                                    variant="link"
                                    size="sm"
                                    disabled={
                                        isLoadingRewardPool ||
                                        isReloadingRewardPool
                                    }
                                    onClick={reloadRewards}
                                >
                                    {!isReloadingRewardPool && (
                                        <div className="d-flex align-items-center">
                                            <IcIcon
                                                size="md"
                                                className="mr-2"
                                                icon={faSync}
                                            />
                                            Reload Rewards
                                        </div>
                                    )}
                                </Button>
                                <span style={{ fontSize: "0.9rem" }}>
                                    {isReloadingRewardPool && (
                                        <div className="text-primary">
                                            Reloading...
                                        </div>
                                    )}
                                </span>
                            </div>
                            <div>
                                {isAuthorizedForAction(
                                    AccessPermissionModuleNames.REWARD,
                                    AccessPermissionModules[
                                        AccessPermissionModuleNames.REWARD
                                    ].actions.CreateReward
                                ) && (
                                    <CreateReward
                                        isLoading={
                                            isLoadingRewardPool ||
                                            isReloadingRewardPool
                                        }
                                        limit={limit}
                                        skip={skip}
                                        searchText={searchText}
                                        setSearchText={setSearchText}
                                        loadRewards={loadRewards}
                                    />
                                )}
                            </div>
                        </div>
                        <div className="mt-4">
                            <div className="w-100">
                                <RewardPoolTable
                                    regionId={selectedRegion._id}
                                    rewardList={rewards}
                                    totalCount={totalRewards}
                                    isLoading={isLoadingRewardPool}
                                    limit={limit}
                                    skip={skip}
                                    searchText={searchText}
                                    setLimit={setLimit}
                                    setSkip={setSkip}
                                    loadRewards={loadRewards}
                                    onTopUpReward={onTopUpReward}
                                    showTopUp={showTopUp}
                                    setTopUpRewardModal={setTopUpRewardModal}
                                    rewardId={rewardId}
                                    setRewardId={setRewardId}
                                />
                            </div>
                        </div>
                    </div>
                }
            />
        </div>
    );
};

RewardPools.defaultProps = { tab: "", setTabIsLoading: () => {} };

RewardPools.propTypes = {
    tab: PropTypes.string,
    setTabIsLoading: PropTypes.func,
};

export default RewardPools;
