import React, { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { Button, Modal } from '@shoutout-labs/shoutout-themes-enterprise';
import { updateRewardById } from 'Services';
import { toTitleCase } from 'Utils';

const EditPortalVisibility = ({ show, onHide, updatedPortalVisibility, rewardId }) => {
    const [isSaving, setIsSaving] = useState(false);
    const [portalVisibility, setPorttalVisibility] = useState("");

    const onClickStatus = useCallback(async () => {
        try {
            const updatedPortalPayload = { portalVisibility : portalVisibility };

            setIsSaving(true);
            const updatedReward = await updateRewardById(rewardId, updatedPortalPayload);
            onHide(null, updatedReward)
            toast.success(`Successfully updated portal visibility to ${toTitleCase(portalVisibility)}.`);
            setIsSaving(false);
            onHide(null, updatedReward);
        } catch(e) {
            setIsSaving(false);
            console.error(e);
            toast.error(e.message || `Failed to update portal visibility to ${toTitleCase(portalVisibility)}! Please try again.`);
        }
    },[rewardId, portalVisibility, onHide, setIsSaving]);

    useEffect(() => {
        if(updatedPortalVisibility) {
            setPorttalVisibility(updatedPortalVisibility);
        }
    }, [updatedPortalVisibility]);

    return(
        <Modal show={show} onHide={isSaving ? () => {} : onHide} size="md" backdrop={true} centered>
            <Modal.Header closeButton={!isSaving}>
                <Modal.Title>Update Portal Visibility</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>Do you want update this reward's portal visibility to {`"${toTitleCase(portalVisibility)}"`} ?</p>
            </Modal.Body>
            <Modal.Footer>
                <Button size="sm" variant="outline-primary" onClick={onHide} disabled={isSaving}>Cancel</Button>
                <Button size="sm" variant="primary" onClick={onClickStatus} disabled={isSaving}>{isSaving ? "Updating..." : "Update"}</Button>
            </Modal.Footer>
        </Modal>
    );
}

export default EditPortalVisibility