import React, { useCallback, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import { Button, IcIcon, FileUploader, Form, Image, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { faEye, faEyeSlash } from "FaICIconMap";
import { useToggle } from "Hooks";
import { updateRewardById, uploadImage } from "Services";
import { isEmptyObject } from "Utils";
import { LoadingComponent } from "Components/utils/UtilComponents";
import RemoveImageConfirmation from "Components/common/removeImageConfirmation/RemoveImageConfirmation";

import "./EditViewRewardImage.scss";

const EditViewRewardImage = ({ show, onHide, currentDetails, rewardId }) => {
    const [validated, setValidated] = useState(false);
    const [isEdit, setIsEdit] = useToggle(false);
    const [isAdd, setIsAdd] = useState(false);
    const [removeRewardImage, toggleRemoveImage] = useToggle(false);
    const [isRemoving, setIsRemoving] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [isLoadingImg, setIsLoadingImg] = useState(false);
    const [rewardImage, setRewardImage] = useState("");
    const [previewImg, setPreviewImg] = useToggle(false);

    const onChangeRewardImage = useCallback(async imageUrl => {
        if(imageUrl[0]) {
            try {
                setIsLoadingImg(true);
                const imageIntoUrl = await uploadImage(imageUrl[0]);
                setRewardImage(imageIntoUrl.url);
            } catch (e) {
                toast.error(e.message || "Could not upload the image! Please try again.")
            } finally {
                setIsLoadingImg(false);
            }
        }
        else {
            setRewardImage(currentDetails?.rewardImageUrl);
        }
    }, [currentDetails.rewardImageUrl]);

    const onCancelEdit = useCallback(() => {
        setRewardImage(currentDetails?.rewardImageUrl);
        setIsEdit();
    }, [currentDetails?.rewardImageUrl, setRewardImage, setIsEdit]);

    const onSubmit = useCallback(async e => {
        e.preventDefault();
        if (e.target.checkValidity()) {
            try {
                const rewardImagePayload = { imageUrls: [rewardImage] };

                setIsUpdating(true);
                const updatedReward = await updateRewardById(rewardId, rewardImagePayload);
                toast.success(`Successfully ${!currentDetails?.rewardImageUrl ? "added" : "updated"} reward image.`);
                onHide(null, updatedReward)                
            } catch (e) {
                toast.error(e.message || `Could not ${!currentDetails?.rewardImageUrl ? "add" : "update"} reward image! Please try again.`);
            } finally {
                setIsUpdating(false);
            }
        } else {
            setValidated(true);
        }
    }, [rewardId, currentDetails?.rewardImageUrl, rewardImage, onHide, setIsUpdating, setValidated]);

    const onRemoveImage = useCallback(async () => {
        const removeImagePayload = { imageUrls : [""] };

        try {
            setIsRemoving(true);
            const updatedReward = await updateRewardById(rewardId, removeImagePayload);
            toast.success("Successfully removed reward image.");
            onHide(null, updatedReward)                
        } catch (e) {
            toast.error(e.message || "Could not remove reward image! Please try again.");
        } finally {
            setIsRemoving(false);
        }
    }, [rewardId, onHide]);

    useEffect(() => {
        if (!isEmptyObject(currentDetails)) {
            setRewardImage(currentDetails.rewardImageUrl);
            if(!currentDetails.rewardImageUrl) {
                setIsAdd(true);
            }
        }
    }, [currentDetails]);

    return (
        <Modal 
            className="edit-reward-img-view" 
            show={show} 
            onHide={isUpdating ? () => {} : onHide} 
            size="lg" 
            centered
        >
            <Modal.Header closeButton={!isUpdating}>
                <Modal.Title>
                    {currentDetails?.rewardImageUrl ?
                        <>{isEdit ? "Edit" : "View"} Reward Image</> 
                        :
                        "Add Reward Image"
                    }
                </Modal.Title>
            </Modal.Header>
            <Form onSubmit={onSubmit} validated={validated} noValidate>
                <Modal.Body>
                    {isLoadingImg ? 
                        <LoadingComponent />
                        :
                        <>
                            {((!isEdit && !isAdd) || previewImg) && 
                                <>
                                   {previewImg && <Form.Label>Preview</Form.Label>}
                                    <div className="edit-reward-img-container">
                                        {rewardImage ? 
                                            <Image 
                                                className="reward-img-preview"
                                                src={rewardImage} 
                                                alt={rewardImage}
                                            /> : 
                                            <div className="mt-3 reward-img-not-found">
                                                <p>No Image Found</p>
                                            </div>
                                        }
                                    </div>
                                </>
                            }
                            {((isEdit && rewardImage !== currentDetails.rewardImageUrl) || (isAdd && rewardImage)) &&
                                <div className="mt-3 text-center"> 
                                    <Button size="sm" variant="link" onClick={setPreviewImg}>
                                        {previewImg ? 
                                            <div className="d-flex align-items-center">
                                                <IcIcon icon={faEyeSlash} className="mr-2" size="lg" />
                                                Hide Preview
                                            </div>
                                            : 
                                            <div className="d-flex align-items-center">
                                                <IcIcon icon={faEye} className="mr-2" size="lg" />
                                                Preview Image
                                            </div>
                                        }
                                    </Button>
                                </div>
                            }
                        </>
                    }
                    {currentDetails.rewardImageUrl ? 
                        <>
                            {isEdit &&                   
                                <Form.Group className="mt-3" controlId="view-edit-reward-image">
                                    <FileUploader
                                        onChange={onChangeRewardImage}
                                        multiple={false}
                                        defaultSrc={rewardImage}
                                        accept="image/png, image/jpeg"
                                    />
                                </Form.Group>
                            }
                        </>
                        :
                        <>                  
                            <Form.Group className="mt-3" controlId="add-reward-image">
                                <FileUploader
                                    onChange={onChangeRewardImage}
                                    multiple={false}
                                    defaultSrc={rewardImage}
                                    accept="image/png, image/jpeg"
                                />
                            </Form.Group>
                        </>
                    } 
                </Modal.Body>
                <Modal.Footer className={!isEdit && "mt-3"}>
                    {currentDetails.rewardImageUrl ? 
                        <>
                            {isEdit ? 
                                <>
                                    <Button size="sm" variant="outline-danger" onClick={onCancelEdit} type="button" disabled={isUpdating}>
                                        Cancel Edit
                                    </Button>
                                    <Button 
                                        size="sm"
                                        variant="primary" 
                                        type="submit"
                                        disabled={isUpdating || !rewardImage || rewardImage === currentDetails.rewardImageUrl}
                                    >
                                        {isUpdating ? "Updating..." : "Update"}
                                    </Button>
                                </>
                                :
                                <>
                                    <Button size="sm" variant="outline-primary" onClick={onHide} type="button" disabled={isUpdating}>
                                        Close
                                    </Button>
                                    <Button size="sm" variant="outline-danger" onClick={toggleRemoveImage} type="button" disabled={isUpdating}>
                                        Remove Image
                                    </Button>
                                    <Button size="sm" variant="primary" onClick={setIsEdit} disabled={isUpdating}>
                                        Change Image
                                    </Button>
                                </>
                            }
                        </> 
                        :
                        <>
                            <Button size="sm" variant="outline-primary" onClick={onHide} type="button" disabled={isUpdating}>
                                Cancel
                            </Button>
                            <Button 
                                size="sm"
                                variant="primary" 
                                type="submit"
                                disabled={isUpdating || !rewardImage}
                            >
                                {isUpdating ? "Adding..." : "Add Image"}
                            </Button>
                        </>
                    }
                </Modal.Footer>
            </Form>
            
            {removeRewardImage && 
                <RemoveImageConfirmation
                    title="Reward Image"
                    message="Are you sure you want to remove the reward image?"
                    show={removeRewardImage} 
                    onHide={toggleRemoveImage} 
                    isRemoving={isRemoving} 
                    onRemoveImage={onRemoveImage}
                />
            }
        </Modal>
      );
};

EditViewRewardImage.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,
    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,
    /**
     * Current known data
     */
    currentDetails: PropTypes.object,
    /**
     * Reward Id
     */
    rewardId: PropTypes.string.isRequired,
};

export default EditViewRewardImage;