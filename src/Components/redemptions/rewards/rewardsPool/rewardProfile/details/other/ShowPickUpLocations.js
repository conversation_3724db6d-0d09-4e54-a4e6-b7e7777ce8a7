import React from "react";
import PropTypes from "prop-types";
import { Button, Modal } from "@shoutout-labs/shoutout-themes-enterprise";

const ShowPickUpLocations = ({ show, onHide, currentLocations }) => {
  
  return (
      <Modal show={show} onHide={onHide} centered>
        <Modal.Header closeButton>
          <Modal.Title>Pick Up Locations</Modal.Title>
        </Modal.Header>
          <Modal.Body> 
              {currentLocations.map((location, index) => {
                if(Object.keys(location).length !== 0) {
                  return <li key={index}> {location} </li>;
                } 
                else {
                  return (
                    <li key={index} className="py-1 mb-0 text-danger">
                      This location has been deleted! 
                    </li>
                  );
                }
              })}
          </Modal.Body>
          <Modal.Footer>
              <Button size="sm" variant="outline-primary" onClick={onHide}> Close </Button>
          </Modal.Footer>
      </Modal>
    );
};

ShowPickUpLocations.propTypes = {
    /**
     * Show edit view
     */
    show: PropTypes.bool.isRequired,
    /**
     * Callback on close
     */
    onHide: PropTypes.func.isRequired,
    /**
     * Current known data
     */
    currentLocations: PropTypes.object
};

export default ShowPickUpLocations;