import React from "react";
import { Row, Col } from "@shoutout-labs/shoutout-themes-enterprise";
import RewardStatsView from "./rewardStatsView/RewardStatsView";

const RewardStatistics =  ({ isLoading, reloadAfterTopUp, rewardStats }) => {

  return (
    <div className="my-4">
        <Row>
            <Col>
            <RewardStatsView
                rewardStatistic="Total Rewards"
                loadingStats={isLoading || reloadAfterTopUp}
                rewardStatQuantity={rewardStats.totalRewards}
                rewardStatColor="#25BF49"
            />
            </Col>
            <Col>
            <RewardStatsView
                rewardStatistic="Redeemed Rewards"
                loadingStats={isLoading || reloadAfterTopUp}
                rewardStatQuantity={rewardStats.redeemedRewards}
                rewardStatColor="#0069A6"
            />
            </Col>
            <Col>
            <RewardStatsView
                rewardStatistic="Claimed Rewards"
                loadingStats={isLoading || reloadAfterTopUp}
                rewardStatQuantity={rewardStats.claimedRewards}
                rewardStatColor="#42B3F5"
            />
            </Col>
            <Col>
            <RewardStatsView
                rewardStatistic="Remaining Rewards"
                loadingStats={isLoading || reloadAfterTopUp}
                rewardStatQuantity={rewardStats.remainingRewards}
                rewardStatColor="#F47920"
            />
            </Col>
        </Row>
    </div>
  );
};

export default RewardStatistics;
