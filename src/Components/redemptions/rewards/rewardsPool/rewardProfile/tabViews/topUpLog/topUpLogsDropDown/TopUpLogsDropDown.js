import React from 'react';
import { Form } from '@shoutout-labs/shoutout-themes-enterprise';
import PropTypes from 'prop-types';

const TopUpLogsDropDown = ({ onChangeSelect, selectOptions, placeHolder, selectedValue, isLoading }) => {
    return (
        <Form.Select
            id="basic-typeahead-single"
            labelKey="value"
            onChange={onChangeSelect}
            options={selectOptions}
            placeholder={placeHolder}
            selected={selectedValue}
            disabled={isLoading || !selectOptions}
        />
    );
};

TopUpLogsDropDown.propTypes = {
    onChangeSelect : PropTypes.func, 
    selectOptions : PropTypes.array, 
    placeHolder : PropTypes.string,
    selectedValue : PropTypes.array
};

export default TopUpLogsDropDown;