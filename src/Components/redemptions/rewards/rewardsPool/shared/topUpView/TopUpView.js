import React, { useContext, useMemo } from "react";
import { Button, IcIcon, FileUploader, Form, FormCheck } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { TopUpMethod, AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import { faExclamationCircle } from "FaICIconMap";
import { objectToKeyValueArray } from "../../../util/RewardUtility";
import CouponCodeInput from "../couponCodeInput/CouponCodeInput";

import "../../RewardPool.scss";

const TopUpView = ({
    toppingUp,
    topUpOption,
    quantity,
    fileInfo,
    onSetFile,
    couponCodes,
    addCouponCode,
    removeCouponCode,
    setCouponCode,
    onChangeHandler,
    onChangeTopUpOption,
}) => {
    const { isAuthorizedForAction } = useContext(UserContext);

    const TopUpOptions = useMemo(() => {
        if (
            !isAuthorizedForAction(
                AccessPermissionModuleNames.REWARD,
                AccessPermissionModules[AccessPermissionModuleNames.REWARD].actions.UploadRewardTopupFile
            )
        ) {
            const { UPLOAD, ...rest } = TopUpMethod;
            return objectToKeyValueArray(rest, true);
        }
        return objectToKeyValueArray(TopUpMethod, true);
    }, [isAuthorizedForAction]);

    return (
        <div className="rewards-pool-view mt-4">
            <Form onSubmit={(e) => e.preventDefault()}>
                <Form.Group>
                    <Form.Label>Top Up Options</Form.Label>
                    <Form.Group className="input-group mt-3">
                        {TopUpOptions.map((option) => (
                            <FormCheck
                                className="rounded-0 input-check mr-3"
                                custom
                                checked={topUpOption === option.key}
                                onChange={onChangeTopUpOption}
                                value={option.key}
                                id={option.key}
                                label={option.value}
                                name="topUpOption"
                                type="radio"
                                required
                                disabled={toppingUp}
                            />
                        ))}
                    </Form.Group>
                </Form.Group>
                <Form.Group>
                    {topUpOption === TopUpMethod.GENERATED.methodValue && (
                        <div>
                            <Form.Label>Quantity</Form.Label>
                            <Form.Control
                                type="number"
                                name="quantity"
                                value={quantity}
                                onChange={onChangeHandler}
                                required
                                disabled={toppingUp}
                            />
                        </div>
                    )}
                    {topUpOption === TopUpMethod.UPLOAD.methodValue && (
                        <div>
                            <Form.Label>Upload File</Form.Label>
                            <FileUploader
                                accept="text/csv, application/csv, text/comma-separated-values, .csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel,.xls,.xlsx"
                                onChange={onSetFile}
                                defaultSrc={fileInfo}
                                multiple={false}
                                disabled={toppingUp}
                            />
                            <div className="text-orange coupon-warning mt-3">
                                <p className="text-primary ml-1">Please check your CSV file meets these requirements.</p>
                                <p className="d-flex align-items-center my-1">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faExclamationCircle}
                                    />
                                    File extension must be <span className="text-secondary mx-1">.csv</span>{`(eg: filename.csv).`}
                                </p>
                                <p className="d-flex align-items-center my-1">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faExclamationCircle}
                                    />
                                    Must be less than <span className="text-secondary mx-1"> 25MB</span> or <span className="text-secondary mx-1">400,000</span>rows. Split larger files into multiple files.
                                </p>
                            </div>
                        </div>
                    )}
                    {topUpOption === TopUpMethod.MANUAL.methodValue && (
                        <div>
                            <Form.Label>Coupon Codes</Form.Label>
                            <div className="text-orange coupon-warning">
                                <p className="d-flex align-items-center my-1">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faExclamationCircle}
                                    />
                                    Each code should be separated by a new line.
                                </p>
                                <p className="d-flex align-items-center my-1">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faExclamationCircle}
                                    />
                                    Each code must only contain alpha-numeric
                                    and underscore characters.
                                </p>
                                <p className="d-flex align-items-center">
                                    <IcIcon
                                        className="mr-2"
                                        size="lg"
                                        icon={faExclamationCircle}
                                    />
                                    Each code must be 4-10 characters long.
                                </p>
                            </div>
                            <Form.Group>
                                {couponCodes.map((item, index) => (
                                    <CouponCodeInput
                                        toppingUp={toppingUp}
                                        key={index}
                                        index={index}
                                        item={item}
                                        removeCouponCode={removeCouponCode}
                                        setCouponCode={setCouponCode}
                                        couponCodesLength={couponCodes.length}
                                    />
                                ))}
                                <div className="mt-3 text-left">
                                    <Button
                                        type="button"
                                        size="sm"
                                        variant="link"
                                        disabled={toppingUp}
                                        onClick={addCouponCode}
                                    >
                                        + Add Coupon Code
                                    </Button>
                                </div>
                            </Form.Group>
                        </div>
                    )}
                </Form.Group>
            </Form>
        </div>
    );
};

export default TopUpView;
