import React, { useState, useCallback, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Button,
    IcIcon,
    Modal,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faSync } from "FaICIconMap";
import { getMemberById } from "Services";

import "./ViewMember.scss";

const ViewMember = ({ show, onHide, memberId }) => {
    const [isLoadingMember, setIsLoadingMember] = useState(false);
    const [isReloading, setIsReloading] = useState(false);
    const [member, setMember] = useState({});
    const history = useHistory();

    const loadMemberProfile = useCallback(async () => {
        try {
            setIsLoadingMember(true);
            const memberProfileResponse = await getMemberById(memberId);
            setMember(memberProfileResponse);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to load member details!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
        } finally {
            setIsLoadingMember(false);
        }
    }, [memberId, setMember, setIsLoadingMember]);

    const reloadMember = useCallback(async () => {
        setIsReloading(true);
        await loadMemberProfile();
        setIsReloading(false);
    }, [loadMemberProfile, setIsReloading]);

    const navigateToMemberProfile = useCallback(
        () => history.push(`/members/${memberId}`),
        [history, memberId]
    );

    useEffect(() => {
        if (memberId) {
            loadMemberProfile();
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [memberId]);

    return (
        <Modal
            show={show}
            onHide={isLoadingMember ? () => {} : onHide}
            size="md"
            centered
            className="member-details-modal"
        >
            <Modal.Header closeButton={!isLoadingMember}>
                <Modal.Title>Member Details</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className="d-flex flex-row justify-content-between mt-4">
                    <div className="d-flex flex-column w-100 mr-3">
                        <div>
                            <p className="font-weight-bold pb-0 mb-0">Name</p>
                        </div>
                        <div
                            className={`detail-col p-2 border rounded border-light ${
                                isLoadingMember && "loading-member"
                            }`}
                        >
                            <p className="pb-0 mb-0">
                                {isLoadingMember
                                    ? "Loading..."
                                    : `${member?.firstName || ""} ${
                                        member?.lastName || ""
                                    }` || "~ unknown"}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="d-flex flex-row justify-content-between mt-4">
                    <div className="d-flex flex-column w-100 mr-3">
                        <div>
                            <p className="font-weight-bold pb-0 mb-0">
                                Loyalty Card No.
                            </p>
                        </div>
                        <div
                            className={`detail-col p-2 border rounded border-light ${
                                isLoadingMember && "loading-member"
                            }`}
                        >
                            <p className="pb-0 mb-0">
                                {isLoadingMember
                                    ? "Loading..."
                                    : member?.cardNumber ||
                                    "~ card number not found"}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="d-flex flex-row justify-content-between mt-4">
                    <div className="d-flex flex-column w-100 mr-3">
                        <div>
                            <p className="font-weight-bold pb-0 mb-0">
                                Contact Number
                            </p>
                        </div>
                        <div
                            className={`detail-col p-2 border rounded border-light ${
                                isLoadingMember && "loading-member"
                            }`}
                        >
                            <p className="pb-0 mb-0">
                                {isLoadingMember
                                    ? "Loading..."
                                    : member?.mobileNumber || "~ unknown"}
                            </p>
                        </div>
                    </div>
                </div>
            </Modal.Body>
            <Modal.Footer className="d-flex flex-row justify-content-between mt-4">
                <Button
                    className="btn shadow-none"
                    variant="link"
                    size="sm"
                    disabled={isLoadingMember || isReloading}
                    onClick={reloadMember}
                >
                    <div className="d-flex align-items-center">
                        <IcIcon size="md" className="mr-2" icon={faSync} />
                        Reload Member
                    </div>
                </Button>
                <div>
                    <Button
                        size="sm"
                        className="mr-2"
                        variant="outline-primary"
                        onClick={onHide}
                        disabled={isLoadingMember}
                    >
                        Close
                    </Button>
                    <Button
                        size="sm"
                        variant="primary"
                        onClick={navigateToMemberProfile}
                        disabled={isLoadingMember}
                    >
                        View Member Profile
                    </Button>
                </div>
            </Modal.Footer>
        </Modal>
    );
};

ViewMember.propTypes = {
    show: PropTypes.func,
    onHide: PropTypes.func,
    memberId: PropTypes.string,
};

export default ViewMember;
