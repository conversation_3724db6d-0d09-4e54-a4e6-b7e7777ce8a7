.create-segment-wizard-second-page{
  .query-builder-container {
    margin-left: 2%;
    width: 95% !important;
    .query-builder {
      margin: 0;

      .group {
        background-color: transparent;
        border: none;
      }

      .input {
        height: 2rem !important;
      }
    }

    .select-typeahead .dropdown-menu .dropdown-item {
      word-wrap: break-word;
      /* IE 5.5-7 */
      white-space: -moz-pre-wrap;
      /* Firefox 1.0-2.0 */
      white-space: pre-wrap;
      /* current browsers */
    }

    .rule--field {
      width: 30% !important;
    }

    .rule--operator {
      width: 5rem !important;
    }
    .rule--value {
      width: 40% !important;
    }
  }
  .delete-cross{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    padding-top: 0.4%;
  }
  .delete-cross:hover{
    background-color: #00ADF0;
    color: #ffffff;
  }
  .delete-cross:active{
    background-color: #DD3444;
    color: #ffffff;
  }
  .segment-query-card{
    max-height: 100%;
  }
  .segment-query-card:hover{
    cursor: pointer;
  }
  .active-card{
    border: 1px solid #003D61;
  }
  .form-is-valid{
    .css-13cymwt-control{
      border: 1px solid #DFE5EF;
    }
  }
  .form-is-invalid{
    .css-13cymwt-control{
      border: 1px solid var(--bs-danger);
    }
  }
  .active-card-invalid{
    border: 1px solid var(--bs-danger);
  }
}


