import React, { useCallback, useContext, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON> } from "@shoutout-labs/shoutout-themes-enterprise";
import CreateSegmentWizardFirstPage from "./CreateSegmentWizardFirstPage";
import CreateSegmentWizardSecondPage from "./CreateSegmentWizardSecondPage";
import { SegmentationContext } from "../context/SegmentationsContext";
import CreateSegmentCategory from "./CreateSegmentCategory";


const CreateSegmentWizard = () => {
    const {
        showCreateSegmentWizard,
        setShowCreateSegmentWizard,
        isCreatingNewSegment,
        showCreateNewSegmentCategoryView,
        resetSegmentStatus
    } = useContext(SegmentationContext)||{};

    const handleClose = useCallback( () => {
        setShowCreateSegmentWizard?.(false);
        resetSegmentStatus?.();
    }, [setShowCreateSegmentWizard,resetSegmentStatus]);


    return useMemo(
        () => <CreateSegmentWizardView
            handleClose={handleClose}
            show={showCreateSegmentWizard}
            isCreatingNewSegment={isCreatingNewSegment}
            showCreateNewSegmentCategoryView={showCreateNewSegmentCategoryView}
        />,
        [handleClose, showCreateSegmentWizard,isCreatingNewSegment,showCreateNewSegmentCategoryView]
    );
};


const CreateSegmentWizardView=( {
                                    show,
                                    handleClose,
                                    isCreatingNewSegment,
                                    showCreateNewSegmentCategoryView
                                })=>{
    return(
        <Modal
            show={!!show}
            className="create-segment-wizard-view border-0"
            size="lg"
            centered
            backdrop="static"
            onHide={isCreatingNewSegment ?
                () => {}
                :
                handleClose
            }
        >
            <Modal.Header closeButton={true}>
                <Modal.Title>
                    {showCreateNewSegmentCategoryView? "Create New Segment Category" : "Create Segment" }
                </Modal.Title>
            </Modal.Header>
            {showCreateNewSegmentCategoryView? <CreateSegmentCategory/> : <Modal.Body className="pt-3 mt-2 px-2">
                <Wizard
                    validate
                    finishButtonClick={() => {}}
                    color="primary"
                    heightClass="point-wizard-height"
                    disabled={isCreatingNewSegment}
                    reset={() => {}}
                    finishButtonText={isCreatingNewSegment ? "Creating..." : "Create Segment"}
                    steps={[
                        {
                            stepName: "Basic information",
                            component: CreateSegmentWizardFirstPage,
                            showNextBtn: true,
                        },
                        {
                            stepName: "Conditions",
                            component: CreateSegmentWizardSecondPage,
                            showNextBtn: true,
                        }
                    ]}
                />
            </Modal.Body> }
        </Modal>
    )
}
export { CreateSegmentWizard};
