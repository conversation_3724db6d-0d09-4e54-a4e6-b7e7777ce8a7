import React, { useCallback, useContext, useEffect, useState } from "react";
import { faShoppingCart, faUser } from "../../../FaICIconMap";
import { Utils as QbUtils } from "@shoutout-labs/react-awesome-query-builder-shoutout";
import { MembersContext } from "../../../Contexts";
import { getMongoDBQuery, isEmptyObject, isEqualObjects } from "../../../Utils";
import QueryBuilderSection from "../shared/queryBuilderSection/QueryBuilderSection";
import { Button } from "@shoutout-labs/shoutout-themes-enterprise";
import { toast } from "react-toastify";
import { updateSegment } from "../../../Services";
import { useParams } from "react-router-dom";
import { LoadingComponent } from "../../utils";
import "./SegmentConditions.scss"
const queryValue = { id: QbUtils.uuid(), type: "group" };


const SegmentConditions = () => {
    const { id } = useParams();
    const {
        filterConfig = {},
        transactionFilterConfig = {},
        currentFilters = {},
        currentTransactionFilters = {},
        setFilterConfig,
        setTransactionFilterConfig,
        updateSegments,
        isLoading,
    } = useContext(MembersContext) || {};


    const [isUpdatingSegment, setIsUpdatingSegment] = useState(false);
    const [isCustomerAttributesSelected, setIsCustomerAttributesSelected] = useState(false);
    const [isPurchaseAttributesSelected, setIsPurchaseAttributesSelected] = useState(false);


    const [customerAttributesData, setCustomerAttributesData] = useState(
        QbUtils.checkTree(QbUtils.loadTree(queryValue), filterConfig),
    );

    const [purchaseAttributesData, setPurchaseAttributesData] = useState(
        QbUtils.checkTree(QbUtils.loadTree(queryValue), transactionFilterConfig),
    );

    const onChangeCustomerAttributes = useCallback((immutableTree, config) => {
            setCustomerAttributesData(immutableTree);
            setFilterConfig(config);
        },
        [setFilterConfig, setCustomerAttributesData],
    );

    const isEqualFilter = useCallback((currentFilters, updatedFilters, filterConfig) => {

            if (isEmptyObject(currentFilters) || isEmptyObject(updatedFilters)) {
                return false;
            }
            return isEqualObjects(getMongoDBQuery(
                    QbUtils.getTree(updatedFilters || {}), filterConfig)
                , getMongoDBQuery(currentFilters || {}, filterConfig),
            );
        },
        [],
    );

    const onClickEditRule = useCallback(async (e) => {
            e.stopPropagation();
            try {
                setIsUpdatingSegment(true);
                const payload = {
                    ...(isCustomerAttributesSelected ? { memberFilter: QbUtils.getTree(customerAttributesData) } : {memberFilter:{}}),
                    ...(isPurchaseAttributesSelected ? { transactionFilter: QbUtils.getTree(purchaseAttributesData) } : {transactionFilter:{}}),
                };
                await updateSegment(id, payload);
                await updateSegments();
                toast.success("Segment updated successfully");
                setIsUpdatingSegment(false);
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to update segment
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>,
                );
            }
        },
        [
            purchaseAttributesData,
            customerAttributesData,
            id,
            isCustomerAttributesSelected,
            isPurchaseAttributesSelected,
            updateSegments,
        ],
    );


    const onChangePurchaseAttributes = useCallback((immutableTree, config) => {
            setPurchaseAttributesData(immutableTree);
            setTransactionFilterConfig(config);
        },
        [setTransactionFilterConfig, setPurchaseAttributesData],
    );

    useEffect(() => {
        if (
            !!currentFilters &&
            !isEmptyObject(currentFilters) &&
            !isEqualObjects(QbUtils.getTree(customerAttributesData), currentFilters)
        ) {
            setCustomerAttributesData(
                QbUtils.checkTree(QbUtils.loadTree(currentFilters), filterConfig),
            );
            setIsCustomerAttributesSelected(true);
        }
        if (
            !!currentTransactionFilters &&
            !isEmptyObject(currentTransactionFilters) &&
            !isEqualObjects(QbUtils.getTree(purchaseAttributesData), currentTransactionFilters)
        ) {
            setPurchaseAttributesData(
                QbUtils.checkTree(QbUtils.loadTree(currentTransactionFilters), transactionFilterConfig),
            );
            setIsPurchaseAttributesSelected(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentFilters, currentTransactionFilters]);


    return (
        <div className="mt-2 segment-conditions">
            {isLoading ? <LoadingComponent /> : <>
                <div className="d-flex justify-content-end mb-2">
                    <Button
                        className="btn shadow-none"
                        variant="primary"
                        onClick={onClickEditRule}
                        disabled={isUpdatingSegment ||
                            (isCustomerAttributesSelected &&
                                isEqualFilter(currentFilters, customerAttributesData, filterConfig) &&
                                isPurchaseAttributesSelected &&
                                isEqualFilter(currentTransactionFilters, purchaseAttributesData, transactionFilterConfig))
                        }
                        type="button"
                    >
                        Update Segments
                    </Button>
                </div>
                <QueryBuilderSection
                    title="Customer Attributes"
                    icon={faUser}
                    data={customerAttributesData}
                    onChange={onChangeCustomerAttributes}
                    config={filterConfig}
                    showQuery={isCustomerAttributesSelected}
                    setShowQuery={setIsCustomerAttributesSelected}
                />
                <QueryBuilderSection
                    title="Purchase Attributes"
                    icon={faShoppingCart}
                    data={purchaseAttributesData}
                    onChange={onChangePurchaseAttributes}
                    config={transactionFilterConfig}
                    showQuery={isPurchaseAttributesSelected}
                    setShowQuery={setIsPurchaseAttributesSelected}
                />
            </>}
        </div>
    );
};
export default SegmentConditions;
