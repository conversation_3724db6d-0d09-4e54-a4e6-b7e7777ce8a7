import React, { useCallback } from "react";
import {Card, Col, Form, IcIcon, Row } from "@shoutout-labs/shoutout-themes-enterprise";
import { Builder, Query } from "@shoutout-labs/react-awesome-query-builder-shoutout";

const renderBuilder = (props) => (
    <div className="query-builder-container">
        <div className="query-builder qb-lite">
            <Builder {...props} />
        </div>
    </div>
);

const QueryBuilderSection=({ title, icon, data, onChange, config,showQuery =false,setShowQuery})=>{

    const onChangQuery = useCallback(() => setShowQuery?.(!showQuery), [setShowQuery,showQuery]);
    return(
        <Card className="mb-3">
            <Card.Body className={`${showQuery?"active-card":""} segment-query-card`}>
                <Row className="mm-2">
                    <Col>
                        <div className="d-flex align-items-center">
                            <IcIcon size="md" className="mr-2 mt-n1" icon={icon} />
                            {title}
                        </div>
                    </Col>
                    <Col>
                        <div className="d-flex justify-content-end">
                            <Form.Check
                                id={title}
                                type="switch"
                                name="showQuery"
                                onChange={onChangQuery}
                                checked={showQuery}
                            />
                        </div>
                    </Col>
                </Row>
                {showQuery && <Row>
                    <Query
                        {...config}
                        value={data}
                        onChange={onChange}
                        renderBuilder={renderBuilder}
                    />
                </Row>}
            </Card.Body>
        </Card>
    )
}
export default QueryBuilderSection;
