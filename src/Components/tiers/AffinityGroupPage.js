import React from "react"
import { Route, Switch } from "react-router-dom";
import AffinityGroup from "./AffinityGroup";
import AffinityGroupProfilePage from "./affinityGroupProfile/AffinityGroupProfilePage";

import "./TiersPage.scss";

const AffinityGroupPage = () => {
    return (
        <div className="tiers-page-view">
            <Switch>
                <Route name="AffinityGroup" exact path="/affinity-groups" component={AffinityGroup} />
                <Route name="AffinityGroupProfile" exact path="/affinity-groups/:id" component={AffinityGroupProfilePage} />
            </Switch>
        </div>
    );
};

export default AffinityGroupPage;
