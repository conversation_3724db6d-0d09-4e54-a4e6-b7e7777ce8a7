import React from "react";
import { Route, Switch } from "react-router-dom";
import { CreateTierContextProvider } from "./createTiers/context/CreateTierContext";
import Tiers from "./Tiers";

import "./TiersPage.scss";

const TiersPage = () => {
    return (
        <CreateTierContextProvider>
            <div className="tiers-page-view">
                <Switch>
                    <Route name="Tier" exact path="/tiers" component={Tiers} />
                </Switch>
            </div>
        </CreateTierContextProvider>
    );
};

export default TiersPage;
