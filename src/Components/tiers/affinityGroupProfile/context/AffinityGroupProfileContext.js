import React, {
    useReducer,
    useCallback,
    useMemo,
    useEffect,
    useContext,
} from "react";
import { toast } from "react-toastify";
import { useHistory } from "react-router-dom";
import { UserContext } from "Contexts";
import {
    deleteAffinityGroups,
    importAffinityGroupsJobs,
    updateMemberAffinityGroup,
} from "Services";

const AffinityGroupProfileContext = React.createContext();

const importMembersInitialState = {
    fileToken: "",
    affinityGroupFile: "",
    headerMap: {},
    fileSample: [],
    fileHeaders: [],
    columnNameMap: {
        LOYALTY_CARD: {
            systemAttributeName: "LOYALTY_CARD",
            fileColumnName: "Loyalty Card Number",
            disabled: false,
            masterElement: "",
            childReference: "LOYALTY_CARD",
        },
        NAME: {
            systemAttributeName: "NAME",
            fileColumnName: "Name",
            disabled: false,
            masterElement: "",
            childReference: "NAME",
        },
        EMAIL: {
            systemAttributeName: "EMAIL",
            fileColumnName: "Email",
            disabled: false,
            masterElement: "",
            childReference: "EMAIL",
        },
        MOBILE_NUMBER: {
            systemAttributeName: "MOBILE_NUMBER",
            fileColumnName: "Mobile Number",
            disabled: false,
            masterElement: "",
            childReference: "MOBILE_NUMBER",
        },
        MEMBER_ID: {
            systemAttributeName: "MEMBER_ID",
            fileColumnName: "Member Id",
            disabled: false,
            masterElement: "",
            childReference: "MEMBER_ID",
        },
        JOIN_DATE: {
            systemAttributeName: "JOIN_DATE",
            fileColumnName: "Join Date",
            disabled: false,
            masterElement: "",
            childReference: "JOIN_DATE",
        },
        EXPIRATION_DATE: {
            systemAttributeName: "EXPIRATION_DATE",
            fileColumnName: "Expiration Date",
            disabled: false,
            masterElement: "",
            childReference: "EXPIRATION_DATE",
        },
    },
};
const initialState = {
    showImportAffinityGroupWizard: false,
    refreshProfileDetails: true,
    show: false,
    isImporting: false,
    isLoading: false,
    isLoadingMemberList: false,
    isLoadingImportJobs: false,
    isLoadingImportJobLogs: false,
    isImportingJobs: false,
    isExportingAffinityGroup: false,
    reloadMemberList: false,
    reloadImportedJobList: false,
    isValidatingCard: false,
    isSearchingMember: false,
    isAddingMember: false,
    isMembersAvailable: false,
    isRemovingMember: false,
    isArchivingAffinityGroup: false,
    notificationEmails: [],
    currentEventKey: "",
    isDefaultAffinityGroup: false,
    affinityGroupDetails: {
        _id: "",
        createdOn: "",
        description: "",
        name: "",
        membersCount: "",
        benefits: [],
    },
    memberDetails: {
        _id: "",
        loyaltyCardNumber: "",
        name: "",
        email: "",
        mobileNumber: 0,
        isoCode: "",
        expiryDate: "",
    },
    popUpState: {
        submitButtonName: "",
        modalHeaderName: "",
        renderingComponentName: "",
        modalFooterVisibility: false,
    },
    ...importMembersInitialState,
};

const AffinityGroupProfileContextActions = {
    SET_SHOW_WIZARD: "setShowWizard",
    SET_FILE: "setFile",
    SET_IS_LOADING: "setIsLoading",
    SET_IS_LOADING_MEMBER_LIST: "setIsLoadingMemberList",
    SET_IS_LOADING_IMPORT_JOBS: "setIsLoadingImportJobs",
    SET_IS_LOADING_IMPORT_JOB_LOGS: "setIsLoadingImportJobLogs",
    SET_IS_EXPORTING_AFFINITY_GROUP: "setIsExportingAffinityGroup",
    SET_IS_VALIDATING_CARD: "setIsValidatingCard",
    SET_IS_SEARCHING_MEMBER: "setIsSearchingMember",
    SET_IS_ADDING_MEMBER: "setIsAddingMember",
    SET_IS_MEMBERS_AVAILABLE: "setIsMembersAvailable",
    SET_STEP_STATE: "setStepState",
    RESET: "reset",
    ADD_MEMBERS: "addMembers",
    HANDLE_CLOSE: "handleClose",
    SET_STATES: "setStates",
    SET_IS_IMPORTING: "setIsImporting",
    SET_LOADING_STATE: "setLoadingState",
    SET_NOTIFICATION_EMAILS: "setNotificationEmails",
    SET_IS_DEFAULT_AG: "setIsDefaultAG",
};

const reducer = (state, action) => {
    switch (action.type) {
        case AffinityGroupProfileContextActions.SET_SHOW_WIZARD: {
            return {
                ...state,
                showImportAffinityGroupWizard:
                    action.showImportAffinityGroupWizard,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_LOADING: {
            return {
                ...state,
                isLoading: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_LOADING_MEMBER_LIST: {
            return {
                ...state,
                isLoadingMemberList: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_LOADING_IMPORT_JOBS: {
            return {
                ...state,
                isLoadingImportJobs: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_LOADING_IMPORT_JOB_LOGS: {
            return {
                ...state,
                isLoadingImportJobLogs: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_EXPORTING_AFFINITY_GROUP: {
            return {
                ...state,
                isExportingAffinityGroup: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_VALIDATING_CARD: {
            return {
                ...state,
                isValidatingCard: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_SEARCHING_MEMBER: {
            return {
                ...state,
                isSearchingMember: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_ADDING_MEMBER: {
            return {
                ...state,
                isAddingMember: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_MEMBERS_AVAILABLE: {
            return {
                ...state,
                isMembersAvailable: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_FILE: {
            return {
                ...state,
                affinityGroupFile: action.affinityGroupFile,
            };
        }
        case AffinityGroupProfileContextActions.SET_STEP_STATE: {
            return {
                ...state,
                columnNameMap: action.updatedData?.columnNameMap,
                headerMap: action.updatedData?.headerMap,
            };
        }
        case AffinityGroupProfileContextActions.HANDLE_CLOSE: {
            return {
                ...state,
                show: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_IS_IMPORTING: {
            return {
                ...state,
                isImportingJobs: action.isImporting,
            };
        }
        case AffinityGroupProfileContextActions.SET_STATES: {
            return {
                ...state,
                ...action.states,
            };
        }
        case AffinityGroupProfileContextActions.ADD_MEMBERS: {
            return {
                ...state,
                show: true,
                popUpState: {
                    submitButtonName: "",
                    modalHeaderName: "Search Loyalty Member",
                    renderingComponentName: "ADD_MEMBERS",
                    modalFooterVisibility: false,
                },
            };
        }
        case AffinityGroupProfileContextActions.SET_LOADING_STATE: {
            return {
                ...state,
                [action.key]: action.status,
            };
        }
        case AffinityGroupProfileContextActions.RESET: {
            return { ...state, ...importMembersInitialState };
        }
        case AffinityGroupProfileContextActions.SET_IS_DEFAULT_AG: {
            return {
                ...state,
                isDefaultAffinityGroup: action.status,
            };
        }
        case AffinityGroupProfileContextActions.SET_NOTIFICATION_EMAILS: {
            return { ...state, notificationEmails: action.notificationEmails };
        }
        default:
            return state;
    }
};

const AffinityGroupProfileContextProvider = (props) => {
    const { selectedRegion } = useContext(UserContext);
    const [state, dispatch] = useReducer(reducer, initialState);
    const history = useHistory();

    const setShowImportAffinityGroupWizard = useCallback(
        (showImportAffinityGroupWizard) => {
            dispatch({
                type: AffinityGroupProfileContextActions.SET_SHOW_WIZARD,
                showImportAffinityGroupWizard,
            });
        },
        [dispatch]
    );
    const setAffinityGroupFile = useCallback(
        (affinityGroupFile) => {
            dispatch({
                type: AffinityGroupProfileContextActions.SET_FILE,
                affinityGroupFile,
            });
        },
        [dispatch]
    );
    const updateStepState = useCallback(
        (updatedData) => {
            dispatch({
                type: AffinityGroupProfileContextActions.SET_STEP_STATE,
                updatedData,
            });
        },
        [dispatch]
    );

    const onClickAddMembers = useCallback(
        () =>
            dispatch({ type: AffinityGroupProfileContextActions.ADD_MEMBERS }),
        [dispatch]
    );

    const handleClose = useCallback(() => {
        dispatch({
            type: AffinityGroupProfileContextActions.HANDLE_CLOSE,
            status: false,
        });
    }, [dispatch]);

    const setStates = useCallback(
        (states) => {
            dispatch({
                type: AffinityGroupProfileContextActions.SET_STATES,
                states,
            });
        },
        [dispatch]
    );

    const setIsLoading = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_LOADING,
                status,
            }),
        [dispatch]
    );

    const setIsLoadingMemberList = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_LOADING_MEMBER_LIST,
                status,
            }),
        [dispatch]
    );

    const setIsLoadingImportJobs = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_LOADING_IMPORT_JOBS,
                status,
            }),
        [dispatch]
    );

    const setIsLoadingImportJobLogs = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_LOADING_IMPORT_JOB_LOGS,
                status,
            }),
        [dispatch]
    );

    const setIsExportingAffinityGroup = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_EXPORTING_AFFINITY_GROUP,
                status,
            }),
        [dispatch]
    );

    const setIsValidatingCard = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_VALIDATING_CARD,
                status,
            }),
        [dispatch]
    );

    const setIsSearchingMember = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_SEARCHING_MEMBER,
                status,
            }),
        [dispatch]
    );

    const setIsAddingMember = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_ADDING_MEMBER,
                status,
            }),
        [dispatch]
    );

    const setIsMembersAvailable = useCallback(
        (status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_MEMBERS_AVAILABLE,
                status,
            }),
        [dispatch]
    );

    const setLoadingState = useCallback(
        (key, status) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_LOADING_STATE,
                key,
                status,
            }),
        [dispatch]
    );

    const setNotificationEmails = useCallback(
        (notificationEmails) =>
            dispatch({
                type: AffinityGroupProfileContextActions.SET_NOTIFICATION_EMAILS,
                notificationEmails,
            }),
        [dispatch]
    );

    const detailsSubmitHandler = useCallback(
        async (event) => {
            event.preventDefault();
            switch (state.popUpState.renderingComponentName) {
                case "DELETE_AG":
                    try {
                        setLoadingState("isArchivingAffinityGroup", true);
                        await deleteAffinityGroups(
                            state.affinityGroupDetails._id
                        );
                        setLoadingState("isArchivingAffinityGroup", false);
                        dispatch({
                            type: AffinityGroupProfileContextActions.HANDLE_CLOSE,
                            status: false,
                        });
                        history.push("/tiers/affinity_group");
                        toast.success("Successfully deleted affinity group.");
                    } catch (e) {
                        console.error(e);
                        setLoadingState("isArchivingAffinityGroup", false);
                        toast.error(
                            <div>
                                Failed to archive affinity group!
                                <br />
                                {e.message
                                    ? `Error: ${e.message}`
                                    : "Please try again later."}
                            </div>
                        );
                    }
                    break;
                case "REMOVE_AFFINITY_GROUP_MEMBER":
                    try {
                        setLoadingState("isRemovingMember", true);
                        await updateMemberAffinityGroup(
                            state.popUpState.memberId,
                            {
                                affinityGroupId: null,
                            }
                        );
                        setLoadingState("isRemovingMember", false);
                        setStates({ reloadMemberList: true });
                        dispatch({
                            type: AffinityGroupProfileContextActions.HANDLE_CLOSE,
                            status: false,
                        });
                        toast.success(
                            "Successfully delete affinity group member"
                        );
                    } catch (e) {
                        console.error(e);
                        setLoadingState("isRemovingMember", false);
                        toast.error(
                            <div>
                                Failed to remove member from affinity group!
                                <br />
                                {e.message
                                    ? `Error: ${e.message}`
                                    : "Please try again later."}
                            </div>
                        );
                    }
                    break;
                default:
            }
        },
        [state, history, setLoadingState, setStates]
    );
    const reset = useCallback(
        () => dispatch({ type: AffinityGroupProfileContextActions.RESET }),
        [dispatch]
    );

    const importAffinityGroupMembers = useCallback(async () => {
        try {
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_IMPORTING,
                isImporting: true,
            });
            await importAffinityGroupsJobs({
                fieldMappings: Object.keys(state.headerMap).map((key) => ({
                    fileColumnName: key,
                    systemAttributeName:
                        state.headerMap[key].systemAttributeName,
                })),
                fileToken: state.fileToken,
            });
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_IMPORTING,
                isImporting: false,
            });
            toast.success("Transactions imported successfully.");
            reset();
            setStates({
                reloadImportedJobList: true,
                showImportAffinityGroupWizard: false,
            });
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to import members!
                    <br />
                    {e.message
                        ? `Error: ${e.message}`
                        : "Please try again later."}
                </div>
            );
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_IMPORTING,
                isImporting: false,
            });
        }
    }, [setStates, reset, state]);

    useEffect(() => {
        if (state.affinityGroupDetails._id) {
            dispatch({
                type: AffinityGroupProfileContextActions.SET_IS_DEFAULT_AG,
                status:
                    state.affinityGroupDetails._id ===
                    selectedRegion?.memberConfiguration.defaultAffinityGroupId,
            });
        }
    }, [
        selectedRegion?.memberConfiguration.defaultAffinityGroupId,
        state.affinityGroupDetails._id,
    ]);

    const value = useMemo(
        () => ({
            ...state,
            setShowImportAffinityGroupWizard,
            setAffinityGroupFile,
            updateStepState,
            reset,
            onClickAddMembers,
            handleClose,
            setStates,
            detailsSubmitHandler,
            importAffinityGroupMembers,
            setIsLoading,
            setIsLoadingMemberList,
            setIsLoadingImportJobs,
            setIsLoadingImportJobLogs,
            setIsExportingAffinityGroup,
            setIsValidatingCard,
            setIsSearchingMember,
            setIsAddingMember,
            setIsMembersAvailable,
            setNotificationEmails,
        }),
        [
            state,
            detailsSubmitHandler,
            handleClose,
            importAffinityGroupMembers,
            onClickAddMembers,
            reset,
            setAffinityGroupFile,
            setIsAddingMember,
            setIsExportingAffinityGroup,
            setIsLoading,
            setIsLoadingImportJobLogs,
            setIsLoadingImportJobs,
            setIsLoadingMemberList,
            setIsMembersAvailable,
            setIsSearchingMember,
            setIsValidatingCard,
            setNotificationEmails,
            setShowImportAffinityGroupWizard,
            setStates,
            updateStepState,
        ]
    );

    return (
        <AffinityGroupProfileContext.Provider value={value}>
            {props.children}
        </AffinityGroupProfileContext.Provider>
    );
};

const AffinityGroupProfilContextConsumer = AffinityGroupProfileContext.Consumer;

export {
    AffinityGroupProfileContextProvider,
    AffinityGroupProfileContext,
    AffinityGroupProfilContextConsumer,
};
