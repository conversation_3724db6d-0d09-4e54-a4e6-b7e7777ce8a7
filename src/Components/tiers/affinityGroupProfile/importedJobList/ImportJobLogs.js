import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import { toast } from "react-toastify";
import PropTypes from "prop-types";
import {
    Badge,
    Button,
    Col,
    Row,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCalendar, faListAlt, faUser } from "FaICIconMap";
import { UserContext } from "Contexts";
import { AccessPermissionModuleNames, AccessPermissionModules } from "Data";
import {
    exportAffinityGroupImportedJobLog,
    getImportedJobLogs,
} from "Services";
import { downloadLink, formatToCommonReadableFormat, toTitleCase } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import SharedTable from "Components/tiers/shared/sharedTableView/SharedTableView";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import NameIconTemplate from "Components/utils/table/NameIconTemplate";
import { AffinityGroupProfileContext } from "../context/AffinityGroupProfileContext";
import ImportLogItemDetails from "./importLogItemDetails/ImportLogItemDetails";

const columns = [
    {
        dataField: "name",
        text: NameIconTemplate({ name: "Name", icon: faUser }),
    },
    {
        dataField: "loyaltyCardNo",
        text: NameIconTemplate({ name: "Loyalty Card No", icon: faListAlt }),
    },
    {
        dataField: "contactNumber",
        text: NameIconTemplate({ name: "Contact Number", icon: faCalendar }),
    },
    {
        dataField: "expirationDate",
        text: NameIconTemplate({ name: "Expiration Date", icon: faCalendar }),
    },
    {
        dataField: "status",
        text: NameIconTemplate({ name: "Status", icon: faUser }),
    },
    { dataField: "member", hidden: true },
    { dataField: "logItem", hidden: true },
];

const importJobLogsStatusMap = (status) => {
    switch (status) {
        case "PENDING":
            return applyBadgeStyling({
                text: toTitleCase(status),
                variant: "secondary",
            });
        case "PROCESSING":
            return applyBadgeStyling({
                text: toTitleCase(status),
                variant: "primary",
            });
        case "COMPLETED":
            return applyBadgeStyling({
                text: toTitleCase(status),
                variant: "success",
            });
        case "FAILED":
            return applyBadgeStyling({
                text: toTitleCase(status),
                variant: "danger",
            });
        default:
            break;
    }
};

const defaultLimit = 25,
    defaultSkip = 1;

const ImportJobLogs = ({ activeTab, affinityGroupId, importJobId }) => {
    const { selectedRegion, isAuthorizedForAction } = useContext(UserContext);
    const { isLoadingImportJobLogs: isLoading, setIsLoadingImportJobLogs } =
        useContext(AffinityGroupProfileContext);
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [showImportLogDetails, setShowImportLogDetails] = useState(false);
    const [importLogDetails, setImportLogDetails] = useState();
    const [isExporting, setIsExporting] = useState(false);

    const loadImportJobLogs = useCallback(
        async ({ skip, limit }) => {
            try {
                setData([]);
                setIsLoadingImportJobLogs(true);

                const importJobLogsData = await getImportedJobLogs({
                    limit,
                    skip: (skip - 1) * limit,
                    regionId: selectedRegion._id,
                    affinityGroupId,
                    importJobId,
                    ...(activeTab !== "ALL" ? { status: activeTab } : {}),
                });

                setData(
                    importJobLogsData.items.length !== 0
                        ? importJobLogsData.items.map((logItem) => ({
                            name: toTitleCase(
                                logItem?.memberDetails.name || "-"
                            ),
                            loyaltyCardNo:
                                logItem?.memberDetails.loyaltyCard || "-",
                            contactNumber:
                                logItem?.memberDetails.contactNumber || "-",
                            expirationDate: logItem?.memberDetails
                                ?.expirationDate ? (
                                formatToCommonReadableFormat(
                                    logItem?.memberDetails?.expirationDate
                                )
                            ) : (
                                <Badge className="px-3 py-2" variant="info">
                                    No Expiry
                                </Badge>
                            ),
                            status: importJobLogsStatusMap(logItem?.status),
                            id: logItem?._id,
                            logItem,
                        }))
                        : []
                );
                setTotalCount(importJobLogsData.total);
                setIsLoadingImportJobLogs(false);
            } catch (e) {
                console.error(e);
                setIsLoadingImportJobLogs(false);
                toast.error(
                    <div>
                        Failed to load affinity group's import job logs!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [
            selectedRegion,
            affinityGroupId,
            importJobId,
            activeTab,
            setIsLoadingImportJobLogs,
        ]
    );

    const onChangePagination = useCallback(
        (newSkip) => {
            setSkip(newSkip);
            loadImportJobLogs({
                skip: newSkip,
                limit,
            });
        },
        [limit, setSkip, loadImportJobLogs]
    );

    const onChangePageSize = useCallback(
        (newLimit) => {
            setLimit(newLimit);
            setSkip(defaultSkip);
            loadImportJobLogs({
                skip: defaultSkip,
                limit: newLimit,
            });
        },
        [setLimit, setSkip, loadImportJobLogs]
    );

    const onClickExport = useCallback(async () => {
        try {
            setIsExporting(true);
            const exportURL = await exportAffinityGroupImportedJobLog({
                regionId: selectedRegion._id,
                affinityGroupId,
                importJobId,
                ...(activeTab !== "ALL" ? { status: activeTab } : {}),
            });
            downloadLink(exportURL.url);
        } catch (e) {
            console.error(e);
            toast.error(
                <div>
                    Failed to export affinity group's import job logs!
                    <br />
                    Error: {e.message || "Please try again."}
                </div>
            );
        } finally {
            setIsExporting(false);
        }
    }, [
        activeTab,
        affinityGroupId,
        importJobId,
        selectedRegion,
        setIsExporting,
    ]);

    const onShowDetails = useCallback(
        (row) => {
            setImportLogDetails({
                ...row?.logItem,
                ...row?.logItem?.memberDetails,
            });
            setShowImportLogDetails(true);
        },
        [setImportLogDetails, setShowImportLogDetails]
    );

    const onCloseShowDetails = useCallback(() => {
        setImportLogDetails();
        setShowImportLogDetails(false);
    }, [setImportLogDetails, setShowImportLogDetails]);

    useEffect(() => {
        if (
            isAuthorizedForAction(
                AccessPermissionModuleNames.AFFINITY_GROUPS,
                AccessPermissionModules[
                    AccessPermissionModuleNames.AFFINITY_GROUPS
                ].actions.ListAffinityGroupMemberImportLogs
            )
        ) {
            loadImportJobLogs({ skip: defaultSkip, limit });
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeTab]);

    return (
        <>
            <BaseLayout
                containerClassName="affinity-group-profile p-3"
                bottom={
                    <>
                        {isAuthorizedForAction(
                            AccessPermissionModuleNames.AFFINITY_GROUPS,
                            AccessPermissionModules[
                                AccessPermissionModuleNames.AFFINITY_GROUPS
                            ].actions.ListAffinityGroupMemberImportLogs
                        ) ? (
                            <>
                                <Row className="mt-3">
                                    <Col sm={5} />
                                    <Col>
                                        {activeTab !== "ALL" && (
                                            <div className="float-right">
                                                <Button
                                                    className="mx-2"
                                                    variant="outline-dark"
                                                    size="sm"
                                                    disabled={
                                                        isLoading ||
                                                        isExporting ||
                                                        totalCount === 0
                                                    }
                                                    onClick={onClickExport}
                                                >
                                                    {isExporting &&
                                                        "Exporting..."}
                                                    {!isExporting && "Export"}
                                                </Button>
                                            </div>
                                        )}
                                    </Col>
                                </Row>
                                <SharedTable
                                    columns={columns}
                                    data={data}
                                    sizePerPage={limit}
                                    page={skip}
                                    onChangePagination={onChangePagination}
                                    onChangePageSize={onChangePageSize}
                                    isLoading={isLoading}
                                    onShowDetails={onShowDetails}
                                    totalCount={totalCount}
                                />
                            </>
                        ) : (
                            <h4 className="text-danger text-center">
                                You are not authorized to view affinity group
                                import job logs!
                            </h4>
                        )}
                    </>
                }
            />
            {showImportLogDetails && (
                <ImportLogItemDetails
                    show={showImportLogDetails}
                    onHide={onCloseShowDetails}
                    logItem={importLogDetails}
                />
            )}
        </>
    );
};

ImportJobLogs.defaultProps = {
    activeTab: "",
    affinityGroupId: "",
    importJobId: "",
};

ImportJobLogs.propTypes = {
    activeTab: PropTypes.string,
    affinityGroupId: PropTypes.string,
    importJobId: PropTypes.string,
};

export default ImportJobLogs;
