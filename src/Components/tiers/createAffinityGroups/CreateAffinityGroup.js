import React, { useCallback, useContext, useEffect, useMemo } from "react";
import { Button, IcIcon, } from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlusCircle} from 'FaICIconMap';
import { CreateAffinityGroupContext, CreateAffinityGroupContextProvider } from "./context/CreateAffinityGroupContext";
import CreateAffinityGroupWizard from "./createAffinityGropuWizard/CreateAffinityGroupWizard";

const CreateAffinityGroupWizardPage = ({ onCreateGroup }) => {
    const { showCreateWizard: show, isCreating, isCreated, setShowCreateWizard, setIsCreated, reset } = useContext(CreateAffinityGroupContext);

    const onShowCreateWizard = useCallback(() => setShowCreateWizard(true), [setShowCreateWizard]);
    const onCloseCreateWizard = useCallback(() => {
        reset();
        setShowCreateWizard(false)
    }, [reset, setShowCreateWizard]);

    useEffect(() => {
        if(isCreated) {
            onCreateGroup();
            setIsCreated(false);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isCreated, setIsCreated]);

    return useMemo(
        () =>
        <CreateAffinityGroupWizardView
            show={show}
            isCreating={isCreating}
            onHide={onCloseCreateWizard}
            onShowCreateWizard={onShowCreateWizard}
        />, [show, isCreating, onShowCreateWizard, onCloseCreateWizard]
    );
};

const CreateAffinityGroupWizardView = ({ show, isCreating, onHide, onShowCreateWizard }) => {
    return (
        <>
            <Button className="action-btn float-right" variant="primary" size="sm" onClick={onShowCreateWizard}>
                <IcIcon className="mr-2" size="lg" icon={faPlusCircle} /> 
                Create Affinity Group
            </Button>
            <CreateAffinityGroupWizard show={show} onHide={onHide} isCreating={isCreating} />
        </>
    );
};

const CreateAffinityGroup = ({ onCreateGroup }) => {
    return (
        <CreateAffinityGroupContextProvider>
            <CreateAffinityGroupWizardPage onCreateGroup={onCreateGroup} />
        </CreateAffinityGroupContextProvider>
    );
};

export default CreateAffinityGroup
