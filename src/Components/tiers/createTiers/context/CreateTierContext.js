import React, { useReducer, useCallback } from "react";
import { uploadImage } from '../../../../Services';
const CreateTierContext = React.createContext()

const initialState = {
    name: "",
    points: "",
    benefits: [""],
    imageUrl: null
};

const CreateTierContextActions = {
    SET_ATTRIBUTE: "setTierAttribute",
    ADD_BENEFIT: "addTierBenefit",
    UPDATE_BENEFIT: "updateTierBenefit",
    REMOVED_BENEFIT:"removedBenefit"
};


const reducer = (state, action) => {
    console.debug("Action:", action)
    switch (action.type) {

        case CreateTierContextActions.SET_ATTRIBUTE: {

            return {
                ...state,
                [action.key]: action.value
            }
        }
        case CreateTierContextActions.ADD_BENEFIT: {

            return {
                ...state,
                benefits: [...state.benefits, action.value]
            }
        }
        case CreateTierContextActions.REMOVED_BENEFIT: {
            const newBenefits = [...state.benefits];
          return {
                ...state,
                benefits: newBenefits.filter((benefit, i) =>i!==parseInt(action.key))
            }
        }

        case CreateTierContextActions.UPDATE_BENEFIT: {
            const newBenefits = [...state.benefits];
            newBenefits[action.key] = action.value;
            return {
                ...state,
                benefits: newBenefits
            }
        }
        default:
            return state;
    }
};

const CreateTierContextProvider = (props) => {
    const [state, dispatch] = useReducer(reducer, initialState);

    const setName = useCallback((e) => {
        dispatch({ type: CreateTierContextActions.SET_ATTRIBUTE, key: "name", value: e.target.value });
    }, [dispatch])

    const setPoints = useCallback((e) => {
        dispatch({ type: CreateTierContextActions.SET_ATTRIBUTE, key: "points", value: e.target.value });
    }, [dispatch])

    const setImageUrl = useCallback(async (imageUrl) => {
        if(imageUrl[0]) {
            const imageIntoUrl = await uploadImage(imageUrl[0])
            dispatch({type: CreateTierContextActions.SET_ATTRIBUTE, key: "imageUrl", value: imageIntoUrl.url});
        }
    }, [dispatch])

    const addBenefit = useCallback(() => {
        dispatch({ type: CreateTierContextActions.ADD_BENEFIT, value: "" });
    }, [dispatch])

    const updateBenefit = useCallback((e) => {
        dispatch({ type: CreateTierContextActions.UPDATE_BENEFIT, key: e.target.name, value: e.target.value });
    }, [dispatch])


    const handleClose=useCallback(()=>{
            props.handleClose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[props.handleClose])
    const refreshTable=useCallback(()=>{
        props.refreshTable();
     // eslint-disable-next-line react-hooks/exhaustive-deps
    },[ props.refreshTable])

    const onRemoveBenefit =useCallback((event)=>{
        event.preventDefault()
        dispatch({ type: CreateTierContextActions.REMOVED_BENEFIT, key: event.currentTarget.id});
    },[dispatch])

    const value = { ...state, setName, setPoints, setImageUrl, onRemoveBenefit,addBenefit, updateBenefit,handleClose ,refreshTable};

    return (
        <CreateTierContext.Provider value={value}>{props.children}</CreateTierContext.Provider>
    );
}

const CreateTierContextConsumer = CreateTierContext.Consumer;

export { CreateTierContext, CreateTierContextProvider, CreateTierContextConsumer };
