import React, {useCallback} from "react";
import {<PERSON><PERSON>, Modal} from "@shoutout-labs/shoutout-themes-enterprise";
import PropTypes from 'prop-types';
import moment from "moment";
import { useHistory } from "react-router";

import "./AffinityGroupDetails.scss";

const AffinityGroupDetails=({ show, onHide, selectedAffinityGroup, pointRules })=>{

    const history = useHistory();

    const showPointRules=useCallback(() => {
        history.push({
          pathname: "/point-rules",
          state: { filter: { key: "ruleType"}}
        })
    },[
        history, 
        //selectedAffinityGroup
    ]);

    return(
        <Modal show={show} onHide={onHide} className="affinity-group-details-modal" size="lg" centered={true}>
            <Modal.Header closeButton>
                <Modal.Title >Affinity Group Details</Modal.Title>
            </Modal.Header>
            <div>
                <Modal.Header className="mt-4">
                    <Modal.Title>Basic Details</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="d-flex flex-row justify-content-between">
                        <div className="d-flex flex-column w-50 mr-3">
                            <div>
                                <p className="font-weight-bold mb-0 pb-0">ID</p>
                            </div>
                            <div className="detail-col p-2 pl-3">
                                <p className="pb-0 mb-0">{selectedAffinityGroup._id}</p>
                            </div>
                        </div>
                        <div className="d-flex flex-column w-50 ml-3">
                            <div>
                                <p className="font-weight-bold mb-0 pb-0">Name</p>
                            </div>
                            <div className="detail-col p-2 pl-3">
                                <p className="pb-0 mb-0">{selectedAffinityGroup.name}</p>
                            </div>
                        </div>
                    </div>
                    <div className="d-flex flex-row justify-content-between mt-4">
                        <div className="d-flex flex-column w-50 mr-3">
                            <div>
                                <p className="font-weight-bold mb-0 pb-0">Description</p>
                            </div>
                            <div className="detail-col p-2 pl-3">
                                <p className="pb-0 mb-0">{selectedAffinityGroup.description}</p>
                            </div>
                        </div>
                        <div className="d-flex flex-column w-50 ml-3">
                            <div>
                                <p className="font-weight-bold mb-0 pb-0">Members</p>
                            </div>
                            <div className="detail-col p-2 pl-3">
                                <p className="pb-0 mb-0">{selectedAffinityGroup.membersCount || 0}</p>
                            </div>
                        </div>
                    </div>
                    <div className="d-flex flex-row justify-content-between mt-4">
                        <div className="d-flex flex-column w-50 pr-3">
                            <div>
                                <p className="font-weight-bold mb-0 pb-0">Created Date</p>
                            </div>
                            <div className="detail-col p-2 pl-3">
                                <p className="pb-0 mb-0">{moment(selectedAffinityGroup.createdOn).format("LLL")}</p>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4">
                        <div className="">
                            <p className="font-weight-bold mb-0 pb-0">Benefits</p>
                        </div>
                        <div className="detail-row p-2 pl-3">
                            <p className="pb-0 mb-0 pl-1">
                                {selectedAffinityGroup?.benefits?.map(benefit => <li>{benefit}</li>)}
                            </p>
                        </div>
                    </div>
                </Modal.Body>
                {/* <Modal.Header className="mt-5"> */}
                    <Modal.Title className="mt-4">Point Rules</Modal.Title>
                {/* </Modal.Header> */}
                <Modal.Body>
                    <div className="d-flex flex-row justify-content-between mt-2">
                        <Button 
                            size="sm" 
                            variant="link" 
                            onClick={showPointRules} 
                            className="p-0"
                        > 
                            View Point Rules 
                        </Button>
                    </div>
                    {/* <div>
                        <div className="d-flex flex-row justify-content-between">
                            <div className="d-flex flex-column w-50 mr-3">
                                <div>
                                    <p className="font-weight-bold mb-0 pb-0">Merchant</p>
                                </div>
                            </div>
                            <div className="d-flex flex-column w-50 mr-3">
                                <div>
                                    <p className="font-weight-bold mb-0 pb-0">Point Rule</p>
                                </div>
                            </div>
                        </div>
                        <hr className="w-100"/>
                    </div>
                    {pointRules.map( rule => rule.ruleData?.affinityGroupId === selectedAffinityGroup?._id &&
                        <div>
                            <div className="d-flex flex-row justify-content-between">
                                <div className="d-flex flex-column w-50 mr-3">
                                    <div>
                                        <p className="mb-0 pb-0">{rule.name}</p>
                                    </div>
                                </div>
                                <div className="d-flex flex-column w-50 mr-3">
                                    <div>
                                        <p className="mb-0 pb-0">{rule.description}</p>
                                    </div>
                                </div>
                            </div>
                            <hr className="w-100"/>
                        </div>
                    )} */}
                </Modal.Body>
            </div>
            <Modal.Footer>
                <Button variant="primary" type="button" onClick={onHide}> Close </Button>
            </Modal.Footer>
        </Modal>
    )
}

AffinityGroupDetails.propTypes = {
    show: PropTypes.bool.isRequired,
    onHide: PropTypes.func.isRequired,
    selectedAffinityGroup: PropTypes.object.isRequired,
    pointRules: PropTypes.array.isRequired
};

export default AffinityGroupDetails
