import React, { useCallback, useState } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import EditAffinityGroup from "../../editAffinityGroup/EditAffinityGroup";
import SearchLoyaltyMember from "../../affinityGroupProfile/addMembers/SearchLoyaltyMember";
import AddMembers from "../../affinityGroupProfile/addMembers/AddMembers";
import MemberDetails from "../../affinityGroupProfile/memberList/MemberDetails";
import ExportAffinityGroupMembers from "../../affinityGroupProfile/exportAffinityGroupMembers/ExportAffinityGroupMembers";

const PopUpWindow = ({
    isLoading,
    show,
    handleClose,
    popUpState,
    detailsSubmitHandler,
}) => {
    const [isLoadingModal, setIsLoadingModal] = useState(false);

    const modelHeaderRender = useCallback(
        (renderType) => {
            switch (renderType) {
                case "DELETE_AG":
                    return <p>Do you want to archive this affinity group?</p>;

                case "REMOVE_AFFINITY_GROUP_MEMBER":
                    return (
                        <p>
                            Do you want to remove this member from affinity
                            group?
                        </p>
                    );

                case "ARCHIVE_TIERS":
                    return <p>Do you want to archive this tier?</p>;

                case "VIEW_BENEFIT":
                    return (
                        <>
                            {popUpState.hasOwnProperty("benefits") ? (
                                <ul className="list-group list-group-flush">
                                    {popUpState.benefits.map(
                                        (benefit, index) => (
                                            <li
                                                className="list-group-item"
                                                key={`benefit${index}`}
                                            >
                                                {benefit}
                                            </li>
                                        )
                                    )}
                                </ul>
                            ) : (
                                <span>No benefits found.</span>
                            )}
                        </>
                    );

                case "EDIT_AFFINITY_GROUP":
                    return (
                        <EditAffinityGroup
                            setIsLoadingModal={setIsLoadingModal}
                        />
                    );

                case "ADD_MEMBERS":
                    return <SearchLoyaltyMember />;

                case "ACCOUNT_DETAILS":
                    return <AddMembers />;

                case "SHOW_DETAILS":
                    return <MemberDetails />;

                case "EXPORT_MEMBERS":
                    return <ExportAffinityGroupMembers />;

                default:
                    break;
            }
        },
        [popUpState]
    );

    return (
        <Modal
            show={show}
            onHide={handleClose}
            size="md"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!(isLoading || isLoadingModal)}>
                <Modal.Title>{popUpState.modalHeaderName}</Modal.Title>
            </Modal.Header>
            <Modal.Body className="pt-3 mt-2 px-2">
                {modelHeaderRender(popUpState.renderingComponentName)}
            </Modal.Body>
            {popUpState.modalFooterVisibility && (
                <Modal.Footer>
                    <Button
                        variant="outline-primary"
                        size="sm"
                        type="button"
                        disabled={isLoading || isLoadingModal}
                        onClick={handleClose}
                    >
                        {popUpState?.modalCloseBtnName || "Cancel"}
                    </Button>
                    {!!popUpState?.hasOtherAction && (
                        <Button
                            className="ml-2"
                            type="submit"
                            variant="danger"
                            size="sm"
                            disabled={isLoading || isLoadingModal}
                            onClick={detailsSubmitHandler}
                        >
                            {isLoading
                                ? popUpState?.stateChaningBtnName ||
                                    popUpState.submitButtonName
                                : popUpState.submitButtonName}
                        </Button>
                    )}
                </Modal.Footer>
            )}
        </Modal>
    );
};

PopUpWindow.propTypes = {
    show: PropTypes.bool.isRequired,
    handleClose: PropTypes.func.isRequired,
    detailsSubmitHandler: PropTypes.func.isRequired,
    popUpState: PropTypes.object.isRequired,
};

export default PopUpWindow;
