import React from "react";
import paginationFactory, {
    PaginationProvider,
} from "react-bootstrap-table2-paginator";
import PropTypes from "prop-types";
import { BootstrapTable } from "@shoutout-labs/shoutout-themes-enterprise";
import { BootstrapTableOverlay } from "../../../utils/UtilComponents";
import SizePerPageRenderer from "../../../utils/table/sizePerPageRenderer/SizePerPageRenderer";

const NoDataIndication = ({ loading }) => {
    if (loading) return null;
    return <div>No data found.</div>;
};

const SharedTable = ({
    columns,
    data,
    sizePerPage,
    page,
    onChangePagination,
    onChangePageSize,
    totalCount,
    isLoading,
    onShowDetails,
}) => {
    const rowClick = (_e, row) => onShowDetails(row);

    const tableRowEvents = { onClick: rowClick };

    const options = {
        page: page,
        sizePerPage: sizePerPage,
        sizePerPageRenderer: SizePerPageRenderer,
        totalSize: totalCount,
        paginationSize: 5,
        pageStartIndex: 1,
        showTotal: true,
        withFirstAndLast: true,
        sizePerPageList: [
            {
                text: "25",
                value: 25,
            },
            {
                text: "50",
                value: 50,
            },
            {
                text: "100",
                value: 100,
            },
        ],

        onPageChange: onChangePagination,
        onSizePerPageChange: onChangePageSize,
    };

    return (
        <div className="tier-table">
            <PaginationProvider
                pagination={paginationFactory(options)}
                keyField="id"
                columns={columns}
                data={data}
            >
                {({ paginationTableProps }) => (
                    <BootstrapTable
                        {...paginationTableProps}
                        remote={{ pagination: true }}
                        keyField="id"
                        data={data}
                        columns={columns}
                        columnToggle
                        rowEvents={tableRowEvents}
                        loading={isLoading}
                        noDataIndication={() => (
                            <NoDataIndication loading={isLoading} />
                        )}
                        overlay={BootstrapTableOverlay}
                    />
                )}
            </PaginationProvider>
        </div>
    );
};

SharedTable.defaultProps = {
    sizePerPage: 25,
    totalCount: 0,
    data: [],
    columns: [],
    page: 1,
    onShowDetails: () => {},
};

SharedTable.propTypes = {
    sizePerPage: PropTypes.number,
    page: PropTypes.number.isRequired,
    totalCount: PropTypes.number.isRequired,
    data: PropTypes.arrayOf(PropTypes.object).isRequired,
    columns: PropTypes.arrayOf(PropTypes.object).isRequired,
    onChangePagination: PropTypes.func,
    onChangePageSize: PropTypes.func,
    isLoading: PropTypes.bool,
    onShowDetails: PropTypes.func,
};

export default SharedTable;
