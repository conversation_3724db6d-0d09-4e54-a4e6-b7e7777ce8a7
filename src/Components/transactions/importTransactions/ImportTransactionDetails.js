import React from "react";
import { But<PERSON>, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import PropTypes from "prop-types";
import DetailsAsLabelValue from "Components/common/detailsAsLabelValue/DetailsAsLabelValue";

const ImportTransactionDetails = ({
    showModel,
    hideModal,
    selectedTransaction,
}) => {
    return (
        <Modal show={showModel} onHide={hideModal} size="lg" centered>
            <Modal.Header closeButton>
                <Modal.Title>Import Transaction Details</Modal.Title>
            </Modal.Header>
            <Modal.Body className="mt-3">
                <div className="d-flex justify-content-between mt-3">
                    <div className="w-50 mr-2">
                        <DetailsAsLabelValue
                            label="Job Id"
                            value={selectedTransaction?.jobId || "~ unknown"}
                        />
                    </div>
                    <div className="w-50 ml-2">
                        <DetailsAsLabelValue
                            label="Created On"
                            value={
                                selectedTransaction?.createdOn || "~ unknown"
                            }
                        />
                    </div>
                </div>
                <div className="d-flex flex-row justify-content-between mt-3">
                    <div className="w-50 mr-2">
                        <DetailsAsLabelValue
                            label="Merchant"
                            value={
                                selectedTransaction?.merchantText || "~ unknown"
                            }
                        />
                    </div>
                    <div className="w-50 ml-2">
                        <DetailsAsLabelValue
                            label="Merchant Location"
                            value={
                                selectedTransaction?.merchantLocationText ||
                                "~ unknown"
                            }
                        />
                    </div>
                </div>
                <div className="d-flex flex-row justify-content-between mt-3">
                    <div className="w-50 mr-2">
                        <DetailsAsLabelValue
                            label="Transaction Type"
                            value={selectedTransaction?.type || "~ unknown"}
                        />
                    </div>
                    <div className="w-50 ml-2">
                        <DetailsAsLabelValue
                            label="Total Records"
                            value={selectedTransaction?.totalRecords || 0}
                        />
                    </div>
                </div>
                <div className="d-flex flex-row justify-content-around mt-3">
                    <div className="w-100 mr-2">
                        <DetailsAsLabelValue
                            label="Valid Records"
                            value={selectedTransaction?.validRecords || 0}
                        />
                    </div>
                    <div className="w-100 ml-2">
                        <DetailsAsLabelValue
                            label="Invalid Records"
                            value={selectedTransaction?.invalidRecords || 0}
                        />
                    </div>
                </div>
            </Modal.Body>
            <Modal.Footer className="mt-4">
                <Button size="sm" variant="primary" onClick={hideModal}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

ImportTransactionDetails.propTypes = {
    showModel: PropTypes.func,
    hideModal: PropTypes.func,
    selectedTransaction: PropTypes.object,
};

export default ImportTransactionDetails;
