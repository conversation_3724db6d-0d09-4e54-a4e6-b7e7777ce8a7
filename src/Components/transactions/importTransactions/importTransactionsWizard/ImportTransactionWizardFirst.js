import React, { useState, useRef, useContext, useImperativeHandle, useMemo, useEffect, forwardRef, useCallback, } from "react";
import {Form, FormSelect} from '@shoutout-labs/shoutout-themes-enterprise';
import { DataContext,UserContext } from "Contexts";
import { ImportTransactionContext } from "../context/ImportTransactionsContext";
import { TransactionTypes} from "Data";
import FileUploaderComponent from "Components/common/fileUploaderComponent/FileUploaderComponent";
import { toast } from "react-toastify";
import { getSubTransactionTypes, importTransactionFileUpload } from "Services";
import { toTitleCase } from "../../../../Utils";


const ImportTransactionWizardFirst =  forwardRef(({reset,fileToken,onChangeMerchantLocation,onChangeSubTransactionType,onChangeTransactionType, onChangeMerchant,merchants, merchant, transactionType, setTransactionsFile, transactionsFile, setFileData, regionId, transactionSubTypeId, setFileToken, setColumnNames }, ref) => {
    const formRef = useRef();
    const [validated, setValidated] = useState(false);
    const [validatedFile, setValidatedFile] = useState(false);
    const [fileUploading, setFileUploading] = useState(false);
    const [loadingSubTransaction, setLoadingSubTransaction] = useState(false);
    const [transactionTypeChanged, setTransactionTypeChanged] = useState(false);
    const [subTransactionTypes, setSubTransactionTypes] = useState([]);

    const loadSubTransactions = useCallback(async () => {
        try {
              const queryObj = {
                ...(transactionType?.length!==0?{
                    transactionType : transactionType[0]?.value
                }:{}),
              }
              setLoadingSubTransaction(true);
              setSubTransactionTypes([]);
              onChangeSubTransactionType([]);
              const transactions = await getSubTransactionTypes(queryObj);
              setSubTransactionTypes(transactions?.items);
         } catch (e) {
          console.error(e);
          toast.error("Could not load the sub transactions. Please retry later");
        }finally {
            setLoadingSubTransaction(false);
        }
    },[setSubTransactionTypes, transactionType, setLoadingSubTransaction, onChangeSubTransactionType]);

    const onChangeTransactionsFile= useCallback(async (file)=>{
        if(file){
            try {
                setFileUploading(true);
                setTransactionsFile(file);
                onChangeMerchantLocation([]);
                const fileFormData = new FormData();
                fileFormData.append('file', new Blob([file], { type: 'text/csv' }));
                const queryObj = { regionId : regionId, merchantId : merchant[0]?.value }
                const uploadResponse = await importTransactionFileUpload(queryObj,fileFormData);
                setFileToken(uploadResponse.fileToken);
                setFileData(uploadResponse.headers);
                setFileUploading(false);
            } catch (e) {
                setFileUploading(false);
                toast.error("Can not getting attributes. Please try again");
            }
        }else {
            setFileUploading(true);
            reset();
            setFileUploading(false);
        }
    },[merchant, onChangeMerchantLocation, regionId, setFileData, setFileToken, setTransactionsFile,setFileUploading,reset]);

    const onChangeTransaction=useCallback((event)=>{
        if(transactionType.value!==event[0].value){
            onChangeTransactionType(event);
            if(event[0].value===TransactionTypes.COLLECTION){
                setColumnNames({
                    'loyaltyCard': { systemAttributeName: 'LOYALTY_CARD', fileColumnName: "",visibleAttributeName:"Card No"},
                    'billAmount': { systemAttributeName: 'BILL_AMOUNT', fileColumnName: "",visibleAttributeName:"Bill Amount"},
                    'transactionDate': { systemAttributeName: 'TRANSACTION_DATE', fileColumnName: "",visibleAttributeName:"Transaction Date"},
                    'billReference': { systemAttributeName: 'BILL_REFERENCE', fileColumnName: "",visibleAttributeName:"Bill Reference"},
                    'pointAmount': { systemAttributeName: 'POINTS_AMOUNT', fileColumnName: "", visibleAttributeName: "Point Amount" }

                });
            }else {
                setColumnNames({
                    'loyaltyCard': { systemAttributeName: 'LOYALTY_CARD', fileColumnName: "",visibleAttributeName:"Card No"},
                    'pointAmount': { systemAttributeName: 'POINTS_AMOUNT', fileColumnName: "", visibleAttributeName: "Points Amount" },
                    'transactionDate': { systemAttributeName: 'TRANSACTION_DATE', fileColumnName: "",visibleAttributeName:"Transaction Date"},
                });

            }
            setTransactionTypeChanged(true);
        }
    },[onChangeTransactionType,transactionType,setTransactionTypeChanged,setColumnNames]);


    useImperativeHandle(ref, () => ({
            isValidated() {
                const formValid = formRef.current.checkValidity()&&!!transactionsFile&&fileToken!=="";
                if (!formValid) {
                    setValidated(true);
                    if(transactionsFile===""){
                        setValidatedFile(true);
                    }
                }
                return formValid;
            },
            async onClickNext() {
                return new Promise(async (resolve, reject) => {
                    resolve();
                });
            }
        })
    );

    useEffect(() => {
        if(transactionTypeChanged){
            loadSubTransactions();
            setTransactionTypeChanged(false);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [transactionTypeChanged]);

    useEffect(() => {
        if(!!transactionsFile){
            setValidatedFile(false);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [transactionsFile]);

    return (
        <div ref={ref} className="mt-5">
            <Form ref={formRef} validated={validated}>
                <Form.Group controlId="merchant">
                    <Form.Label className="d-flex align-items-center">
                        Merchant
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <FormSelect
                        clearButton
                        onChange={onChangeMerchant}
                        multiple={false}
                        labelKey="label"
                        id="merchants"
                        options={merchants ? merchants.map(merchant => (
                            {
                                label: merchant?.merchantName||"",
                                value: merchant?._id,
                            }
                        )):[]}
                        placeholder="Select Merchant"
                        required
                        selected={merchant}
                    />
                </Form.Group>
                <Form.Group controlId="transaction-type">
                    <Form.Label className="d-flex align-items-center">
                        Transaction Type
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <FormSelect
                        clearButton
                        id="transactionType"
                        onChange={onChangeTransaction}
                        multiple={false}
                        disabled={loadingSubTransaction}
                        options={[
                            {
                                label:"Collection",
                                value:TransactionTypes.COLLECTION,
                            },
                            {
                                label:"Adjustment",
                                value:TransactionTypes.ADJUSTMENT,
                            }
                        ]}
                        placeholder=" Select Transaction Type"
                        required
                        selected={transactionType}
                    />
                </Form.Group>
                <Form.Group controlId="subTransactionType">
                    <Form.Label className="d-flex align-items-center">
                        Sub Transaction Type
                        <div className="ml-1 text-danger">*</div>
                    </Form.Label>
                    <FormSelect
                        clearButton
                        onChange={onChangeSubTransactionType}
                        id="subTransactionType"
                        disabled={loadingSubTransaction}
                        options={subTransactionTypes.length > 0 ? subTransactionTypes.map(transaction => (
                            {
                                label:toTitleCase(transaction.name),
                                value:transaction._id,
                            }
                        )):[]}
                        placeholder="Select Sub Transaction Type"
                        required
                        selected={transactionSubTypeId}
                    />
                </Form.Group>
                <Form.Group>
                    <Form.Label>Data File {!transactionsFile&&<span className="text-danger">*</span>}</Form.Label>
                    {merchant.length!==0? (!fileUploading ?
                            <FileUploaderComponent onChange={onChangeTransactionsFile} defaultSrc={transactionsFile} /> :
                            <span className="d-flex text-primary p-2">
                         Uploading....
                       </span>
                    ) : <p className="text-danger">Please select a merchant</p>}
                    {validatedFile && <span className="text-danger p-2">
                       Please select a file.
                    </span>}
                </Form.Group>
                <div className="">
                    <p>Please check your CSV file meets these requirements</p> <br/>
                    <p>File extention must be <span className="text-secondary">.csv</span>{`(eg: filename.csv)`}</p>
                    <p>Must be less than <span className="text-secondary">25MB</span> or <span className="text-secondary">400,000</span>rows. Split larger files into multiple files</p>
                    <p>The first row must contain <span className="text-secondary">column names</span>{`(eg: Points, Loyalty ID)`}</p>
                </div>
            </Form>
        </div>
    )
});

const ImportTransactionWizardFirstContainer = (props, ref) => {
    const { merchants } = useContext(DataContext);
    const { regionId } = useContext(UserContext);
    const { onChangeSubTransactionType, reset,onChangeMerchantLocation, merchant, transactionType, onChangeTransactionType, setValues, setTransactionsFile, transactionsFile, setFileData, setFileToken, transactionSubTypeId, setColumnNames, onChangeMerchant,fileToken } = useContext(ImportTransactionContext);

    return useMemo(() => {
        return <ImportTransactionWizardFirst ref={ref} {...props} reset={reset} fileToken={fileToken} onChangeMerchantLocation ={onChangeMerchantLocation} onChangeSubTransactionType={onChangeSubTransactionType} merchants = {merchants} merchant = {merchant} transactionType = {transactionType} setValues = {setValues} setTransactionsFile = {setTransactionsFile} transactionsFile = {transactionsFile} setFileData = {setFileData} setFileToken = {setFileToken} regionId = {regionId} transactionSubTypeId = {transactionSubTypeId} setColumnNames={setColumnNames} onChangeMerchant={onChangeMerchant} onChangeTransactionType={onChangeTransactionType} />
    }, [onChangeMerchantLocation ,reset,fileToken, onChangeSubTransactionType, onChangeTransactionType,merchants, merchant, transactionType, setValues, setTransactionsFile, transactionsFile, setFileData, setFileToken, setColumnNames, transactionSubTypeId, regionId, onChangeMerchant, props, ref]);
};

export default ImportTransactionWizardFirstContainer;

export {ImportTransactionWizardFirst};
