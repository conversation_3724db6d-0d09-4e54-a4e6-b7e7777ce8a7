import React, {
    useState,
    useRef,
    useContext,
    useImperativeHandle,
    useCallback,
    useMemo,
    forwardRef,
} from "react";
import {
    Form,
    FormCheck,
    Card,
    Button,
    IcIcon,
    FormSelect,
} from "@shoutout-labs/shoutout-themes-enterprise";
import { faCheckCircle } from "FaICIconMap";
import { DataContext } from "Contexts";
import { ImportTransactionContext } from "../context/ImportTransactionsContext";

const locationOptions = {
    STATIC: "STATIC",
    DYNAMIC: "DYNAMIC",
};

const ImportTransactionWizardSecond = forwardRef(({ onChangeMerchantLocation, locationOption, selectedMerchantLocation, setValues, fileHeaders, fileSample, setSelectedLocationField, selectedLocationField, locations, }, ref) => {
        const formRef = useRef();
        const [validated, setValidated] = useState(false);

        const onChangeHandler = useCallback(
            (e) => {
                setValues(e);
            },
            [setValues]
        );

        const onSelectAttribute = useCallback(
            (e) => {
                e.preventDefault();

                const header = e.currentTarget.dataset.id;
                setSelectedLocationField(header);
            },
            [setSelectedLocationField]
        );

        useImperativeHandle(ref, () => ({
            isValidated() {
                const formValid =
                    formRef.current.checkValidity() &&
                    (locationOption === locationOptions.DYNAMIC
                        ? !!selectedLocationField
                        : true);
                if (!formValid) {
                    setValidated(true);
                }
                return formValid;
            },
            async onClickNext() {
                return new Promise(async (resolve, reject) => {
                    resolve();
                });
            },
        }));

        return (
            <div ref={ref} className="mt-5">
                <Form ref={formRef} validated={validated}>
                    <Form.Group controlId="selectedLocation">
                        <FormCheck
                            className="rounded-0 input-check mr-4"
                            custom
                            onChange={onChangeHandler}
                            id={locationOptions.STATIC}
                            value={locationOptions.STATIC}
                            label="Select the merchant location from the dropdown"
                            name="locationOption"
                            checked={locationOption === locationOptions.STATIC}
                            type="radio"
                            required
                        />
                        <FormSelect
                            clearButton
                            onChange={onChangeMerchantLocation}
                            id="merchantLocation"
                            required={locationOption === locationOptions.STATIC}
                            disabled={locationOption !== locationOptions.STATIC}
                            options={locations.map((merchantLocation) => ({
                                label: merchantLocation.locationName,
                                value: merchantLocation._id,
                                name: "selectedMerchantLocation",
                            }))}
                            placeholder="Select Location"
                            selected={selectedMerchantLocation}
                        />
                        <FormCheck
                            className="rounded-0 input-check mt-4"
                            custom
                            onChange={onChangeHandler}
                            id={locationOptions.DYNAMIC}
                            value={locationOptions.DYNAMIC}
                            label="File- field selection"
                            name="locationOption"
                            checked={locationOption === locationOptions.DYNAMIC}
                            type="radio"
                        />
                    </Form.Group>
                    <div>
                        {fileHeaders.map((header) => {
                            return (
                                <Card key={header} className="mt-2">
                                    <Card.Body>
                                        <div className="d-flex flex-row">
                                            <div className="flex-fill text-left">
                                                <strong>{header}</strong>
                                                {fileSample.map((data) => {
                                                    return (
                                                        <p key={data[header]}>
                                                            {data[header]}
                                                        </p>
                                                    );
                                                })}
                                            </div>

                                            <div className="my-auto text-right">
                                                <Button
                                                    key={header + "-m"}
                                                    id={header + "-did"}
                                                    data-id={header}
                                                    onClick={onSelectAttribute}
                                                    className="map-selector"
                                                    variant={
                                                        !selectedLocationField &&
                                                        locationOption ===
                                                            locationOptions.DYNAMIC &&
                                                        validated
                                                            ? "outline-danger"
                                                            : "outline-primary"
                                                    }
                                                    disabled={
                                                        locationOption !==
                                                        locationOptions.DYNAMIC
                                                    }
                                                >
                                                    {selectedLocationField ===
                                                        header && (
                                                        <span className="header-map-correct text-success mr-2">
                                                            &nbsp;&nbsp;
                                                            <IcIcon
                                                                icon={
                                                                    faCheckCircle
                                                                }
                                                            />
                                                        </span>
                                                    )}
                                                    Select As Location
                                                </Button>
                                            </div>
                                        </div>
                                    </Card.Body>
                                </Card>
                            );
                        })}
                    </div>
                </Form>
            </div>
        );
    }
);

const ImportTransactionWizardSecondContainer = (props, ref) => {
    const { locationOption, onChangeMerchantLocation, selectedMerchantLocation, setValues, fileHeaders, fileSample, setSelectedLocationField, selectedLocationField, merchant} = useContext(ImportTransactionContext);

    const { merchantLocations } = useContext(DataContext);

    const locations = useMemo(
        () => Object.values(merchantLocations[merchant[0]?.value] || {}),
        [merchant, merchantLocations]
    );

    return useMemo(() => {
        return <ImportTransactionWizardSecond ref={ref} {...props} onChangeMerchantLocation={onChangeMerchantLocation} locationOption={locationOption} selectedMerchantLocation={selectedMerchantLocation} setValues={setValues} fileHeaders={fileHeaders} fileSample={fileSample} setSelectedLocationField={setSelectedLocationField} selectedLocationField={selectedLocationField} locations={locations} />
    }, [locationOption, selectedMerchantLocation, setValues, fileHeaders, fileSample, setSelectedLocationField, selectedLocationField, locations, onChangeMerchantLocation, props, ref,]);
};

export default ImportTransactionWizardSecondContainer;

export { ImportTransactionWizardSecond };
