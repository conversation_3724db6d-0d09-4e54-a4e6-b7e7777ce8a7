import React, { useCallback, useMemo, useState } from "react";
import { useHistory } from "react-router-dom";
import moment from "moment";
import PropTypes from "prop-types";
import { Button } from "@shoutout-labs/shoutout-themes-enterprise";
import {
    faCalendarAlt,
    faBuilding,
    faFile,
    faMap,
    faObjectGroup,
} from "FaICIconMap";
import { TransactionTabs, TransactionTypeColorCode } from "Data";
import { toTitleCase } from "Utils";
import NameIconTemplateForCamelCase from "Components/utils/table/NameIconTemplateForCamelCase";
import { applyBadgeStyling } from "Components/utils/styling/Styling";
import ImportTransactionDetails from "../importTransactions/ImportTransactionDetails";
import TransactionsTable from "./TransactionsTable";

const importTransactionsCols = [
    { name: "createdDate", headerStyle: { width: "11%" }, icon: faCalendarAlt },
    { name: "merchant", icon: faBuilding, headerStyle: { width: "9%" } },
    { name: "merchantLocation", headerStyle: { width: "10%" }, icon: faMap },
    { name: "type", headerStyle: { width: "7%" }, icon: faObjectGroup },
    {
        name: "totalRecords",
        icon: faFile,
        headerStyle: { width: "8%" },
        style: { textAlign: "center" },
    },
    {
        name: "validRecords",
        icon: faFile,
        headerStyle: { width: "8%" },
        style: { textAlign: "center" },
    },
    {
        name: "invalidRecords",
        icon: faFile,
        headerStyle: { width: "10%" },
        style: { textAlign: "center" },
    },
];

const defaultColumnTemplate = ({ name, icon, ...rest }) => ({
    dataField: name,
    text: NameIconTemplateForCamelCase({ name, icon }),
    sort: false,
    ...rest,
});

const ImportTransactionJobTable = ({
    tab,
    loadData,
    setSkip,
    skip,
    limit,
    setLimit,
    transactionsData,
    totalItems,
    isLoading,
    onChangePagination,
}) => {
    const [showImportTransactionDetails, setShowImportTransactionDetails] =
        useState(false);
    const [selectedTransaction, setSelectedTransaction] = useState({});
    const history = useHistory();

    const columns = useMemo(() => {
        const columns = [];

        if (tab === TransactionTabs.IMPORT_TRANSACTIONS) {
            importTransactionsCols.forEach((item) => {
                columns.push(defaultColumnTemplate(item));
            });
        }

        return columns.sort((a, b) => a.order - b.order);
    }, [tab]);

    const hideDetailsModal = useCallback(() => {
        setShowImportTransactionDetails(false);
        setSelectedTransaction({});
    }, [setShowImportTransactionDetails, setSelectedTransaction]);

    const navigateTransactionHistory = useCallback(
        (event) => {
            event.stopPropagation();
            history.push("/transactions/history", {
                importJobId: event.target.dataset.job_id,
            });
        },
        [history]
    );

    const navigateInvalidTransactions = useCallback(
        (event) => {
            event.stopPropagation();
            history.push("/transactions/invalid", {
                importJobId: event.target.dataset.job_id,
            });
        },
        [history]
    );

    const Data = useMemo(
        () =>
            transactionsData.map((transaction) => {
                return {
                    id: transaction?._id,
                    jobId: transaction?._id,
                    date: moment(transaction?.completedOn).format("LLL"),
                    createdDate: moment(transaction?.createdOn).format("LLL"),
                    transactionType: toTitleCase(transaction?.transactionType),
                    type: applyBadgeStyling({
                        text: toTitleCase(transaction?.transactionType),
                        variant:
                            TransactionTypeColorCode[
                                transaction?.transactionType
                            ],
                    }),
                    merchantText: transaction?.merchant?.merchantName,
                    merchantLocationText:
                        transaction?.merchantLocation?.locationName,
                    merchant:
                        transaction?.merchant?.merchantName ||
                        applyBadgeStyling({
                            customValue: "Merchant not found.",
                        }),
                    merchantLocation:
                        transaction?.merchantLocation?.locationName ||
                        applyBadgeStyling({
                            customValue: "Location not found.",
                        }),
                    totalRecords: transaction?.totalRecordsCount || 0,
                    validRecords: (
                        <Button
                            className="m-0 p-0 shadow-none"
                            variant="link"
                            size="sm"
                            data-job_id={transaction?._id}
                            onClick={navigateTransactionHistory}
                        >
                            {transaction?.successRecordsCount || 0}
                        </Button>
                    ),
                    invalidRecords: (
                        <Button
                            className="m-0 p-0 shadow-none"
                            variant="link"
                            size="sm"
                            data-job_id={transaction?._id}
                            onClick={navigateInvalidTransactions}
                        >
                            {transaction?.failedRecordsCount || 0}
                        </Button>
                    ),
                    maxTransactionValue: transaction?.maxTransactionValue,
                    minTransactionValue: transaction?.minTransactionValue,
                    total: transaction?.totalRecordsCount,
                    failed: transaction?.failedRecordsCount,
                };
            }),
        [
            transactionsData,
            navigateTransactionHistory,
            navigateInvalidTransactions,
        ]
    );

    const onPageChange = useCallback(
        (newSkip) => {
            onChangePagination({
                skip: newSkip,
            });
        },
        [onChangePagination]
    );

    const onSizePerPageChange = useCallback(
        (newLimit) => {
            onChangePagination({
                limit: newLimit,
            });
        },
        [onChangePagination]
    );

    return (
        <>
            <div className="mt-5">
                <TransactionsTable
                    columns={columns}
                    data={Data}
                    sizePerPage={limit}
                    page={skip}
                    onChangePagination={onPageChange}
                    onChangePageSize={onSizePerPageChange}
                    totalCount={totalItems}
                    isLoading={isLoading}
                    setSelectedTransaction={setSelectedTransaction}
                    tab={tab}
                    setShowImportTransactionDetails={
                        setShowImportTransactionDetails
                    }
                />
            </div>
            <ImportTransactionDetails
                showModel={showImportTransactionDetails}
                hideModal={hideDetailsModal}
                selectedTransaction={selectedTransaction}
            />
        </>
    );
};

ImportTransactionJobTable.propTypes = {
    tab: PropTypes.string.isRequired,
    loadData: PropTypes.func,
    setSkip: PropTypes.func,
    skip: PropTypes.number,
    limit: PropTypes.number,
    setLimit: PropTypes.func,
    transactionsData: PropTypes.arrayOf(PropTypes.object),
    totalItems: PropTypes.number,
    isLoading: PropTypes.bool,
    onChangePagination: PropTypes.func,
};
export default ImportTransactionJobTable;
