import React, { useCallback, useContext, useEffect, useState } from "react";
import { Button, Form, IcIcon, Modal } from "@shoutout-labs/shoutout-themes-enterprise";
import { faPlus, faTimes } from "../../../FaICIconMap";
import { UserContext } from "../../../Contexts";
import { exportTransactions } from "../../../Services";
import { toast } from "react-toastify";
import { TransactionStatus, TransactionTabs } from "../../../Data";
import { getQueryFilters } from "../../../Utils";

const TransactionsExport = ({
                                showExportTransactions,
                                handleClose,
                                isExporting,
                                setIsExporting,
                                appliedFilters,
                                searchText,
                                tab,
                            }) => {
    const [validated, setValidated] = useState(false);
    const [notificationEmails, setNotificationEmails] = useState([]);
    const { loggedUser, selectedRegion } = useContext(UserContext);


    const onChangeEmail = useCallback((event) => {
        event.preventDefault();
        const emails = [...notificationEmails];
        emails[event.target.name] = event.target.value;
        setNotificationEmails(emails);
    }, [notificationEmails, setNotificationEmails]);

    const addNewNotificationEmail = useCallback(() => setNotificationEmails([...notificationEmails, ""]), [notificationEmails, setNotificationEmails]);

    const onRemoveEmail = useCallback((event) => {
        event.preventDefault();
        setNotificationEmails(notificationEmails.filter((email, i) => i !== parseInt(event.currentTarget.id)));
    }, [notificationEmails, setNotificationEmails]);

    const onSubmitExportTransaction = useCallback(async (e) => {
        e.preventDefault();
        if (e.target.checkValidity()) {
            try {
                setIsExporting(true);
                let queryObj = {
                    regionId: selectedRegion._id,
                    ...(tab === TransactionTabs.TRANSACTION_HISTORY ? { status: TransactionStatus.VALID } : {}),
                    ...(tab === TransactionTabs.INVALID_TRANSACTIONS ? { status: TransactionStatus.INVALID } : {}),
                    ...(searchText ? { searchKey: searchText } : {}),
                    notificationEmails: notificationEmails,
                };

                if (appliedFilters?.length !== 0) {
                    queryObj = { ...queryObj, ...getQueryFilters(appliedFilters) };
                }
                await exportTransactions(queryObj);
                toast.success("You will receive the exported file to the provided email");
                handleClose();
            } catch (e) {
                console.error(e);
                toast.error(
                    <div>
                        Failed to export transaction list!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>);
            }finally {
                setIsExporting(false);
            }
        } else {
            setValidated(true);
        }
    }, [handleClose, setIsExporting, notificationEmails, appliedFilters, searchText, selectedRegion?._id, tab]);


    useEffect(() => {
        if (loggedUser.hasOwnProperty("emailVerified") && loggedUser.hasOwnProperty("email")) {
            setNotificationEmails([loggedUser.email]);
        } else {
            setNotificationEmails([""]);
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (
        <Modal
            show={showExportTransactions}
            onHide={handleClose}
            className="create-user border-0"
            centered
            backdrop="static"
        >
            <Modal.Header closeButton={!isExporting}>
                <Modal.Title>Export Transaction</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form onSubmit={onSubmitExportTransaction} validated={validated} noValidate>
                    <Form.Group controlId="notification-emails">
                        <Form.Label>Notification Emails</Form.Label>
                        {notificationEmails.map((email, index) => {
                                return (
                                    <Form.Group controlId={`user-notification-emails-${index}`}
                                                key={`user-notification-emails-${index}`}>
                                        <div className="d-flex flex-row mt-3">
                                            <Form.Control type="email" value={email} name={index} required
                                                          onChange={onChangeEmail} />
                                            <div className="ml-2 mt-1">
                                                <Button variant="link" size="sm" id={index} onClick={onRemoveEmail}
                                                        className="text-danger" disabled={index === 0}>
                                                    <IcIcon size="md" icon={faTimes} />
                                                </Button>
                                            </div>
                                        </div>
                                    </Form.Group>
                                );
                            },
                        )}
                        <div>
                            <Button size="sm" variant="" onClick={addNewNotificationEmail} type="button">
                                <IcIcon className="mr-2 text-primary" size="md" icon={faPlus} />
                                Add new notification email.
                            </Button>
                        </div>
                    </Form.Group>
                    <div className="d-flex justify-content-end">
                        <Button variant="danger" size="sm" type="button" disabled={isExporting}
                                onClick={handleClose}>Cancel</Button>
                        <Button className="ml-2" type="submit" variant="outline-primary" size="sm"
                                disabled={isExporting}> {isExporting ? "Exporting..." : "Export Transactions"}</Button>
                    </div>
                </Form>
            </Modal.Body>
        </Modal>
    );
};
export default TransactionsExport;