import React, {useState, useCallback} from "react";
import paginationFactory, {
  PaginationProvider,
} from "react-bootstrap-table2-paginator";
import { BootstrapTable } from "@shoutout-labs/shoutout-themes-enterprise";
import { BootstrapTableOverlay } from "../../utils/UtilComponents";
import ToolkitProvider from "react-bootstrap-table2-toolkit";
import PropTypes from "prop-types";
import { TransactionTabs} from "Data";
import SizePerPageRenderer from "../../utils/table/sizePerPageRenderer/SizePerPageRenderer";
import "./TransactionsTable.scss"

const NoDataIndication = ({ loading }) => {
    if (loading) return null;
    return <div>No data found.</div>;
  };

const TransactionsTable = ({
  columns,
  data,
  sizePerPage,
  page,
  onChangePagination,
  onChangePageSize,
  totalCount,
  isLoading,
  // setSelectedTransactionId,
  setSelectedTransaction,
  tab,
  setShowTransactionDetails,
  setShowInvalidTransactionDetails,
  setShowImportTransactionDetails
}) => {

 // eslint-disable-next-line
  const [sortingOrder, setSortingOrder] = useState();

  const options = {
    page: page,
    sizePerPage: sizePerPage,
    sizePerPageRenderer:SizePerPageRenderer,
    totalSize: totalCount,
    paginationSize: 5,
    pageStartIndex: 1,
    showTotal: true,
    withFirstAndLast: true,
    sizePerPageList: [
      {
        text: "25",
        value: 25,
      },
      {
        text: "50",
        value: 50,
      },
      {
        text: "100",
        value: 100,
      },
    ],

    onPageChange: onChangePagination,
    onSizePerPageChange: onChangePageSize,
  };

  const onRowClick = useCallback((rowData) => {
    
    setSelectedTransaction(rowData);

    if(tab === TransactionTabs.TRANSACTION_HISTORY){
      setShowTransactionDetails(true);
      // setSelectedTransactionId(rowData.transactionId);
    }else if (tab === TransactionTabs.INVALID_TRANSACTIONS){
      setShowInvalidTransactionDetails(true);
      // setSelectedTransactionId(rowData.transactionId);
    }else if(tab === TransactionTabs.IMPORT_TRANSACTIONS){
      setShowImportTransactionDetails(true);
    }

    },[
      // setSelectedTransactionId,
      setSelectedTransaction,
      tab,
      setShowTransactionDetails,
      setShowInvalidTransactionDetails,
      setShowImportTransactionDetails
    ]);

  const tableRowEvents = {
    onClick: (e, row) => {
      onRowClick(row);
    },
  };

  const handleTableChange = useCallback(
    (type, data) => {

      switch (type) {
        case "sort": {
          setSortingOrder({
            sortBy: data.sortField,
            sortDirection: data.sortOrder,
          });
          break;
        }
        default: {
        }
      }
    },
    [setSortingOrder]
  );

  return (
    <>
      <div className="d-flex justify-content-start">
            <div className="w-100">
                <div className="h-100 transactions-table">
                    <PaginationProvider
                        pagination={paginationFactory(options)}
                        keyField="id"
                        columns={columns}
                        data={data}
                    >
                        {({ paginationTableProps }) => (
                            <ToolkitProvider
                                keyField="id"
                                data={data}
                                columns={columns}
                                columnToggle
                            >
                                {(props) => (
                                        <div>
                                            <BootstrapTable
                                                {...paginationTableProps}
                                                remote={{ search: true, pagination: true }}
                                                onTableChange={handleTableChange}
                                                loading={isLoading}
                                                rowEvents={tableRowEvents}
                                                keyField="id"
                                                noDataIndication={() => (
                                                    <NoDataIndication loading={isLoading} />
                                                )}
                                                overlay={BootstrapTableOverlay}
                                                {...props.baseProps}
                                                />
                                            </div>
                                        )}
                                    </ToolkitProvider>
                                )}
                        </PaginationProvider>
                    </div>
                </div>
            </div>
    </>
  );
};

TransactionsTable.defaultProps = {
  sizePerPage: 25,
  totalCount: 0,
  data: [],
  columns: [],
  page: 1,
};

TransactionsTable.propTypes = {
  /**
   * Limit per page
   */
  sizePerPage: PropTypes.number,

  /**
   * Current page
   */
  page: PropTypes.number.isRequired,

  /**
   * Total dataset size
   */
  totalCount: PropTypes.number.isRequired,

  /**
   * dataset
   */
  data: PropTypes.arrayOf(PropTypes.object).isRequired,

  /**
   * column list
   */
  columns: PropTypes.arrayOf(PropTypes.object).isRequired,
  /**
   * on change pagination callback
   */
  onChangePagination: PropTypes.func,

  /**
   * on page size change callback
   */
  onChangePageSize: PropTypes.func,

  /**
   * on loading data
   */
  isLoading: PropTypes.bool,
};

export default TransactionsTable;
