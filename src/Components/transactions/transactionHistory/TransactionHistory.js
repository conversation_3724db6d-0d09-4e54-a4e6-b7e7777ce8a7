import React, { useState, useCallback, useEffect, useContext } from "react";
import { useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import { Heading } from "@shoutout-labs/shoutout-themes-enterprise";
import { UserContext } from "Contexts";
import { TransactionTabs, TransactionStatus } from "Data";
import { useToggle } from "Hooks";
import { getTransactions, getTransactionsCount } from "Services";
import { getQueryFilters } from "Utils";
import BaseLayout from "Layout/BaseLayout";
import TopPanel from "../shared/TopPanel";
import TransactionTableViewContainer from "../shared/TransactionTableViewContainer";

const defaultSkip = 1,
    defaultLimit = 25;
let searchStateUpdateTimeout;

const TransactionHistory = () => {
    const { selectedRegion } = useContext(UserContext);
    const [isLoading, setIsLoading] = useState(false);
    const location = useLocation();
    const [limit, setLimit] = useState(defaultLimit);
    const [skip, setSkip] = useState(defaultSkip);
    const [showFilters, toggleShowFilters] = useToggle(false);
    const [searchText, setSearchText] = useState("");
    const [data, setData] = useState([]);
    const [totalItems, setTotalItems] = useState(0);
    const [appliedFilters, setAppliedFilters] = useState([]);

    const loadTransactionHistoryData = useCallback(
        async (
            { limit, skip, searchText = "" },
            filters = [],
            reloadCount = true
        ) => {
            let queryObj = {
                status: TransactionStatus.VALID,
                regionId: selectedRegion._id,
                searchKey: searchText,
            };

            if (filters.length !== 0) {
                queryObj = { ...queryObj, ...getQueryFilters(filters) };
            }

            try {
                setIsLoading(true);
                const promises = [
                    (async () => {
                        const transactions = await getTransactions(
                            queryObj,
                            limit,
                            (skip - 1) * limit
                        );
                        setData(transactions.items);
                        return Promise.resolve();
                    })(),
                ];
                if (reloadCount) {
                    promises.push(
                        (async () => {
                            const transactionCountResponse =
                                await getTransactionsCount(queryObj);
                            setTotalItems(transactionCountResponse.count);
                            return Promise.resolve();
                        })()
                    );
                }
                await Promise.all(promises);
                setIsLoading(false);
            } catch (e) {
                console.error(e);
                setIsLoading(false);
                toast.error(
                    <div>
                        Failed to load transactions history!
                        <br />
                        {e.message
                            ? `Error: ${e.message}`
                            : "Please try again later."}
                    </div>
                );
            }
        },
        [selectedRegion._id, setIsLoading, setTotalItems]
    );

    const onSearch = useCallback(
        (searchText) => {
            if (searchStateUpdateTimeout) {
                clearTimeout(searchStateUpdateTimeout);
            }
            setSearchText(searchText);

            searchStateUpdateTimeout = setTimeout(async () => {
                setSkip(defaultSkip);
                await loadTransactionHistoryData(
                    {
                        limit,
                        skip: defaultSkip,
                        searchText: searchText,
                    },
                    appliedFilters,
                    true
                );
            }, 2000);
        },
        [limit, appliedFilters, setSearchText, loadTransactionHistoryData]
    );

    useEffect(() => {
        if (!location?.state?.importJobId && appliedFilters.length === 0) {
            setSkip(defaultSkip);
            loadTransactionHistoryData(
                { limit, skip: defaultSkip, searchText },
                [],
                true
            );
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location?.state?.importJobId, appliedFilters]);

    useEffect(() => {
        if (appliedFilters.length > 0) {
            setSkip(defaultSkip);
            loadTransactionHistoryData(
                { limit, skip: defaultSkip, searchText },
                appliedFilters,
                true
            );
        }
        //eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appliedFilters]);

    useEffect(() => {
        if (location?.state?.importJobId) {
            setAppliedFilters([{ importJobId: location.state.importJobId }]);
            toggleShowFilters(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location?.state?.importJobId]);

    return (
        <BaseLayout
            topLeft={<Heading text="Transaction History" />}
            bottom={
                <div className="container-fluid transaction-history mt-3">
                    <TopPanel
                        tab={TransactionTabs.TRANSACTION_HISTORY}
                        limit={limit}
                        showFilters={showFilters}
                        searchText={searchText}
                        isLoading={isLoading}
                        appliedFilters={appliedFilters}
                        setSkip={setSkip}
                        setSearchText={onSearch}
                        toggleShowFilters={toggleShowFilters}
                        loadTransactionData={loadTransactionHistoryData}
                        setAppliedFilters={setAppliedFilters}
                    />
                    <TransactionTableViewContainer
                        tab={TransactionTabs.TRANSACTION_HISTORY}
                        loadData={loadTransactionHistoryData}
                        setSkip={setSkip}
                        skip={skip}
                        limit={limit}
                        setLimit={setLimit}
                        transactionsData={data}
                        totalItems={totalItems}
                        isLoading={isLoading}
                        searchText={searchText}
                        appliedFilters={appliedFilters}
                    />
                </div>
            }
        />
    );
};

export default TransactionHistory;
