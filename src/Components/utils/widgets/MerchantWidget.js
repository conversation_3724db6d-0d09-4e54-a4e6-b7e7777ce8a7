import React, { useCallback, useContext, useMemo } from "react";
import { Form, FormSelect } from "@shoutout-labs/shoutout-themes-enterprise";
import { DataContext } from "../../../Contexts";

const MerchantWidget=(props)=>{
    const { merchants } = useContext(DataContext);
    const { value, onChange } = props;

    const handleChange = useCallback( val =>
            onChange(val.length!==0?val[0].value:"")
        , [onChange]);

    const selectedMerchant = useMemo( () =>
            (merchants&&value)?
                merchants.filter(merchant=>merchant._id===value)
                    .map(merchant => (
                        {
                            label: merchant.merchantName,
                            value: merchant._id,
                            name:"merchant",
                        }
                    )):[]
        , [merchants, value]);

    return (
        <Form.Group controlId="merchant">
            <FormSelect
                clearButton
                onChange={handleChange}
                multiple={false}
                options={merchants ? merchants.map(merchant => (
                    {
                        label: merchant.merchantName,
                        value: merchant._id,
                        name:"merchant",
                    }
                )):[]}
                placeholder="Select Merchant"
                required
                selected={selectedMerchant}
            />
        </Form.Group>
    )
}
export default MerchantWidget
