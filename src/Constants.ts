const LOYALTY_SERVICE_URL: string =
  import.meta.env.VITE_LOYALTY_API_URL ||
  "https://api.loyaltybeta.cxforge.com/api/loyaltyservice";
const LOYALTY_CAMPAIGN_SERVICE_URL: string = import.meta.env.VITE_LOYALTY_CAMPAIGN_SERVICE_URL || "https://api.loyaltybeta.cxforge.com/api/campaignservice";

const KEYCLOAK_URL: string = import.meta.env.VITE_KEYCLOAK_URL || "";
const KEYCLOAK_REALM: string = import.meta.env.VITE_KEYCLOAK_REALM || "";
const K<PERSON>Y<PERSON>OAK_CLIENT_ID: string =
  import.meta.env.VITE_KEYCLOAK_CLIENT_ID || "";
const REGION_ID: string = import.meta.env.VITE_REGION_ID || "";
const DEFAULT_MERHCHANT_LOCATION_ID: string =
  import.meta.env.VITE_DEFAULT_MERHCHANT_LOCATION_ID || "";

export {
  LOYALTY_SERVICE_URL,
  KEYCLOAK_URL,
  KEYCLOAK_REALM,
  KEYCLOAK_CLIENT_ID,
  REGION_ID,
  DEFAULT_MERHCHANT_LOCATION_ID,
  LOYALTY_CAMPAIGN_SERVICE_URL
};
