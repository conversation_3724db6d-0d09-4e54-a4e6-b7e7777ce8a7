import { AccessPermissionModules, AccessPermissionModuleNames } from "Data";
import { getPermissionPath } from "Utils";
import { fetchGet, fetchPut, jsonToQueryParam } from "./CommonServiceUtils";
import Constants from "../Constants";

const BASE_URL = `${Constants.BASE_URL}loyaltyservice/`;

const getTiers = ({ limit = 100, skip = 0, regionId }) => {
    return fetchGet(
        `${BASE_URL}tiers?${jsonToQueryParam({
            limit,
            skip,
            regionId,
        })}`,
        true,
        getPermissionPath(
            AccessPermissionModuleNames.TIER,
            AccessPermissionModules[AccessPermissionModuleNames.TIER].actions
                .ListTiers
        )
    );
};

const configureTiers = (payload = {}) => {
    return fetchPut(
        `${BASE_URL}tiers`,
        payload,
        true,
        getPermissionPath(
            AccessPermissionModuleNames.TIER,
            AccessPermissionModules[AccessPermissionModuleNames.TIER].actions
                .UpdateTier
        )
    );
};

export { getTiers, configureTiers };
