import { Link } from 'react-router-dom'
import { cn } from '@/lib/utils'

interface FooterProps {
    className?: string
}

const FooterLinks = [{
    id: "TERMS_AND_CONDITIONS",
    name: "Terms & Conditions",
    to: "/terms"
}, {
    id: "PRIVACY_POLICY",
    name: "Privacy Policy",
    to: "/privacy-policy"
}];

export default function Footer({ className }: FooterProps) {
    return (
        <footer className="text-center py-4">
            <div className='flex flex-col gap-2'>
                {FooterLinks.map(fL =>
                    <Link
                        key={fL.id}
                        to={fL.to}
                        className={cn(
                            "text-sm text-primary underline-offset-4 hover:underline hover:text-primary/80 transition-colors duration-200",
                            className
                        )}
                        rel="noopener noreferrer"
                        target="_blank"
                    >
                        {fL.name}
                    </Link>
                )}
            </div>
        </footer>
    )
} 