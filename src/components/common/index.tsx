import { FC } from "react";
import { Loader2 } from "lucide-react";
import React from "react";

interface LoaderProps {
    className?: string,
    loaderText?: string;
}

export const Loader: FC<LoaderProps> = ({ className, loaderText }) => {
    return (
        <div className={`text-primary min-h-[200px] flex flex-col justify-center items-center ${className}`}>
            <Loader2 className="h-10 w-10 animate-spin" />
            <h2 className="mt-3 text-lg text-primary text-center">{loaderText || "Please wait"}...</h2>
        </div>
    )
}