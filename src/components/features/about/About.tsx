'use client'

import { Card } from '@/components/ui/card'
import { useAuthStore } from '@/stores/authStore'
import { usePointRules, useTiers, useGeneralSpendingRule } from '@/stores/aboutStore'
import { useQuery } from '@tanstack/react-query'
import { ReactNode } from 'react'
import Tiers from './Tiers'
import { Loader } from '../../common'
import { ASSETS } from '@/config'
import { useRegion } from '@/stores/userStore'

interface OrganizationConfig {
  configuration?: {
    tierConfiguration?: {
      tierCalculationWindow?: number
    }
  }
}

const About = () => {
  const auth = useAuthStore()
  // Queries
  const { regionData: regionsConfig, isLoading: isLoadingRegionData, } = useRegion();

  const { data: organizationsData } = useQuery({
    queryKey: ['organizations'],
    queryFn: () => ({ /* Your organizations fetch function */ }),
    enabled: auth.isAuthenticated,
  })

  // Use the new store hooks
  const { pointRules, isLoading: isLoadingPointRules } = usePointRules()
  const { tiers, isLoading: isLoadingTiers } = useTiers()
  const generalSpendingPointRule = useGeneralSpendingRule()

  const organizationsConfig = organizationsData as OrganizationConfig


  const enabledPointRules = pointRules.filter(rule => rule.status === 'ENABLED');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderPointRule = (rule: any) => {
      const hasValidPoints = rule?.ruleData?.points || rule?.ruleData?.completionBonusConfigurations?.totalBonusPoints || rule?.ruleData?.birthdayPointsConfiguration?.fixedPointsAmount;
    if (!hasValidPoints) {
      return null;
    }

    let pointRule: ReactNode = null;
    const points = rule?.ruleData?.points || rule?.ruleData?.completionBonusConfigurations?.totalBonusPoints;

    switch (rule?.subType) {
      case 'BIRTHDAY':
        pointRule = (
          <span className="text-primary">
            on your{' '}
            <span className="font-bold text-primary">
              Birthday
            </span>
            .
          </span>
        )
        break;
      case 'ENROLL':
        pointRule = (
          <span className="text-primary">
            upon{' '}
            <span className="font-bold text-primary">
              Enrolling
            </span>
            .
          </span>
        )
        break;
      case 'SIGNUP':
        pointRule = (
          <span className="text-primary">
            on{' '}
            <span className="font-bold text-primary">
              Registration
            </span>
            .
          </span>
        )
        break;
      case 'PROFILE_COMPLETION':
        pointRule = (
          <span className="text-primary">
            for{' '}
            <span className="font-bold text-primary">
              Profile Completion
            </span>
            .
          </span>
        )
        break;
      default:
        return null;
    }

    return (
      <Card key={rule._id} className="my-2 rounded-[21px] bg-foreground border border-secondary/30">
        <div className="p-4">
          <div className="flex items-center">
            <img
              src={ASSETS.REWARDS}
              alt="point rule"
              width={30}
              height={30}
              className="brightness-0 invert-0"
            />
            <div className="ml-3">
              <span className="font-bold text-primary">
                {points}{' '}
              </span>
              <span className="text-primary">
                {`${points === 1
                  ? regionsConfig?.pointConfiguration?.singularAlias || 'point'
                  : regionsConfig?.pointConfiguration?.alias || 'points'
                  } `}
                {pointRule}
              </span>
              <span className="text-xs font-medium text-primary bg-secondary/20 px-2 py-1 rounded-full font-glacial">
                {rule.ruleState}
              </span>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  const pointRulesCard = enabledPointRules.length > 0 ? (
    <div>
      {enabledPointRules.map(renderPointRule)}
    </div>
  ) : (
    <Card className="rounded-[21px] bg-foreground border border-secondary/30">
      <div className="p-4">
        <div className="flex justify-center items-center">
          <img
            className="opacity-100"
            src={ASSETS.REWARDS}
            alt="point rules coming soon"
            width={30}
            height={30}
          />
          <div className="mt-2 ml-3 text-center font-bold text-primary">
            Point rules coming soon...
          </div>
        </div>
      </div>
    </Card>
  )

  return (
    <div className="animate-in fade-in-0">
      <h4 className="mb-3 font-bold text-2xl font-perpetua text-primary">About</h4>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <Card className="rounded-[21px] border-0 bg-foreground border border-secondary/30">
          <div className="p-6 flex items-center h-full">
            {isLoadingRegionData || isLoadingPointRules ? (
              <div className="h-full w-full"><Loader /></div>
            ) : (
              <div>
                <div className="mb-1">
                  <p className="text-sm text-primary font-bold font-glacial">
                    When earning
                  </p>
                  <h4 className="mt-1 leading-tight font-perpetua text-primary">
                    <span className="text-primary">
                      {`${regionsConfig?.defaultCurrencyCode || 'not found'} `}
                      {generalSpendingPointRule ?? "~not found~"}
                    </span>
                    {` = 1 `}
                    {regionsConfig?.pointConfiguration?.singularAlias || 'point'}
                  </h4>
                </div>
                <div className="mt-1">
                  <p className="text-sm text-primary font-bold font-glacial">
                    When redeeming
                  </p>
                  <h4 className="mt-1 leading-tight font-perpetua text-primary">
                    <span className="text-primary">
                      {`1 `}
                      {regionsConfig?.pointConfiguration?.singularAlias || 'point'}
                    </span>
                    {` = ${regionsConfig?.defaultCurrencyCode || 'Unkown'} ${regionsConfig?.pointConfiguration?.currencyAmountPerPoint || 'Unknown'
                      }`}
                  </h4>
                </div>
              </div>
            )}
          </div>
        </Card>
        <Card className="rounded-[21px] border-0 bg-foreground border border-secondary/30">
          <div className="p-6 flex items-center h-full">
            <img
              className="mr-3 w-[50px] brightness-0 invert-0 opacity-80 filter-secondary"
              src={ASSETS.REWARD_CONFIG}
              alt="about rewards icon"
              width={60}
              height={60}
            />
            <div>
              <p className="text-sm text-primary font-bold font-glacial">
                Redeem Points
              </p>
              <p className="text-sm text-justify text-primary font-glacial">
                {`Redeem your `}
                {regionsConfig?.pointConfiguration?.alias || 'points'} for
                {` numerous rewards provided by us, `}
                {regionsConfig?.pointConfiguration?.alias || 'points'} you
                need to redeem a reward may depend on the rewards.
              </p>
            </div>
          </div>
        </Card>
        <Card className="rounded-[21px] border-0 bg-foreground border border-secondary/30">
          <div className="p-6 flex items-center h-full">
            <img
              src={ASSETS.TIER_BENEFITS}
              alt="about tier benefits icon"
              className="mr-3 w-[50px] brightness-0 invert-0 opacity-80 filter-secondary"
              width={60}
              height={60}
            />
            <div>
              <p className="text-sm text-primary font-bold font-glacial">
                Tier Benefits
              </p>
              <p className="text-sm text-justify text-primary  font-glacial">
                Tiers are like different privillage levels you can earn based
                {` on the number of tier `}
                {regionsConfig?.pointConfiguration?.alias || 'points'} you
                {` earned. Tier `}
                {regionsConfig?.pointConfiguration?.alias || 'points'} are
                {` equal to the sum of the `}
                {regionsConfig?.pointConfiguration?.alias || 'points'} you
                {` earned in the last `}
                {organizationsConfig?.configuration?.tierConfiguration?.tierCalculationWindow || 365}
                {` days.`}
              </p>
            </div>
          </div>
        </Card>
      </div>
      <div className="my-3">
          <p className="mb-3 text-lg font-bold font-perpetua text-primary">Rules</p>
        {isLoadingPointRules ? (
          <div className="h-full w-full"><Loader loaderText="Loading point rules" /></div>
        ) : (
          pointRulesCard
        )}
      </div>
      <div className="my-3">
        {isLoadingTiers ? (
          <div className="h-full w-full"><Loader loaderText="Loading tiers" /></div>
        ) : (
          <>
            <h2 className="mb-3 text-lg font-bold font-perpetua text-primary">Tiers</h2>
            <Tiers
              tiers={tiers}
              alias={regionsConfig?.pointConfiguration?.alias || 'points'}
              singularAlias={regionsConfig?.pointConfiguration?.singularAlias || 'point'}
            />
          </>
        )}
      </div>
    </div>
  )
}

export default About 