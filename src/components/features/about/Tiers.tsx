'use client'

import { useState, useCallback } from 'react'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { TierData } from '@/lib/services/types'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { useAuthStore } from '@/stores/authStore'
import { useQuery } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import userService from '@/lib/services/userService'
import { ASSETS } from '@/config'

interface TiersProps {
  tiers?: TierData[]
  alias?: string
  singularAlias?: string
}

const Tiers = ({
  tiers = [],
  alias = 'points',
  singularAlias = 'point'
}: TiersProps) => {
  const [expandBenefitId, setExpandBenefitId] = useState<string>('')

  const changeExpandId = useCallback(
    (id: string) => {
      setExpandBenefitId(id === expandBenefitId ? '' : id)
    },
    [expandBenefitId]
  )

  const { data: profile, isLoading: isLoadingProfile } = useQuery({
    queryKey: ['userProfile'],
    queryFn: userService.getProfile,
    enabled: useAuthStore.getState().isAuthenticated,
  })

  if (isLoadingProfile) {
    return (
      <div className="flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (!Array.isArray(tiers)) {
    return (
      <Card className="rounded-[21px] bg-background border border-secondary/30">
        <div className="p-4">
          <div className="flex justify-center items-center">
            <img
              src={ASSETS.TIER_IMAGE}
              alt="tier"
              width={30}
              height={30}
            />
            <div className="ml-3 text-center font-bold text-primary font-glacial">
              Tiers coming soon...
            </div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div id="tiers" className="w-full space-y-2">
      {tiers.map((tier) => (
        <Card
          key={`rl-${tier._id}`}
          className={cn(
            "rounded-[21px] bg-foreground border border-secondary/30 overflow-hidden",
            tier.name === profile?.tierData?.name && "border-secondary"
          )}
        >
          <button
            onClick={() => changeExpandId(tier._id.toString())}
            className="w-full px-6 py-4 text-left hover:bg-accent transition-colors"
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center space-x-3">
              <img
                    className="object-contain"
                    src={tier.imageUrl.startsWith("https") ? tier.imageUrl : ASSETS.TIER_IMAGE}
                    alt={tier.name || 'tier image'}
                    width={45}
                    height={45}
                  />
        
                <h6 className="hidden lg:block font-bold mb-0 ml-2 font-perpetua text-primary">
                  {tier.name}
                </h6>
              </div>

              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <h6 className="mb-0 font-perpetua text-primary">
                    <span className="text-primary font-bold">{tier.points || tier.points || 0}</span>{' '}
                    <span className="text-primary">
                      Tier {tier.points === 1 || tier.points === 1 ? singularAlias : alias}
                    </span>
                  </h6>
                </div>

                {expandBenefitId === tier._id.toString() ? (
                  <ChevronDown className="h-4 w-4 text-primary transition-transform" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-primary transition-transform" />
                )}
              </div>
            </div>

            <div className="lg:hidden mt-2">
              <h6 className="mb-0 font-perpetua text-primary">
                {tier.name}
                <br />
                <span className="text-primary font-bold">{tier.points || tier.points || 0}</span>{' '}
                <span className="text-primary">
                  Tier {tier.points === 1 || tier.points === 1 ? singularAlias : alias}
                </span>
              </h6>
            </div>
          </button>

          {expandBenefitId === tier._id.toString() && (
            <div className="px-6 py-4 bg-background">
              {tier.benefits && tier.benefits.length > 0 ? (
                <div className="space-y-2">
                  {tier.benefits.map((benefit: string, index: number) => (
                    <div
                      key={`b-${index}-${tier._id}`}
                      className="flex items-center px-3 py-2 rounded bg-foreground border border-secondary/10"
                    >
                      <p className="m-0 text-sm text-primary font-glacial">{benefit}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-primary font-glacial">
                  No benefits found.
                </div>
              )}
            </div>
          )}
        </Card>
      ))}
    </div>
  )
}

export default Tiers 