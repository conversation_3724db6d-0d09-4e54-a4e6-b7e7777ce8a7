'use client'

import { use<PERSON><PERSON><PERSON>, use<PERSON>tate, use<PERSON>ffect } from "react"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "react-hot-toast"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader2, Pencil, Save, X } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import userService from "@/lib/services/userService"
import type { NotificationPreference } from "@/lib/services/types"
import { ASSETS } from "@/config"
import MobileNumber from "@/components/ui/mobile-number-input"
import { EditingSection, MyProfileCommonProps, PREFERRED_CONTACT } from "@/lib/data"
import { useUser } from "@/stores/userStore"
import { <PERSON>, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { toTitleCase } from "@/lib/utils/stringUtils"
import { Checkbox } from "@/components/ui/checkbox"

const formSchema = z.object({
  notificationPreference: z.object({
    preferredChannel: z.enum([PREFERRED_CONTACT.MOBILE, PREFERRED_CONTACT.EMAIL, PREFERRED_CONTACT.EMAIL_AND_MOBILE]),
    allowPromotionalNotifications: z.boolean()
  })
})

type FormValues = z.infer<typeof formSchema>

const PreferredContactOptions = Object.values(PREFERRED_CONTACT)
  .filter(item => item === PREFERRED_CONTACT.MOBILE) // TODO: REMOVE THIS FILTER WHEN UPDATING "EMAIL" IS AVAILABLE.
  .map((pC) => ({
    label: toTitleCase(pC),
    value: pC,
  }));

const notificationPreferenceLabelMapping = (preferredChannel: string): string => {
  // TODO: Check why preferredChannel is not updating properly (WHEN UPDATING "EMAIL" IS AVAILABLE).
  switch (preferredChannel) {
    case PREFERRED_CONTACT.EMAIL:
      return " only email ";
    case PREFERRED_CONTACT.MOBILE:
      return " only text message ";
    case PREFERRED_CONTACT.EMAIL_AND_MOBILE:
      return " both email and text message ";
    default:
      return "";
  }
};

const ContactInfo = ({ editingSection, disableActions, setEditingSection, setDisableActions }: MyProfileCommonProps) => {
  const [isEditingPreferences, setIsEditingPreferences] = useState(false)
  const queryClient = useQueryClient()

  const { isLoading: isLoadingProfile, user: profile } = useUser();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      notificationPreference: {
        preferredChannel: PREFERRED_CONTACT.MOBILE,
        allowPromotionalNotifications: false
      }
    }
  })

  // TODO: Change this when multiple edits are available.
  const onEdit = useCallback(() => {
    setEditingSection(EditingSection.NOTIFICATION_PREFERENCE)
    setIsEditingPreferences(true);
  }, [setEditingSection, setIsEditingPreferences])

  const onCancelEdit = useCallback(() => {
    setEditingSection("")
    setIsEditingPreferences(false);
    form.reset({
      notificationPreference: {
        preferredChannel: profile?.notificationPreference?.preferredChannel ?? PREFERRED_CONTACT.MOBILE,
        allowPromotionalNotifications: profile?.notificationPreference?.allowPromotionalNotifications ?? false
      }
    })
  }, [form, profile?.notificationPreference?.preferredChannel, profile?.notificationPreference?.allowPromotionalNotifications, setEditingSection])

  // TODO: Change this when multiple edits are available.
  const onCancel = useCallback(() => {
    setEditingSection("")
    onCancelEdit()
  }, [setEditingSection, onCancelEdit])

  const updateProfileMutation = useMutation({
    mutationFn: (preferences: NotificationPreference) =>
      userService.updateProfile({ notificationPreference: preferences as NotificationPreference }),
    onSuccess: () => {
      toast.success("Successfully updated notification preferences")
      setIsEditingPreferences(false)
      queryClient.invalidateQueries({ queryKey: ['userProfile'] })
    },
    onError: (err) => {
      console.error(err);
      toast.error(
        <div>
          {err?.message ? <div>
            <div className="text-lg">Failed to updatenotification preferences!</div>
            <div className="font-glacial">Error: {err.message}</div>
          </div> : "Oops! Something went wrong. Please try again."}
          <small>If the issue persists, please contact Celeste support.</small>
        </div>
      );
    }
  })

  const onSubmit = useCallback((data: FormValues) => {
    if (data.notificationPreference) {
      const updateData = {
        ...data.notificationPreference,
        preferredChannel: data.notificationPreference.preferredChannel as "MOBILE" | "EMAIL" | "EMAIL_AND_MOBILE"
      }
      updateProfileMutation.mutate(updateData)
    }
  }, [updateProfileMutation])

  useEffect(() => {
    if (profile) {
      form.reset({
        notificationPreference: {
          preferredChannel: profile.notificationPreference?.preferredChannel ?? PREFERRED_CONTACT.MOBILE,
          allowPromotionalNotifications: profile.notificationPreference?.allowPromotionalNotifications ?? false
        }
      })
    }
  }, [profile, form])

  // TODO: Change this when multiple edits are available.
  useEffect(() => {
    if (isEditingPreferences && editingSection && editingSection !== EditingSection.NOTIFICATION_PREFERENCE) {
      onCancelEdit();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editingSection, isEditingPreferences])

  useEffect(() => {
    setDisableActions(updateProfileMutation.isPending);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateProfileMutation.isPending])

  return (
    <Card className="mb-4 sm:mb-6 bg-foreground rounded-[21px] shadow-sm border border-secondary/30">
      <div className="p-4 sm:p-6">
        <h2 className="text-lg font-bold m-0 pb-3 text-center sm:text-left font-perpetua text-primary">Contact Information</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Mobile Number Section */}
          <Card className="bg-background rounded-[21px] p-4 sm:p-6 border border-secondary/30">
            <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-6">
              <div className="w-32 h-32 sm:w-40 sm:h-40 relative">
                <img
                  className="min-landing-logo hover:scale-105 transition-transform duration-300"
                  src={ASSETS.LOGO}
                  alt="Logo"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  style={{
                    objectFit: 'contain',
                  }}
                />
              </div>
              <div className="flex-1 w-full">
                <h3 className="font-bold mb-2 font-perpetua text-primary">Mobile Number</h3>
                <div className="sm:col-span-7 w-full">
                  <MobileNumber
                    inputId="phone"
                    inputName="phone"
                    inputClass="font-glacial flex h-9 w-full text-primary bg-foreground rounded-md border border-input px-3 py-1 shadow-sm transition-colors text-sm"
                    mobileNumber={profile?.mobileNumber || "~unknown"}
                    disabled
                    setMobileNumber={() => { }}
                    setIsMobileNumberValid={() => { }}
                  />
                </div>
              </div>
            </div>
          </Card>
          {/* Address Section */}
          <Card className="bg-background rounded-[21px] p-4 sm:p-6 border border-secondary/30">
            <div className="space-y-4">
              <div>
                <h3 className="font-bold mb-2 font-perpetua text-primary">Address</h3>
                <div className="border p-2 rounded text-primary font-glacial text-sm sm:text-base">
                  {profile?.residentialAddress?.line1 || "Address not found."}
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-bold mb-2 font-perpetua text-primary">City</h3>
                  <div className="border p-2 rounded text-primary font-glacial text-sm sm:text-base">
                    {profile?.residentialAddress?.city || "City not found."}
                  </div>
                </div>
                <div>
                  <h3 className="font-bold mb-2 font-perpetua text-primary">District</h3>
                  <div className="border p-2 rounded text-primary font-glacial text-sm sm:text-base">
                    {profile?.residentialAddress?.stateOrProvince || "District not found."}
                  </div>
                </div>
              </div>
            </div>
          </Card>
          {/* Notification Preferences Section */}
          <Card className="bg-background rounded-[21px] border border-secondary/30">
            <div className="p-4 sm:p-6">
              <div className="text-lg font-bold mb-3 text-center sm:text-left">Notification Preferences</div>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
                  <div className="grid grid-cols-1 gap-4 sm:gap-6">
                    <FormField
                      control={form.control}
                      name="notificationPreference.preferredChannel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-bold font-perpetua text-primary">Preferred Contact Method</FormLabel>
                          <Select
                            disabled={!isEditingPreferences || disableActions || updateProfileMutation.isPending || isLoadingProfile}
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              {isEditingPreferences ? (
                                <SelectTrigger className="rounded font-glacial h-10">
                                  <SelectValue placeholder="Select notification preferences" />
                                </SelectTrigger>
                              ) : (
                                <div className="border p-2 rounded text-primary font-glacial">
                                  {toTitleCase(field.value ? field.value : "~unknown")}
                                </div>
                              )}
                            </FormControl>
                            <SelectContent className="bg-background">
                              {PreferredContactOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value} className="text-primary font-glacial">
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage className="text-red-400 text-sm" />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="notificationPreference.allowPromotionalNotifications"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            {isEditingPreferences ? (
                              <Checkbox
                                ref={field.ref}
                                name={field.name}
                                label="I want to receive text message alerts with exclusive offers and promotions from Celeste Loyalty."
                                checked={field.value}
                                disabled={!isEditingPreferences || disableActions || updateProfileMutation.isPending || isLoadingProfile}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                              />
                            ) : (
                              <>
                                <FormLabel className="font-bold font-perpetua text-primary">
                                  {`Receive${notificationPreferenceLabelMapping(form.getValues("notificationPreference.preferredChannel"))}alerts with exclusive offers and promotions from Celeste Loyalty.`}
                                </FormLabel>
                                <div className="border p-2 rounded text-primary font-glacial">
                                  {field.value ? "Yes" : "No"}
                                </div>
                              </>
                            )}
                          </FormControl>
                          <FormMessage className="text-red-400 text-sm" />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-6">
                    {!isEditingPreferences &&
                      <Button
                        className="w-auto px-6 py-2 font-glacial"
                        type="button"
                        variant="default"
                        disabled={updateProfileMutation.isPending || disableActions || isLoadingProfile}
                        onClick={onEdit}
                      >
                        <Pencil className="h-5 w-5" />
                        Edit Preferences
                      </Button>
                    }
                    {isEditingPreferences && <>
                      <Button
                        className="w-full max-w-[200px] px-6 font-glacial"
                        variant="outline-destructive"
                        disabled={updateProfileMutation.isPending || disableActions || isLoadingProfile}
                        onClick={onCancel}
                      >
                        <div className="font-glacial flex items-center gap-2">
                          <X className="h-5 w-5" />
                          Cancel
                        </div>
                      </Button>
                      <Button
                        className="w-full max-w-[200px] px-6 font-glacial"
                        type="submit"
                        variant="default"
                        disabled={!form.formState.isDirty || disableActions || updateProfileMutation.isPending || isLoadingProfile}
                      >
                        {updateProfileMutation.isPending && <Loader2 className="h-6 w-6 animate-spin text-primary-foreground" />}
                        {updateProfileMutation.isPending ?
                          "Updating..."
                          :
                          <div className="font-glacial flex items-center gap-2">
                            <Save className="h-5 w-5" />
                            Update
                          </div>
                        }
                      </Button>
                    </>
                    }
                  </div>
                </form>
              </Form>
            </div>
          </Card>
        </div>
      </div>
    </Card>
  )
}

export default ContactInfo
