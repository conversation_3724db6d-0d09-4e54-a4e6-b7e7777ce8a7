import { useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import Sidebar from './Sidebar'
import Footer from '../common/Footer'


export default function PortalLayout() {
  const { isAuthenticated, login } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated) {
      login();
    }
  }, [isAuthenticated, login])

  return (
    <div className="flex min-h-screen bg-background">
      {/* Sidebar */}
      <Sidebar />
      {/* 
        // * Below <div /> is used as a placeholder to position the side nav bar correctly.
      */}
      <div className="lg:w-20" />
      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <main>
          <Outlet />
        </main>
        <div className='bg-foreground border-t border-secondary/30'>
          <Footer />
        </div>
      </div>
    </div>
  )
} 