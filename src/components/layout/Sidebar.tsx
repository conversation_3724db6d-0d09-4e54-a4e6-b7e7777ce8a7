"use client";

import { useState } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import { useAuthStore } from "@/stores/authStore";
import { useUser } from "@/stores/userStore";
import { cn } from "@/lib/utils";
import {
  Home,
  Gift,
  History,
  Info,
  User,
  LogOut,
  Menu,
  Loader2,
  UserCog2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ASSETS } from "@/config";

export default function Sidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { logout, isAuthenticated } = useAuthStore();
  const { isLoading } = useUser();

  const navItems = [
    { icon: Home, href: "/portal/home", label: "Home" },
    { icon: Gift, href: "/portal/rewards", label: "Rewards" },
    { icon: History, href: "/portal/transactions", label: "Transactions" },
    { icon: Info, href: "/portal/about", label: "About" },
  ];

  const handleSignOut = async () => {
    if (!isAuthenticated) return;
    await logout();
  };

  return (
    <>
      {/* Mobile Navigation */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-[hsl(var(--primary-sidebar))] border-b border-gray-dark/10">
        <div className="flex items-center justify-between p-4">
          <div className="relative w-10 h-10">
            <Link to="/portal/home" onClick={() => setIsMobileMenuOpen(false)}>
              <img
                className="hover:scale-105 transition-transform duration-300 w-full h-full object-cover"
                src={ASSETS.LOGO}
                alt="Celeste"
              />
            </Link>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 text-primary hover:text-gold"
          >
            <Menu className="w-6 h-6" />
          </button>
        </div>
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <nav className="border-t border-gray-dark/10 bg-[hsl(var(--primary-sidebar))]">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;

              return (
                <Link
                  key={item.href}
                  to={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-3 font-glacial",
                    isActive
                      ? "text-gold-dark"
                      : "text-primary hover:bg-gold-light"
                  )}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
            {/* Profile Link */}
            <Link
              to="/portal/profile"
              onClick={() => setIsMobileMenuOpen(false)}
              className={cn(
                "flex items-center space-x-2 px-4 py-3 font-glacial",
                location.pathname.startsWith("/portal/profile")
                  ? "text-gold-dark"
                  : "text-primary hover:bg-gold-light"
              )}
            >
              <User className="w-5 h-5" />
              <span>My Profile</span>
            </Link>
            <div className="border-t border-gray-dark/10" />
            <div className="w-full flex justify-center items-center">
              <Button
                className="w-full max-w-[200px] flex justify-center items-center space-x-2 my-2 px-4 py-3 font-glacial"
                variant="destructive"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  handleSignOut();
                }}
              >
                <LogOut className="w-5 h-5" />
                <span className="">Sign Out</span>
              </Button>
            </div>
          </nav>
        )}
      </div>
      {/* Desktop Sidebar */}
      <aside className="hidden lg:flex flex-col justify-between fixed left-0 top-0 h-full w-20 bg-[hsl(var(--primary-sidebar))] border-r border-gray-dark/10">
        <div className="flex flex-col items-center pt-0 pb-6">
          <div className="relative w-full h-16">
            <Link to="/portal/home">
              <img
                className="transition-transform duration-300 w-full h-full object-cover"
                src={ASSETS.LOGO}
                alt="Celeste"
              />
            </Link>
          </div>
          <nav className="flex flex-col space-y-6 mt-4">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.href}
                  to={item.href}
                  className={cn(
                    "p-3 rounded-lg transition-colors font-glacial",
                    isActive
                      ? "bg-gold-light"
                      : "text-primary hover:bg-gold-light"
                  )}
                  title={item.label}
                >
                  <Icon className="w-6 h-6" />
                </Link>
              );
            })}
          </nav>
        </div>
        {/* User Profile Dropdown */}
        <div className="flex flex-col items-center pb-6">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="p-3 rounded-lg text-primary hover:bg-gold-light">
                {isLoading ? "Loading..." : <UserCog2 className="w-6 h-6" />}
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-56 bg-[hsl(var(--primary-sidebar))] border border-gray-dark/10"
            >
              <DropdownMenuItem
                onClick={() => navigate("/portal/profile")}
                className={`text-primary ${
                  location.pathname === "/portal/profile"
                    ? "bg-gold-light"
                    : "text-primary hover:bg-gold-light"
                }`}
              >
                <User className="w-4 h-4 mr-2" />
                <span>My Profile</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-dark/10" />
              <DropdownMenuItem
                className="text-red-500 hover:bg-gold-light focus:bg-gold-ligh"
                onClick={handleSignOut}
                disabled={!isAuthenticated}
              >
                {!isAuthenticated ? (
                  <div className="flex items-center text-red-500">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    <span>Signing out...</span>
                  </div>
                ) : (
                  <div className="flex items-center text-red-500">
                    <LogOut className="w-4 h-4 mr-2" />
                    <span>Sign Out</span>
                  </div>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </aside>
    </>
  );
}
