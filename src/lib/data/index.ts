export const EditingSection = {
  PERSONAL: "PERSONAL",
  ADDRESS: "ADDRESS",
  EMAIL: "EMA<PERSON>",
  NOTIFICATION_PREFERENCE: "NOTIFICATION_PREFERENCE",
} as const;

export interface MyProfileCommonProps {
  editingSection:
    | "PERSONAL"
    | "ADDRESS"
    | "EMAIL"
    | "NOTIFICATION_PREFERENCE"
    | "";
  disableActions: boolean;
  setEditingSection: (
    editingSection:
      | "PERSONAL"
      | "ADDRESS"
      | "EMAIL"
      | "NOTIFICATION_PREFERENCE"
      | ""
  ) => void;
  setDisableActions: (status: boolean) => void;
}

export const PREFERRED_CONTACT = {
  MOBILE: "MOBILE",
  EMAIL: "EMAIL",
  EMAIL_AND_MOBILE: "EMAIL_AND_MOBILE",
};
