
import {ApiResponse, PaginatedResponse} from './types';
import {LOYALTY_CAMPAIGN_SERVICE_URL} from "@/Constants.ts";
import {createApiInstance} from "@/lib/services/createAPIInstance.ts";

const api =  createApiInstance(LOYALTY_CAMPAIGN_SERVICE_URL);
const BASE_PATH = '/campaigns/banners';


export interface Banner {
    _id: string;
    organizationId: string;
    regionId: string;
    name: string;
    description: string;
    segmentIds: string[];
    segmentFilters: {
        memberFilter: string;
        transactionFilter: string;
    }[];
    scheduleOn: string;
    channel: string;
    senderId: string;
    message: {
        messageBody: string;
        messageSubject: string;
    };
    status: string;
    type: string;
    visibility: string;
    endOn: string;
    historyEvents: {
        eventDate: string;
        eventDetails: string;
        eventBy: string;
    };
    createdOn: string;
    updatedOn: string;
    createdBy: string;
    updatedBy: string;
}

export interface BannerResponse extends ApiResponse<PaginatedResponse<Banner>>   {
    items: Banner[];
    total: number;
    skip: number;
    limit: number;
}

const campaignService = {
    getPrivateBanners: async ({limit, skip,regionId}: { limit: number; skip: number, regionId: string }) => {
        try {
            const response = await api.get<BannerResponse>(`${BASE_PATH}/private`,{
                params: {
                    limit,
                    skip,
                    regionId
                },
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching banners:', error);
            throw error;
        }
    },
};

export default campaignService;