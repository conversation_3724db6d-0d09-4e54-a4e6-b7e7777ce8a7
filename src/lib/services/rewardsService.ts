import api from "./axiosConfig";
import {
  ApiResponse,
  PaginatedResponse,
  RewardRedeemRequest,
  RewardRedeemResponse,
  RedemptionLogsResponse,
  RewardItem,
} from "./types";

const BASE_PATH = "portal/";

interface RewardsParams {
  limit: number;
  skip: number;
}

const rewardsService = {
  getRewards: async (params: RewardsParams = { limit: 30, skip: 0 }) => {
    try {
      const response = await api.get<PaginatedResponse<RewardItem>>(
        `${BASE_PATH}rewards`,
        {
          params: {
            limit: params.limit,
            skip: params.skip,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching rewards:", error);
      throw error;
    }
  },

  getRewardById: async (rewardId: string) => {
    try {
      const response = await api.get<ApiResponse<RewardItem>>(
        `${BASE_PATH}rewards/${rewardId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching reward by ID:", error);
      throw error;
    }
  },

  redeemReward: async (
    rewardId: string,
    locationId: string,
    memberId: string
  ) => {
    const redeemRequest: RewardRedeemRequest = {
      rewardId,
      memberId,
      metadata: {
        claimLocationId: locationId,
      },
      merchantLocationId: locationId,
    };

    try {
      const response = await api.post<ApiResponse<RewardRedeemResponse>>(
        `${BASE_PATH}rewardredeem`,
        redeemRequest
      );
      return response.data;
    } catch (error) {
      console.error("Error redeeming reward:", error);
      throw error;
    }
  },

  getRedemptionLogs: async (params: RewardsParams = { limit: 30, skip: 0 }) => {
    try {
      const response = await api.get<RedemptionLogsResponse>(
        `${BASE_PATH}redemptionlogs`,
        {
          params: {
            limit: params.limit,
            skip: params.skip,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching redemption logs:", error);
      throw error;
    }
  },
};

export default rewardsService;
