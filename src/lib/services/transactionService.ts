import api from './axiosConfig'
import { ApiResponse, PaginatedResponse, Transaction, TransactionType } from './types'

const BASE_PATH = 'portal/'

export interface TransactionFilters {
  skip?: number
  limit?: number
  transactionOnFromDate?: string
  transactionOnToDate?: string
  transactionType?: TransactionType
  status?: string
}

const transactionService = {
  getTransactions: async (filters: TransactionFilters = {}) => {
    try {
      const response = await api.get<PaginatedResponse<Transaction>>(`${BASE_PATH}transactions`, {
        params: filters,
      })
      return {
        data: response.data,
        message: 'Success',
        status: 200
      } as unknown as ApiResponse<PaginatedResponse<Transaction>>
    } catch (error) {
      console.error('Transaction service error:', error)
      throw error
    }
  },


}

export default transactionService 