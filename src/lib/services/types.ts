export interface CommonQueryParams {
  limit: number;
  skip: number;
}

export interface CommonQueryParamsWithRegionId {
  limit: number;
  skip: number;
  regionId: string;
}

export interface PaginatedResponse<T> {
  limit: number;
  skip: number;
  total: number;
  items: T[];
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface RewardRedeemMetadata {
  claimLocationId: string;
}

export interface RewardRedeemRequest {
  rewardId: string;
  memberId: string;
  metadata: object;
  merchantLocationId: string;
}

export interface RewardRedeemResponse {
  success: boolean;
  message: string;
  redemptionId?: string;
}

export enum TransactionType {
  COLLECTION = "COLLECTION",
  REDEMPTION = "REDEMPTION",
  ADJUSTMENT = "ADJUSTMENT",
}

export enum TransactionStatus {
  VALID = "VALID",
  INVALID = "INVALID",
  PENDING = "PENDING",
}

export interface Transaction {
  organizationId: string;
  regionId: string;
  merchantId: string;
  merchantLocationId: string;
  memberId: string;
  loyaltyId: string;
  cardId: string;
  transactionOn: string;
  redeemablePoints: number;
  tierPoints: number;
  transactionAmount: number;
  type: TransactionType;
  subType: string;
  importJobId: string;
  transactionSubType: {
    name: string;
  };
  merchant: {
    merchantName: string;
  };
  merchantLocation: {
    locationName: string;
  };
  card: {
    cardNo: string;
  };
  productItems: Array<{
    productId: string;
    productName: string;
    productCategory: string[];
    quantity: number;
    amount: number;
  }>;
  invoiceData: {
    invoiceId: string;
    invoiceDate: string;
    invoiceAmountWithTax: number;
    invoiceAmountWithoutTax: number;
    discountAmount: number;
    billAmount: number;
  };
  pointRuleEvaluations: Array<{
    pointRuleId: string;
    points: number;
  }>;
  createdBy: string;
  updatedBy: string;
  status: TransactionStatus;
}

export interface Address {
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  stateOrProvince: string;
  zipOrPostcode: string;
}

export interface Identification {
  identificationType: "NATIONAL_ID";
  identificationNumber: string;
}

export interface NotificationPreference {
  preferredChannel: "EMAIL" | "MOBILE" | "EMAIL_AND_MOBILE";
  allowPromotionalNotifications: boolean;
}

export interface RewardMetadata {
  rewardId: string;
  refNumber: string;
  refName: string;
  notes: string;
}

export interface Card {
  cardNo: number;
  status: "ASSIGNED" | "UNASSIGNED";
}

export interface UserProfile {
  _id: string;
  regionId: string;
  merchantLocationId: string;
  profilePicture?: string;
  identifications?: Identification[];
  firstName: string;
  lastName: string;
  preferredName?: string;
  mobileNumber: string;
  countryCode: string;
  country: string;
  email?: string;
  birthDate: string;
  gender: "MALE" | "FEMALE" | "OTHER";
  residentialAddress?: Address;
  points: number;
  tierPoints: number;
  pointsToExpire: number;
  pointsExpireOn: string;
  affinityGroupId?: string;
  additionalPhoneNumbers?: string[];
  type: "PRIMARY" | "SECONDARY" | "DEPENDENT";
  notificationPreference: NotificationPreference;
  rewardMetadata?: RewardMetadata[];
  affinityGroup?: {
    name: string;
  };
  cards: Card[];
  status: string;
  merchantLocation?: {
    merchantId?: string;
    locationName?: string;
  };
  tierData?: {
    name?: string;
    imageUrl?: string;
  };
}

export interface RewardItem {
  _id: string;
  merchantId: string;
  name: string;
  description: string;
  type: "TANGIBLE" | "DIGITAL" | "SERVICE";
  subType: string;
  imageUrls: string[];
  pointValueType: "STATIC" | "DYNAMIC";
  pointsStatic: number;
  pointsBundles: Array<{
    points: number;
    bundleName: string;
    bundleValue: number;
  }>;
  validityPeriod: "FIXED" | "FLEXIBLE";
  validFrom: string;
  validTo: string;
  dailyRedemptionLimit: "UNLIMITED" | "LIMITED";
  dailyRedemptionAmount: number;
  rewardMetadata: {
    allowAllClaimLocations: boolean;
    claimLocations: string[];
  };
  claimLocations: Array<{
    locationName: string;
    status: "DRAFT" | "PUBLISHED";
  }>;
  createdOn: string;
  shortDescription: string;
  menuIcon: string;
  redemptionSteps: Array<{
    step: number;
    details: string;
  }>;
}

export enum RewardType {
  DIGITAL = "DIGITAL",
  TANGIBLE = "TANGIBLE",
  SERVICE = "SERVICE",
}

export enum RewardSubType {
  VOUCHER = "VOUCHER",
}

export enum RedemptionStatus {
  REQUESTED = "REQUESTED",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

export enum ProcessingStatus {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

export enum RefundStatus {
  NONE = "NONE",
  REQUESTED = "REQUESTED",
  COMPLETED = "COMPLETED",
  REJECTED = "REJECTED",
}

export interface RedemptionMetadata {
  partnerRefNumber: string;
  partnerRefName: string;
  partnerNotes: string;
  claimLocation: string;
}

export interface RedemptionReward {
  name: string;
  imageUrls: string[];
}

export interface RedemptionVoucher {
  voucherCode: string;
}

export interface RedemptionLocation {
  locationName: string;
}

export interface RedemptionHistoryEvent {
  eventDate: string;
  eventDetails: string;
  eventBy: string;
}

export interface RedemptionLog {
  rewardId: string;
  distributionJobId: string;
  transactionId: string;
  pointsRedeemed: number;
  status: RedemptionStatus;
  processingStatus: ProcessingStatus;
  rewardType: RewardType;
  rewardSubType: RewardSubType;
  refundStatus: RefundStatus;
  notes: string;
  metadata: RedemptionMetadata;
  reward: RedemptionReward;
  voucher?: RedemptionVoucher;
  pickupLocation: RedemptionLocation;
  historyEvents: RedemptionHistoryEvent[];
  createdOn: string;
}

export interface RedemptionLogsResponse {
  limit: number;
  skip: number;
  total: number;
  items: RedemptionLog[];
}

// * Accounts
export interface RequestCreatePortalAccountVerifyTokenPayload {
  email?: string;
  mobileNumber?: string;
  cardNumber?: string;
}

export interface VerifyCreatePortalAccountRequestPayload {
  accountVerifyToken: string;
  otpCode: string;
}

export interface CreatePortalAccountPayload {
  accountCreateToken: string;
  regionId: string;
  merchantLocationId?: string;
  firstName: string;
  lastName: string;
  preferredName?: string;
  birthDate?: string;
  gender?: Gender;
  username: string;
  password: string;
  isExistingLoyaltyMember: boolean;
  memberId?: string;
}

export interface RequestCreatePortalAccountVerifyTokenResponse {
  accountVerifyToken: string;
}

export interface VerifyCreatePortalAccountRequestResponse {
  memberId: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email: string;
  merchantLocationId: string;
  cardNumber: string;
  username: string;
  accountCreateToken: string;
}

export interface CreatePortalAccountResponse {
  message: string;
  memberId: string;
}

export interface LoginAfterSignupPayload {
  username: string;
  password: string;
}

export interface Location {
  _id: string;
  locationName: string;
  isPickupLocation: boolean;
  contact: {
    address: Address;
  };
}
export interface TierData {
  _id: string;
  name: string;
  imageUrl: string;
  points: number;
  benefits: string[];
}

export interface PointRule {
  _id: string;
  name: string;
  status: string;
  ruleState: string;
  description: string;
  points: number;
  subType: string;
  merchantId: string;
  ruleData: {
    amountPerPoint: number;
  };
}

export enum Gender {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHER = "OTHER",
}

export const GenderTypeOptions = [
  { value: Gender.MALE, label: "Male" },
  { value: Gender.FEMALE, label: "Female" },
  { value: Gender.OTHER, label: "Other" },
];

export interface LoyaltyErrorObject {
  response?: {
    data?: {
      error?: string;
      errorCode?: string;
    };
  };
}

export const LoyaltyErrorCodes = {
  "200001": { code: "200001", message: "Account already exists" },
} as const;

export interface PublicRegion {
  _id: string;
  regionName: string;
  defaultCountryISO2Code: string;
  supportInfo: {
    phoneNumbers: [string];
    email: string;
    whatsappNumber: string;
  };
}

export interface PublicLocation {
  _id: string;
  locationName: string;
}

export interface PublicData {
  regions: PublicRegion[];
  locations: PublicLocation[];
}

export interface ExistingMember {
  memberId: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email: string;
  merchantLocationId: string;
  cardNumber: string;
  gender?: string;
  username: string;
}

export type RewardTabType = "all" | "my";

export enum RewardTabs {
  ALL = "all",
  MY = "my",
}

export interface Region {
  _id: string;
  regionName: string;
  defaultCountryISO2Code: string;
  defaultCurrencyCode: string;
  regionIconUrl: string;
  defaultMerchantId: string;
  defaultMerchantLocationId: string;
  pointConfiguration: {
    minPointRedemptionAmount: number;
    maxPointRedemptionAmount: number;
    minPointsBalanceForRedemption: number;
    pointExpiryMethod: "FIXED" | "ROLLING";
    pointExpiryStartMonth: number;
    pointExpiryEndMonth: number;
    pointExpiryPeriod: number;
    pointExpiryGracePeriod: number;
    currencyAmountPerPoint: number;
    regionalPointConversionRates: [
      {
        destinationRegionId: string;
        rate: number;
      }
    ];
    alias?: string;
    singularAlias?: string;
  };
  supportInfo: {
    phoneNumbers: [string];
    email: string;
    whatsappNumber: string;
  };
}
