export const formatToCommonReadableFormat = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}; 

export const secondsToMinuteAndSeconds = (seconds: number) => {
  const m = Math.floor((seconds % 3600) / 60).toString(),
    s = Math.floor(seconds % 60)
      .toString()
      .padStart(2, "0");
  return `${m}:${s}`;
};
