import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

(async () => {
  const browser: Browser = await chromium.launch({ headless: false }); // So you can see the browser
  const context: BrowserContext = await browser.newContext();
  const page: Page = await context.newPage();

  await page.goto('https://www.linkedin.com/login');
  // Log in manually, then:
  console.log('After logging in, press Enter in your terminal...');
  process.stdin.once('data', async () => {
    await context.storageState({ path: 'linkedin-auth.json' });
    await browser.close();
    console.log('Saved session to linkedin-auth.json');
    process.exit(0);
  });
})();