'use client'

import { useEffect, useState } from 'react'
import RewardsCatalog from '@/components/features/rewards/RewardsCatalog'
import MyRewards from '@/components/features/rewards/MyRewards'
import PortalPageLayout from '@/components/layout/PortalPageLayout'
import { RewardTabs, RewardTabType } from '@/lib/services/types'
import { useLocation } from 'react-router-dom'

export default function RewardsPage() {
  const [activeTab, setActiveTab] = useState<RewardTabType>(RewardTabs.ALL)
  const data = useLocation()

  useEffect(() => {
    if (data.state?.tab) {
      setActiveTab(data.state.tab)
    }
  }, [data.state?.tab])

  return (
    <PortalPageLayout>
      <div className="space-y-4">
        <div className="mb-4 lg:mb-6">
          <div className="border-b border-secondary/30">
            <nav className="-mb-px flex space-x-4 lg:space-x-8 overflow-x-auto">
              <button
                onClick={() => setActiveTab(RewardTabs.ALL)}
                className={`
                whitespace-nowrap py-3 lg:py-4 px-1 mt-6 border-b-2 font-medium text-sm font-glacial
                ${activeTab === RewardTabs.ALL
                    ? 'border-secondary text-secondary'
                    : 'border-transparent text-primary hover:text-accent hover:border-secondary/30'}
              `}
              >
                All Rewards
              </button>
              <button
                onClick={() => setActiveTab(RewardTabs.MY)}
                className={`
                whitespace-nowrap py-3 lg:py-4 px-1 border-b-2 mt-6 font-medium text-sm font-glacial
                ${activeTab === RewardTabs.MY
                    ? 'border-secondary text-secondary'
                    : 'border-transparent text-primary hover:text-accent hover:border-secondary/30'}
              `}
              >
                My Rewards
              </button>
            </nav>
          </div>
        </div>

        {activeTab === RewardTabs.ALL ? (
          <RewardsCatalog />
        ) : (
          <MyRewards />
        )}
      </div>
    </PortalPageLayout>
  )
} 