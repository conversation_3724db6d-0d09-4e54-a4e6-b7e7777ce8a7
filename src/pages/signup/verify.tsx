"use client";

import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast"
import * as z from "zod";
import { useMutation } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import accountService from "@/lib/services/accountService";
import { LoyaltyErrorCodes, LoyaltyErrorObject } from "@/lib/utils/custom.error.code.map";
import { signupStore } from "@/pages/signup/stores/signupStore";
import VerificationInput from "@/pages/signup/components/verification/VerificationInput";
import { RestartSignup, SignupHeader, SignupLoader } from "@/pages/signup/components/common";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

const verifySchema = z.object({
    otpCode: z.string().nonempty({ message: "Verification code cannot be empty!" })
});

type VerifyFormValues = z.infer<typeof verifySchema>;

export default function Verify() {
    const navigate = useNavigate();
    const { mobileNumber, isSignupProcessStarted, accountVerifyToken, setDoesPortalAccountAlreadyExists, setIsSignupProcessStarted, setIsCreatePortalRequestVerified, resetSignup } = signupStore()
    const [isRedirecting, setIsRedirecting] = useState<boolean>(false);

    const verificationInputForm = useForm<VerifyFormValues>({
        resolver: zodResolver(verifySchema),
        defaultValues: {
            otpCode: "",
        },
    });

    const { isPending: isVerifyingRequest, mutate: onVerifyAccountCreateRequest } = useMutation({
        mutationFn: async (otpCode: string) => {
            setIsRedirecting(false);
            const response = await accountService.verifyPortalAccountCreateRequest({ accountVerifyToken, otpCode });
            return response;
        },
        onSuccess: (data) => {
            setIsRedirecting(true);
            const { accountCreateToken, ...existingMember } = data;
            setIsCreatePortalRequestVerified(accountCreateToken, existingMember)
            toast.success("Successfully verified the mobile number.");
            navigate("/signup");
        },
        onError: (err: LoyaltyErrorObject) => {
            setIsRedirecting(false);
            console.error(err);

            const { error: errMsg = "" } = err?.response?.data || {};

            toast.error(
                <div>
                    {errMsg ? `${errMsg}!` : "Oops! Something went wrong. Please try again."}
                    <br />
                    <small>If the issue persists, please contact Celeste support.</small>
                </div>
            );

        },
    });

    const { isPending: isResedingOtp, mutate: onResendCode } = useMutation({
        mutationFn: async () => {
            const response = await accountService.requestPortalAccountCreateToken({ mobileNumber: mobileNumber.replace("0", "94") });
            return response;
        },
        onSuccess: (data) => {
            setIsSignupProcessStarted(true, data.accountVerifyToken)
            toast.success("Successfully re-sent an OTP to the given mobile number.");
        },
        onError: (err: LoyaltyErrorObject) => {
            console.error(err);
            const { error: errMsg = "", errorCode = "" } = err?.response?.data || {};

            if (errorCode && errorCode === LoyaltyErrorCodes['200001'].code) {
                setDoesPortalAccountAlreadyExists(true);
                navigate("/signup");
            } else {
                toast.error(
                    <div>
                        {errorCode?.startsWith("2") && errMsg ? `${errMsg}!` : "Oops! Something went wrong. Please try again."}
                        <br />
                        <small>If the issue persists, please contact Celeste support.</small>
                    </div>
                );
            }

        },
    });

    const onSubmit = async (data: VerifyFormValues) => {
        onVerifyAccountCreateRequest(data.otpCode)
    };

    // * To show a warning if the user is navigating out of the page or is going to reload the page.
    const handleBeforeUnload = (event: { preventDefault: () => void; returnValue: string; }) => {
        event.preventDefault();
        const message =
            "Are you sure you want to leave? This will cancel the sign up process.";
        event.returnValue = message;
        return message;
    };

    // * To show a warning if the user is navigating out of the page or is going to reload the page.
    useEffect(() => {
        if (isSignupProcessStarted) {
            window.addEventListener("beforeunload", handleBeforeUnload);
        }

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [isSignupProcessStarted]);

    useEffect(() => {
        if (!accountVerifyToken) {
            navigate('/signup')
        }
    }, [accountVerifyToken, navigate])

    return (
        <div className="bg-foreground text-primary min-h-screen flex flex-col justify-center items-center">
            <div className="w-full max-w-[1000px] px-4 py-8">
                <Card className="border-0 shadow-md bg-background">
                    <CardContent className="p-6">
                        <SignupHeader router={navigate} disabled={isVerifyingRequest || isResedingOtp || isRedirecting} resetSignup={resetSignup} />
                        {isVerifyingRequest || isRedirecting ? <SignupLoader loaderText={isRedirecting ? "Redirecting" : "Verifying"} /> :
                            <>
                                {accountVerifyToken ? <div>
                                    <Form {...verificationInputForm}>
                                        <form onSubmit={verificationInputForm.handleSubmit(onSubmit)} className="w-ful">
                                            <FormField
                                                control={verificationInputForm.control}
                                                name="otpCode"
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormControl>
                                                            <VerificationInput
                                                                verifyMethod="MOBILE"
                                                                codeItem={field.value}
                                                                isRequestingVerifyToken={isResedingOtp}
                                                                disableResend={isVerifyingRequest || isResedingOtp || isRedirecting}
                                                                setCodeItem={field.onChange}
                                                                onResendCode={onResendCode}
                                                            />
                                                        </FormControl>
                                                    </FormItem>
                                                )}
                                            />
                                        </form>
                                    </Form>
                                </div> :
                                    <RestartSignup disabled={isVerifyingRequest || isResedingOtp || isRedirecting} resetSignup={() => {
                                        resetSignup();
                                        navigate("/signup");
                                    }} />}
                            </>
                        }
                    </CardContent>
                </Card>
            </div>
        </div >
    );
}

