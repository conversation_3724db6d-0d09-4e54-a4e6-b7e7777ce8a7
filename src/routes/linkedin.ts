import express, { Request, Response } from 'express';
import * as linkedinService from '../services/linkedin';

const router = express.Router();

/**
 * @route   GET /api/linkedin/status
 * @desc    Check LinkedIn login status
 * @access  Public
 */
router.get('/status', async (req: Request, res: Response) => {
  try {
    const isLoggedIn = await linkedinService.checkLoginStatus();
    res.json({ 
      success: true, 
      isLoggedIn 
    });
  } catch (error) {
    console.error('Error checking login status:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to check LinkedIn login status' 
    });
  }
});

interface ProfileScreenshotRequest {
  profileUrl: string;
}

/**
 * @route   POST /api/linkedin/screenshot
 * @desc    Capture a screenshot of a LinkedIn profile page and upload it to Azure
 * @access  Public
 * @body    { profileUrl: string }
 */
router.post('/screenshot', async (req: Request, res: Response) => {
  try {
    const { profileUrl } = req.body as ProfileScreenshotRequest;

    // Validate input
    if (!profileUrl) {
      return res.status(400).json({ 
        success: false, 
        error: 'Profile URL is required' 
      });
    }

    // Check if URL is a valid LinkedIn profile URL
    if (!profileUrl.includes('linkedin.com/in/')) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid LinkedIn profile URL' 
      });
    }

    // Capture profile screenshot
    const result = await linkedinService.captureProfileScreenshot(profileUrl);

    // Return result
    res.json({
      success: true,
      data: {
        profileUrl,
        imageUrl: result.imageUrl,
        azureUrl: result.azureUrl
      }
    });
  } catch (error) {
    console.error('Error capturing profile screenshot:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to capture profile screenshot' 
    });
  }
});

interface BatchProfileScreenshotsRequest {
  profileUrls: string[];
}

interface ProfileScreenshotResult {
  profileUrl: string;
  imageUrl: string | null;
  azureUrl: string | null;
}

interface ProfileError {
  profileUrl: string;
  error: string;
}

/**
 * @route   POST /api/linkedin/batch-profile-screenshots
 * @desc    Capture screenshots of multiple LinkedIn profiles and upload them to Azure
 * @access  Public
 * @body    { profileUrls: string[] }
 */
router.post('/batch-profile-screenshots', async (req: Request, res: Response) => {
  try {
    const { profileUrls } = req.body as BatchProfileScreenshotsRequest;

    // Validate input
    if (!profileUrls || !Array.isArray(profileUrls) || profileUrls.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Profile URLs array is required' 
      });
    }

    // Process each profile URL
    const results: ProfileScreenshotResult[] = [];
    const errors: ProfileError[] = [];

    for (const profileUrl of profileUrls) {
      try {
        // Check if URL is a valid LinkedIn profile URL
        if (!profileUrl.includes('linkedin.com/in/')) {
          errors.push({ profileUrl, error: 'Invalid LinkedIn profile URL' });
          continue;
        }

        // Capture profile screenshot
        const result = await linkedinService.captureProfileScreenshot(profileUrl);
        results.push({
          profileUrl,
          imageUrl: result.imageUrl,
          azureUrl: result.azureUrl
        });
      } catch (error: any) {
        errors.push({ profileUrl, error: error.message || 'Unknown error' });
      }
    }

    // Return results
    res.json({
      success: true,
      data: {
        results,
        errors,
        totalProcessed: profileUrls.length,
        successCount: results.length,
        errorCount: errors.length
      }
    });
  } catch (error) {
    console.error('Error processing batch profile screenshots:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to process batch profile screenshots' 
    });
  }
});

export default router;
