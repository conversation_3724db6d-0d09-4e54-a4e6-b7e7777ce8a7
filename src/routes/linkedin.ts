import express, { Request, Response } from 'express';
import * as linkedinService from '../services/linkedin';

const router = express.Router();

/**
 * @route   GET /api/linkedin/status
 * @desc    Check LinkedIn login status
 * @access  Public
 */
router.get('/status', async (req: Request, res: Response) => {
  try {
    const isLoggedIn = await linkedinService.checkLoginStatus();
    res.json({ 
      success: true, 
      isLoggedIn 
    });
  } catch (error) {
    console.error('Error checking login status:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to check LinkedIn login status' 
    });
  }
});

interface ProfileImageRequest {
  profileUrl: string;
}

/**
 * @route   POST /api/linkedin/screenshot
 * @desc    Extract profile image from LinkedIn profile
 * @access  Public
 * @body    { profileUrl: string }
 */
router.post('/screenshot', async (req: Request, res: Response) => {
  try {
    const { profileUrl } = req.body as ProfileImageRequest;
    
    // Validate input
    if (!profileUrl) {
      return res.status(400).json({ 
        success: false, 
        error: 'Profile URL is required' 
      });
    }
    
    // Check if URL is a valid LinkedIn profile URL
    if (!profileUrl.includes('linkedin.com/in/')) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid LinkedIn profile URL' 
      });
    }
    
    // Extract profile image
    const result = await linkedinService.captureProfileScreenshot(profileUrl);
    
    // Return result
    res.json({
      success: true,
      data: {
        profileUrl,
        imageUrl: result.imageUrl,
        azureUrl: result.azureUrl
      }
    });
  } catch (error) {
    console.error('Error extracting profile image:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to extract profile image' 
    });
  }
});

export default router;