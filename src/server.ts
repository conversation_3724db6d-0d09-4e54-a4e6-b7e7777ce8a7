import dotenv from 'dotenv';
import express, { Request, Response, NextFunction } from 'express';
import bodyParser from 'body-parser';
import * as fileUtils from './utils/fileUtils';
import * as azureService from './services/azure';
import linkedinRoutes from './routes/linkedin';

// Initialize environment variables
dotenv.config();

const app = express();
const port = process.env.PORT || 3000;

// Initialize temp directory and schedule cleanup
fileUtils.ensureTempDir();
fileUtils.scheduleCleanup();

// Initialize Azure Blob Storage container
(async () => {
  try {
    await azureService.initContainer();
  } catch (error) {
    console.error('Failed to initialize Azure Blob Storage container:', error);
    console.log('API will continue to function, but file uploads to Azure may fail');
  }
})();

// Middleware
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Add request logging middleware
app.use((req: Request, res: Response, next: NextFunction) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
});

// Use routes
app.use('/api/linkedin', linkedinRoutes);

// Basic route for testing
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'LinkedIn Profile Scraper API',
    status: 'running',
    version: '1.0.0',
    endpoints: {
      status: 'GET /api/linkedin/status',
      profileImage: 'POST /api/linkedin/screenshot',
    }
  });
});

// Error handling middleware
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Handle 404 errors
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
app.listen(port, () => {
  console.log(`LinkedIn Profile Scraper API running on port ${port}`);
  console.log(`Server started at: ${new Date().toISOString()}`);
});