import { BlobServiceClient, ContainerClient, BlockBlobClient } from '@azure/storage-blob';


// Get connection string and container name from environment variables
let connectionString: string | undefined;
let containerName: string;
let blobServiceClient: BlobServiceClient | null = null;

// Initialize Azure configuration
function initializeAzureConfig() {
  connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
  containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || 'linkedin-profile-images';

  console.log('Azure connection string exists:', !!connectionString);
  console.log('Container name:', containerName);

  // Create BlobServiceClient
  blobServiceClient = connectionString
    ? BlobServiceClient.fromConnectionString(connectionString)
    : null;
}

/**
 * Initialize the Azure Blob Storage container
 * @returns {Promise<boolean>} - True if initialization was successful, false otherwise
 */
export async function initContainer(): Promise<boolean> {
  // Initialize Azure configuration if not already done
  if (!blobServiceClient) {
    initializeAzureConfig();
  }

  if (!blobServiceClient) {
    console.warn('Azure Blob Storage connection string not provided. Storage functionality disabled.');
    return false;
  }

  try {
    // Get a reference to a container
    const containerClient: ContainerClient = blobServiceClient.getContainerClient(containerName);
    
    // Create the container if it doesn't exist
    const containerExists = await containerClient.exists();
    if (!containerExists) {
      console.log(`Creating container: ${containerName}`);
      await containerClient.create({ access: 'blob' }); // 'blob' access means public read access for blobs
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing Azure Blob Storage container:', error);
    return false;
  }
}

/**
 * Upload an image to Azure Blob Storage
 * @param {string} imagePath - Local path to the image file
 * @param {string} blobName - Name to use for the blob (filename)
 * @returns {Promise<string|null>} - URL of the uploaded blob or null if upload failed
 */
export async function uploadImage(imagePath: string, blobName: string): Promise<string | null> {
  // Initialize Azure configuration if not already done
  if (!blobServiceClient) {
    initializeAzureConfig();
  }

  // If connection string is not provided, return null
  if (!blobServiceClient) {
    console.warn('Azure Blob Storage connection string not provided. Upload skipped.');
    return null;
  }
  
  try {
    // Initialize container
    const containerInitialized = await initContainer();
    if (!containerInitialized) {
      return null;
    }
    
    // Get a reference to a container
    const containerClient: ContainerClient = blobServiceClient.getContainerClient(containerName);
    
    // Get a block blob client
    const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    // Upload file
    console.log(`Uploading to Azure Blob Storage as ${blobName}`);
    await blockBlobClient.uploadFile(imagePath);
    
    // Return the URL of the blob
    return blockBlobClient.url;
  } catch (error) {
    console.error('Error uploading to Azure Blob Storage:', error);
    return null;
  }
}

/**
 * Delete a blob from Azure Blob Storage
 * @param {string} blobName - Name of the blob to delete
 * @returns {Promise<boolean>} - True if deletion was successful, false otherwise
 */
export async function deleteBlob(blobName: string): Promise<boolean> {
  // Initialize Azure configuration if not already done
  if (!blobServiceClient) {
    initializeAzureConfig();
  }

  // If connection string is not provided, return false
  if (!blobServiceClient) {
    console.warn('Azure Blob Storage connection string not provided. Delete skipped.');
    return false;
  }
  
  try {
    // Get a reference to a container
    const containerClient: ContainerClient = blobServiceClient.getContainerClient(containerName);
    
    // Get a block blob client
    const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    // Delete the blob
    console.log(`Deleting blob: ${blobName}`);
    await blockBlobClient.delete();
    
    return true;
  } catch (error) {
    console.error('Error deleting blob from Azure Blob Storage:', error);
    return false;
  }
}