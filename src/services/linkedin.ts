import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'playwright';
import path from 'path';
import fs from 'fs';
import * as azureService from './azure';

// Directory to temporarily store profile screenshots
const TEMP_DIR = path.join(__dirname, '../../temp');

// Ensure temp directory exists
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

interface BrowserInit {
  browser: Browser;
  context: BrowserContext;
}

/**
 * Initialize browser and context with LinkedIn authentication
 */
export async function initBrowser(): Promise<BrowserInit> {
  const browser = await chromium.launch({ headless: true });

  // Check if auth file exists
  const authPath = path.join(__dirname, '../../linkedin-auth.json');
  let context: BrowserContext;

  if (fs.existsSync(authPath)) {
    // Use existing authentication
    context = await browser.newContext({ storageState: authPath });
  } else {
    // Create new authentication
    context = await browser.newContext();
    const page = await context.newPage();

    // Navigate to LinkedIn login
    await page.goto('https://www.linkedin.com/login');

    // Fill in login form
    await page.fill('#username', process.env.LINKEDIN_USERNAME || '');
    await page.fill('#password', process.env.LINKEDIN_PASSWORD || '');
    await page.click('button[type="submit"]');

    // Wait for navigation to complete
    await page.waitForNavigation();

    // Save authentication state
    await context.storageState({ path: authPath });
  }

  return { browser, context };
}

export interface ProfileScreenshotResult {
  imageUrl: string | null;
  localPath: string | null;
  azureUrl: string | null;
}

/**
 * Capture a screenshot of a LinkedIn profile page and upload it to Azure Blob Storage
 */
export async function captureProfileScreenshot(profileUrl: string): Promise<ProfileScreenshotResult> {
  const { browser, context } = await initBrowser();
  let imageUrl: string | null = null;
  let imagePath: string | null = null;
  let azureUrl: string | null = null;

  try {
    const page = await context.newPage();
    console.log(`Navigating to: ${profileUrl}`);

    // Navigate to profile
    await page.goto(profileUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await page.waitForTimeout(3000); // Wait for content to load

    // Generate a unique filename for the screenshot
    const filename = `profile_${Date.now()}.jpg`;
    imagePath = path.join(TEMP_DIR, filename);

    // Take a screenshot of the LinkedIn profile page
    await page.screenshot({ path: imagePath, fullPage: true });
    console.log(`Screenshot saved as ${imagePath}`);

    // Upload to Azure Blob Storage
    azureUrl = await azureService.uploadImage(imagePath, filename);

    // Set imageUrl to null since we're not extracting it anymore
    imageUrl = null;
  } catch (error) {
    console.error('Error capturing profile screenshot:', error);
    throw error;
  } finally {
    await browser.close();
  }

  return { 
    imageUrl, 
    localPath: imagePath,
    azureUrl
  };
}

/**
 * Check if user is logged in to LinkedIn
 */
export async function checkLoginStatus(): Promise<boolean> {
  const { browser, context } = await initBrowser();
  let isLoggedIn = false;

  try {
    const page = await context.newPage();
    await page.goto('https://www.linkedin.com/feed/', { waitUntil: 'domcontentloaded' });

    // The "Me" menu is only visible if you are logged in
    isLoggedIn = await page.locator('img.global-nav__me-photo').count() > 0;
  } catch (error) {
    console.error('Error checking login status:', error);
  } finally {
    await browser.close();
  }

  return isLoggedIn;
}
