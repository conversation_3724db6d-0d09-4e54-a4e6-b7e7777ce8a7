import { useLocation } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import portalPublicService from "@/lib/services/portalPublicService";
import { REGION_ID } from "@/Constants";

// Custom hook to fetch recent transactions
export const useData = () => {
  const location = useLocation();

  const { data, isLoading, error } = useQuery({
    queryKey: ["publicdata"],
    queryFn: async () => {
      const response = await portalPublicService.getPublicData(REGION_ID);
      return response;
    },
    staleTime: 5 * 60 * 1000,
    retry: 3,
    enabled: location.pathname === "/signup", // TODO: REMOVE if needed to be used in the landing page.
  });

  return {
    data,
    isLoading,
    error,
  };
};
