import { create } from "zustand";
import { useQuery } from "@tanstack/react-query";
import { Transaction } from "./types";
import transactionService, {
  TransactionFilters,
} from "@/lib/services/transactionService";

interface TransactionStore {
  // State
  transactions: Transaction[];
  total: number;
  skip: number;
  limit: number;
  error: Error | null;

  // Actions

  clearTransactions: () => void;

  // Query key
  transactionsQueryKey: string[];
}

// Create the store
export const useTransactionStore = create<TransactionStore>((set) => ({
  transactions: [],
  total: 0,
  skip: 0,
  limit: 30,
  error: null,
  transactionsQueryKey: ["transactions", "list"],

  clearTransactions: () =>
    set({
      transactions: [],
      total: 0,
      skip: 0,
      limit: 30,
    }),
}));

// Custom hook to fetch and sync transactions
export const useTransactions = (filters: TransactionFilters = {}) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["transactions", "list", filters],
    queryFn: () => transactionService.getTransactions(filters),
    select: (response) => ({
      items: response.data.items || [],
      total: response.data.total || 0,
      skip: response.data.skip || 0,
      limit: response.data.limit || 30,
    }),
  });

  return {
    transactions: data?.items || [],
    total: data?.total || 0,
    isLoading,
    error,
  };
};
