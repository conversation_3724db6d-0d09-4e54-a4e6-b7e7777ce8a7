import fs from 'fs';
import path from 'path';

// Directory to temporarily store profile images
const TEMP_DIR = path.join(__dirname, '../../temp');

/**
 * Ensure the temporary directory exists
 */
export function ensureTempDir(): void {
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
    console.log(`Created temporary directory: ${TEMP_DIR}`);
  }
}

/**
 * Clean up temporary files older than the specified age
 * @param {number} maxAgeMs - Maximum age of files in milliseconds (default: 1 hour)
 */
export function cleanupTempFiles(maxAgeMs: number = 60 * 60 * 1000): void {
  try {
    ensureTempDir();
    
    const now = Date.now();
    const files = fs.readdirSync(TEMP_DIR);
    
    let deletedCount = 0;
    
    for (const file of files) {
      const filePath = path.join(TEMP_DIR, file);
      const stats = fs.statSync(filePath);
      
      // Check if the file is older than maxAgeMs
      if (now - stats.mtimeMs > maxAgeMs) {
        fs.unlinkSync(filePath);
        deletedCount++;
      }
    }
    
    if (deletedCount > 0) {
      console.log(`Cleaned up ${deletedCount} temporary files`);
    }
  } catch (error) {
    console.error('Error cleaning up temporary files:', error);
  }
}

/**
 * Schedule periodic cleanup of temporary files
 * @param {number} intervalMs - Interval in milliseconds (default: 1 hour)
 */
export function scheduleCleanup(intervalMs: number = 60 * 60 * 1000): NodeJS.Timeout {
  // Clean up on startup
  cleanupTempFiles();
  
  // Schedule periodic cleanup
  const intervalId = setInterval(() => {
    cleanupTempFiles();
  }, intervalMs);
  
  console.log(`Scheduled temporary file cleanup every ${intervalMs / 1000 / 60} minutes`);
  
  return intervalId;
}

// Export the TEMP_DIR constant
export { TEMP_DIR };