const config = require('../lib/config');
const logger = require('../lib/logger');

const log = logger(config.logger);

class DateUtils {
    // ? By default when called, will add 1 hour to the current timestamp.
    static addHoursToDate(hours = 1, date = new Date()) {
        try {
            return new Date(new Date(date).setTime(new Date(date).getTime() + hours * 60 * 60 * 1000));
        } catch (err) {
            log.error('Failed to add hours to date', err);
            return null;
        }
    }
}

module.exports = DateUtils;
