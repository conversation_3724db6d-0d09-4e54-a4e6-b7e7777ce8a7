const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const secretConfig = require('../../config');
const CampaignsDAO = require('../../lib/db/dao/CampaignsDAO');
const LoyaltyService = require('../../lib/services/LoyaltyService');
const { STATUS } = require('../../lib/db/models/enums/campaign.enums');
const CampaignProgressTrackerQueue = require('../../workers/processors/campaign.progress.tracker.mq').getQueue();
const CampaignMessagesForwardingQueue =
    require('../../workers/processors/campaign.messages.forwarding.processor.mq').getQueue();
const Mustache = require('mustache');
const QueuedCampaignMessageDAO = require('../../lib/db/dao/QueuedCampaignMessagesDAO');

const jobLog = (job, msg) => {
    log.info(msg);
    job.log(msg);
};

class CampaignProcessorBaseClass {
    constructor(job) {
        if (this.constructor === CampaignProcessorBaseClass) {
            throw new Error('Class is of abstract type and can\'t be instantiated');
        }

        this.job = job;
        this.messagesCount = 0;
        this.processedMessages = [];
    }

    async run(transport) {
        const { organizationId, campaignId } = this.job.data;
        try {
            jobLog(this.job, `starting campaign job processing for ${this.job.id}`);
            log.debug(this.job.data);

            const { message, organizationId, regionId, senderId, segmentFilters ,providerName} = this.job.data;

            await CampaignsDAO.updateCampaign(
                {
                    status: STATUS.RUNNING
                },
                campaignId,
                organizationId
            );
            jobLog(this.job, `updated campaign with id: "${campaignId}" ${STATUS.RUNNING}`);

            await this.processSegments(segmentFilters, organizationId, regionId, senderId, message, campaignId, providerName);

            const progressTrackerJob = await CampaignProgressTrackerQueue.getJob(this.job.id);
            if (progressTrackerJob) {
                await progressTrackerJob.updateData({
                    ...progressTrackerJob.data,
                    totalMessagesCount: this.messagesCount
                });
            }

            await CampaignsDAO.updateCampaign(
                {
                    totalMessages: this.messagesCount
                },
                campaignId,
                organizationId
            );

            if (!secretConfig.TEST_CAMPAIGN_BUILDING) {
                await CampaignMessagesForwardingQueue.add(
                    '',
                    {
                        campaignId,
                        transport
                    },
                    {
                        jobId: campaignId
                    }
                );
            }

            return Promise.resolve();
        } catch (err) {
            log.error(err);
            await CampaignsDAO.updateCampaign(
                {
                    endOn: new Date(),
                    status: STATUS.FAILED
                },
                campaignId,
                organizationId
            );
            log.error(`updated campaign with id: "${campaignId}" ${STATUS.FAILED}`);
            return Promise.reject(err);
        }
    }

    async processSegments(segmentFilters, organizationId, regionId) {
        const processSegment = async (segment, segmentIndex) => {
            delete segment._id;
            delete segment.id;
            const parsedSegment = {};
            for (const key in segment) {
                try {
                    parsedSegment[key] = JSON.parse(segment[key]);
                } catch (e) {
                    console.error(`Failed to parse ${key}:`, e);
                    parsedSegment[key] = segment[key];
                }
            }

            const { lastId, resumeCount } = await LoyaltyService.processMembersStream(
                organizationId,
                parsedSegment,
                regionId,
                this.job,
                this.job?.data,
                segmentIndex,
                this.processMemberData.bind(this)
            );
            if (this.processedMessages.length > 0) {
                const insertedDocumentsCount = await QueuedCampaignMessageDAO.createQueuedCampaignMessageBulk(
                    this.processedMessages
                );
                this.messagesCount += insertedDocumentsCount;
                this.processedMessages = [];
            }
            this.job.updateData({ ...this.job.data, segmentIndex, lastId, resumeCount });
        };

        if (segmentFilters && segmentFilters.length > 0) {
            const segmentIndex = this.job.data.segmentIndex ?? 0;
            for (let i = segmentIndex; i < segmentFilters.length; i++) {
                await processSegment(segmentFilters[i], i);
            }
        } else {
            await processSegment({}, null);
        }
    }

    async processMemberData(members, lastId, resumeCount, segmentIndex) {
        for (const member of members) {
            const pointsToExpire = member.pointsToExpire;
            if (pointsToExpire && Array.isArray(pointsToExpire) && pointsToExpire.length > 0) {
                member.pointsToExpire = Number(pointsToExpire[0]?.pointsToExpire).toFixed(2) || 0;
                member.pointsExpireOn = pointsToExpire[0]?.pointsExpireOn || '';
            } else {
                member.pointsToExpire = 0;
                member.pointsExpireOn = '';
            }

            const { message, organizationId, regionId, senderId, campaignId ,providerName} = this.job.data;
            await this.buildAndAddMessageToTheQueue(organizationId, regionId, senderId, campaignId, message, member,providerName);
            if (this.processedMessages.length >= secretConfig.LOYALTY_SERVICE_MEMBER_REQUEST_CHUCK_SIZE) {
                const insertedDocumentsCount = await QueuedCampaignMessageDAO.createQueuedCampaignMessageBulk(
                    this.processedMessages
                );
                this.messagesCount += insertedDocumentsCount;
                this.processedMessages = [];
            }
        }

        if (resumeCount % 10000 === 0) {
            this.job.updateData({ ...this.job.data, segmentIndex, lastId, resumeCount });
        }
    }

    buildMessageBody(messageTemplate, templateData) {
        Mustache.parse(messageTemplate);
        return Mustache.render(messageTemplate, templateData);
    }

    async buildAndAddMessageToTheQueue() {
        throw new Error('Must be implemented by subclass');
    }

    static startProcess() {
        throw new Error('Must be implemented by subclass');
    }
}

module.exports = CampaignProcessorBaseClass;
