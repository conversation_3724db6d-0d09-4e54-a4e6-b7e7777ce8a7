const GLOBAL_PREFFIX = 'GLOBAL';

const GLO<PERSON>L_CACHE_KEY_LAST_PRODUCT_ITEM_ID = `${GLOBAL_PREFFIX}:last_product_item_object_id`;

module.exports = {
    QUEUES: {
        TRANSACTION_IMPORT_JOBS_QUEUE: 'transaction_import_jobs',
        TRANSACTIONS_QUEUE: 'transaction_jobs',
        TRANSACTIONS_RETRY_QUEUE: 'transaction_retry_jobs',
        REWARDS_TOPUP_JOB_QUEUE: 'reward_topup_jobs',
        MESSAGES_QUEUE: 'messages',
        EMAIL_MESSAGES_QUEUE: 'email_messages',
        CONTACT_METATDATA_QUEUE: 'contact_metadata',
        ACTIVITIES_QUEUE: 'activities',
        POINT_EXPIRY_MASTER_QUEUE: 'point_expiry_master_queue',
        POINT_EXPIRY_REGIONAL_QUEUE: 'point_expiry_regional_queue',
        POINT_EXPIRY_CALCULATION_QUEUE: 'point_expiry_calculation_queue',
        CARD_STOCK_VIEW_REFRESH_QUEUE: 'card_stock_view_refresh_queue',
        TIER_STAT_REFRESH_QUEUE: 'tier_stat_refresh_queue',
        TIER_CALCULATION_QUEUE: 'tier_calculation_queue',
        TIER_CALCULATION_MASTER_QUEUE: 'tier_calculation_master_queue',
        AFFINITY_GROUP_MEMBER_IMPORT_JOBS_QUEUE: 'affinity_group_member_import_jobs',
        AFFINITY_GROUP_MEMBER_IMPORTS_QUEUE: 'affinity_group_member_imports_queue',
        AFFINITY_GROUP_EXPIRY_QUEUE: 'affinity_group_expiry_queue',
        REWARD_DISTRIBUTIONS_IMPORT_JOBS_QUEUE: 'reward_distributions_import_jobs_queue',
        REWARD_DISTRIBUTIONS_IMPORTS_QUEUE: 'reward_distributions_imports_queue',
        REWARD_REDEMPTION_LOGS_BULK_REFUND_QUEUE: 'reward_redemption_logs_bulk_refund_queue',
        SECONDARY_MEMBER_REACTIVATE_QUEUE: 'secondary_member_reactivate_queue',
        // * JOBS
        EXPIRY_POINTS_MONTHLY_LIVE_JOBS_QUEUE: 'expiry_points_monthly_live_jobs_queue',
        EXPIRY_POINTS_MONTHLY_SIMULATION_JOBS_QUEUE: 'expiry_points_monthly_simulation_jobs_queue',
        ROLLING_POINTS_BALANCE_SUMMARY_JOBS_QUEUE: 'rolling_points_balance_summary_jobs_queue',
        ROLLING_POINTS_BALANCE_DETAILED_JOBS_QUEUE: 'rolling_points_balance_detailed_jobs_queue',
        MLS_POINTS_MOVEMENT_SUMMARY_JOBS_QUEUE: 'mls_points_movement_summary_jobs_queue',
        MLS_POINTS_MOVEMENT_DETAILED_JOBS_QUEUE: 'mls_points_movement_detailed_jobs_queue',
        MEMBERS_EXPORT_JOBS_QUEUE: 'members_export_jobs_queue',
        MEMBER_STAT_REFRESH_QUEUE: 'member_stat_refresh_queue',
        MEMBER_STAT_UPDATE_QUEUE: 'member_stat_update_queue',
        INTER_REGION_POINTS_COLLECTION_QUEUE: 'inter_region_points_collection_queue',
        ADJUST_SECONDARY_ACCOUNT_POINT_COLLECTION_QUEUE: 'adjust_secondary_account_point_collection_queue',
        FORGET_MEMBER_LOYALTY_SERVICE_QUEUE: 'forget_member_loyalty_service_queue',
        FORGET_MEMBER_CORE_SERVICE_QUEUE: 'forget_member_core_service_queue',
        FORGET_MEMBER_MESSAGE_SERVICE_QUEUE: 'forget_member_message_service_queue',
        TRANSACTION_SUMMARY_REGIONAL_JOB_QUEUE: 'transaction_summary_regional_job_queue',
        AFFINITY_GROUP_COUNT_REFRESH_QUEUE: 'affinity_group_count_refresh_queue',
        KAFKA_FAILED_TRANSACTION_RETRY_QUEUE: 'kafka_failed_transaction_retry_queue',
        ANALYTICS_TRANSACTIONS_SYNC_JOB_QUEUE: 'analytics_transactions_sync_job_queue',
        LOYALTY_SERVICE_BOOTSTRAP_JOBS: 'loyalty_service_bootstrap_jobs',
        CORE_SERVICE_BOOTSTRAP_JOBS: 'core_service_bootstrap_jobs',
        ANOMALY_SERVICE_BOOTSTRAP_JOBS: 'anomaly_service_bootstrap_jobs',
        TRANSACTIONS_EXPORT_JOBS_QUEUE: 'transactions_export_jobs_queue',
        POINTS_OVERVIEW_EXPORT_JOBS_QUEUE: 'points_overview_export_jobs_queue',
        POINTS_SUMMARY_EXPORT_JOBS_QUEUE: 'points_summary_export_jobs_queue',
        MEMBER_AUTO_SUSPENSION_JOBS_QUEUE: 'member_auto_suspension_jobs_queue',
        PRE_BIRTHDAY_ACTIVITIES_GENERATOR_QUEUE: 'pre_birthday_activities_generator_queue',
        BIRTHDAY_POINT_RULE_JOBS_QUEUE: 'BIRTHDAY_POINT_RULE_JOBS_QUEUE',
        BIRTHDAY_ACTIVITIES_GENERATOR_QUEUE: 'birthday_activities_generator_queue',
        SEGMENTS_MEMBER_POINT_STATS_GENERATOR: 'segments_member_point_stats_generator',
        PRODUCT_CATALOGUE_GENERATION_JOBS_QUEUE: 'product_catalogue_generation_jobs_queue',
        PRODUCT_CATALOGUE_EXPORT_JOBS_QUEUE: 'product_catalogue_export_jobs_queue'
    },
    DEFAULT_OPTIONS: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: { count: 1000 },
        removeOnFail: { count: 1000 }
    },
    CATEGORY: { SYSTEM: 'SYSTEM', CORE: 'CORE', CAMPAIGN: 'CAMPAIGN', UNLISTED: 'UNLISTED' },
    GLOBAL_VARIABLES: { GLOBAL_CACHE_KEY_LAST_PRODUCT_ITEM_ID }
};
