const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const config = require('./../lib/config');
const logger = require('./../lib/logger');
const log = logger(config.logger);
const MongooseConnector = require('./../lib/db/connectors/MongooseConnector');
require('./../lib/db/models');

const TransactionsImportJobProcessor = require('./processors/transactions.import.job.processor');
const TransactionsProcessor = require('./processors/transactions.processor');
const RewardsTopupJobProcessor = require('./processors/rewards.topup.job.processor');
const PointExpiryMasterJobProcessor = require('./processors/point.expiry.master.job.processor');
const PointExpiryRegionalJobProcessor = require('./processors/point.expiry.regional.job.processor');
const PointExpiryCalculationJobProcessor = require('./processors/point.expiry.calculation.processor');
const CardStockViewRefreshJobProcessor = require('./processors/card.stock.view.refresh.job.processor');
const TierJobProcessor = require('./processors/tier.calculation.processor');
const AffinityGroupMemberImportJobProcessor = require('./processors/affinity.group.member.import.job.processor');
const AffinityGroupMemberImportProcessor = require('./processors/affinity.group.member.import.processor');
const TransactionsStreamConsumer = require('./processors/transactions.stream.consumer');
const AffinityGroupExpiryJobProcessor = require('./processors/affinity.group.expiry.job.processor');
const ExpiryPointsMonthlyLiveJobProcessor = require('./processors/expiry.points.monthly.live.job.processor');
const ExpiryPointsMonthlySimulationJobProcessor = require('./processors/expiry.points.monthly.simulation.job.processor');
const RollingPointBalanceSummaryJobProcessor = require('./processors/rolling.point.balance.summary.job.processor');
const RollingPointBalanceDetailedJobProcessor = require('./processors/rolling.point.balance.detailed.job.processor');
const MlsPortalPointMovementJobProcessor = require('./processors/mls.portal.point.movement.job.processor');
const MlsPortalPointMovementDetailedJobProcessor = require('./processors/mls.portal.point.movement.detailed.job.processor');
const RewardDistributionsImportJobProcessor = require('./processors/reward.distributions.import.job.processor');
const RewardDistributionsImportProcessor = require('./processors/reward.distributions.import.processor');
const RewardRedemptionLogsBulkRefundJobProcessor = require('./processors/reward.redemption.logs.bulk.refund.job.processor');
const TierStatRefreshJobProcessor = require('./processors/tier.stat.refresh.job.processor');
const MemberExportJobProcessor = require('./processors/member.export.job.processor');
const TransactionsRetryProcessor = require('./processors/transactions.retry.processor');
const PointsToExpireAndTierPointsCalculationTransactionStreamConsumer = require('./processors/points.to.expire.and.tier.points.calculation.transaction.stream.consumer');
const InsightCalculationsRefreshJobProcessor = require('./processors/insight.calculations.refresh.job.processor');
const InsightCalculationsMemberUpdateJobProcessor = require('./processors/insight.calculations.member.update.job.processor');
const ForgetMemberJobProcessor = require('./processors/forget.member.job.processor');
const TransactionsSummaryJobProcessor = require('./processors/transaction.summary.regional.job.processor');
const SecondaryMemberReactivateJobProcessor = require('./processors/member.reactivate.secondary.members.processor');
const AffinityGroupCountRefreshJobProcessor = require('./processors/affinity.group.count.refresh.job.processor');
const AuthActivitiesEventStreamProcessor = require('./processors/auth.activities.event.stream.processor');
const KafkaFailedTransactionRetryQueue = require('./processors/kafka.failed.transaction.retry.processor');
const PostgresTransactionsSyncProcessor = require('./processors/postgres.transactions.sync.processor');
const BootstrapJobsProcessor = require('./processors/bootstrap.jobs.processor');
const TransactionExportJobProcessor = require('./processors/transaction.export.job.processor');
const SegmentsProcessor = require('./processors/segments.processor');
const PointsOverviewExportJobProcessor = require('./processors/points.overview.export.job.processor');
const PointsSummaryExportJobProcessor = require('./processors/points.summary.export.job.processor');
const TransactionSyncIntegrationProcessor = require('./processors/transaction.sync.integration.processor');
const TransactionSyncProcessorProductItems = require('./processors/transactions.stream.consumer.product.items');
const SegmentsMemberPointsStatsGenerator = require('./processors/segments.member.points.stats.generator');
const BirthdayActivitiesGenerator = require('./processors/birthday.activities.generator');
const PreBirthdayActivitiesGenerator = require('./processors/pre.birthday.activities.generator');
const BirthdayPointRuleProcessor = require('./processors/birthday.point.rule.processor');
const AnomalyMemberAutoSuspensionJobProcessor = require('./processors/anomaly.member.auto.suspension.job.processor');
const ProductCatalogueGenerationProcessor = require('./processors/product.catalogue.generation.processor');
const ProcessInitializersProcessor = require('./processors/process.initializers.processor');
const ProductCatalogueExportJobProcessor = require('./processors/product.catalogue.export.job.processor');

(async () => {
    await MongooseConnector.initialize();
    log.info('starting worker processes...');
    TransactionsImportJobProcessor.startProcess();
    TransactionsProcessor.startProcess();
    RewardsTopupJobProcessor.startProcess();
    PointExpiryMasterJobProcessor.startProcess();
    PointExpiryRegionalJobProcessor.startProcess();
    PointExpiryCalculationJobProcessor.startProcess();
    CardStockViewRefreshJobProcessor.startProcess();
    TierJobProcessor.startProcess();
    AffinityGroupMemberImportJobProcessor.startProcess();
    AffinityGroupMemberImportProcessor.startProcess();
    AffinityGroupExpiryJobProcessor.startProcess();
    TransactionsStreamConsumer.startProcess();
    ExpiryPointsMonthlyLiveJobProcessor.startProcess();
    ExpiryPointsMonthlySimulationJobProcessor.startProcess();
    RollingPointBalanceSummaryJobProcessor.startProcess();
    RollingPointBalanceDetailedJobProcessor.startProcess();
    MlsPortalPointMovementJobProcessor.startProcess();
    RewardDistributionsImportJobProcessor.startProcess();
    RewardDistributionsImportProcessor.startProcess();
    RewardRedemptionLogsBulkRefundJobProcessor.startProcess();
    TierStatRefreshJobProcessor.startProcess();
    MemberExportJobProcessor.startProcess();
    TransactionsRetryProcessor.startProcess();
    PointsToExpireAndTierPointsCalculationTransactionStreamConsumer.startProcess();
    MlsPortalPointMovementDetailedJobProcessor.startProcess();
    InsightCalculationsRefreshJobProcessor.startProcess();
    InsightCalculationsMemberUpdateJobProcessor.startProcess();
    TransactionsSummaryJobProcessor.startProcess();
    ForgetMemberJobProcessor.startProcess();
    SecondaryMemberReactivateJobProcessor.startProcess();
    AffinityGroupCountRefreshJobProcessor.startProcess();
    AuthActivitiesEventStreamProcessor.startProcess();
    KafkaFailedTransactionRetryQueue.startProcess();
    PostgresTransactionsSyncProcessor.startProcess();
    BootstrapJobsProcessor.startProcess();
    TransactionExportJobProcessor.startProcess();
    SegmentsProcessor.startProcess();
    PointsOverviewExportJobProcessor.startProcess();
    PointsSummaryExportJobProcessor.startProcess();
    AnomalyMemberAutoSuspensionJobProcessor.startProcess();
    PreBirthdayActivitiesGenerator.startProcess();
    BirthdayActivitiesGenerator.startProcess();
    BirthdayPointRuleProcessor.startProcess();
    TransactionSyncIntegrationProcessor.startProcess();
    TransactionSyncProcessorProductItems.startProcess();
    SegmentsMemberPointsStatsGenerator.startProcess();
    ProductCatalogueGenerationProcessor.startProcess();
    ProductCatalogueExportJobProcessor.startProcess();
    ProcessInitializersProcessor.startProcess();
})();
