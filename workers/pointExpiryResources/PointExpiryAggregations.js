const GetDataForPointExpiryCalculationAggregation = (organizationId, regionId, fromDate) => [
  {
    '$match': {
      'organizationId': organizationId,
      'regionId': regionId,
      'transactionOn': {
        '$gt': fromDate
      }
    }
  }, {
    '$group': {
      '_id': '$memberId',
      'totalPoints': {
        '$sum': '$signedRedeemablePoints'
      }
    }
  }, {
    '$lookup': {
      'from': 'members',
      'localField': '_id',
      'foreignField': '_id',
      'as': 'member'
    }
  }, {
    '$unwind': {
      'path': '$member',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        '_id': '$_id',
        'totalTransactionPoints': '$totalPoints',
        'memberPoints': '$member.points'
      }
    }
  }, {
    '$match': {
      '$expr': {
        '$gt': [
          '$memberPoints', '$totalTransactionPoints'
        ]
      }
    }
  }
];

const GetDataForPointExpiryCalculationAggregationV3 = (organizationId, regionId, fromDate, pointExpiryCalculationSubTransactionTypeIds) => [
  {
    '$match': {
      'organizationId': organizationId,
      'regionId': regionId,
      'transactionOn': {
        '$lt': fromDate
      }
    }
  }, {
    '$group': {
      '_id': '$memberId',
      'totalPoints': {
        '$sum': '$signedRedeemablePoints'
      }
    }
  }, {
    '$lookup': {
      'from': 'transactions',
      'localField': '_id',
      'foreignField': 'memberId',
      'pipeline': [
        {
          '$match': {
            '$expr': {
              '$or': [
                {
                  'type': 'REDEMPTION'
                }, {
                  '$in': [
                    '$subType', pointExpiryCalculationSubTransactionTypeIds ? pointExpiryCalculationSubTransactionTypeIds : []
                  ]
                }
              ]
            },
            'organizationId': organizationId,
            'regionId': regionId,
            'signedRedeemablePoints': {
              '$lt': 0
            },
            'transactionOn': {
              '$gte': fromDate
            }
          }
        }, {
          '$group': {
            '_id': '$memberId',
            'totalPoints': {
              '$sum': '$signedRedeemablePoints'
            }
          }
        }
      ],
      'as': 'redemptions'
    }
  }, {
    '$unwind': {
      'path': '$redemptions',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': true
    }
  }, {
    '$group': {
      '_id': {
        '_id': '$_id',
        'totalPoints': '$totalPoints'
      },
      'totalRedemptions': {
        '$sum': '$redemptions.totalPoints'
      }
    }
  }, {
    '$replaceRoot': {
      'newRoot': {
        '_id': '$_id._id',
        'totalPointsBeforeDate': '$_id.totalPoints',
        'totalRedemptionsAfterDate': {
          '$cond': {
            'if': {
              '$eq': [
                {
                  '$ifNull': [
                    '$totalRedemptions', 0
                  ]
                }, 0
              ]
            },
            'then': 0,
            'else': '$totalRedemptions'
          }
        }
      }
    }
  }, {
    '$addFields': {
      'pointsToExpire': {
        '$add': [
          '$totalPointsBeforeDate', '$totalRedemptionsAfterDate'
        ]
      }
    }
  }, {
    '$match': {
      'pointsToExpire': {
        '$gt': 0
      }
    }
  }, {
    '$lookup': {
      'from': 'members',
      'localField': '_id',
      'foreignField': '_id',
      'pipeline': [
        {
          '$project': {
            'points': '$points',
            'pointsToExpire': '$pointsToExpire'
          }
        }
      ],
      'as': 'member'
    }
  }, {
    '$unwind': {
      'path': '$member',
      'includeArrayIndex': '0',
      'preserveNullAndEmptyArrays': false
    }
  }
];

module.exports = {
  GetDataForPointExpiryCalculationAggregation,
  GetDataForPointExpiryCalculationAggregationV3
}
