const Queue = require('bull');
const csv = require('csv-parser');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const secretConfig = require('./../../config');
const { find, snakeCase, trim } = require('lodash');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const AffinityGroupMemberImportJobsDAO = require('../../lib/db/dao/AffinityGroupMemberImportJobsDAO');
const { STATUS } = require('../../lib/db/models/enums/affinity.group.member.import.job.enums');
const AffinityGroupMemberImportProcessor = require('./affinity.group.member.import.processor');
const StorageWrapper = require('../../lib/wrappers/StorageWrapper');
const { QUEUES: {
    AFFINITY_GROUP_MEMBER_IMPORT_JOBS_QUEUE
} } = require('../constants');
const mongoose = require('mongoose');
const USER_ASSETS_BUCKET = secretConfig.USER_ASSETS_BUCKET;

const queue = new Queue(AFFINITY_GROUP_MEMBER_IMPORT_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`affinity group member import job ${job.id} completed`);
});

const mapHeader = (header, fieldMappings) => {
    const fieldMapping = find(fieldMappings, (mapping) => {
        return mapping.fileColumnName === header;
    });
    return fieldMapping ? fieldMapping.systemAttributeName : snakeCase(header);
}

const mapValue = (header, value) => {
    return trim(value);
}

class AffinityGroupMemberImportJobProcessor {
    static startProcess() {
        queue.process(async function (job) {
            try {
                log.info(`starting affinity group member import job ${job.id}`);
                log.debug(job.data);

                const jobId = job.id;
                const { organizationId, regionId, affinityGroupId, merchantId, fileId, fieldMappings, callerId } = job.data;

                const initialJobUpdate = {
                    status: STATUS.PROCESSING,
                    startedOn: new Date()
                }

                const updatedJob = await AffinityGroupMemberImportJobsDAO.updateImportJob(initialJobUpdate, jobId, organizationId, {
                    status: STATUS.PENDING
                });
                if (!updatedJob) {
                    return Promise.reject(new Error(`affinity group member import job status is not ${STATUS.PENDING}`));
                }

                let totalRecordsCount = 0, failedRecordsCount = 0, successRecordsCount = 0;
                const stream = await StorageWrapper.getFileStream(USER_ASSETS_BUCKET, fileId);
                stream
                  .pipe(csv({
                      mapHeaders: ({ header, index }) => {
                          return mapHeader(header, fieldMappings);
                      },
                      mapValues: ({ header, index, value }) => {
                          return mapValue(header, value);
                      }
                  }))
                  .on('data', async (data) => {
                      log.debug(data);
                      totalRecordsCount++;

                      AffinityGroupMemberImportProcessor.addJob({ organizationId, regionId, merchantId, affinityGroupId, callerId, jobId, rawData: data });
                  })
                  .on('end', async () => {
                      const completionJobUpdate = {
                          totalRecordsCount,
                          status: STATUS.PROCESSING
                      }
                      await AffinityGroupMemberImportJobsDAO.updateImportJob(completionJobUpdate, jobId, organizationId);
                      job.progress(100);
                      return Promise.resolve();
                  });
            } catch (err) {
                log.error(err);
                const jobId = job.id;
                const { organizationId } = job.data;
                await AffinityGroupMemberImportJobsDAO.updateImportJob({
                    status: STATUS.FAILED
                }, jobId, organizationId);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(data, jobId) {
        queue.add({
            ...data
        },{
            jobId: jobId || mongoose.Types.ObjectId().toString()
        });
    }
}

module.exports = AffinityGroupMemberImportJobProcessor;
