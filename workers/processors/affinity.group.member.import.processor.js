const Queue = require('bull');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const AffinityGroupMemberImportJobsDAO = require('../../lib/db/dao/AffinityGroupMemberImportJobsDAO');
const AffinityGroupMemberImportLogsDAO = require('../../lib/db/dao/AffinityGroupMemberImportLogsDAO');
const { SYSTEM_ATTRIBUTE_NAME, STATUS } = require('../../lib/db/models/enums/affinity.group.member.import.job.enums');
const CardDAO = require('../../lib/db/dao/CardDAO');
const MembersHandler = require('../../lib/handlers/MembersHandler');
const { QUEUES: {
    AFFINITY_GROUP_MEMBER_IMPORTS_QUEUE
} } = require('../constants');
const mongoose = require('mongoose');

const queue = new Queue(AFFINITY_GROUP_MEMBER_IMPORTS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`affinity group member import processing ${job.id} completed`);
});

class AffinityGroupMemberImportProcessor {
    static startProcess() {
        queue.process(async function (job) {
            try {
                log.info(`starting affinity group member import processing ${job.id}`);
                log.debug(job.data);

                const { organizationId, regionId, merchantId, affinityGroupId, callerId, jobId, rawData: data } = job.data;

                const cardNumber = data[SYSTEM_ATTRIBUTE_NAME.LOYALTY_CARD]?.toString();
                const joinDate = data[SYSTEM_ATTRIBUTE_NAME.JOIN_DATE]?.toString();
                const expirationDate = data[SYSTEM_ATTRIBUTE_NAME.EXPIRATION_DATE]?.toString();
                const membershipId = data[SYSTEM_ATTRIBUTE_NAME.MEMBER_ID]?.toString();

                const importLog = {
                    regionId,
                    merchantId,
                    affinityGroupId,
                    affinityGroupMemberImportJobId: jobId,
                    memberDetails: {
                        name: data[SYSTEM_ATTRIBUTE_NAME.NAME]?.toString(),
                        loyaltyCard: cardNumber,
                        email: data[SYSTEM_ATTRIBUTE_NAME.EMAIL]?.toString(),
                        contactNumber: data[SYSTEM_ATTRIBUTE_NAME.MOBILE_NUMBER]?.toString(),
                        memberId: data[SYSTEM_ATTRIBUTE_NAME.MEMBER_ID]?.toString(),
                        joinDate: joinDate,
                        expirationDate: expirationDate,
                    }
                }

                try {
                    if (!cardNumber) {
                        throw new Error(`no loyalty card number found in the file`)
                    }

                    const loyaltyCard = await CardDAO.getCard(organizationId, {
                        regionId: mongoose.Types.ObjectId(regionId),
                        cardNoStr: cardNumber
                    });
                    if (!loyaltyCard) {
                        throw new Error(`no loyalty card for the given loyalty card number`);
                    }
                    if (!loyaltyCard.memberId) {
                        throw new Error(`no loyalty member found for the given loyalty card number`);
                    }

                    await MembersHandler.updateMemberAffinityGroup({
                        affinityGroupId,
                        membershipId,
                        expiryDate: expirationDate
                    }, organizationId, loyaltyCard.memberId, callerId);

                    await AffinityGroupMemberImportLogsDAO.createImportLog({
                        ...importLog,
                        status: STATUS.COMPLETED,
                    }, organizationId);

                    const completionJobUpdate = {
                        $inc: {
                            processedRecordsCount: 1,
                            successRecordsCount: 1
                        }
                    }
                    await AffinityGroupMemberImportJobsDAO.updateImportJob(completionJobUpdate, jobId, organizationId);

                } catch (err) {
                    await AffinityGroupMemberImportLogsDAO.createImportLog({
                        ...importLog,
                        failureReason: err.message,
                        status: STATUS.FAILED,
                    }, organizationId);

                    const completionJobUpdate = {
                        $inc: {
                            processedRecordsCount: 1,
                            failedRecordsCount: 1
                        }
                    }
                    await AffinityGroupMemberImportJobsDAO.updateImportJob(completionJobUpdate, jobId, organizationId);

                    log.error(err);
                    return Promise.reject(err);
                }
            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(data, jobId) {
        queue.add({
            ...data
        },{
            jobId: jobId || mongoose.Types.ObjectId().toString()
        });
    }
}

module.exports = AffinityGroupMemberImportProcessor;
