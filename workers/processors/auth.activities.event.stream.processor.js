const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const KafkaConnector = require('../../lib/db/connectors/KafkaConnector');
const config = require('../../lib/config');
const secretConfig = require('../../config');
const logger = require('../../lib/logger');
const EventHandler = require('../../lib/handlers/eventHandler');
const OrganizationDAO = require('../../lib/db/dao/OrganizationDAO');
const log = logger(config.logger);

class AuthActivitiesEventStreamProcessor {
    static async startProcess() {
        try {
            const consumerGroupId = 'authActivitiesEventStreamConsumer';

            const consumer = KafkaConnector.getConsumer(consumerGroupId);
            await consumer.connect();
            log.info(`consumer with groupId: '${consumerGroupId}' connected`);

            await consumer.subscribe({
                topic: secretConfig.AUTH_ACTIVITY_TOPIC,
                fromBeginning: true
            });
            log.info(`subscribed topic: '${secretConfig.AUTH_ACTIVITY_TOPIC}'`);

            await consumer.run({
                eachMessage: async ({ message }) => {
                    try {
                        if (message?.value) {
                            const userLoginEvent = JSON.parse(message.value.toString());
                            if (userLoginEvent?.realmName !== secretConfig.KEYCLOAK_REALM) {
                                if (userLoginEvent?.identityProviderId && userLoginEvent?.loginDate) {
                                    const organizationDataCursor = await OrganizationDAO.getOrganizationsByFilter({});
                                    for (
                                        let organization = await organizationDataCursor.next();
                                        organization != null;
                                        organization = await organizationDataCursor.next()
                                    ) {
                                        if (
                                            organization?.configuration?.portalConfiguration?.idpMetadata?.realm ===
                                            userLoginEvent?.realmName
                                        ) {
                                            await EventHandler.updateMemberLastAccessedDate({
                                                identityProviderId: userLoginEvent?.identityProviderId,
                                                loginDate: userLoginEvent?.loginDate,
                                                organizationId: organization?._id
                                            });
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        log.error(e);
                        log.error(`error while processing the member: ${message.value.toString()}`);
                    }
                }
            });
        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }
}

module.exports = AuthActivitiesEventStreamProcessor;
