const config = require('../../../lib/config');
const logger = require('../../../lib/logger');
const log = logger(config.logger);

class JobLogger {
    constructor(job) {
        this.job = job;
    }

    logError(err) {
        if (typeof err === 'object') err = JSON.stringify(err);
        log.error(err);
        this.job.log(err);
    }

    logInfo(msg) {
        if (typeof msg === 'object') msg = JSON.stringify(msg);
        log.info(msg);
        this.job.log(msg);
    }
}

module.exports = JobLogger;