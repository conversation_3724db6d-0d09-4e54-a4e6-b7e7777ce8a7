const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const moment = require('moment-timezone');
const Utils = require('../../lib/Utils');
const mongoose = require('mongoose');
const { birthdayPointRulesProcessingQueue: queue } = require('../queues/queues');
const { getAsync, setAsync } = require('../../lib/db/connectors/RedisConnector').getCommands();
const { BIRTHDAY_BONUS_POINTS_TYPE } = require('./../../lib/db/models/enums/point.rule.enums');
const PointRuleDAO = require('../../lib/db/dao/PointRuleDAO');
const { TYPE, SUB_TYPE } = require('../../lib/db/models/enums/point.rule.enums');
const { STATUS } = require('../../lib/db/models/enums/point.rule.enums');
const PointsHandler = require('../../lib/handlers/PointsHandler');

queue.on('completed', function(job, result) {
    log.info(`birthday point rule processing job ${job.id} completed`);
});

let globalJob;
const writeLogs = (msg) => {
    globalJob.log(msg);
    log.debug(msg);
};

const getPointRule = async (organizationId, regionId) => {
    const cacheKey = `pointRules:${organizationId}:${regionId}:birthday`;
    const strFromCache = await getAsync(cacheKey);

    if (strFromCache) {
        return JSON.parse(strFromCache);
    } else {
        const pointRules = await PointRuleDAO.getPointRulesByFilter(organizationId, {
            regionId: mongoose.Types.ObjectId(regionId),
            type: TYPE.NON_TRANSACTIONAL,
            subType: SUB_TYPE.BIRTHDAY,
            status: STATUS.ENABLED
        });
        if (pointRules && Array.isArray(pointRules) && pointRules.length > 0) {
            const birthdayPointRuleConfig = {
                enabled: true,
                rule: pointRules[0]
            }
            await setAsync(cacheKey, JSON.stringify(birthdayPointRuleConfig), 'EX', 60 * 60 * 3);
            return birthdayPointRuleConfig;
        } else {
            const birthdayPointRuleConfig = {
                enabled: false,
                rule: null
            }
            await setAsync(cacheKey, JSON.stringify(birthdayPointRuleConfig), 'EX', 60 * 60 * 3);
            return birthdayPointRuleConfig;
        }
    }
};

class BirthdayPointRuleProcessor {
    static startProcess() {
        queue.process(async function(job) {
            try {
                log.info(`starting birthday point rule processing job processing for ${job.id}`);
                log.debug(job.data);
                globalJob = job;

                const { organizationId, regionId, memberId, baseAmount } = job.data;

                writeLogs(`Fetching birthday point rule for organizationId=${organizationId}, regionId=${regionId}`);
                const birthdayPointRuleConfig = await getPointRule(organizationId, regionId);

                if (birthdayPointRuleConfig?.enabled) {
                    writeLogs(`birthday rule available`);

                    const birthdayPointRule = birthdayPointRuleConfig.rule?.ruleData?.birthdayPointsConfiguration;
                    writeLogs(`retrieving organization and region data...`);
                    const { subtransactionTypeIdMap: { birthdayBonusPointsCollection } } = await Utils.getOrganizationData(organizationId);
                    const { defaultMerchantId, defaultMerchantLocationId, timeZone } = await Utils.getRegionData(organizationId, regionId);

                    const transaction = {
                        memberId,
                        merchantId: defaultMerchantId.toString(),
                        merchantLocationId: defaultMerchantLocationId.toString(),
                        transactionDate: new Date(),
                        transactionSubTypeId: birthdayBonusPointsCollection.toString()
                    };

                    if (birthdayPointRule.type === BIRTHDAY_BONUS_POINTS_TYPE.FIXED_POINTS) {
                        writeLogs(`adding fixed points amount`);
                        transaction.pointsAmount = birthdayPointRule.fixedPointsAmount;
                    } else if (birthdayPointRule.type === BIRTHDAY_BONUS_POINTS_TYPE.PERCENTAGE) {
                        const pointsAmount = (Number(baseAmount) / 100) * Number(birthdayPointRule.percentageOfTotalPoints);
                        writeLogs(`calculated points amount: ${pointsAmount}`);
                        if (pointsAmount && pointsAmount > 0) {
                            transaction.pointsAmount = birthdayPointRule.fixedPointsAmount;
                        }
                    } else {
                        throw new Error('Unsupported birthday point rule type');
                    }

                    if (birthdayPointRule?.awardForSingleTransaction) {
                        writeLogs(`awardForSingleTransaction is enabled. generating idempotent key...`);
                        transaction.idempotentKey = Utils.getHash(`${organizationId}:${regionId}:${memberId}:${moment().tz(timeZone).format('YYYY-MM-DD')}`);
                    }

                    writeLogs(`adjusting points...`);
                    await PointsHandler.adjustPoints(organizationId, transaction, null, null, null, true);
                }

                job.progress(100);
                return Promise.resolve();
            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }
}

BirthdayPointRuleProcessor.getBirthdayPointRule = getPointRule;
module.exports = BirthdayPointRuleProcessor;
