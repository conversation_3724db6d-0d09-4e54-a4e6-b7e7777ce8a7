const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const { QueueEvents, Worker, Queue } = require('bullmq');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const QueuedCampaignMessagesDAO = require('../../lib/db/dao/QueuedCampaignMessagesDAO');
const {
    QUEUES: { CAMPAIGN_MESSAGES_FORWARDING_QUEUE, CAMPAIGN_EMAIL_JOBS_QUEUE, CAMPAIGN_SMS_JOBS_QUEUE },
    DEFAULT_OPTIONS
} = require('../constants');
const { CHANNEL } = require('../../lib/db/models/enums/campaign.enums');
const BullQueue = require('bull');

const connection = RedisConnector.getConfig();

const queueEvents = new QueueEvents(CAMPAIGN_MESSAGES_FORWARDING_QUEUE, { connection });

queueEvents.on('completed', ({ jobId }) => {
    log.info(`campaign message forward job processing ${jobId} completed`);
});

queueEvents.on('failed', ({ failedReason }) => {
    log.error('error while processing campaign message forwarding job', failedReason);
});

const queue = new Queue(CAMPAIGN_MESSAGES_FORWARDING_QUEUE, {
    connection,
    defaultJobOptions: DEFAULT_OPTIONS
});

const getMessageQueue = (transport) => {
    return new BullQueue(transport === CHANNEL.SMS ? CAMPAIGN_SMS_JOBS_QUEUE : CAMPAIGN_EMAIL_JOBS_QUEUE, {
        redis: RedisConnector.getConfig(),
        defaultJobOptions: DEFAULT_OPTIONS
    });
};

const runJob = async (job) => {
    const { campaignId, transport } = job.data;
    const dataCursor = await QueuedCampaignMessagesDAO.getQueuedCampaignMessagesByCampaignId(campaignId, true);

    const campaignMessagesQueue = getMessageQueue(transport);

    for (let record = await dataCursor.next(); record != null; record = await dataCursor.next()) {
        const {
            organizationId,
            regionId,
            memberId,
            message: { messageBody, messageSubject },
            to,
            senderId,
            campaignId
        } = record;
        await campaignMessagesQueue.add({
            organizationId,
            regionId,
            memberId,
            subject: messageSubject,
            message: messageBody,
            to: to,
            senderId,
            campaignId
        });
    }
};

class CampaignMessagesForwardingProcessorMq {
    static startProcess() {
        const worker = new Worker(
            CAMPAIGN_MESSAGES_FORWARDING_QUEUE,
            async (job) => {
                await runJob(job);
            },
            { connection }
        );
        worker.on('error', (err) => {
            log.error(err);
        });
    }

    static getQueue() {
        return queue;
    }
}

CampaignMessagesForwardingProcessorMq.runJob = runJob;
module.exports = CampaignMessagesForwardingProcessorMq;
