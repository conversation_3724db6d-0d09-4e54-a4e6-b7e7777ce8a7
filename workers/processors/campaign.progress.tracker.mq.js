const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const moment = require('moment');
const { Queue, QueueEvents, Worker } = require('bullmq');
const config = require('../../lib/config');
const { CAMPAIGN_PROGRESS_TRACKER_DELAY } = require('../../config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const CampaignsDAO = require('../../lib/db/dao/CampaignsDAO');
const Utils = require('../../lib/Utils');
const { STATUS } = require('../../lib/db/models/enums/campaign.enums');
const {
    QUEUES: { CAMPAIGN_PROGRESS_TRACKER_QUEUE },
    DEFAULT_OPTIONS
} = require('../constants');

const connection = RedisConnector.getConfig();

const queue = new Queue(CAMPAIGN_PROGRESS_TRACKER_QUEUE, {
    connection,
    defaultJobOptions: {
        ...DEFAULT_OPTIONS,
        removeOnComplete: {
            count: 10
        },
        removeOnFail: {
            count: 10
        }
    }
});

const queueEvents = new QueueEvents(CAMPAIGN_PROGRESS_TRACKER_QUEUE, { connection });

queueEvents.on('completed', ({ jobId }) => {
    log.info(`campaign progress tracking ${jobId} completed`);
});

queueEvents.on('failed', ({ failedReason }) => {
    log.error('error while tracking campaign progress', failedReason);
});

const runJob = async (job) => {
    const {
        organizationId,
        campaignId,
        totalMessagesCount,
        successCount,
        failedCount,
        lastResponseOn,
        initialExecution
    } = job.data;

    const campaign = await CampaignsDAO.getCampaignById(organizationId, campaignId);

    let event;
    const campaignUpdateObj = {};
    if (totalMessagesCount !== 0 && totalMessagesCount === successCount + failedCount) {
        campaignUpdateObj.status = STATUS.FINISHED;
        campaignUpdateObj.endOn = lastResponseOn;
        campaignUpdateObj.successCount = successCount;
        campaignUpdateObj.failedCount = failedCount;
    } else if (campaign.successCount !== successCount || campaign.failedCount !== failedCount) {
        campaignUpdateObj.status = STATUS.RUNNING;
        campaignUpdateObj.successCount = successCount;
        campaignUpdateObj.failedCount = failedCount;
    }

    if (lastResponseOn && moment().isAfter(moment(lastResponseOn).add(1, 'day'))) {
        campaignUpdateObj.status = STATUS.FINISHED;
        campaignUpdateObj.endOn = lastResponseOn;
        event = 'Status changed to FINISHED. No responses received in the past 24 hours.';
    }

    if (initialExecution) {
        if (!lastResponseOn && moment().isAfter(moment(initialExecution).add(1, 'day'))) {
            campaignUpdateObj.status = STATUS.FINISHED;
            campaignUpdateObj.endOn = moment();
            event = 'Status changed to FINISHED due to no responses from the campaign messages';
        }
    } else {
        await job.updateData({
            ...job.data,
            initialExecution: moment()
        });
    }

    if (Object.keys(campaignUpdateObj).length > 0) {
        const campaignUpdateBuiltEvent = Utils.getEventUpdateObj(
            null,
            event || `status changed to ${campaignUpdateObj.status}`,
            campaignUpdateObj
        );
        await CampaignsDAO.updateCampaign(null, campaignId, organizationId, null, null, campaignUpdateBuiltEvent);
        if (campaignUpdateObj.status === STATUS.FINISHED) {
            return true;
        }
    }

    const timestamp = Date.now() + CAMPAIGN_PROGRESS_TRACKER_DELAY;
    await job.moveToDelayed(timestamp);
};

class CampaignProgressTrackerMq {
    static startProcess() {
        const worker = new Worker(
            CAMPAIGN_PROGRESS_TRACKER_QUEUE,
            async (job) => {
                await runJob(job);
            },
            { connection }
        );
        worker.on('error', (err) => {
            log.error(err);
        });
    }

    static getQueue() {
        return queue;
    }
}

CampaignProgressTrackerMq.runJob = runJob;
module.exports = CampaignProgressTrackerMq;
