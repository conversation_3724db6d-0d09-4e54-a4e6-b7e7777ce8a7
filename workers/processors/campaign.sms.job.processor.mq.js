const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const { Queue } = require('bullmq');
const BullQueue = require('bull');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const CampaignProcessorBaseClass = require('../base.classes/campaign.processor.base.class');
const {
    QUEUES: { CAMPAIGN_SMS_JOBS_QUEUE, CAMPAIGN_JOBS_QUEUE },
    DEFAULT_OPTIONS
} = require('../constants');
const QueuedCampaignMessage = require('../../lib/db/models/queued.campaign.job.model');

const connection = RedisConnector.getConfig();

const queue = new Queue(CAMPAIGN_JOBS_QUEUE, {
    connection,
    defaultJobOptions: DEFAULT_OPTIONS
});
const campaignMessagesQueue = new BullQueue(CAMPAIGN_SMS_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: DEFAULT_OPTIONS
});

class CampaignSmsJobProcessor extends CampaignProcessorBaseClass {
    constructor(job) {
        super(job);
    }

    async buildAndAddMessageToTheQueue(organizationId, regionId, senderId, campaignId, message, member, providerName) {
        const messageBody = this.buildMessageBody(message.messageBody, member);
        this.processedMessages.push(
            new QueuedCampaignMessage({
                organizationId,
                regionId,
                memberId: member._id,
                message: {
                    messageBody
                },
                to: member.mobileNumber,
                senderId,
                providerName,
                campaignId
            })
        );
    }

    static getQueue() {
        return queue;
    }

    static getMessageQueue() {
        return campaignMessagesQueue;
    }
}

module.exports = CampaignSmsJobProcessor;
