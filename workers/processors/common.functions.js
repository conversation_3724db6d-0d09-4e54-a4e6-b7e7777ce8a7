const LoyaltyService = require('../../lib/services/LoyaltyService');

class CommonFunctions {

  static async selectMobileProviderBasedOnNumber(organizationId, regionId, to) {
    const { providerConfiguration } = await LoyaltyService.getRegionById(organizationId, regionId);
    let providersList = [];
    if (providerConfiguration?.smsProvidersList && providerConfiguration?.smsProvidersList.length > 0) {
      providersList = providerConfiguration.smsProvidersList;
    } else {
      const { configuration } = await LoyaltyService.getOrganization(organizationId);
      providersList = configuration?.providerConfiguration?.smsProvidersList;
    }

    let selectedProvider;
    for (const provider of providersList) {
      const re = new RegExp(provider.numberPattern);
      if (re.test(to)) {
        selectedProvider = provider;
        break;
      }
    }

    return selectedProvider;
  }

}

module.exports = CommonFunctions;
