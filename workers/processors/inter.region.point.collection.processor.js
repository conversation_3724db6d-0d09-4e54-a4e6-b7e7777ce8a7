const Queue = require('bull');
const Utils = require('../../lib/Utils');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const Shoutout = require('../../lib/services/Shoutout');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const { QUEUES: {
    INTER_REGION_POINTS_COLLECTION_QUEUE
} } = require('../constants');
const PointsHandler = require('./../../lib/handlers/PointsHandler');

let globalJob;

const queue = new Queue(INTER_REGION_POINTS_COLLECTION_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 4,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`inter region points collection job ${job.id} completed`);
});

const logMessage = (message, data = {}) => {
    log.info(message, data);
    globalJob.log(`${message}:\n ${JSON.stringify(data)}`);
}

class InterRegionPointCollectionProcessor {
    static startProcess() {
        queue.process(async function (job) { // don't forget to remove the done callback!
            try {
                globalJob = job;
                log.info(`starting inter region points collection processing for ${job.id}`);
                log.debug(job.data);

                const { organizationId, merchantLocationRegionId, memberRegionId, callerId,
                    pointsToAdd,
                    merchantId,
                    merchantLocationId,
                    memberId,
                    adjustPointsRegionalTransferSubtract,
                    adjustPointsRegionalTransferAdd,
                    transactionId,
                    memberAffinityGroupId,
                    memberTierId,
                    memberLastTransactionOn,
                    memberRegisteredOn,
                    activity
                } = job.data;

                logMessage(`retrieving regions...`);

                const [{ pointConfiguration: { regionalPointConversionRates } },
                    { defaultMerchantId, defaultMerchantLocationId }
                ]=await Promise.all([
                    Utils.getRegionData(organizationId, merchantLocationRegionId),
                    Utils.getRegionData(organizationId, memberRegionId)
                ]);

                await PointsHandler.interRegionPointCollection(organizationId, callerId, {
                    pointsAmount: pointsToAdd,
                    merchantId: merchantId,
                    merchantLocationId: merchantLocationId,
                    memberId: memberId,
                    subtractSubTransactionType: adjustPointsRegionalTransferSubtract,
                    memberRegion: memberRegionId,
                    addSubTransactionType: adjustPointsRegionalTransferAdd,
                    regionalPointConversionRates,
                    parentTransactionId: transactionId,
                    regionalDefaultMerchantId: defaultMerchantId,
                    regionalDefaultMerchantLocationId: defaultMerchantLocationId,
                    affinityGroupId: memberAffinityGroupId,
                    tierId: memberTierId,
                    newMember: Utils.isNewMember(memberLastTransactionOn, memberRegisteredOn),
                });
                try{
                    await Shoutout.produceActivityToTopic(activity); //this should not fail the job in case of an issue since the update is already done
                }catch(err){
                    log.error("Activity creation failed:",err);
                }
                

                logMessage(`inter region point collection complete.`);
                job.progress(100);
                return Promise.resolve();
            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(organizationId, merchantLocationRegionId, memberRegionId, callerId, pointsToAdd, merchantId, merchantLocationId, memberId, adjustPointsRegionalTransferSubtract, adjustPointsRegionalTransferAdd, transactionId, memberAffinityGroupId, memberTierId, memberLastTransactionOn, memberRegisteredOn, activity) {
        queue.add({
            organizationId,
            merchantLocationRegionId,
            memberRegionId,
            callerId,
            pointsToAdd,
            merchantId,
            merchantLocationId,
            memberId,
            adjustPointsRegionalTransferSubtract,
            adjustPointsRegionalTransferAdd,
            transactionId,
            memberAffinityGroupId,
            memberTierId,
            memberLastTransactionOn,
            memberRegisteredOn,
            activity
        });
    }

}

module.exports = InterRegionPointCollectionProcessor;
