const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const Queue = require('bull');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const MembersDAO = require('../../lib/db/dao/MembersDAO');
const MembersHandler = require('../../lib/handlers/MembersHandler');
const { Type, Status } = require('../../lib/db/models/enums/member.enums');
const CustomHttpError = require('../../lib/CustomHttpError');
const {
    QUEUES: { SECONDARY_MEMBER_REACTIVATE_QUEUE },
    DEFAULT_OPTIONS
} = require('../constants');

const log = logger(config.logger);
const queue = new Queue(SECONDARY_MEMBER_REACTIVATE_QUEUE, {
    redis: RedisConnector.getConfig(),
    defaultJobOptions: DEFAULT_OPTIONS
});

queue.on('completed', function (job) {
    log.info(`secondary member reactivate job ${job.id} completed`);
});

const runJob = async (job) => {
    try {
        log.info(`starting secondary member reactivate job processing for ${job.id}`);
        log.debug(job.data);

        const { organizationId, secondaryMemberValidatedDataObj } = job.data;

        const secondaryMember = await MembersDAO.getMemberById(
            secondaryMemberValidatedDataObj.memberId,
            organizationId,
            null,
            null,
            { type: Type.SECONDARY }
        );

        if (!secondaryMember) {
            throw new CustomHttpError('Secondary member not found', '404');
        }

        const secondaryMemberEventUpdateObj = MembersHandler.getEventUpdateObj(
            job.data?.callerId || null,
            `Status changed to ${Status.ACTIVE} because primary member was re-activated.`,
            { status: Status.ACTIVE }
        );

        const reactivateSecondaryMemberResponse = await MembersHandler.reactivateMember(
            {
                organizationId,
                callerId: job.data?.callerId || null,
                memberId: secondaryMemberValidatedDataObj?.memberId,
                status: secondaryMemberValidatedDataObj.status,
                cardId: secondaryMemberValidatedDataObj?.cardId,
                cardNumber: secondaryMemberValidatedDataObj?.cardNumber,
                waiveCardReplacementFee: secondaryMemberValidatedDataObj?.waiveCardReplacementFee,
                eventUpdateObj: secondaryMemberEventUpdateObj
            },
            { type: secondaryMemberValidatedDataObj.type },
            null,
            null,
            true
        );
        log.info(`Successfully updated secondary member status to ${Status.ACTIVE}`);
        log.debug(reactivateSecondaryMemberResponse);

        job.progress(100);
        return Promise.resolve();
    } catch (error) {
        log.error(error);
        return Promise.reject(error);
    }
};

class SecondaryMemberReactivateJobProcessor {
    static startProcess() {
        queue.process(async function (job) {
            await runJob(job);
        });
    }

    static getQueue() {
        return queue;
    }
}

module.exports = SecondaryMemberReactivateJobProcessor;
