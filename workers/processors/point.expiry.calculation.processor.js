const Queue = require('bull');
const mongoose = require('mongoose');
const moment = require('moment');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const MembersDAO = require('../../lib/db/dao/MembersDAO');
const TransactionsAnalyticsDAO = require('../../lib/db/dao/analytics/TransactionsAnalyticsDAO');
const RegionDAO = require('../../lib/db/dao/RegionDAO');
const PointExpirationCalculationJobDAO = require('../../lib/db/dao/PointExpirationCalculationJobDAO');
const { GetDataForPointExpiryCalculationAggregationV3 } = require('./../pointExpiryResources/PointExpiryAggregations');
const { QUEUES: {
    POINT_EXPIRY_CALCULATION_QUEUE
} } = require('../constants');

const dbRequestChunkSize = Number(process.env.WORKER_DB_REQUEST_CHUNK_SIZE || 500);

const queue = new Queue(POINT_EXPIRY_CALCULATION_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`point expiry calculation job ${job.id} completed`);
});


class PointExpiryCalculationProcessor {

    static async runJob(job) {
        try {
            log.info(`starting point expiry calculation processing for ${job.id}`);
            log.debug(job.data);

            const { regionId, organizationId } = job.data;

            const { pointConfiguration: { pointExpiryPeriod, pointExpiryGracePeriod, pointExpiryCalculationSubTransactionTypeIds } } = await RegionDAO.getRegion(regionId);

            const pointExpirationCalculationJob = await PointExpirationCalculationJobDAO.createPointExpirationCalculationJob({
                regionId: mongoose.Types.ObjectId(regionId),
                executionStartTime: new Date()
            }, organizationId);
            const pointExpirationCalculationJobId = pointExpirationCalculationJob._id.toString();

            const fromDate = moment().subtract(pointExpiryPeriod, 'days').toDate();
            const pointsExpireOn = moment().add(pointExpiryGracePeriod, 'days').toDate();

            const pointExpiryCalculationSubTransactionTypeIdsMapped = [];
            if (Array.isArray(pointExpiryCalculationSubTransactionTypeIds) && pointExpiryCalculationSubTransactionTypeIds.length > 0) {
                pointExpiryCalculationSubTransactionTypeIds.forEach((subTransactionTypeId) => pointExpiryCalculationSubTransactionTypeIdsMapped.push(mongoose.Types.ObjectId(subTransactionTypeId)));
            }
            const pipeline = await GetDataForPointExpiryCalculationAggregationV3(mongoose.Types.ObjectId(organizationId), mongoose.Types.ObjectId(regionId), fromDate, pointExpiryCalculationSubTransactionTypeIdsMapped);
            const dataCursor = await TransactionsAnalyticsDAO.runAggregationAndStream(pipeline);

            job.progress(10);

            let memberUpdates = [];
            let totalPointsToExpire = 0, updatedMembersCount = 0, membersToBeUpdated = 0;
            for (let row = await dataCursor.next(); row != null; row = await dataCursor.next()) {
                membersToBeUpdated++;

                let totalPreviousPointsToExpire = 0;
                if (row.member.pointsToExpire && Array.isArray(row.member.pointsToExpire) && row.member.pointsToExpire.length > 0) {
                    for (const expireObj of row.member.pointsToExpire) {
                        totalPreviousPointsToExpire += expireObj.pointsToExpire;
                    }
                }

                const pointsToExpire = row.pointsToExpire - totalPreviousPointsToExpire;
                totalPointsToExpire = totalPointsToExpire + pointsToExpire;

                if (pointsToExpire <= 0) {
                    continue;
                }

                memberUpdates.push({
                    updateOne: {
                        filter: { organizationId: mongoose.Types.ObjectId(organizationId), _id: row._id },
                        update: {
                            $push: {
                                pointsToExpire: {
                                    $each: [
                                        {
                                            pointsToExpire,
                                            pointsExpireOn
                                        }
                                    ],
                                    $sort: { pointsExpireOn: 1 }
                                }
                            },
                            $currentDate: { modifiedOn: true }
                        },
                        upsert: false
                    }
                });
                if (memberUpdates.length % dbRequestChunkSize === 0 ) {
                    const updateData = await MembersDAO.bulkUpdateMembers(memberUpdates);
                    updatedMembersCount = updatedMembersCount + updateData.result.nModified;
                    memberUpdates = [];
                }
            }

            if (memberUpdates.length > 0) {
                const updateData = await MembersDAO.bulkUpdateMembers(memberUpdates);
                updatedMembersCount = updatedMembersCount + updateData.result.nModified;
            }

            await PointExpirationCalculationJobDAO.updatePointExpirationCalculationJob({
                executionEndTime: new Date(),
                totalPointsToExpire,
                membersToBeUpdated,
                updatedMembersCount,
            }, pointExpirationCalculationJobId, organizationId);

            log.info('cleaning member cache...');
            await RedisConnector.scanAndDelete(`members:${organizationId}:*`);

            job.progress(100);
            return Promise.resolve();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static startProcess() {
        const ctx = this;
        queue.process(async function (job) {
            await ctx.runJob(job);
        });
    }

    static getQueue() {
        return queue;
    }
}

module.exports = PointExpiryCalculationProcessor;
