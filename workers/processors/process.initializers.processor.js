const config = require('../../lib/config');
const logger = require('../../lib/logger');
const ProductCatalogueGenerationProcessor = require('./product.catalogue.generation.processor');

const log = logger(config.logger);

class ProcessInitializersProcessor {
    static async startProcess() {
        try {
            log.info('running process initializers...');

            await ProductCatalogueGenerationProcessor.initializeProcess();
        } catch (err) {
            log.warn('failed to run process initializers');
            log.error(err);
        }
    }
}

module.exports = ProcessInitializersProcessor;
