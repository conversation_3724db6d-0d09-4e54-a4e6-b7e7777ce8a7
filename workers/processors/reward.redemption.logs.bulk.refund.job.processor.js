const Queue = require('bull');
const mongoose = require('mongoose');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const Member = require('../../lib/db/models/member.model');
const TransactionsDAO = require('../../lib/db/dao/TransactionsDAO');
const MembersDAO = require('../../lib/db/dao/MembersDAO');
const Utils = require('../../lib/Utils');
const RewardRedemptionLogsDAO = require('../../lib/db/dao/RewardRedemptionLogsDAO');
const Shoutout = require('../../lib/services/Shoutout');
const RedemptionLogsBulkRefundJobsDAO = require('../../lib/db/dao/RedemptionLogsBulkRefundJobsDAO');
const { QUEUES: {
    REWARD_REDEMPTION_LOGS_BULK_REFUND_QUEUE
} } = require('../constants');
const { PARTNER_STATUS, REFUND_STATUS } = require('../../lib/db/models/enums/reward.redemption.log.enums');
const { STATUS } = require('../../lib/db/models/enums/reward.redemption.logs.bulk.refund.jobs.enums');
const PointsHandler = require('../../lib/handlers/PointsHandler');
const CustomHttpError = require('../../lib/CustomHttpError');
const { getRegionData } = require('../../lib/Utils');
const { formatDateTimeToReadableRegionalTimeZone } = require('../../lib/utils/DateUtils');
const log = logger(config.logger);
const dbRequestChunkSize = Number(process.env.WORKER_DB_REQUEST_CHUNK_SIZE || 500);

const queue = new Queue(
    REWARD_REDEMPTION_LOGS_BULK_REFUND_QUEUE, 
    {
        redis: RedisConnector.getConfig(),
        defaultJobOptions: {
            attempts: 1,
            timeout: 24 * 60 * 60 * 1000,
            removeOnComplete: {
                count: 1000,
            },
            removeOnFail: {
                count: 1000,
            },
        }
    }
);

queue.on('completed', (job, result) => {
    log.info(`reward redemption logs bulk refund job ${job.id} completed.`);
});

const getEventUpdateObj = (callerId, eventDetails, setObj) => {
    return {
        $set: setObj,
        $push: {
            historyEvents: {
                $each: [
                    {
                        eventDate: new Date(),
                        eventDetails,
                        eventBy: callerId
                    }
                ],
                $sort: { eventDate: -1 }
            }
        },
        $currentDate: { modifiedOn: true },
        updatedBy: callerId
    };
};

const handleDbTransactions = async (organizationId, membersToBeUpdated, adjustmentTransactions, redemptionsToBeUpdated) => {
        const session = await Member.startSession();

        try {    
            session.startTransaction();             
            const opts = { session }; 
    
            log.debug('Adjustment transactions: ', adjustmentTransactions);
            await TransactionsDAO.bulkCreateTransactions(organizationId, adjustmentTransactions, opts);
    
            log.debug('Members to be updated: ', membersToBeUpdated);
            await MembersDAO.bulkUpdateMembers(membersToBeUpdated, opts);
    
            log.debug('Redemptions to be updated: ', redemptionsToBeUpdated);
            await RewardRedemptionLogsDAO.updateRedemptionLogBulkWrite(redemptionsToBeUpdated, opts);
    
            await session.commitTransaction();
            session.endSession();
        } catch (e) {
            await session.abortTransaction();
            session.endSession();
            throw e;        
        }
};

class RewardRedemptionLogsBulkRefundJobProcessor {
    static startProcess() {
        queue.process(async job => {
            try {
                log.info(`starting reward redemption logs bulk refund job ${job.id}`);
                log.debug(job.data);

                const jobId = job.id;
                const {
                    organizationId,
                    distributionJobId,
                    refundPointsSubTransactionId,
                    eligibleRefundCount,
                    notes,
                    createdBy: callerId,
                } = job.data;

                const filters = {
                    distributionJobId: mongoose.Types.ObjectId(distributionJobId),
                    status: { $in: [PARTNER_STATUS.PROCESSING, PARTNER_STATUS.FAILED] },
                    $or: [
                        { refundStatus: { $exists: false } }, 
                        { refundStatus: { $exists: true, $eq: REFUND_STATUS.NONE } }
                    ],
                };

                const initialJobUpdate = {
                    status: STATUS.PROCESSING,
                    startedOn: new Date(),
                }

                const updatedJob = await RedemptionLogsBulkRefundJobsDAO.updateRedemptionLogsBulkRefundJob(
                    initialJobUpdate, 
                    jobId, 
                    organizationId, 
                    { status: STATUS.PENDING }
                );

                if (!updatedJob) {
                    return Promise.reject(new Error(`Reward redemption logs bulk refund job status is not ${STATUS.PENDING}`));
                }

                const { timeZone } = await getRegionData(organizationId, updatedJob.regionId.toString());

                const redemptionsToBeRefunded = await RewardRedemptionLogsDAO.streamData(organizationId, filters);
                let membersToBeUpdated = [];
                let adjustmentTransactions = [];
                let redemptionsToBeUpdated = [];  
                let adjustmentTransactionsBullActivities = [];
                let totalPointsToBeRefunded = 0;

                for (let redemption = await redemptionsToBeRefunded.next(); redemption !== null; redemption = await redemptionsToBeRefunded.next()) {
                    const transaction = await TransactionsDAO.getTransaction(
                        organizationId, 
                        { _id: mongoose.Types.ObjectId(redemption.transactionId) }
                    );

                    const member = await MembersDAO.getMemberById(redemption.memberId, organizationId);

                    if (!transaction) {
                        throw new CustomHttpError(`Transaction with the id ${redemption.transactionId} was not found!`, '404');
                    }

                    if (!member) {
                        throw new CustomHttpError(`Member with the id ${redemption.memberId} was not found!`, '404');
                    }

                    const {
                        merchant,
                        merchantLocation,
                        subTransactionType
                    } = await PointsHandler.validateAdjustPointsRequest(
                        organizationId, 
                        transaction.merchantId?.toString(), 
                        { merchantLocationId: transaction.merchantLocationId?.toString() }, 
                        { transactionSubTypeId: refundPointsSubTransactionId?.toString() }
                    );

                    const transactionObj = {
                        organizationId: mongoose.Types.ObjectId(organizationId),
                        ...(
                            PointsHandler.buildAdjustmentTransactionObj(
                                redemption.regionId, 
                                redemption.pointsRedeemed || 0, 
                                redemption.memberId, 
                                merchant._id, 
                                merchantLocation._id, 
                                subTransactionType._id,
                                null,
                                refundPointsSubTransactionId
                            )
                        ),
                    };

                    transactionObj.idempotentKey = Utils.generateIdempotentKey(new Date().toISOString(), JSON.stringify(transactionObj));
                    let event = 'Points refunded. ';

                    adjustmentTransactions.push({
                        ...transactionObj,
                        notes,
                        createdBy: mongoose.Types.ObjectId(callerId),
                    });

                    membersToBeUpdated.push({
                        updateOne: {
                            filter: { 
                                organizationId: mongoose.Types.ObjectId(organizationId), 
                                _id: redemption.memberId 
                            },
                            update: {
                                $inc: { points: redemption.pointsRedeemed || 0 },
                                $set: { lastTransactionLocation: merchantLocation._id, },
                                $currentDate: { modifiedOn: true }
                            },
                            upsert: false
                        }
                    });

                    event += `Status changed from ${redemption.status} to ${PARTNER_STATUS.REFUNDED}`;
                    redemptionsToBeUpdated.push({
                        updateOne: {
                            filter: { 
                                organizationId: mongoose.Types.ObjectId(organizationId), 
                                _id: redemption._id 
                            },
                            update: getEventUpdateObj(
                                callerId, 
                                event, 
                                {
                                    status: PARTNER_STATUS.REFUNDED,
                                    refundStatus: REFUND_STATUS.REFUNDED,
                                    notes
                                }
                            ),
                            upsert: false
                        }
                    });
                    event = '';

                    totalPointsToBeRefunded += redemption.pointsRedeemed;

                    const transactionDate = new Date();
                    const transactionDateToRegionalTZ = formatDateTimeToReadableRegionalTimeZone(transactionDate, timeZone);
                    adjustmentTransactionsBullActivities.push({
                        data: PointsHandler.buildTransactionActivity(
                                organizationId, 
                                redemption.regionId, 
                                redemption.pointsRedeemed, 
                                redemption.memberId.toString(), 
                                transactionDate, 
                                (member.points + totalPointsToBeRefunded),
                                transactionDateToRegionalTZ,
                            { subTypeName: subTransactionType.name }
                            ),
                        opts: {
                            jobId: mongoose.Types.ObjectId().toString()
                        }
                    });

                    if (adjustmentTransactions.length % dbRequestChunkSize === 0 ) {
                        log.debug(`Splitting redemptions into chucks of ${dbRequestChunkSize}`);
                        await handleDbTransactions(organizationId, membersToBeUpdated, adjustmentTransactions, redemptionsToBeUpdated);

                        log.debug('Adjustment bull activities: ', adjustmentTransactionsBullActivities);
                        Shoutout.produceActivityToTopic(adjustmentTransactionsBullActivities);

                        adjustmentTransactions = [];
                        membersToBeUpdated = [];
                        redemptionsToBeUpdated = [];
                        adjustmentTransactionsBullActivities = [];
                    }
                }
                if (adjustmentTransactions.length > 0) {
                    await handleDbTransactions(organizationId, membersToBeUpdated, adjustmentTransactions, redemptionsToBeUpdated);
                    
                    log.debug('Adjustment bull activities: ', adjustmentTransactionsBullActivities);
                    Shoutout.produceActivityToTopic(adjustmentTransactionsBullActivities);
                }

                log.debug('All eligible redemptions were successfully refunded.');
                const completionJobUpdate = {
                    totalRecordsCount: eligibleRefundCount || 0,
                    successRecordsCount: eligibleRefundCount || 0,
                    failedRecordsCount: 0,
                    failureReason: '',
                    status: STATUS.COMPLETED,
                    completedOn: new Date(),
                };

                await RedemptionLogsBulkRefundJobsDAO.updateRedemptionLogsBulkRefundJob(completionJobUpdate, jobId, organizationId);
                job.progress(100);
                return Promise.resolve();                
            } catch (e) {
                log.error(e);
                const jobId = job.id;
                const { organizationId, eligibleRefundCount } = job.data;

                const failureJobUpdate = {
                    totalRecordsCount: eligibleRefundCount || 0,
                    successRecordsCount: 0,
                    failedRecordsCount: eligibleRefundCount || 0,
                    failureReason: e.message || 'Unknown error',
                    status: STATUS.FAILED,
                };

                await RedemptionLogsBulkRefundJobsDAO.updateRedemptionLogsBulkRefundJob(failureJobUpdate, jobId, organizationId);
                return Promise.reject(e);                
            }
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(data, jobId) {
        queue.add(data, { jobId: jobId || mongoose.Types.ObjectId().toString() });
    }
}

module.exports = RewardRedemptionLogsBulkRefundJobProcessor;
