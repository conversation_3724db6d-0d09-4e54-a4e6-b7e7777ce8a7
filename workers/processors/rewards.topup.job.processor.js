const Queue = require('bull');
const csv = require('csv-parser');
const config = require('../../lib/config');
const secretConfig = require('../../config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const { trim } = require('lodash');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const { QUEUES: {
    REWARDS_TOPUP_JOB_QUEUE
} } = require('../constants');
const { STATUS, METHOD } = require('../../lib/db/models/enums/reward.topup.enums');
const { STATUS: VOUCHER_STATUS } = require('../../lib/db/models/enums/reward.voucher.enums');
const USER_ASSETS_BUCKET = secretConfig.USER_ASSETS_BUCKET;
const RewardTopUpDAO = require('../../lib/db/dao/RewardTopUpDAO');
const StorageWrapper = require('../../lib/wrappers/StorageWrapper');
const mongoose = require('mongoose');
const Utils = require('./../../lib/Utils');
const RewardVoucherDAO = require('../../lib/db/dao/RewardVoucherDAO');
const RewardDAO = require('../../lib/db/dao/RewardDAO');
const VoucherCodeGenerator = require('voucher-code-generator');

const queue = new Queue(REWARDS_TOPUP_JOB_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`reward topup job ${job.id} completed`);
});

const mapValue = (header, value) => {
    return trim(value);
}

const buildVoucherCodesFromFile = async (fileUrl) => {
    try {
        const stream = await StorageWrapper.getFileStream(USER_ASSETS_BUCKET, fileUrl);
        const voucherCodes = [];
        const result = { totalCount: 0, voucherCodes: [] }
        let totalCount = 0;

        stream
            .pipe(csv({
                headers: false,
                mapValues: ({ header, index, value }) => {
                    return mapValue(header, value);
                }
            }))
            .on('data', async (data) => {
                log.debug(data);
                totalCount++;
                if (data && typeof data['0'] === 'string') {
                    const code = data['0'];
                    voucherCodes.push(code);
                }
            })
            .on('end', () => {
                result.totalCount = totalCount;
                result.voucherCodes = voucherCodes;
            })
            .on('error', (err) => {
                throw err;
            });

        return Promise.resolve(result)
    } catch (err) {
        log.error(err);
        return Promise.reject(err)
    }
}

class RewardsTopupJobProcessor {
    static startProcess() {
        queue.process(async function (job) {
            try {
                log.info(`starting transactions import job ${job.id}`);
                log.debug(job.data);

                const jobId = job.id;
                const { organizationId, regionId, rewardId, fileUrl, createdBy, method, requestedVouchersCount, voucherCodes } = job.data;

                const initialJobUpdate = {
                    status: STATUS.PROCESSING
                }

                const updatedJob = await RewardTopUpDAO.updateRewardTopup(initialJobUpdate, jobId, organizationId, {
                    status: STATUS.PENDING
                });
                job.progress(10);
                if (!updatedJob) {
                    return Promise.reject(new Error(`reward top up status is not ${STATUS.PENDING}`));
                }
                let totalRecordsCount = 0;
                let voucherCodesToSave = [];
                job.progress(30);
                switch (method) {
                    case METHOD.UPLOAD:
                        const { totalCount, voucherCodes: voucherCodesFromFile } = await buildVoucherCodesFromFile(fileUrl);
                        voucherCodesToSave = voucherCodesFromFile;
                        totalRecordsCount = totalCount;
                        break;
                    case METHOD.GENERATED:
                        voucherCodesToSave = VoucherCodeGenerator.generate({
                            count: requestedVouchersCount,
                            length: 5,
                            charset: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
                        });
                        totalRecordsCount = voucherCodesToSave.length;
                        break;
                    case METHOD.MANUAL:
                        voucherCodesToSave = voucherCodes;
                        totalRecordsCount = voucherCodes.length;
                        break;
                    default:

                }
                job.progress(70);
                const voucherRecords = voucherCodesToSave.map((code) => {
                    return {
                        regionId: mongoose.Types.ObjectId(regionId),
                        rewardId: mongoose.Types.ObjectId(rewardId),
                        rewardTopUpId: mongoose.Types.ObjectId(jobId),
                        voucherCode: Utils.encrypt(code, secretConfig.DATA_ENCRYPTION_SECRET),
                        voucherCodeHash: Utils.getHash(code),
                        status: VOUCHER_STATUS.OPEN
                    }
                })
                let successVouchersCount = 0;
                try {
                    const result = await RewardVoucherDAO.bulkCreateRewardVouchers(voucherRecords, organizationId, createdBy);
                    successVouchersCount = result.length;
                } catch (err) {
                    log.error(err);
                    successVouchersCount = err.insertedCount;
                }
                job.progress(90);
                const completionJobUpdate = {
                    requestedVocuhersCount: totalRecordsCount,
                    failedVouchersCount: totalRecordsCount - successVouchersCount,
                    successVouchersCount,
                    status: STATUS.SUCCESS
                }
                await RewardDAO.updateRewardWithIncrement({
                    totalCount: successVouchersCount
                }, rewardId, organizationId, createdBy);
                await RewardTopUpDAO.updateRewardTopup(completionJobUpdate, jobId, organizationId);
                job.progress(100);
                return Promise.resolve();
            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }
}

module.exports = RewardsTopupJobProcessor;
