const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../../.env')});
const fs = require("fs");
const Queue = require('bull');
const moment = require('moment');
const mongoose = require('mongoose');
const config = require('../../lib/config');
const Utils = require('../../lib/Utils');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const secretConfig = require('../../config');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const IdentityService = require('../../lib/services/IdentityService');
const { RollingPointBalanceDetailedAggregation } = require('../scheduledJobResources/Aggregations');
const TransactionsAnalyticsDAO = require('../../lib/db/dao/analytics/TransactionsAnalyticsDAO');
const JobExecutionsDAO = require('../../lib/db/dao/JobExecutionsDAO');
const JobsDAO = require('../../lib/db/dao/JobsDAO');
const OrganizationDAO = require('../../lib/db/dao/OrganizationDAO');
const RegionDAO = require('../../lib/db/dao/RegionDAO');
const { STATUS, SCHEDULE_TYPE, PROCESS_TYPE } = require('../../lib/db/models/enums/job.enums');
const { JOB_STATUS } = require('../../lib/db/models/enums/common.enums');
const ExcelJS = require('exceljs');
const parser = require('cron-parser');
const { QUEUES: {
    ROLLING_POINTS_BALANCE_DETAILED_JOBS_QUEUE
} } = require('../constants');

const queue = new Queue(ROLLING_POINTS_BALANCE_DETAILED_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 1,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`rolling point balance detailed report generation job ${job.id} completed`);
});

const generateReport = async (organizationId, regionId,  regionName, username, requestedDate, pointExpirySubTransactionType, fromDate, toDate) => {
    fromDate   = moment(fromDate).format('YYYY-MM-DD');
    toDate   = moment(toDate).format('YYYY-MM-DD');

    const reportName = `rolling_points_detailed_report-${Date.now()}.xlsx`;
    const folderLocation = `${secretConfig.TEMP_FILE_UPLOAD_PATH}/scheduled_reports`;
    const localFilePath = `${folderLocation}/${reportName}`;

    if (!fs.existsSync(folderLocation)) {
        fs.mkdirSync(folderLocation, { recursive: true });
    }

    const options = {
        filename: localFilePath,
        useStyles: true,
        useSharedStrings: true
    };
    const workbook = new ExcelJS.stream.xlsx.WorkbookWriter(options);

    const ws = workbook.addWorksheet(`${regionName} Rolling Points Report SE - F`);

    ws.addRow([`MLS ${regionName}`]).commit();
    ws.addRow([`${regionName} Rolling Points Detailed Report SE - Fin Prds ${fromDate} to ${toDate} (Scheduled Report for ${username})`]).commit();

    ws.addRow(['TTYPE', 'financial']).commit();
    ws.addRow(['START', fromDate]).commit();
    ws.addRow(['END', toDate]).commit();
    ws.addRow(['Requested', moment(requestedDate).format('YYYY-MM-DD')]).commit();
    ws.addRow(['Started', moment(requestedDate).format('YYYY-MM-DD hh:mm:ss')]).commit();

    ws.addRow([]).commit();

    const tableHeader = ws.addRow([
        'card',
        'cust_name',
        'card_type',
        'card_status',
        'active_individual',
        'linked_primary',
        'opening_bal',
        'earnings',
        'adjustments',
        'redemptions',
        'expiry_pts',
        'ending_pts',
    ]);
    tableHeader.font = {
        bold: true
    };
    tableHeader.commit();

    const membersDataCursor = await TransactionsAnalyticsDAO.runAggregationWithDateRange(RollingPointBalanceDetailedAggregation(organizationId, regionId, pointExpirySubTransactionType, fromDate), fromDate, toDate, true);

    let rowId = 12, dataLength = 0;
    for (let record = await membersDataCursor.next(); record != null; record = await membersDataCursor.next()) {
        ws.addRow([
            record.cardNumber || '',
            `${record.lastName  || ''} / ${record.firstName  || ''}`,
            record.type || '',
            record.cardNumber ? 'ASSIGNED' : 'NULL',
            record.cardNumber || '',
            record.linked_primary || '',
            record.openingBalance || 0,
            record.earnings || 0,
            record.adjustments || 0,
            record.redemptions || 0,
            record.expiryPoints || 0,
            record.ending_pts || 0,
        ]).commit();
        rowId++;
        dataLength++;
    }

    ws.commit();
    await workbook.commit();

    console.log("DONE!");

    const { fileUploadPath, url } = await Utils.uploadReportAndGenerateLink(organizationId, 'scheduled_reports', reportName, folderLocation, localFilePath);

    return Promise.resolve({
        recordsCount: dataLength,
        fileUploadPath,
        url
    });
}

// generateReport('6128e3537ed841e246e2e394', '6137a9fff48b8eb1845c78cf', 'Guyana', 'MSP', new Date(), '618d7897bde1dc1c4da6c5a7', new Date('2022-08-01'), new Date('2022-08-31')); //TODO: REMOVE

class RollingPointBalanceDetailedJobProcessor {

    static startProcess() {
        queue.process(async function (job) {
            try {
                log.info(`starting rolling point balance detailed report generation job processing for ${job.id}`);
                log.debug(job.data);

                const { organizationId, regionId, regionName, fromAddress, jobId } = job.data;
                log.info(`retrieving job details...`);
                const jobDetails = await JobsDAO.getJobById(jobId, organizationId);

                const jobExecution = await JobExecutionsDAO.createJobExecution({
                    regionId: mongoose.Types.ObjectId(regionId),
                    jobId: mongoose.Types.ObjectId(jobId),
                    status: JOB_STATUS.PROCESSING,
                    executionStartTime: new Date()
                }, organizationId);
                log.info(`created job execution`);

                log.info(`retrieving point expiry sub transaction type...`);
                const { subtransactionTypeIdMap } = await OrganizationDAO.getOrganization(organizationId);

                log.info(`retrieving username...`);
                const userNames = await IdentityService.getUsersByIds([jobDetails.createdBy?.toString()]);

                const fromDate = jobDetails?.metadata?.fromDate ? moment(jobDetails?.metadata?.fromDate).format('YYYY-MM-DD') : moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
                const toDate = jobDetails?.metadata?.toDate ? moment(jobDetails?.metadata?.toDate).format('YYYY-MM-DD') : moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD');

                log.info(`starting report generation...`);
                const { recordsCount, url, fileUploadPath } = await generateReport(organizationId, regionId, regionName, userNames[jobDetails.createdBy?.toString()] || 'Name not found', new Date(), subtransactionTypeIdMap.expirePoints, fromDate, toDate);

                //TODO: SEND A PROPER HTML BODY
                const emailBody = `You can download the generated file using the provided URL and the URL will only be valid for 24 Hours \n Additionally, you may download the file by accessing the job's execution history on the admin dashboard. \n\n File URL - ${url}`;
                log.info(`sending notification emails to "${jobDetails.notificationEmails.join(', ')}" for jobExecution: ${jobExecution._id.toString()}`);
                for (let notificationEmail of jobDetails.notificationEmails) {
                    await Utils.sendEmail(
                      organizationId,
                      regionId,
                      "SYSTEM-USER",
                      `Job results for job: ${jobDetails.name} ran on ${jobExecution.executionStartTime}`,
                      emailBody,
                      emailBody,
                      notificationEmail,
                      fromAddress
                    );
                }

                if (jobDetails.processType === PROCESS_TYPE.IMMEDIATE || jobDetails.scheduleType === SCHEDULE_TYPE.ONCE) {
                    await JobsDAO.updateJob({
                        status: STATUS.COMPLETED,
                        lastExecutionOn: new Date(),
                        nextExecutionOn: null
                    }, jobId, organizationId);
                } else {
                    const region = await RegionDAO.getRegion(regionId);
                    const interval = parser.parseExpression(jobDetails.recurringFrequency, {
                        currentDate: new Date(),
                        tz: region.timeZone,
                    });
                    const nextJobInLocaleTime = moment(interval.next().toISOString());
                    await JobsDAO.updateJob({
                        lastExecutionOn: new Date(),
                        nextExecutionOn: nextJobInLocaleTime
                    }, jobId, organizationId);
                }

                log.info(`updating job execution...`);
                await JobExecutionsDAO.updateJobExecution({
                    executionEndTime: new Date(),
                    results: {
                        fileUploadPath
                    },
                    totalRecordsCount: recordsCount,
                    status: JOB_STATUS.COMPLETED,
                }, jobExecution._id.toString(), organizationId);

                job.progress(100);
                return Promise.resolve();
            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(data, options) {
        queue.add(data,{
            jobId: mongoose.Types.ObjectId().toString(),
            ...options
        });
    }
}

module.exports = RollingPointBalanceDetailedJobProcessor;
