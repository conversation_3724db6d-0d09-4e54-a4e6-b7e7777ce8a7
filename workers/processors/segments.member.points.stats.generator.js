const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../../.env')});
require('../../lib/db/models/index');
const Queue = require('bull');
const moment = require('moment');
const mongoose = require('mongoose');
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const TransactionsAnalyticsDAO = require('../../lib/db/dao/analytics/TransactionsAnalyticsDAO');
const log = logger(config.logger);
const {
    QUEUES: {
        SEGMENTS_MEMBER_POINT_STATS_GENERATOR,
    }, DEFAULT_OPTIONS
} = require('../constants');


const queue = new Queue(SEGMENTS_MEMBER_POINT_STATS_GENERATOR, {
    redis: RedisConnector.getConfig(), defaultJobOptions: DEFAULT_OPTIONS
});

queue.on('completed', function (job) {
    log.info(`segments member stats generator job ${job.id} completed`);
});

class SegmentsMemberPointsStatsGenerator {

    static async runJob(job) {
        try {
            log.info(`starting segments member stats generator job processing for organizationId=${job.data.organizationId} regionId=${job.data.regionId}`);
            const { regionId, organizationId } = job.data;

            const startOfTheDay = moment().startOf('day');
            const endOfTheDay = moment().endOf('day');

            const pipeline = [
                {
                    '$match': {
                        'organizationId': mongoose.Types.ObjectId(organizationId),
                        'regionId': mongoose.Types.ObjectId(regionId),
                        'transactionOn': {
                            $gte: new Date(startOfTheDay),
                            $lte: new Date(endOfTheDay)
                        }
                    }
                }, {
                    '$project': {
                        'organizationId': 1,
                        'regionId': 1,
                        'memberId': 1,
                        'transactionOn': 1,
                        'redeemablePoints': 1,
                        'type': 1,
                        'transactionAmount': 1
                    }
                }, {
                    '$group': {
                        '_id': {
                            'organizationId': '$organizationId',
                            'regionId': '$regionId',
                            'memberId': '$memberId',
                            'date': {
                                '$dateToString': {
                                    'format': '%Y-%m-%d',
                                    'date': '$transactionOn'
                                }
                            }
                        },
                        'collectionCount': {
                            '$sum': {
                                '$cond': [
                                    {
                                        '$eq': [
                                            '$type', 'COLLECTION'
                                        ]
                                    }, 1, 0
                                ]
                            }
                        },
                        'redemptionCount': {
                            '$sum': {
                                '$cond': [
                                    {
                                        '$eq': [
                                            '$type', 'REDEMPTION'
                                        ]
                                    }, 1, 0
                                ]
                            }
                        },
                        'collectionSum': {
                            '$sum': {
                                '$cond': [
                                    {
                                        '$eq': [
                                            '$type', 'COLLECTION'
                                        ]
                                    }, '$redeemablePoints', 0
                                ]
                            }
                        },
                        'redemptionSum': {
                            '$sum': {
                                '$cond': [
                                    {
                                        '$eq': [
                                            '$type', 'REDEMPTION'
                                        ]
                                    }, '$redeemablePoints', 0
                                ]
                            }
                        },
                        'totalBillValue': {
                            '$sum': '$transactionAmount'
                        }
                    }
                }, {
                    '$replaceRoot': {
                        'newRoot': {
                            'organizationId': '$_id.organizationId',
                            'regionId': '$_id.regionId',
                            'memberId': '$_id.memberId',
                            'date': {
                                '$toDate': '$_id.date'
                            },
                            'transactionCount': {
                                '$sum': [
                                    '$collectionCount', '$redemptionCount'
                                ]
                            },
                            'collectionCount': '$collectionCount',
                            'redemptionCount': '$redemptionCount',
                            'collectionSum': '$collectionSum',
                            'redemptionSum': '$redemptionSum',
                            'totalBillValue': '$totalBillValue'
                        }
                    }
                },
                {
                    '$match': {
                        'transactionCount': {
                            '$gt': 0
                        }
                    }
                },
                {
                    '$merge': {
                        'into': 'points_summary_by_members',
                        'on': ['organizationId', 'regionId', 'memberId', 'date'],
                        'whenMatched': 'replace',
                        'whenNotMatched': 'insert'
                    }
                }
            ];
            await TransactionsAnalyticsDAO.runAggregation(pipeline);
            job.progress(100);
            return Promise.resolve();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static startProcess() {
        const ctx = this;
        queue.process(async function (job) {
            await ctx.runJob(job);
        });
    }

}

module.exports = SegmentsMemberPointsStatsGenerator;