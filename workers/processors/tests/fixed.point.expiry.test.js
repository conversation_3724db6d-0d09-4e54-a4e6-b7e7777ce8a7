const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });
const Transaction = require('./../../../lib/db/models/transaction.model');
const Member = require('./../../../lib/db/models/member.model');
const mongoose = require('mongoose');
const moment = require('moment');
const { objIds: { collectPointsBill } } = require('../../../.jest/testDatabase');
const PointExpiryCalculationProcessor = require('../point.expiry.calculation.processor');

const callerId = '613d08491df0e38df616f411';
const organizationId = process.env.ORGANIZATION_ID;
const regionId = process.env.REGION_ID;
const merchantId = process.env.MERCHANT_ID;
const merchantLocationId = process.env.MERCHANT_LOCATION_ID;
const memberId = process.env.MEMBER_ID;

const transactionDataset = {
    regionId,
    merchantId,
    merchantLocationId,
    memberId,
    transactionOn: moment(),
    redeemablePoints: 10,
    signedRedeemablePoints: 10,
    type: 'COLLECTION',
    subType: collectPointsBill,
    status: 'VALID'
};

const createdTransactionIds = [];

describe('Fixed point expiry processor test', () => {

    beforeAll(async () => {
        const { _id: transactionId1 } = await (new Transaction({
            ...transactionDataset, organizationId, createdBy: callerId
        })).save();
        createdTransactionIds.push(transactionId1);

        const { _id: transactionId2 } = await (new Transaction({
            ...transactionDataset,
            redeemablePoints: 100,
            signedRedeemablePoints: 100,
            transactionOn: moment().subtract(7, 'month'),
            organizationId, createdBy: callerId
        })).save();
        createdTransactionIds.push(transactionId2);

        const { _id: transactionId3 } = await (new Transaction({
            ...transactionDataset,
            redeemablePoints: 10,
            signedRedeemablePoints: -10,
            type: 'REDEMPTION',
            transactionOn: moment().subtract(7, 'month'),
            organizationId, createdBy: callerId
        })).save();
        createdTransactionIds.push(transactionId3);

        const { _id: transactionId4 } = await (new Transaction({
            ...transactionDataset,
            redeemablePoints: 10,
            signedRedeemablePoints: -10,
            type: 'REDEMPTION',
            transactionOn: moment().subtract(1, 'month'),
            organizationId, createdBy: callerId
        })).save();
        createdTransactionIds.push(transactionId4);

        return Promise.resolve();
    });

    afterAll(async () => {
        await Transaction.deleteMany(createdTransactionIds);
        return true;
    });

    test('fixed point expiry processor', async () => {
        await PointExpiryCalculationProcessor.runJob({
            data: {
                regionId,
                organizationId
            },
            progress: ()=>{}
        });
        const member = await Member.findOne({ _id: mongoose.Types.ObjectId(memberId) });
        console.log(member);
        expect(member.pointsToExpire[0].pointsToExpire).toEqual(80);
    });

});
