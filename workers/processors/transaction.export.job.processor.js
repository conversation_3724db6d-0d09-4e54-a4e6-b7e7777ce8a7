const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../../.env')});
const Queue = require('bull');
const mongoose = require('mongoose');
const Utils = require('./../../lib/Utils');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const TransactionsAnalyticsDAO = require('../../lib/db/dao/analytics/TransactionsAnalyticsDAO');
const TransactionsDAO = require('../../lib/db/dao/TransactionsDAO');
const StagedTransactionsDAO = require('../../lib/db/dao/StagedTransactionsDAO');
const JobsDAO = require('../../lib/db/dao/JobsDAO');
const JobExecutionsDAO = require('../../lib/db/dao/JobExecutionsDAO');
const { JOB_STATUS } = require('../../lib/db/models/enums/common.enums');
const { QUEUES: {
    TRANSACTIONS_EXPORT_JOBS_QUEUE
}, DEFAULT_OPTIONS } = require('../constants');

let globalJob;

const logMessage = (message, data = {}) => {
    log.info(message, data);
    globalJob.log(`${message}:\n ${JSON.stringify(data)}`);
}

const queue = new Queue(TRANSACTIONS_EXPORT_JOBS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: DEFAULT_OPTIONS
});

queue.on('completed', function (job) {
    log.info(`transactions export job ${job.id} completed`);
});

const exportTransactionsList = async (dataCursor, organizationId, stagedTransactions = false) => {
    const transactionHeaders = [
        { id: 'cardId', title: 'CARD_ID' },
        { id: 'parentTransactionId', title: 'PARENT_TRANSACTION_ID' },
        { id: 'signedRedeemablePoints', title: 'SIGNED_REDEEMABLE_POINTS' },
        { id: 'type', title: 'TYPE' },
        { id: 'subType', title: 'SUB_TYPE' },
    ];

    const stagedTransactionHeaders = [
        { id: 'transactionType', title: 'TYPE' },
        { id: 'transactionSubTypeId', title: 'SUB_TYPE' },
        { id: 'failedReason', title: 'FAILED_REASON' },
        ]
    const headers = [
        { id: 'merchantId', title: 'MERCHANT_ID' },
        { id: 'merchantLocationId', title: 'MERCHANT_LOCATION_ID' },
        { id: 'memberId', title: 'MEMBER_ID' },
        { id: 'cardNo', title: 'CARD_NO' },
        { id: 'transactionOn', title: 'TRANSACTION_ON' },
        { id: 'redeemablePoints', title: 'REDEEMABLE_POINTS' },
        { id: 'transactionAmount', title: 'TRANSACTION_AMOUNT' },
        { id: 'notes', title: 'NOTES' },
        { id: 'createdBy', title: 'CREATED_BY' },
        { id: 'status', title: 'STATUS' },
    ]

    if (stagedTransactions) {
        headers.push(...stagedTransactionHeaders);
    } else {
        headers.push(...transactionHeaders);
    }

    const { url, fileUploadPath } = await Utils.exportToCSV(dataCursor, organizationId, `transactions_list`, headers, 'transactions', true, null, 86400);
    return Promise.resolve({
        url,
        fileUploadPath
    });
}

const generateTransactionsExportFile = async (organizationId, metadata, stagedTransactions, userId) => {
    let dataCursor;
    if (stagedTransactions) {
        const filter = await StagedTransactionsDAO.getStagedTransactionsFilter(organizationId, metadata, userId);
        dataCursor = await StagedTransactionsDAO.getStagedTransactionsStream(filter);
    } else {
        const filter = await TransactionsDAO.generateTransactionsFilter(organizationId, metadata);
        dataCursor = await TransactionsAnalyticsDAO.getTransactionsStream(filter);
    }
    logMessage(`starting report generation...`);
    return await exportTransactionsList(dataCursor, organizationId, stagedTransactions);
}

class TransactionExportJobProcessor {

     static async runJob(job) {
        try {
            globalJob = job;
            logMessage(`starting transactions export job processing for ${job.id}`);
            log.debug(job.data);

            const { organizationId, regionId, jobId, fromAddress } = job.data;

            const jobDetails = await JobsDAO.getJobById(jobId, organizationId);

            const jobExecution = await JobExecutionsDAO.createJobExecution({
                regionId: mongoose.Types.ObjectId(regionId),
                jobId: mongoose.Types.ObjectId(jobId),
                status: JOB_STATUS.PROCESSING,
                executionStartTime: new Date()
            }, organizationId);
            log.info(`created job execution`);

            const { metadata: { validatedObj, stagedTransactions, userId } } = jobDetails;

            const { fileUploadPath, url } = await generateTransactionsExportFile(organizationId, validatedObj, stagedTransactions, userId);

            //TODO: SEND A PROPER HTML BODY
            const emailBody = `You can download the generated file using the provided URL and the URL will only be valid for 24 Hours \n Additionally, you may download the file by accessing the job's execution history on the admin dashboard. \n\n File URL - ${url}`;
            logMessage(`sending notification emails to "${jobDetails.notificationEmails.join(', ')}" for jobExecution: ${jobExecution._id.toString()}`);
            for (let notificationEmail of jobDetails.notificationEmails) {
                await Utils.sendEmail(
                  organizationId,
                  regionId,
                  "SYSTEM-USER",
                  `Job results for job: ${jobDetails.name} ran on ${jobExecution.executionStartTime}`,
                  emailBody,
                  emailBody,
                  notificationEmail,
                  fromAddress
                );
            }

            await JobsDAO.updateJob({
                status: JOB_STATUS.COMPLETED,
                lastExecutionOn: new Date(),
                nextExecutionOn: null
            }, jobId, organizationId);

            log.info(`updating job execution...`);
            await JobExecutionsDAO.updateJobExecution({
                executionEndTime: new Date(),
                results: {
                    fileUploadPath
                },
                status: JOB_STATUS.COMPLETED,
            }, jobExecution._id.toString(), organizationId);

            job.progress(100);
            return Promise.resolve();
        } catch (err) {
            log.error(err);
            return Promise.reject(err);
        }
    }

    static startProcess() {
        queue.process(async function (job) {
            await TransactionExportJobProcessor.runJob(job);
        });
    }

    static getQueue() {
        return queue;
    }
}

module.exports = TransactionExportJobProcessor;