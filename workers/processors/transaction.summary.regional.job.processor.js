const Queue = require('bull');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const mongoose = require("mongoose");
const {getJobById} = require("../../lib/db/dao/JobsDAO");
const {createJobExecution} = require("../../lib/db/dao/JobExecutionsDAO");
const {JOB_STATUS} = require("@shoutout-labs/constants");
const MembersDAO = require("../../lib/db/dao/MembersDAO");
const Utils = require("../../lib/Utils");
const {PROCESS_TYPE, SCHEDULE_TYPE, STATUS} = require("../../lib/db/models/enums/job.enums");
const JobsDAO = require("../../lib/db/dao/JobsDAO");
const RegionDAO = require("../../lib/db/dao/RegionDAO");
const parser = require("cron-parser");
const moment = require("moment/moment");
const JobExecutionsDAO = require("../../lib/db/dao/JobExecutionsDAO");
const OrganizationDAO = require("../../lib/db/dao/OrganizationDAO");
const {Status} = require("../../lib/db/models/enums/member.enums");
const path=require('path');
const TransactionsDAO = require("../../lib/db/dao/TransactionsDAO");
const {STATUS: TRANSACTION_STATUS} = require("../../lib/db/models/enums/transaction.enums");
const RewardDAO = require("../../lib/db/dao/RewardDAO");
const pug = require("pug");
const { QUEUES: {
    TRANSACTION_SUMMARY_REGIONAL_JOB_QUEUE,
}, DEFAULT_OPTIONS
} = require('../constants');


const queue = new Queue(TRANSACTION_SUMMARY_REGIONAL_JOB_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: DEFAULT_OPTIONS
});

queue.on('completed', function (job) {
    log.info(`monthly transaction summary generation job ${job.id} completed`);
});


class TransactionSummaryRegionalJobProcessor {
    static startProcess() {
        queue.process(async function (job) { // don't forget to remove the done callback!
            try {
                log.info(`starting monthly transaction summary job processing for ${job.id}`);
                log.debug(job.data);
                const {organizationId, regionId, jobId} = job.data;

                log.info(`retrieving job details...`);
                const [jobDetails,jobExecution,organization,rewards] = await Promise.all([
                    getJobById(jobId, organizationId),
                    createJobExecution({
                        regionId: mongoose.Types.ObjectId(regionId),
                        jobId: mongoose.Types.ObjectId(jobId),
                        status: JOB_STATUS.PROCESSING,
                        executionStartTime: new Date()
                    }, organizationId),
                    OrganizationDAO.getOrganization(organizationId),
                    RewardDAO.getRewards(organizationId, {
                    regionId,
                    limit: 1000,
                    skip: 0,
                     }, {
                    name: 1,
                    pointsStatic: 1,
                    imageUrls: 1
                },{pointsStatic: -1}),
                ]);
                log.info(`created job execution`);
                const fromAddress = organization?.configuration?.notificationConfiguration?.emailConfiguration?.fromAddress || ""
                const filterPayload = {
                    organizationId: mongoose.Types.ObjectId(organizationId),
                    regionId: mongoose.Types.ObjectId(regionId),
                    status: Status.ACTIVE,
                }
                const projection = {
                    loyaltyId: 1,
                    tierPoints: 1,
                    points: 1,
                    email: 1,
                    isValidEmail: 1,
                    firstName: 1,
                    lastName: 1
                }
                const membersDataCursor = await MembersDAO.getMemberStreamWithPopulate(filterPayload, projection);
                let recordsCount = 0;
                for (let member = await membersDataCursor.next(); member != null; member = await membersDataCursor.next()) {
                    const toAddress = member?.email
                    if (toAddress) {
                        const memberTransactionHistory = await TransactionsDAO.getTransactions(organizationId, {
                            regionId,
                            memberId: member._id,
                            limit: 3,
                            skip: 0,
                            status: TRANSACTION_STATUS.VALID,
                            transactionOnToDate: moment(),
                            transactionOnFromDate: moment().subtract(1, 'months')
                        });

                        let redeemableRewards =[];
                        if(Number(member?.points)!==0){
                            const lowestRedeemableRewardIndex= rewards?.items?.findIndex((reward) =>  reward.pointsStatic <= Number(member?.points));
                            redeemableRewards =rewards?.items?.slice(lowestRedeemableRewardIndex,lowestRedeemableRewardIndex+3);
                        }
                        const emailBody = pug.renderFile(path.join(__dirname, `../../views/transactionSummary.pug`) , {
                            organizationName: organization?.organizationName || "-",
                            organizationLogo: organization?.organizationLogoImageUrl || "",
                            points: member?.points?.toString() || "0",
                            tierPoints: member?.tierPoints?.toString() || "0",
                            tierName: member?.tierData?.name || "-",
                            transactionHistory: memberTransactionHistory?.items.map((transaction) => ({
                                transactionOn: moment(transaction?.transactionOn).format("YYYY/MM/DD"),
                                signedRedeemablePoints: transaction?.signedRedeemablePoints || "0",
                                type: transaction?.type || "",
                            })) || [],
                            rewards: redeemableRewards || [],
                        });
                        log.info(`sending notification emails to ${toAddress}`);
                        await Utils.sendEmail(
                            organizationId,
                            regionId,
                            "SYSTEM-USER",
                            `Job results for job: ${jobDetails.name} ran on ${jobExecution.executionStartTime}`,
                            emailBody,
                            emailBody,
                            toAddress,
                            fromAddress
                        );
                        recordsCount += recordsCount

                        await JobExecutionsDAO.updateJobExecution({
                            executionEndTime: new Date(),
                            updatedRecordsCount: recordsCount,
                            status: JOB_STATUS.RUNNING,
                        }, jobExecution._id.toString(), organizationId);

                    }
                }

                if (jobDetails.processType === PROCESS_TYPE.IMMEDIATE || jobDetails.scheduleType === SCHEDULE_TYPE.ONCE) {
                    await JobsDAO.updateJob({
                        status: STATUS.COMPLETED,
                        lastExecutionOn: new Date(),
                        nextExecutionOn: null
                    }, jobId, organizationId);
                } else {
                    const region = await RegionDAO.getRegion(regionId);
                    const interval = parser.parseExpression(jobDetails.recurringFrequency, {
                        currentDate: new Date(),
                        tz: region.timeZone,
                    });
                    const nextJobInLocaleTime = moment(interval.next().toISOString());
                    await JobsDAO.updateJob({
                        lastExecutionOn: new Date(),
                        nextExecutionOn: nextJobInLocaleTime
                    }, jobId, organizationId);
                }

                log.info(`updating job execution...`);
                await JobExecutionsDAO.updateJobExecution({
                    executionEndTime: new Date(),
                    totalRecordsCount: recordsCount,
                    status: JOB_STATUS.COMPLETED,
                }, jobExecution._id.toString(), organizationId);

                job.progress(100);
                return Promise.resolve();

            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }

    static addJob(organizationId, memberId, regionId) {
        queue.add({
            organizationId: organizationId, regionId: regionId, memberId: memberId
        }, {
            jobId: mongoose.Types.ObjectId().toString()
        });
    }
}

module.exports = TransactionSummaryRegionalJobProcessor;
