const Queue = require('bull');
const Utils = require('../../lib/Utils');
const config = require('../../lib/config');
const logger = require('../../lib/logger');
const moment = require('moment-timezone');
const log = logger(config.logger);
const RedisConnector = require('../../lib/db/connectors/RedisConnector');
const { QUEUES: {
    TRANSACTIONS_QUEUE
} } = require('../constants');
const { TRANSACTION_TYPE } = require('../../lib/db/models/enums/transaction.import.job.enums');
const { STATUS: STAGED_TRANSACTION_STATUS } = require('./../../lib/db/models/enums/staged.transaction.enums');
const TransactionImportJobsDAO = require('../../lib/db/dao/TransactionImportJobsDAO');
const PointRuleDAO = require('../../lib/db/dao/PointRuleDAO');
const PointsHandler = require('./../../lib/handlers/PointsHandler');
const StagedTransactionsHandler = require('./../../lib/handlers/StagedTransactionsHandler');
const { COLLECT_POINTS_CALLER_TYPES } = require('../../lib/constants/Constants');

let globalJob;

const queue = new Queue(TRANSACTIONS_QUEUE, {
    redis: RedisConnector.getConfig(), defaultJobOptions: {
        attempts: 4,
        timeout: 24 * 60 * 60 * 1000,
        removeOnComplete: {
            count: 1000,
        },
        removeOnFail: {
            count: 1000,
        },
    }
});

queue.on('completed', function (job, result) {
    log.info(`transactions import job ${job.id} completed`);
});

const createStagedTransaction = async (err, transaction, organizationId, regionId, rawData, transactionsImportJobId, callerId) => {
    log.error(err);
    logMessage(`staged transaction data`, {
        err, transaction, organizationId, regionId, rawData, transactionsImportJobId, callerId
    });
    const { merchantId, cardNo, transactionDate, transactionType, transactionSubTypeId, invoiceData, merchantLocationId } = transaction;
    const stagedTransaction = {
        organizationId,
        regionId,
        redeemablePoints: transaction.redeemablePoints,
        transactionAmount: transaction.transactionAmount,
        cardNo,
        merchantId,
        merchantLocationId,
        transactionOn: transactionDate,
        status: STAGED_TRANSACTION_STATUS.PENDING_ACTION,
        transactionType,
        transactionSubTypeId,
        invoiceData,
        rawData,
        importJobId: transactionsImportJobId,
        failedReason: err
    }
    const createdStagedTransaction = await StagedTransactionsHandler.createStagedTransactions(organizationId, stagedTransaction, callerId);
    logMessage(`created staged transaction`, createdStagedTransaction);
}

const logMessage = (message, data = {}) => {
    log.info(message, data);
    globalJob.log(`${message}:\n ${JSON.stringify(data)}`);
}

const logErrorMessage = (message, data = {}) => {
    log.error(message, data);
    globalJob.log(`${message}:\n ${JSON.stringify(data)}`);
}

const runPointRuleEvaluations = async (organizationId, member, merchantId, merchantLocationId, billValue) => {
    try {
        const affinityGroupId = member.affinityGroup?.affinityGroupId ? member.affinityGroup?.affinityGroupId.toString() : null;
        logMessage(`member affinity group`, { affinityGroupId });

        const tierId = member.tier?.tierId ? member.tier?.tierId?.toString() : null;
        logMessage(`member tier`, { tierId });

        logMessage(`running evaluation...`, { merchantId, billValue, merchantLocationId, affinityGroupId, tierId });
        const { selectedPointRule: { pointRuleId, points } } = await PointsHandler.calculatePoints(organizationId, {
            merchantId,
            billAmount: billValue,
            merchantLocationId
        }, affinityGroupId, tierId);
        logMessage(`evaluation results`, { pointRuleId, points });

        if (pointRuleId) {
            logMessage(`updating point rule...`, { pointRuleId });
            const updatedPointRule = await PointRuleDAO.updatePointRule({
                $inc: {
                    matchedCount: 1
                },
            }, pointRuleId, organizationId, null);
            logMessage(`point rule update result`, { updatedPointRule });
        }

        return Promise.resolve({ pointRuleId, points });
    } catch (err) {
        logErrorMessage(`error`, { err });
        return Promise.reject(err);
    }
}

const runImportJob = async (job) => {
    globalJob = job;
    log.info(`starting transaction processing for ${job.id}`);
    log.debug(job.data);

    const { organizationId, regionId, callerId, transactionsImportJobId, transaction, rawData, evaluatePointRules } = job.data;

    const initialJobUpdate = {
        $inc: {
            processedRecordsCount: 1
        }
    }

    logMessage(`transaction`, transaction);

    logMessage(`retrieving region...`);
    const region = await Utils.getRegionData(organizationId, regionId);
    if (region && region.timeZone) {
        try {
            logMessage(`region and time zone found. attaching timezone...`, { timeZone: region.timeZone });
            transaction.transactionDate = moment.tz(transaction.transactionDate, region.timeZone).toDate();
            logMessage(`timezone attached date`, { date: transaction.transactionDate });
        } catch (e) {
            logErrorMessage(`couldn't attach timezone`, { err: e });
        }
    }

    if (transaction.invoiceData?.invoiceId) {
        transaction.idempotentKey = Utils.getHash(`${transaction.invoiceData?.invoiceId}-${transaction.transactionType}-${transaction.billAmount}-${transaction.merchantId}-${transaction.merchantLocationId}`)
    }

    if (evaluatePointRules) {
        logMessage(`negative bill amount. running point rule evaluations...`);
        const absoluteBillValue = Math.abs(transaction.billAmount);
        logMessage(`absolute bill value`, absoluteBillValue);

        const { merchantId, merchantLocationId } = transaction;

        logMessage(`retrieving member...` );
        const member = await PointsHandler.getMemberDetails(transaction, organizationId);
        logMessage(`member retrieved.` );
        const { points } = await runPointRuleEvaluations(organizationId, member, merchantId, merchantLocationId, absoluteBillValue);

        log.info(`retrieving sub transaction type ids map...`);
        const { subtransactionTypeIdMap: { transactionImportMinusAdjustment } } = await Utils.getOrganizationData(organizationId);

        delete transaction.billAmount;
        delete transaction.invoiceData;
        transaction.pointsAmount = points;
        transaction.transactionType = TRANSACTION_TYPE.ADJUSTMENT;
        transaction.transactionSubTypeId = transactionImportMinusAdjustment.toString();

        logMessage(`updated transaction after point rule evaluations`, transaction);
    }

    let transactionType = transaction.transactionType, transactionSubTypeId = transaction.transactionSubTypeId;
    if (transaction.stagedTransaction) {
        logMessage(`staged transaction found.`);
        if (transaction.billAmount) {
            transaction.transactionAmount = transaction.billAmount;
        }
        if (transaction.pointsAmount) {
            transaction.redeemablePoints = transaction.pointsAmount;
        }
        logMessage(`creating staged transaction...`);
        await createStagedTransaction('Unauthorized location', transaction, organizationId, regionId, rawData, transactionsImportJobId, callerId);
        initialJobUpdate['$inc']['failedRecordsCount'] = 1;
    } else {
        try {
            logMessage(`valid transaction found.`);
            if (transaction.merchantLocationCode) {
                logMessage(`merchant location code found. deleting location _id...`);
                delete transaction.merchantLocationId;
            }
            if (transactionType === TRANSACTION_TYPE.COLLECTION) {
                if (transaction.transactionType) delete transaction.transactionType;
                if (transaction.transactionSubTypeId) delete transaction.transactionSubTypeId;
                logMessage(`collecting points...`, {
                    organizationId, transaction, callerId, transactionsImportJobId
                });
                const collectResult = await PointsHandler.collectPoints(organizationId, transaction, callerId, null, transactionsImportJobId, null, {}, false, transaction.pointsAmount ? COLLECT_POINTS_CALLER_TYPES.COLLECT_POINTS_AMOUNT : COLLECT_POINTS_CALLER_TYPES.COLLECT_POINTS_BILL);
                log.info(`import job point collection successful`, collectResult);
            } else if (transactionType === TRANSACTION_TYPE.ADJUSTMENT) {
                if (transaction.transactionType) delete transaction.transactionType;
                logMessage(`adjusting points...`, {
                    organizationId, transaction, callerId, transactionsImportJobId
                });
                delete transaction.invoiceData;
                const adjustResult = await PointsHandler.adjustPoints(organizationId, transaction, callerId, null, transactionsImportJobId, true);
                log.info(`import job point adjustment successful`, adjustResult);
            }
            initialJobUpdate['$inc']['successRecordsCount'] = 1;
        } catch (err) {
            logErrorMessage(`error`, { err });
            if (transaction.billAmount) {
                transaction.transactionAmount = transaction.billAmount;
            }
            if (transaction.pointsAmount) {
                transaction.redeemablePoints = transaction.pointsAmount;
            }
            transaction.transactionType = transactionType;
            transaction.transactionSubTypeId = transactionSubTypeId;
            logMessage(`creating staged transaction...`);
            await createStagedTransaction(err.message, transaction, organizationId, regionId, rawData, transactionsImportJobId, callerId);
            initialJobUpdate['$inc']['failedRecordsCount'] = 1;
        }
    }

    logMessage(`updating transaction import job...`, {
        initialJobUpdate, transactionsImportJobId, organizationId
    });
    const updatedJob = await TransactionImportJobsDAO.updateTransactionImportJob(initialJobUpdate, transactionsImportJobId, organizationId);
    logMessage(`updated transaction import job`, updatedJob);
    if (!updatedJob) {
        return Promise.reject(new Error(`transactions import job not found by id ${transactionsImportJobId}`));
    }
    job.progress(100);
    return Promise.resolve();
}

class TransactionsProcessor {
    static startProcess() {
        queue.process(async function (job) { // don't forget to remove the done callback!
            try {
                await runImportJob(job);
            } catch (err) {
                log.error(err);
                return Promise.reject(err);
            }
        });
    }

    static getQueue() {
        return queue;
    }
}

module.exports = TransactionsProcessor;