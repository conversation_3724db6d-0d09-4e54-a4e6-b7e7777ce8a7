const path = require('path');
require('dotenv').config({path: path.resolve(__dirname, '../../.env')});
const mongoose = require('mongoose');
const moment = require('moment');
const KafkaConnector = require('../../lib/db/connectors/KafkaConnector');
const MongooseConnector = require('../../lib/db/connectors/MongooseConnector');
const PostgreSQLConnector = require('../../lib/db/connectors/PostgreSQLConnector');
const { STATUS } = require('../../lib/db/models/enums/consumer.metadata.status.enum');
const {models: { Transaction } } = PostgreSQLConnector;
const Utils = require('../../lib/Utils');
const config = require('../../lib/config');
const secretConfig = require('../../config');
const logger = require('../../lib/logger');
const log = logger(config.logger);

class TransactionsStreamConsumer {

    static TransactionModel;

    static async startProcess() {
        try {
            await PostgreSQLConnector.initialize();

            const consumer = KafkaConnector.getConsumer('transaction-consumer');
            await consumer.connect();
            log.info(`consumer with groupId: transaction-consumer connected`);

            await consumer.subscribe({
                topic: secretConfig.TRANSACTIONS_TOPIC,
                fromBeginning: true
            });
            log.info(`subscribed topic: '${secretConfig.TRANSACTIONS_TOPIC}'`);

            log.info(`initializing mongo secondary connection...`);
            const conn = await MongooseConnector.getSecondaryConnection();
            this.TransactionModel = conn.model('Transaction', new mongoose.Schema({}, { strict: false }), 'transactions');

            const context = this;
            await consumer.run({
                eachMessage: async ({ topic, partition, message }) => {
                    await context.processAndCreateTransaction({ partition, message });
                }
            });

        } catch (e) {
            log.error(e);
            return Promise.reject(e);
        }
    }

    static async processAndCreateTransaction({ partition, message }) {
        try {
            log.info(`Received data from partition: ${partition} and message: ${message.value.toString()} into ${secretConfig.KAFKA_CLIENT_ID}`);
            const transaction = JSON.parse(message.value.toString());

            const updateResult = await this.TransactionModel.findOneAndUpdate({
                _id: mongoose.Types.ObjectId(transaction._id),
                organizationId: mongoose.Types.ObjectId(transaction.organizationId),
                'consumerMetadata.analyticsProcessor': STATUS.PENDING,
            }, {
                $set: {
                    'consumerMetadata.analyticsProcessor': STATUS.PROCESSING,
                }, $currentDate: { updatedOn: true },
            }, {
                upsert: false, new: true, returnOriginal: false,
            });

            if (updateResult) {
                const updateObj = {
                    'consumerMetadata.analyticsProcessor': STATUS.COMPLETED,
                };

                const member = await Utils.getMemberFromCache(transaction.organizationId, transaction.memberId);
                let memberAge = -1;
                if (member.birthDate) {
                    memberAge = moment().diff(moment(member.birthDate), 'years');
                }

                const subType = await Utils.getSubTypeFromCache(transaction.organizationId, transaction.subType);
                const { subtransactionTypeIdMap: { expirePoints } } = await Utils.getOrganizationData(transaction.organizationId);
                const isExpiryPoints = expirePoints.toString() === subType._id.toString();

                const insertResult = await Transaction.create({
                    transactionId: transaction._id,
                    memberType: member.type,
                    gender: member.gender,
                    memberAge,
                    subTypeOperation: subType.operationType,
                    isExpiryPoints,
                    ...transaction
                });
                log.info(`Insert success! row id: ${insertResult.id}`);

                const updateResult = await this.TransactionModel.findOneAndUpdate({
                    _id: mongoose.Types.ObjectId(transaction._id),
                    organizationId: mongoose.Types.ObjectId(transaction.organizationId),
                    'consumerMetadata.analyticsProcessor': STATUS.COMPLETED,
                }, {
                    $set: updateObj, $currentDate: { updatedOn: true },
                }, {
                    upsert: false, new: true, returnOriginal: false,
                });

                if (updateResult) {
                    log.info(`transaction updated!`);
                } else {
                    log.error(`couldn't update the transaction`);
                }

            } else {
                log.warn(`analytics processor: couldn't process the transaction. possible duplicate transaction from the transactions topic`);
            }
        } catch (e) {
            log.error(e);
            log.error(`error while processing the transaction: ${message.value.toString()}`);
            const transaction = JSON.parse(message.value.toString());
            await this.TransactionModel.findOneAndUpdate({
                _id: mongoose.Types.ObjectId(transaction._id),
                organizationId: mongoose.Types.ObjectId(transaction.organizationId)
            }, {
                $set: {
                    'consumerMetadata.analyticsProcessor': STATUS.FAILED,
                }, $currentDate: { updatedOn: true },
            }, {
                upsert: false, new: true, returnOriginal: false,
            });
        }
    }

}

module.exports = TransactionsStreamConsumer;