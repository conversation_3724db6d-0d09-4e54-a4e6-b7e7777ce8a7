const CampaignJobsQueue = require('../processors/campaign.sms.job.processor').getQueue();

CampaignJobsQueue.add('SMS', {
    message: {
        messageBody: "Dear Customer, our newest outlet is now open in Dikwella, Matara. Visit now to enjoy your favorite P&S goodies."
    },
    organizationId: "63933b349f814fecb255266b",
    regionId: "6393a17143923c516bfd7960",
    channel: "SMS",
    senderId: "PnSFamFirst",
    providerName: "ShoutOUT",
    segmentFilters: [
        "{\"$or\":[{\"merchantLocationId\":\"63a51d7c4b25200529a87523\"},{\"merchantLocationId\":\"642a9719b315b9f89433af65\"},{\"merchantLocationId\":\"64256055a3215bd2c959eaac\"},{\"merchantLocationId\":\"651b956041275e829006b2e0\"},{\"merchantLocationId\":\"642adc79a3215bd2c95a955c\"},{\"merchantLocationId\":\"6427c2d4b315b9f894336be1\"}]}"
    ],
    campaignId: "662f4f5cd4d0de76a0dcdcc0",
    skip: 50
}, {
    jobId: `63933b349f814fecb255266b:6393a17143923c516bfd7960:662f4f5cd4d0de76a0dcdcc023`,
    removeOnComplete: 1000,
    removeOnFail: 1000
});